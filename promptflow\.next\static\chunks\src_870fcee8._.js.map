{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAM6C;QAN7C,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD,GAN7C;IAOb,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/supabase-browser.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Single Supabase client instance to prevent multiple GoTrueClient warnings\nexport const supabaseBrowser = createBrowserClient(supabaseUrl, supabaseAnonKey, {\n  auth: {\n    persistSession: true,\n    autoRefreshToken: true,\n    detectSessionInUrl: true,\n    flowType: 'pkce',\n    debug: process.env.NODE_ENV === 'development',\n    storageKey: 'sb-iqehopwgrczylqliajww-auth-token',\n    storage: typeof window !== 'undefined' ? window.localStorage : undefined,\n  },\n  db: {\n    schema: 'public',\n  },\n  realtime: {\n    params: {\n      eventsPerSecond: 10, // Rate limit realtime events\n    },\n  },\n  global: {\n    headers: {\n      'X-Client-Info': 'promptflow-web',\n      'X-Client-Version': '1.0.0',\n    }\n  }\n})\n\n// Session debug için\nif (typeof window !== 'undefined') {\n  supabaseBrowser.auth.onAuthStateChange((event, session) => {\n    console.log(`🔐 [SUPABASE_BROWSER] Auth state change: ${event}`, {\n      hasSession: !!session,\n      userId: session?.user?.id,\n      email: session?.user?.email,\n      expiresAt: session?.expires_at ? new Date(session.expires_at * 1000).toISOString() : null\n    })\n    \n    // Manually set cookies for server-side access\n    if (session) {\n      const maxAge = Math.round((session.expires_at! * 1000 - Date.now()) / 1000)\n      document.cookie = `sb-iqehopwgrczylqliajww-auth-token=${JSON.stringify(session)}; path=/; max-age=${maxAge}; SameSite=Lax; secure=${location.protocol === 'https:'}`\n      console.log(`🍪 [SUPABASE_BROWSER] Set auth cookie with maxAge: ${maxAge}s`)\n    } else {\n      document.cookie = 'sb-iqehopwgrczylqliajww-auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'\n      console.log(`🍪 [SUPABASE_BROWSER] Cleared auth cookie`)\n    }\n  })\n}\n"], "names": [], "mappings": ";;;AAEoB;AAFpB;AAAA;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,kBAAkB,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,iBAAiB;IAC/E,MAAM;QACJ,gBAAgB;QAChB,kBAAkB;QAClB,oBAAoB;QACpB,UAAU;QACV,OAAO,oDAAyB;QAChC,YAAY;QACZ,SAAS,uCAAgC,OAAO,YAAY,GAAG;IACjE;IACA,IAAI;QACF,QAAQ;IACV;IACA,UAAU;QACR,QAAQ;YACN,iBAAiB;QACnB;IACF;IACA,QAAQ;QACN,SAAS;YACP,iBAAiB;YACjB,oBAAoB;QACtB;IACF;AACF;AAEA,qBAAqB;AACrB,wCAAmC;IACjC,gBAAgB,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO;YAGnC,eACD;QAHT,QAAQ,GAAG,CAAC,AAAC,4CAAiD,OAAN,QAAS;YAC/D,YAAY,CAAC,CAAC;YACd,MAAM,EAAE,oBAAA,+BAAA,gBAAA,QAAS,IAAI,cAAb,oCAAA,cAAe,EAAE;YACzB,KAAK,EAAE,oBAAA,+BAAA,iBAAA,QAAS,IAAI,cAAb,qCAAA,eAAe,KAAK;YAC3B,WAAW,CAAA,oBAAA,8BAAA,QAAS,UAAU,IAAG,IAAI,KAAK,QAAQ,UAAU,GAAG,MAAM,WAAW,KAAK;QACvF;QAEA,8CAA8C;QAC9C,IAAI,SAAS;YACX,MAAM,SAAS,KAAK,KAAK,CAAC,CAAC,QAAQ,UAAU,GAAI,OAAO,KAAK,GAAG,EAAE,IAAI;YACtE,SAAS,MAAM,GAAG,AAAC,sCAAiF,OAA5C,KAAK,SAAS,CAAC,UAAS,sBAAoD,OAAhC,QAAO,2BAAwD,OAA/B,SAAS,QAAQ,KAAK;YAC1J,QAAQ,GAAG,CAAC,AAAC,sDAA4D,OAAP,QAAO;QAC3E,OAAO;YACL,SAAS,MAAM,GAAG;YAClB,QAAQ,GAAG,CAAE;QACf;IACF;AACF", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/supabase.ts"], "sourcesContent": ["// Import and re-export the browser client as the main client to avoid multiple instances\nimport { supabaseBrowser } from './supabase-browser'\nexport { supabaseBrowser as supabase } from './supabase-browser'\n\n// Session debug için\nif (typeof window !== 'undefined') {\n  supabaseBrowser.auth.onAuthStateChange((event, session) => {\n    console.log(`🔐 [SUPABASE_CLIENT] Auth state change: ${event}`, {\n      hasSession: !!session,\n      userId: session?.user?.id,\n      email: session?.user?.email,\n      expiresAt: session?.expires_at ? new Date(session.expires_at * 1000).toISOString() : null\n    })\n  })\n}\n\n// Global auth error handler\nif (typeof window !== 'undefined') {\n  supabaseBrowser.auth.onAuthStateChange((event) => {\n    if (event === 'SIGNED_OUT') {\n      // Oturum sonlandığında localStorage'ı temizle\n      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\n      if (supabaseUrl) {\n        localStorage.removeItem('sb-' + supabaseUrl.split('//')[1].split('.')[0] + '-auth-token')\n      }\n    }\n  })\n}\n\n// Database types\nexport interface Database {\n  public: {\n    Tables: {\n      projects: {\n        Row: {\n          id: string\n          user_id: string\n          name: string\n          context_text: string\n          description?: string\n          status?: string\n          tags?: string[]\n          is_public?: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          name: string\n          context_text?: string\n          description?: string\n          status?: string\n          tags?: string[]\n          is_public?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          name?: string\n          context_text?: string\n          description?: string\n          status?: string\n          tags?: string[]\n          is_public?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      prompts: {\n        Row: {\n          id: string\n          project_id: string\n          user_id: string\n          prompt_text: string\n          title: string | null\n          description: string | null\n          category: string | null\n          tags: string[]\n          order_index: number\n          is_used: boolean\n          is_favorite: boolean\n          usage_count: number\n          last_used_at: string | null\n          task_code: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          project_id: string\n          user_id: string\n          prompt_text: string\n          title?: string\n          description?: string\n          category?: string\n          tags?: string[]\n          order_index: number\n          is_used?: boolean\n          is_favorite?: boolean\n          usage_count?: number\n          last_used_at?: string\n          task_code?: string\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          project_id?: string\n          user_id?: string\n          prompt_text?: string\n          title?: string\n          description?: string\n          category?: string\n          tags?: string[]\n          order_index?: number\n          is_used?: boolean\n          is_favorite?: boolean\n          usage_count?: number\n          last_used_at?: string\n          task_code?: string\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      plan_types: {\n        Row: {\n          id: string\n          name: string\n          display_name: string\n          description: string | null\n          price_monthly: number\n          price_yearly: number\n          max_projects: number\n          max_prompts_per_project: number\n          features: Record<string, boolean | string | number>\n          is_active: boolean\n          sort_order: number\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          name: string\n          display_name: string\n          description?: string\n          price_monthly?: number\n          price_yearly?: number\n          max_projects?: number\n          max_prompts_per_project?: number\n          features?: Record<string, boolean | string | number>\n          is_active?: boolean\n          sort_order?: number\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          name?: string\n          display_name?: string\n          description?: string\n          price_monthly?: number\n          price_yearly?: number\n          max_projects?: number\n          max_prompts_per_project?: number\n          features?: Record<string, boolean | string | number>\n          is_active?: boolean\n          sort_order?: number\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      user_plans: {\n        Row: {\n          id: string\n          user_id: string\n          plan_type_id: string\n          status: string\n          billing_cycle: string\n          started_at: string\n          expires_at: string | null\n          cancelled_at: string | null\n          trial_ends_at: string | null\n          cancellation_reason: string | null\n          refund_status: string\n          refund_amount: number\n          auto_renew: boolean\n          payment_method: string | null\n          subscription_id: string | null\n          metadata: Record<string, boolean | string | number>\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          plan_type_id: string\n          status?: string\n          billing_cycle?: string\n          started_at?: string\n          expires_at?: string | null\n          cancelled_at?: string | null\n          trial_ends_at?: string | null\n          cancellation_reason?: string | null\n          refund_status?: string\n          refund_amount?: number\n          auto_renew?: boolean\n          payment_method?: string | null\n          subscription_id?: string | null\n          metadata?: Record<string, boolean | string | number>\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          plan_type_id?: string\n          status?: string\n          billing_cycle?: string\n          started_at?: string\n          expires_at?: string | null\n          cancelled_at?: string | null\n          trial_ends_at?: string | null\n          cancellation_reason?: string | null\n          refund_status?: string\n          refund_amount?: number\n          auto_renew?: boolean\n          payment_method?: string | null\n          subscription_id?: string | null\n          metadata?: Record<string, boolean | string | number>\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      usage_stats: {\n        Row: {\n          id: string\n          user_id: string\n          stat_date: string\n          projects_count: number\n          prompts_count: number\n          api_calls_count: number\n          storage_used_mb: number\n          last_activity_at: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          stat_date?: string\n          projects_count?: number\n          prompts_count?: number\n          api_calls_count?: number\n          storage_used_mb?: number\n          last_activity_at?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          stat_date?: string\n          projects_count?: number\n          prompts_count?: number\n          api_calls_count?: number\n          storage_used_mb?: number\n          last_activity_at?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      plan_transactions: {\n        Row: {\n          id: string\n          user_id: string\n          from_plan_id: string | null\n          to_plan_id: string\n          transaction_type: string\n          amount: number\n          currency: string\n          payment_status: string\n          payment_provider: string | null\n          payment_reference: string | null\n          notes: string | null\n          processed_at: string | null\n          created_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          from_plan_id?: string | null\n          to_plan_id: string\n          transaction_type: string\n          amount?: number\n          currency?: string\n          payment_status?: string\n          payment_provider?: string | null\n          payment_reference?: string | null\n          notes?: string | null\n          processed_at?: string | null\n          created_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          from_plan_id?: string | null\n          to_plan_id?: string\n          transaction_type?: string\n          amount?: number\n          currency?: string\n          payment_status?: string\n          payment_provider?: string | null\n          payment_reference?: string | null\n          notes?: string | null\n          processed_at?: string | null\n          created_at?: string\n        }\n      }\n    }\n    Functions: {\n      get_user_active_plan: {\n        Args: { user_uuid: string }\n        Returns: {\n          plan_id: string\n          plan_name: string\n          display_name: string\n          max_projects: number\n          max_prompts_per_project: number\n          features: Record<string, boolean | string | number>\n          status: string\n          expires_at: string | null\n        }[]\n      }\n      check_user_limits: {\n        Args: { user_uuid: string }\n        Returns: {\n          current_projects: number\n          current_prompts: number\n          max_projects: number\n          max_prompts_per_project: number\n          can_create_project: boolean\n          can_create_prompt: boolean\n        }[]\n      }\n      change_user_plan: {\n        Args: {\n          user_uuid: string\n          new_plan_name: string\n          billing_cycle_param?: string\n          payment_reference_param?: string\n        }\n        Returns: string\n      }\n      shared_prompts: {\n        Row: {\n          id: string\n          prompt_id: string\n          user_id: string\n          share_token: string\n          title: string | null\n          description: string | null\n          is_public: boolean\n          password_hash: string | null\n          expires_at: string | null\n          view_count: number\n          copy_count: number\n          is_active: boolean\n          metadata: any\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          prompt_id: string\n          user_id: string\n          share_token: string\n          title?: string | null\n          description?: string | null\n          is_public?: boolean\n          password_hash?: string | null\n          expires_at?: string | null\n          view_count?: number\n          copy_count?: number\n          is_active?: boolean\n          metadata?: any\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          prompt_id?: string\n          user_id?: string\n          share_token?: string\n          title?: string | null\n          description?: string | null\n          is_public?: boolean\n          password_hash?: string | null\n          expires_at?: string | null\n          view_count?: number\n          copy_count?: number\n          is_active?: boolean\n          metadata?: any\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      shared_prompt_views: {\n        Row: {\n          id: string\n          shared_prompt_id: string\n          viewer_ip: string | null\n          viewer_user_agent: string | null\n          referrer: string | null\n          viewed_at: string\n          session_id: string | null\n        }\n        Insert: {\n          id?: string\n          shared_prompt_id: string\n          viewer_ip?: string | null\n          viewer_user_agent?: string | null\n          referrer?: string | null\n          viewed_at?: string\n          session_id?: string | null\n        }\n        Update: {\n          id?: string\n          shared_prompt_id?: string\n          viewer_ip?: string | null\n          viewer_user_agent?: string | null\n          referrer?: string | null\n          viewed_at?: string\n          session_id?: string | null\n        }\n      }\n    }\n  }\n}"], "names": [], "mappings": "AAAA,yFAAyF;;AAqB/D;AApB1B;;;AAGA,qBAAqB;AACrB,wCAAmC;IACjC,oIAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO;YAGnC,eACD;QAHT,QAAQ,GAAG,CAAC,AAAC,2CAAgD,OAAN,QAAS;YAC9D,YAAY,CAAC,CAAC;YACd,MAAM,EAAE,oBAAA,+BAAA,gBAAA,QAAS,IAAI,cAAb,oCAAA,cAAe,EAAE;YACzB,KAAK,EAAE,oBAAA,+BAAA,iBAAA,QAAS,IAAI,cAAb,qCAAA,eAAe,KAAK;YAC3B,WAAW,CAAA,oBAAA,8BAAA,QAAS,UAAU,IAAG,IAAI,KAAK,QAAQ,UAAU,GAAG,MAAM,WAAW,KAAK;QACvF;IACF;AACF;AAEA,4BAA4B;AAC5B,wCAAmC;IACjC,oIAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACtC,IAAI,UAAU,cAAc;YAC1B,8CAA8C;YAC9C,MAAM;YACN,wCAAiB;gBACf,aAAa,UAAU,CAAC,QAAQ,YAAY,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;YAC7E;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/database-optimization.ts"], "sourcesContent": ["/**\n * Database Query Optimization\n * Supabase query optimization, indexing and connection pooling\n */\n\nimport { supabase } from '@/lib/supabase'\nimport { BrowserCache, CachePerformance } from '@/lib/cache-strategies'\n\n// Optimized query configurations\nexport const QUERY_CONFIGS = {\n  // Pagination settings\n  pagination: {\n    defaultLimit: 20,\n    maxLimit: 100,\n    prefetchPages: 2\n  },\n  \n  // Field selection for different use cases\n  fields: {\n    // Minimal fields for lists\n    projectList: 'id, name, description, created_at, updated_at',\n    promptList: 'id, title, content, hashtags, created_at, updated_at, project_id',\n    userBasic: 'id, email, created_at',\n    \n    // Detailed fields for single items\n    projectDetail: `\n      id, name, description, created_at, updated_at, user_id,\n      prompts(count),\n      user:users(email)\n    `,\n    promptDetail: `\n      id, title, content, hashtags, created_at, updated_at, project_id,\n      project:projects(name),\n      user:projects(user:users(email))\n    `,\n    \n    // Aggregated data\n    userStats: `\n      id,\n      projects(count),\n      prompts:projects(prompts(count))\n    `\n  },\n  \n  // Index hints for complex queries\n  indexHints: {\n    projectsByUser: 'user_id, updated_at',\n    promptsByProject: 'project_id, created_at',\n    searchPrompts: 'title, content, hashtags'\n  }\n} as const\n\n// Query performance monitoring\nclass QueryPerformanceMonitor {\n  private static metrics = new Map<string, {\n    count: number\n    totalTime: number\n    avgTime: number\n    slowQueries: number\n  }>()\n\n  static startQuery(queryKey: string): () => void {\n    const startTime = Date.now()\n    \n    return () => {\n      const endTime = Date.now()\n      const duration = endTime - startTime\n      \n      this.recordQuery(queryKey, duration)\n      \n      // Log slow queries\n      if (duration > 1000) {\n        console.warn(`🐌 [SLOW_QUERY] ${queryKey} took ${duration}ms`)\n      }\n    }\n  }\n\n  private static recordQuery(queryKey: string, duration: number): void {\n    const existing = this.metrics.get(queryKey) || {\n      count: 0,\n      totalTime: 0,\n      avgTime: 0,\n      slowQueries: 0\n    }\n\n    existing.count++\n    existing.totalTime += duration\n    existing.avgTime = existing.totalTime / existing.count\n    \n    if (duration > 1000) {\n      existing.slowQueries++\n    }\n\n    this.metrics.set(queryKey, existing)\n    \n    // Record in cache performance system\n    if (duration < 100) {\n      CachePerformance.recordCacheHit(queryKey, duration)\n    } else {\n      CachePerformance.recordCacheMiss(queryKey, duration)\n    }\n  }\n\n  static getMetrics() {\n    return Array.from(this.metrics.entries())\n      .map(([key, metric]) => ({\n        query: key,\n        ...metric,\n        slowQueryRate: metric.slowQueries / metric.count\n      }))\n      .sort((a, b) => b.avgTime - a.avgTime)\n  }\n}\n\n// Optimized query builders\nexport class OptimizedQueries {\n  // Projects with optimized field selection\n  static async getProjects(options: {\n    userId?: string\n    limit?: number\n    offset?: number\n    search?: string\n    orderBy?: 'created_at' | 'updated_at' | 'name'\n    ascending?: boolean\n  } = {}) {\n    const endTimer = QueryPerformanceMonitor.startQuery('getProjects')\n    \n    try {\n      const {\n        userId,\n        limit = QUERY_CONFIGS.pagination.defaultLimit,\n        offset = 0,\n        search,\n        orderBy = 'updated_at',\n        ascending = false\n      } = options\n\n      let query = supabase\n        .from('projects')\n        .select(QUERY_CONFIGS.fields.projectList)\n\n      // Apply filters\n      if (userId) {\n        query = query.eq('user_id', userId)\n      }\n\n      if (search) {\n        query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`)\n      }\n\n      // Apply ordering and pagination\n      query = query\n        .order(orderBy, { ascending })\n        .range(offset, offset + limit - 1)\n\n      const { data, error, count } = await query\n\n      if (error) throw error\n\n      return { data, count, hasMore: (count || 0) > offset + limit }\n    } finally {\n      endTimer()\n    }\n  }\n\n  // Prompts with optimized joins\n  static async getPrompts(options: {\n    projectId?: string\n    userId?: string\n    limit?: number\n    offset?: number\n    search?: string\n    hashtags?: string[]\n  } = {}) {\n    const endTimer = QueryPerformanceMonitor.startQuery('getPrompts')\n    \n    try {\n      const {\n        projectId,\n        userId,\n        limit = QUERY_CONFIGS.pagination.defaultLimit,\n        offset = 0,\n        search,\n        hashtags\n      } = options\n\n      let query = supabase\n        .from('prompts')\n        .select(QUERY_CONFIGS.fields.promptList)\n\n      // Apply filters\n      if (projectId) {\n        query = query.eq('project_id', projectId)\n      }\n\n      if (userId) {\n        query = query.eq('user_id', userId)\n      }\n\n      if (search) {\n        query = query.or(`title.ilike.%${search}%,content.ilike.%${search}%`)\n      }\n\n      if (hashtags && hashtags.length > 0) {\n        query = query.overlaps('hashtags', hashtags)\n      }\n\n      // Apply ordering and pagination\n      query = query\n        .order('updated_at', { ascending: false })\n        .range(offset, offset + limit - 1)\n\n      const { data, error, count } = await query\n\n      if (error) throw error\n\n      return { data, count, hasMore: (count || 0) > offset + limit }\n    } finally {\n      endTimer()\n    }\n  }\n\n  // User statistics with aggregation\n  static async getUserStats(userId: string) {\n    const endTimer = QueryPerformanceMonitor.startQuery('getUserStats')\n    \n    try {\n      // Check cache first\n      const cacheKey = `user-stats-${userId}`\n      const cached = BrowserCache.getMemory<any>(cacheKey)\n      if (cached) {\n        endTimer()\n        return cached\n      }\n\n      const { data, error } = await supabase\n        .from('users')\n        .select(QUERY_CONFIGS.fields.userStats)\n        .eq('id', userId)\n        .single()\n\n      if (error) throw error\n\n      // Process aggregated data\n      const stats = {\n        projectCount: data.projects?.[0]?.count || 0,\n        promptCount: data.prompts?.reduce((total: number, project: any) => \n          total + (project.prompts?.[0]?.count || 0), 0) || 0\n      }\n\n      // Cache for 5 minutes\n      BrowserCache.setMemory(cacheKey, stats, 5 * 60 * 1000)\n\n      return stats\n    } finally {\n      endTimer()\n    }\n  }\n\n  // Batch operations for better performance\n  static async batchCreatePrompts(prompts: Array<{\n    title: string\n    content: string\n    project_id: string\n    hashtags?: string[]\n  }>) {\n    const endTimer = QueryPerformanceMonitor.startQuery('batchCreatePrompts')\n    \n    try {\n      const { data, error } = await supabase\n        .from('prompts')\n        .insert(prompts)\n        .select(QUERY_CONFIGS.fields.promptList)\n\n      if (error) throw error\n\n      return data\n    } finally {\n      endTimer()\n    }\n  }\n\n  // Optimized search with full-text search\n  static async searchContent(options: {\n    query: string\n    type?: 'projects' | 'prompts' | 'all'\n    userId?: string\n    limit?: number\n  }) {\n    const endTimer = QueryPerformanceMonitor.startQuery('searchContent')\n    \n    try {\n      const { query: searchQuery, type = 'all', userId, limit = 20 } = options\n\n      const results: any = { projects: [], prompts: [] }\n\n      if (type === 'projects' || type === 'all') {\n        let projectQuery = supabase\n          .from('projects')\n          .select(QUERY_CONFIGS.fields.projectList)\n          .or(`name.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`)\n          .limit(limit)\n\n        if (userId) {\n          projectQuery = projectQuery.eq('user_id', userId)\n        }\n\n        const { data: projects } = await projectQuery\n        results.projects = projects || []\n      }\n\n      if (type === 'prompts' || type === 'all') {\n        let promptQuery = supabase\n          .from('prompts')\n          .select(QUERY_CONFIGS.fields.promptList)\n          .or(`title.ilike.%${searchQuery}%,content.ilike.%${searchQuery}%`)\n          .limit(limit)\n\n        if (userId) {\n          promptQuery = promptQuery.eq('user_id', userId)\n        }\n\n        const { data: prompts } = await promptQuery\n        results.prompts = prompts || []\n      }\n\n      return results\n    } finally {\n      endTimer()\n    }\n  }\n}\n\n// Connection pooling and optimization\nexport class ConnectionOptimizer {\n  private static connectionPool = new Map<string, any>()\n  private static maxConnections = 10\n  private static connectionTimeout = 30000 // 30 seconds\n\n  static async getOptimizedConnection(key: string = 'default') {\n    const existing = this.connectionPool.get(key)\n    \n    if (existing && Date.now() - existing.created < this.connectionTimeout) {\n      return existing.client\n    }\n\n    // Create new optimized connection\n    const client = supabase\n    \n    this.connectionPool.set(key, {\n      client,\n      created: Date.now()\n    })\n\n    // Cleanup old connections\n    if (this.connectionPool.size > this.maxConnections) {\n      const oldest = Array.from(this.connectionPool.entries())\n        .sort(([,a], [,b]) => a.created - b.created)[0]\n      \n      this.connectionPool.delete(oldest[0])\n    }\n\n    return client\n  }\n\n  static clearConnections() {\n    this.connectionPool.clear()\n  }\n}\n\n// Database performance monitoring\nexport class DatabasePerformance {\n  static getPerformanceReport() {\n    return {\n      queryMetrics: QueryPerformanceMonitor.getMetrics(),\n      connectionPool: {\n        active: ConnectionOptimizer['connectionPool'].size,\n        max: ConnectionOptimizer['maxConnections']\n      },\n      cacheMetrics: CachePerformance.getCacheMetrics()\n    }\n  }\n\n  static logSlowQueries() {\n    const metrics = QueryPerformanceMonitor.getMetrics()\n    const slowQueries = metrics.filter(m => m.avgTime > 500)\n    \n    if (slowQueries.length > 0) {\n      console.warn('🐌 [SLOW_QUERIES] Found slow queries:', slowQueries)\n    }\n  }\n}\n\n// Export optimized query hooks\nexport { QueryPerformanceMonitor }\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAED;AAAA;AACA;;;;AAGO,MAAM,gBAAgB;IAC3B,sBAAsB;IACtB,YAAY;QACV,cAAc;QACd,UAAU;QACV,eAAe;IACjB;IAEA,0CAA0C;IAC1C,QAAQ;QACN,2BAA2B;QAC3B,aAAa;QACb,YAAY;QACZ,WAAW;QAEX,mCAAmC;QACnC,eAAgB;QAKhB,cAAe;QAMf,kBAAkB;QAClB,WAAY;IAKd;IAEA,kCAAkC;IAClC,YAAY;QACV,gBAAgB;QAChB,kBAAkB;QAClB,eAAe;IACjB;AACF;AAEA,+BAA+B;AAC/B,MAAM;IAQJ,OAAO,WAAW,QAAgB,EAAc;QAC9C,MAAM,YAAY,KAAK,GAAG;QAE1B,OAAO;YACL,MAAM,UAAU,KAAK,GAAG;YACxB,MAAM,WAAW,UAAU;YAE3B,IAAI,CAAC,WAAW,CAAC,UAAU;YAE3B,mBAAmB;YACnB,IAAI,WAAW,MAAM;gBACnB,QAAQ,IAAI,CAAC,AAAC,mBAAmC,OAAjB,UAAS,UAAiB,OAAT,UAAS;YAC5D;QACF;IACF;IAEA,OAAe,YAAY,QAAgB,EAAE,QAAgB,EAAQ;QACnE,MAAM,WAAW,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa;YAC7C,OAAO;YACP,WAAW;YACX,SAAS;YACT,aAAa;QACf;QAEA,SAAS,KAAK;QACd,SAAS,SAAS,IAAI;QACtB,SAAS,OAAO,GAAG,SAAS,SAAS,GAAG,SAAS,KAAK;QAEtD,IAAI,WAAW,MAAM;YACnB,SAAS,WAAW;QACtB;QAEA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU;QAE3B,qCAAqC;QACrC,IAAI,WAAW,KAAK;YAClB,oIAAA,CAAA,mBAAgB,CAAC,cAAc,CAAC,UAAU;QAC5C,OAAO;YACL,oIAAA,CAAA,mBAAgB,CAAC,eAAe,CAAC,UAAU;QAC7C;IACF;IAEA,OAAO,aAAa;QAClB,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,IACnC,GAAG,CAAC;gBAAC,CAAC,KAAK,OAAO;mBAAM;gBACvB,OAAO;gBACP,GAAG,MAAM;gBACT,eAAe,OAAO,WAAW,GAAG,OAAO,KAAK;YAClD;WACC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,OAAO,GAAG,EAAE,OAAO;IACzC;AACF;AA1DE,yKADI,yBACW,WAAU,IAAI;AA6DxB,MAAM;IACX,0CAA0C;IAC1C,aAAa,cAOL;YAPiB,UAAA,iEAOrB,CAAC;QACH,MAAM,WAAW,wBAAwB,UAAU,CAAC;QAEpD,IAAI;YACF,MAAM,EACJ,MAAM,EACN,QAAQ,cAAc,UAAU,CAAC,YAAY,EAC7C,SAAS,CAAC,EACV,MAAM,EACN,UAAU,YAAY,EACtB,YAAY,KAAK,EAClB,GAAG;YAEJ,IAAI,QAAQ,mLAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,YACL,MAAM,CAAC,cAAc,MAAM,CAAC,WAAW;YAE1C,gBAAgB;YAChB,IAAI,QAAQ;gBACV,QAAQ,MAAM,EAAE,CAAC,WAAW;YAC9B;YAEA,IAAI,QAAQ;gBACV,QAAQ,MAAM,EAAE,CAAC,AAAC,eAA4C,OAA9B,QAAO,yBAA8B,OAAP,QAAO;YACvE;YAEA,gCAAgC;YAChC,QAAQ,MACL,KAAK,CAAC,SAAS;gBAAE;YAAU,GAC3B,KAAK,CAAC,QAAQ,SAAS,QAAQ;YAElC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM;YAErC,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE;gBAAM;gBAAO,SAAS,CAAC,SAAS,CAAC,IAAI,SAAS;YAAM;QAC/D,SAAU;YACR;QACF;IACF;IAEA,+BAA+B;IAC/B,aAAa,aAOL;YAPgB,UAAA,iEAOpB,CAAC;QACH,MAAM,WAAW,wBAAwB,UAAU,CAAC;QAEpD,IAAI;YACF,MAAM,EACJ,SAAS,EACT,MAAM,EACN,QAAQ,cAAc,UAAU,CAAC,YAAY,EAC7C,SAAS,CAAC,EACV,MAAM,EACN,QAAQ,EACT,GAAG;YAEJ,IAAI,QAAQ,mLAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,WACL,MAAM,CAAC,cAAc,MAAM,CAAC,UAAU;YAEzC,gBAAgB;YAChB,IAAI,WAAW;gBACb,QAAQ,MAAM,EAAE,CAAC,cAAc;YACjC;YAEA,IAAI,QAAQ;gBACV,QAAQ,MAAM,EAAE,CAAC,WAAW;YAC9B;YAEA,IAAI,QAAQ;gBACV,QAAQ,MAAM,EAAE,CAAC,AAAC,gBAAyC,OAA1B,QAAO,qBAA0B,OAAP,QAAO;YACpE;YAEA,IAAI,YAAY,SAAS,MAAM,GAAG,GAAG;gBACnC,QAAQ,MAAM,QAAQ,CAAC,YAAY;YACrC;YAEA,gCAAgC;YAChC,QAAQ,MACL,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM,GACvC,KAAK,CAAC,QAAQ,SAAS,QAAQ;YAElC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM;YAErC,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE;gBAAM;gBAAO,SAAS,CAAC,SAAS,CAAC,IAAI,SAAS;YAAM;QAC/D,SAAU;YACR;QACF;IACF;IAEA,mCAAmC;IACnC,aAAa,aAAa,MAAc,EAAE;QACxC,MAAM,WAAW,wBAAwB,UAAU,CAAC;QAEpD,IAAI;gBAmBc,iBAAA,gBACD;YAnBf,oBAAoB;YACpB,MAAM,WAAW,AAAC,cAAoB,OAAP;YAC/B,MAAM,SAAS,oIAAA,CAAA,eAAY,CAAC,SAAS,CAAM;YAC3C,IAAI,QAAQ;gBACV;gBACA,OAAO;YACT;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,mLAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,cAAc,MAAM,CAAC,SAAS,EACrC,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,OAAO,MAAM;YAEjB,0BAA0B;YAC1B,MAAM,QAAQ;gBACZ,cAAc,EAAA,iBAAA,KAAK,QAAQ,cAAb,sCAAA,kBAAA,cAAe,CAAC,EAAE,cAAlB,sCAAA,gBAAoB,KAAK,KAAI;gBAC3C,aAAa,EAAA,gBAAA,KAAK,OAAO,cAAZ,oCAAA,cAAc,MAAM,CAAC,CAAC,OAAe;wBACvC,mBAAA;2BAAT,QAAQ,CAAC,EAAA,mBAAA,QAAQ,OAAO,cAAf,wCAAA,oBAAA,gBAAiB,CAAC,EAAE,cAApB,wCAAA,kBAAsB,KAAK,KAAI,CAAC;mBAAG,OAAM;YACtD;YAEA,sBAAsB;YACtB,oIAAA,CAAA,eAAY,CAAC,SAAS,CAAC,UAAU,OAAO,IAAI,KAAK;YAEjD,OAAO;QACT,SAAU;YACR;QACF;IACF;IAEA,0CAA0C;IAC1C,aAAa,mBAAmB,OAK9B,EAAE;QACF,MAAM,WAAW,wBAAwB,UAAU,CAAC;QAEpD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,mLAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC,SACP,MAAM,CAAC,cAAc,MAAM,CAAC,UAAU;YAEzC,IAAI,OAAO,MAAM;YAEjB,OAAO;QACT,SAAU;YACR;QACF;IACF;IAEA,yCAAyC;IACzC,aAAa,cAAc,OAK1B,EAAE;QACD,MAAM,WAAW,wBAAwB,UAAU,CAAC;QAEpD,IAAI;YACF,MAAM,EAAE,OAAO,WAAW,EAAE,OAAO,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,GAAG;YAEjE,MAAM,UAAe;gBAAE,UAAU,EAAE;gBAAE,SAAS,EAAE;YAAC;YAEjD,IAAI,SAAS,cAAc,SAAS,OAAO;gBACzC,IAAI,eAAe,mLAAA,CAAA,WAAQ,CACxB,IAAI,CAAC,YACL,MAAM,CAAC,cAAc,MAAM,CAAC,WAAW,EACvC,EAAE,CAAC,AAAC,eAAiD,OAAnC,aAAY,yBAAmC,OAAZ,aAAY,MACjE,KAAK,CAAC;gBAET,IAAI,QAAQ;oBACV,eAAe,aAAa,EAAE,CAAC,WAAW;gBAC5C;gBAEA,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM;gBACjC,QAAQ,QAAQ,GAAG,YAAY,EAAE;YACnC;YAEA,IAAI,SAAS,aAAa,SAAS,OAAO;gBACxC,IAAI,cAAc,mLAAA,CAAA,WAAQ,CACvB,IAAI,CAAC,WACL,MAAM,CAAC,cAAc,MAAM,CAAC,UAAU,EACtC,EAAE,CAAC,AAAC,gBAA8C,OAA/B,aAAY,qBAA+B,OAAZ,aAAY,MAC9D,KAAK,CAAC;gBAET,IAAI,QAAQ;oBACV,cAAc,YAAY,EAAE,CAAC,WAAW;gBAC1C;gBAEA,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM;gBAChC,QAAQ,OAAO,GAAG,WAAW,EAAE;YACjC;YAEA,OAAO;QACT,SAAU;YACR;QACF;IACF;AACF;AAGO,MAAM;IAKX,aAAa,yBAAgD;YAAzB,MAAA,iEAAc;QAChD,MAAM,WAAW,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QAEzC,IAAI,YAAY,KAAK,GAAG,KAAK,SAAS,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE;YACtE,OAAO,SAAS,MAAM;QACxB;QAEA,kCAAkC;QAClC,MAAM,SAAS,mLAAA,CAAA,WAAQ;QAEvB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK;YAC3B;YACA,SAAS,KAAK,GAAG;QACnB;QAEA,0BAA0B;QAC1B,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE;YAClD,MAAM,SAAS,MAAM,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,IAClD,IAAI,CAAC;oBAAC,GAAE,EAAE,UAAE,GAAE,EAAE;uBAAK,EAAE,OAAO,GAAG,EAAE,OAAO;cAAC,CAAC,EAAE;YAEjD,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;QACtC;QAEA,OAAO;IACT;IAEA,OAAO,mBAAmB;QACxB,IAAI,CAAC,cAAc,CAAC,KAAK;IAC3B;AACF;AAjCE,yKADW,qBACI,kBAAiB,IAAI;AACpC,yKAFW,qBAEI,kBAAiB;AAChC,yKAHW,qBAGI,qBAAoB,QAAM,aAAa;AAkCjD,MAAM;IACX,OAAO,uBAAuB;QAC5B,OAAO;YACL,cAAc,wBAAwB,UAAU;YAChD,gBAAgB;gBACd,QAAQ,mBAAmB,CAAC,iBAAiB,CAAC,IAAI;gBAClD,KAAK,mBAAmB,CAAC,iBAAiB;YAC5C;YACA,cAAc,oIAAA,CAAA,mBAAgB,CAAC,eAAe;QAChD;IACF;IAEA,OAAO,iBAAiB;QACtB,MAAM,UAAU,wBAAwB,UAAU;QAClD,MAAM,cAAc,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,GAAG;QAEpD,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,QAAQ,IAAI,CAAC,yCAAyC;QACxD;IACF;AACF", "debugId": null}}, {"offset": {"line": 463, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/dynamic-imports.ts"], "sourcesContent": ["/**\n * Dynamic Imports for Performance Optimization\n * Lazy loading heavy components and libraries\n */\n\nimport { lazy } from 'react'\n\n// Cache for dynamic imports to avoid re-loading\nconst importCache = new Map<string, Promise<any>>()\n\n/**\n * Cached dynamic import to avoid re-loading with performance tracking\n */\nexport const cachedDynamicImport = async <T>(\n  cacheKey: string,\n  importFn: () => Promise<T>\n): Promise<T> => {\n  // Create performance start mark\n  if (typeof window !== 'undefined' && window.performance) {\n    try {\n      performance.mark(`dynamic-import-${cacheKey}-start`)\n    } catch (error) {\n      // Ignore performance marking errors\n    }\n  }\n\n  if (importCache.has(cacheKey)) {\n    return importCache.get(cacheKey)\n  }\n\n  const startTime = Date.now()\n  const importPromise = importFn().then((result) => {\n    // Track import completion\n    trackDynamicImport(cacheKey, startTime)\n    return result\n  })\n\n  importCache.set(cacheKey, importPromise)\n\n  return importPromise\n}\n\n// Heavy UI Components - Lazy loaded\nexport const LazyContextGallery = lazy(() =>\n  cachedDynamicImport('context-gallery', () =>\n    import('@/components/context-gallery').then((module: any) => ({ default: module.default || module }))\n  )\n)\n\nexport const LazyPromptWorkspace = lazy(() =>\n  cachedDynamicImport('prompt-workspace', () =>\n    import('@/components/prompt-workspace').then((module: any) => ({ default: module.default || module }))\n  )\n)\n\nexport const LazyContextSidebar = lazy(() =>\n  cachedDynamicImport('context-sidebar', () =>\n    import('@/components/context-sidebar').then((module: any) => ({ default: module.default || module }))\n  )\n)\n\nexport const LazyProjectNameEditor = lazy(() => \n  cachedDynamicImport('project-name-editor', () => \n    import('@/components/project-name-editor').then(module => ({\n      default: module.ProjectNameEditor\n    }))\n  )\n)\n\n// Modal Components - Only load when needed\nexport const LazyContextCreationModal = lazy(() =>\n  cachedDynamicImport('context-creation-modal', () =>\n    import('@/components/context-creation-modal').then((module: any) => ({ default: module.default || module }))\n  )\n)\n\nexport const LazyContextEditModal = lazy(() =>\n  cachedDynamicImport('context-edit-modal', () =>\n    import('@/components/context-edit-modal').then((module: any) => ({ default: module.default || module }))\n  )\n)\n\n// Chart and Visualization Libraries\nexport const loadChartLibrary = () => \n  cachedDynamicImport('recharts', () => import('recharts'))\n\nexport const loadDateUtils = () => \n  cachedDynamicImport('date-fns', async () => {\n    const { format, parseISO, formatDistanceToNow } = await import('date-fns')\n    const { tr } = await import('date-fns/locale')\n    return { format, parseISO, formatDistanceToNow, tr }\n  })\n\n// Form and Validation Libraries\nexport const loadFormValidation = () => \n  cachedDynamicImport('zod', async () => {\n    const { z } = await import('zod')\n    return { z }\n  })\n\n// File Processing Libraries\nexport const loadFileProcessing = () => \n  cachedDynamicImport('file-processing', async () => {\n    const Papa = await import('papaparse')\n    return { Papa: Papa.default }\n  })\n\n// Rich Text Editor - Heavy component\nexport const loadRichTextEditor = () => \n  cachedDynamicImport('rich-text-editor', async () => {\n    // Placeholder for future rich text editor\n    return { RichTextEditor: null }\n  })\n\n// Code Syntax Highlighting\nexport const loadSyntaxHighlighter = () => \n  cachedDynamicImport('syntax-highlighter', async () => {\n    const { Prism } = await import('react-syntax-highlighter')\n    const { tomorrow } = await import('react-syntax-highlighter/dist/esm/styles/prism')\n    return { Prism, tomorrow }\n  })\n\n// Image Processing\nexport const loadImageProcessing = () => \n  cachedDynamicImport('image-processing', async () => {\n    // Placeholder for future image processing\n    return { ImageProcessor: null }\n  })\n\n// Animation Libraries\nexport const loadAnimationLibrary = () => \n  cachedDynamicImport('framer-motion', () => import('framer-motion'))\n\n// Utility Functions\nexport const preloadCriticalImports = () => {\n  // Preload commonly used utilities\n  loadDateUtils()\n  loadFormValidation()\n}\n\nexport const preloadPageImports = (page: string) => {\n  switch (page) {\n    case 'dashboard':\n      LazyPromptWorkspace\n      LazyContextSidebar\n      break\n    case 'profile':\n      loadFormValidation()\n      break\n    case 'templates':\n      LazyContextGallery\n      break\n  }\n}\n\n// Route-based code splitting\nexport const LazyDashboardPage = lazy(() =>\n  cachedDynamicImport('dashboard-page', () =>\n    import('@/app/dashboard/page').then((module: any) => ({ default: module.default || module }))\n  )\n)\n\nexport const LazyProfilePage = lazy(() =>\n  cachedDynamicImport('profile-page', () =>\n    import('@/app/profile/page').then((module: any) => ({ default: module.default || module }))\n  )\n)\n\nexport const LazyAuthPage = lazy(() =>\n  cachedDynamicImport('auth-page', () =>\n    import('@/app/auth/page').then((module: any) => ({ default: module.default || module }))\n  )\n)\n\n// Templates page - will be created later\n// export const LazyTemplatesPage = lazy(() =>\n//   cachedDynamicImport('templates-page', () =>\n//     import('@/app/templates/page')\n//   )\n// )\n\n// Performance monitoring for dynamic imports\nexport const trackDynamicImport = (componentName: string, startTime: number) => {\n  const loadTime = Date.now() - startTime\n\n  if (typeof window !== 'undefined' && window.performance) {\n    try {\n      // Track loading performance with defensive programming\n      const startMarkName = `dynamic-import-${componentName}-start`\n      const endMarkName = `dynamic-import-${componentName}-end`\n      const measureName = `dynamic-import-${componentName}`\n\n      // Create end mark\n      performance.mark(endMarkName)\n\n      // Check if start mark exists before measuring - use safer approach\n      try {\n        const marks = performance.getEntriesByName(startMarkName, 'mark')\n        if (marks.length > 0) {\n          performance.measure(measureName, startMarkName, endMarkName)\n        } else {\n          // Create a simple measure without deprecated navigationStart\n          const startTime = performance.timeOrigin || Date.now()\n          performance.measure(measureName, { start: startTime, end: performance.now() })\n        }\n      } catch (error) {\n        // Ignore measurement errors to prevent deprecated API warnings\n        console.debug('Performance measurement skipped:', error)\n      }\n    } catch (error) {\n      // Graceful degradation - performance tracking fails silently\n      console.warn(`Performance tracking failed for ${componentName}:`, error)\n    }\n  }\n\n  console.log(`🚀 [DYNAMIC_IMPORT] ${componentName} loaded in ${loadTime}ms`)\n}\n\n// Preload strategy based on user interaction\nexport const preloadOnHover = (importFn: () => Promise<any>) => {\n  let preloaded = false\n  \n  return {\n    onMouseEnter: () => {\n      if (!preloaded) {\n        preloaded = true\n        importFn()\n      }\n    }\n  }\n}\n\n// Intersection Observer based preloading\nexport const preloadOnVisible = (importFn: () => Promise<any>) => {\n  if (typeof window === 'undefined') return\n  \n  const observer = new IntersectionObserver(\n    (entries) => {\n      entries.forEach((entry) => {\n        if (entry.isIntersecting) {\n          importFn()\n          observer.disconnect()\n        }\n      })\n    },\n    { threshold: 0.1 }\n  )\n  \n  return observer\n}\n\n// Bundle size monitoring\nexport const getBundleInfo = () => {\n  if (typeof window === 'undefined') return null\n  \n  return {\n    cacheSize: importCache.size,\n    loadedComponents: Array.from(importCache.keys()),\n    memoryUsage: (performance as any).memory ? {\n      used: (performance as any).memory.usedJSHeapSize,\n      total: (performance as any).memory.totalJSHeapSize,\n      limit: (performance as any).memory.jsHeapSizeLimit\n    } : null\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;;;;;;;;;;AAED;;AAEA,gDAAgD;AAChD,MAAM,cAAc,IAAI;AAKjB,MAAM,sBAAsB,OACjC,UACA;IAEA,gCAAgC;IAChC,IAAI,aAAkB,eAAe,OAAO,WAAW,EAAE;QACvD,IAAI;YACF,YAAY,IAAI,CAAC,AAAC,kBAA0B,OAAT,UAAS;QAC9C,EAAE,OAAO,OAAO;QACd,oCAAoC;QACtC;IACF;IAEA,IAAI,YAAY,GAAG,CAAC,WAAW;QAC7B,OAAO,YAAY,GAAG,CAAC;IACzB;IAEA,MAAM,YAAY,KAAK,GAAG;IAC1B,MAAM,gBAAgB,WAAW,IAAI,CAAC,CAAC;QACrC,0BAA0B;QAC1B,mBAAmB,UAAU;QAC7B,OAAO;IACT;IAEA,YAAY,GAAG,CAAC,UAAU;IAE1B,OAAO;AACT;AAGO,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,OAAE,IACrC,oBAAoB,mBAAmB,IACrC,yIAAuC,IAAI,CAAC,CAAC,SAAgB,CAAC;gBAAE,SAAS,OAAO,OAAO,IAAI;YAAO,CAAC;;AAIhG,MAAM,oCAAsB,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,QAAE,IACtC,oBAAoB,oBAAoB,IACtC,0IAAwC,IAAI,CAAC,CAAC,SAAgB,CAAC;gBAAE,SAAS,OAAO,OAAO,IAAI;YAAO,CAAC;;AAIjG,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,QAAE,IACrC,oBAAoB,mBAAmB,IACrC,yIAAuC,IAAI,CAAC,CAAC,SAAgB,CAAC;gBAAE,SAAS,OAAO,OAAO,IAAI;YAAO,CAAC;;AAIhG,MAAM,sCAAwB,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,QAAE,IACxC,oBAAoB,uBAAuB,IACzC,6IAA2C,IAAI,CAAC,CAAA,SAAU,CAAC;gBACzD,SAAS,OAAO,iBAAiB;YACnC,CAAC;;AAKE,MAAM,yCAA2B,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,QAAE,IAC3C,oBAAoB,0BAA0B,IAC5C,gJAA8C,IAAI,CAAC,CAAC,SAAgB,CAAC;gBAAE,SAAS,OAAO,OAAO,IAAI;YAAO,CAAC;;AAIvG,MAAM,qCAAuB,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,SAAE,IACvC,oBAAoB,sBAAsB,IACxC,4IAA0C,IAAI,CAAC,CAAC,SAAgB,CAAC;gBAAE,SAAS,OAAO,OAAO,IAAI;YAAO,CAAC;;AAKnG,MAAM,mBAAmB,IAC9B,oBAAoB,YAAY;AAE3B,MAAM,gBAAgB,IAC3B,oBAAoB,YAAY;QAC9B,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,mBAAmB,EAAE,GAAG;QAClD,MAAM,EAAE,EAAE,EAAE,GAAG;QACf,OAAO;YAAE;YAAQ;YAAU;YAAqB;QAAG;IACrD;AAGK,MAAM,qBAAqB,IAChC,oBAAoB,OAAO;QACzB,MAAM,EAAE,CAAC,EAAE,GAAG;QACd,OAAO;YAAE;QAAE;IACb;AAGK,MAAM,qBAAqB,IAChC,oBAAoB,mBAAmB;QACrC,MAAM,OAAO;QACb,OAAO;YAAE,MAAM,KAAK,OAAO;QAAC;IAC9B;AAGK,MAAM,qBAAqB,IAChC,oBAAoB,oBAAoB;QACtC,0CAA0C;QAC1C,OAAO;YAAE,gBAAgB;QAAK;IAChC;AAGK,MAAM,wBAAwB,IACnC,oBAAoB,sBAAsB;QACxC,MAAM,EAAE,KAAK,EAAE,GAAG;QAClB,MAAM,EAAE,QAAQ,EAAE,GAAG;QACrB,OAAO;YAAE;YAAO;QAAS;IAC3B;AAGK,MAAM,sBAAsB,IACjC,oBAAoB,oBAAoB;QACtC,0CAA0C;QAC1C,OAAO;YAAE,gBAAgB;QAAK;IAChC;AAGK,MAAM,uBAAuB,IAClC,oBAAoB,iBAAiB;AAGhC,MAAM,yBAAyB;IACpC,kCAAkC;IAClC;IACA;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,OAAQ;QACN,KAAK;YACH;YACA;YACA;QACF,KAAK;YACH;YACA;QACF,KAAK;YACH;YACA;IACJ;AACF;AAGO,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,SAAE,IACpC,oBAAoB,kBAAkB,IACpC,iIAA+B,IAAI,CAAC,CAAC,SAAgB,CAAC;gBAAE,SAAS,OAAO,OAAO,IAAI;YAAO,CAAC;;AAIxF,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,SAAE,IAClC,oBAAoB,gBAAgB,IAClC,+HAA6B,IAAI,CAAC,CAAC,SAAgB,CAAC;gBAAE,SAAS,OAAO,OAAO,IAAI;YAAO,CAAC;;AAItF,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,SAAE,IAC/B,oBAAoB,aAAa,IAC/B,4HAA0B,IAAI,CAAC,CAAC,SAAgB,CAAC;gBAAE,SAAS,OAAO,OAAO,IAAI;YAAO,CAAC;;AAYnF,MAAM,qBAAqB,CAAC,eAAuB;IACxD,MAAM,WAAW,KAAK,GAAG,KAAK;IAE9B,IAAI,aAAkB,eAAe,OAAO,WAAW,EAAE;QACvD,IAAI;YACF,uDAAuD;YACvD,MAAM,gBAAgB,AAAC,kBAA+B,OAAd,eAAc;YACtD,MAAM,cAAc,AAAC,kBAA+B,OAAd,eAAc;YACpD,MAAM,cAAc,AAAC,kBAA+B,OAAd;YAEtC,kBAAkB;YAClB,YAAY,IAAI,CAAC;YAEjB,mEAAmE;YACnE,IAAI;gBACF,MAAM,QAAQ,YAAY,gBAAgB,CAAC,eAAe;gBAC1D,IAAI,MAAM,MAAM,GAAG,GAAG;oBACpB,YAAY,OAAO,CAAC,aAAa,eAAe;gBAClD,OAAO;oBACL,6DAA6D;oBAC7D,MAAM,YAAY,YAAY,UAAU,IAAI,KAAK,GAAG;oBACpD,YAAY,OAAO,CAAC,aAAa;wBAAE,OAAO;wBAAW,KAAK,YAAY,GAAG;oBAAG;gBAC9E;YACF,EAAE,OAAO,OAAO;gBACd,+DAA+D;gBAC/D,QAAQ,KAAK,CAAC,oCAAoC;YACpD;QACF,EAAE,OAAO,OAAO;YACd,6DAA6D;YAC7D,QAAQ,IAAI,CAAC,AAAC,mCAAgD,OAAd,eAAc,MAAI;QACpE;IACF;IAEA,QAAQ,GAAG,CAAC,AAAC,uBAAiD,OAA3B,eAAc,eAAsB,OAAT,UAAS;AACzE;AAGO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,YAAY;IAEhB,OAAO;QACL,cAAc;YACZ,IAAI,CAAC,WAAW;gBACd,YAAY;gBACZ;YACF;QACF;IACF;AACF;AAGO,MAAM,mBAAmB,CAAC;IAC/B;;IAEA,MAAM,WAAW,IAAI,qBACnB,CAAC;QACC,QAAQ,OAAO,CAAC,CAAC;YACf,IAAI,MAAM,cAAc,EAAE;gBACxB;gBACA,SAAS,UAAU;YACrB;QACF;IACF,GACA;QAAE,WAAW;IAAI;IAGnB,OAAO;AACT;AAGO,MAAM,gBAAgB;IAC3B;;IAEA,OAAO;QACL,WAAW,YAAY,IAAI;QAC3B,kBAAkB,MAAM,IAAI,CAAC,YAAY,IAAI;QAC7C,aAAa,AAAC,YAAoB,MAAM,GAAG;YACzC,MAAM,AAAC,YAAoB,MAAM,CAAC,cAAc;YAChD,OAAO,AAAC,YAAoB,MAAM,CAAC,eAAe;YAClD,OAAO,AAAC,YAAoB,MAAM,CAAC,eAAe;QACpD,IAAI;IACN;AACF", "debugId": null}}, {"offset": {"line": 717, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/performance-monitor.tsx"], "sourcesContent": ["/**\n * Performance Monitor Component\n * Real-time performance monitoring and metrics display\n */\n\n'use client'\n\nimport { useState, useEffect, memo } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { \n  Activity, \n  Database, \n  Clock, \n  Zap, \n  TrendingUp, \n  AlertTriangle,\n  CheckCircle,\n  XCircle\n} from 'lucide-react'\nimport { QueryPerformanceMonitor, DatabasePerformance } from '@/lib/database-optimization'\nimport { BrowserCache, CachePerformance } from '@/lib/cache-strategies'\nimport { getBundleInfo } from '@/lib/dynamic-imports'\n\ninterface PerformanceMetrics {\n  queries: Array<{\n    query: string\n    count: number\n    avgTime: number\n    slowQueryRate: number\n  }>\n  cache: {\n    localStorage: { size: number; items: number }\n    sessionStorage: { size: number; items: number }\n    memory: { items: number }\n  }\n  bundle: {\n    cacheSize: number\n    loadedComponents: string[]\n    memoryUsage: any\n  } | null\n  vitals: {\n    lcp?: number\n    fid?: number\n    cls?: number\n  }\n}\n\nconst PerformanceMonitor = memo(function PerformanceMonitor() {\n  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)\n  const [isVisible, setIsVisible] = useState(false)\n  const [autoRefresh, setAutoRefresh] = useState(true)\n\n  // Collect performance metrics\n  const collectMetrics = () => {\n    const dbPerformance = DatabasePerformance.getPerformanceReport()\n    const cacheInfo = BrowserCache.getCacheInfo()\n    const bundleInfo = getBundleInfo()\n    \n    // Web Vitals\n    const vitals: PerformanceMetrics['vitals'] = {}\n    \n    if (typeof window !== 'undefined' && 'performance' in window) {\n      try {\n        // Get LCP (Largest Contentful Paint) - use PerformanceObserver instead\n        if ('PerformanceObserver' in window) {\n          // Use modern PerformanceObserver API instead of deprecated getEntriesByType\n          const observer = new PerformanceObserver((list) => {\n            const entries = list.getEntries()\n            if (entries.length > 0) {\n              vitals.lcp = entries[entries.length - 1].startTime\n            }\n          })\n          try {\n            observer.observe({ entryTypes: ['largest-contentful-paint'] })\n            // Disconnect after first measurement\n            setTimeout(() => observer.disconnect(), 1000)\n          } catch (e) {\n            // Fallback to legacy API if needed\n            const lcpEntries = performance.getEntriesByType('largest-contentful-paint')\n            if (lcpEntries.length > 0) {\n              vitals.lcp = lcpEntries[lcpEntries.length - 1].startTime\n            }\n          }\n        }\n\n        // Get navigation timing safely\n        if (performance.timing) {\n          vitals.fid = performance.timing.loadEventEnd - performance.timing.loadEventStart\n        }\n\n        // CLS would need layout shift observer\n        vitals.cls = 0 // Placeholder\n      } catch (error) {\n        // Silently handle performance API errors\n        console.debug('Performance monitoring error:', error)\n      }\n    }\n\n    setMetrics({\n      queries: dbPerformance.queryMetrics,\n      cache: cacheInfo,\n      bundle: bundleInfo,\n      vitals\n    })\n  }\n\n  // Auto-refresh metrics\n  useEffect(() => {\n    if (!autoRefresh) return\n\n    const interval = setInterval(collectMetrics, 5000) // Every 5 seconds\n    collectMetrics() // Initial collection\n\n    return () => clearInterval(interval)\n  }, [autoRefresh])\n\n  // Performance status indicators\n  const getPerformanceStatus = (avgTime: number) => {\n    if (avgTime < 100) return { color: 'green', icon: CheckCircle, label: 'Excellent' }\n    if (avgTime < 500) return { color: 'yellow', icon: AlertTriangle, label: 'Good' }\n    return { color: 'red', icon: XCircle, label: 'Needs Improvement' }\n  }\n\n  const getVitalsStatus = (metric: string, value?: number) => {\n    if (!value) return { color: 'gray', label: 'N/A' }\n    \n    switch (metric) {\n      case 'lcp':\n        if (value < 2500) return { color: 'green', label: 'Good' }\n        if (value < 4000) return { color: 'yellow', label: 'Needs Improvement' }\n        return { color: 'red', label: 'Poor' }\n      case 'fid':\n        if (value < 100) return { color: 'green', label: 'Good' }\n        if (value < 300) return { color: 'yellow', label: 'Needs Improvement' }\n        return { color: 'red', label: 'Poor' }\n      case 'cls':\n        if (value < 0.1) return { color: 'green', label: 'Good' }\n        if (value < 0.25) return { color: 'yellow', label: 'Needs Improvement' }\n        return { color: 'red', label: 'Poor' }\n      default:\n        return { color: 'gray', label: 'Unknown' }\n    }\n  }\n\n  if (!isVisible) {\n    return (\n      <Button\n        variant=\"outline\"\n        size=\"sm\"\n        onClick={() => setIsVisible(true)}\n        className=\"fixed bottom-4 right-4 z-50 bg-white shadow-lg\"\n      >\n        <Activity className=\"h-4 w-4 mr-2\" />\n        Performance\n      </Button>\n    )\n  }\n\n  return (\n    <div className=\"fixed bottom-4 right-4 z-50 w-96 max-h-[80vh] overflow-y-auto\">\n      <Card className=\"bg-white shadow-xl border-2\">\n        <CardHeader className=\"pb-3\">\n          <div className=\"flex items-center justify-between\">\n            <CardTitle className=\"text-lg flex items-center gap-2\">\n              <Activity className=\"h-5 w-5\" />\n              Performance Monitor\n            </CardTitle>\n            <div className=\"flex gap-2\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setAutoRefresh(!autoRefresh)}\n              >\n                {autoRefresh ? 'Pause' : 'Resume'}\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={collectMetrics}\n              >\n                Refresh\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setIsVisible(false)}\n              >\n                ×\n              </Button>\n            </div>\n          </div>\n        </CardHeader>\n\n        <CardContent className=\"space-y-4\">\n          {/* Web Vitals */}\n          <div>\n            <h4 className=\"font-semibold flex items-center gap-2 mb-2\">\n              <Zap className=\"h-4 w-4\" />\n              Core Web Vitals\n            </h4>\n            <div className=\"grid grid-cols-3 gap-2\">\n              {Object.entries(metrics?.vitals || {}).map(([key, value]) => {\n                const status = getVitalsStatus(key, value)\n                return (\n                  <div key={key} className=\"text-center\">\n                    <div className=\"text-xs text-gray-500 uppercase\">{key}</div>\n                    <div className=\"font-mono text-sm\">\n                      {value ? `${Math.round(value)}ms` : 'N/A'}\n                    </div>\n                    <Badge variant={status.color as any} className=\"text-xs\">\n                      {status.label}\n                    </Badge>\n                  </div>\n                )\n              })}\n            </div>\n          </div>\n\n          {/* Query Performance */}\n          <div>\n            <h4 className=\"font-semibold flex items-center gap-2 mb-2\">\n              <Database className=\"h-4 w-4\" />\n              Query Performance\n            </h4>\n            <div className=\"space-y-2 max-h-32 overflow-y-auto\">\n              {metrics?.queries.slice(0, 5).map((query, index) => {\n                const status = getPerformanceStatus(query.avgTime)\n                const StatusIcon = status.icon\n                return (\n                  <div key={index} className=\"flex items-center justify-between text-sm\">\n                    <div className=\"flex items-center gap-2 flex-1 min-w-0\">\n                      <StatusIcon className={`h-3 w-3 text-${status.color}-500`} />\n                      <span className=\"truncate font-mono text-xs\">\n                        {query.query}\n                      </span>\n                    </div>\n                    <div className=\"flex gap-2 text-xs\">\n                      <span className=\"text-gray-500\">{query.count}x</span>\n                      <span className=\"font-mono\">{Math.round(query.avgTime)}ms</span>\n                    </div>\n                  </div>\n                )\n              })}\n            </div>\n          </div>\n\n          {/* Cache Status */}\n          <div>\n            <h4 className=\"font-semibold flex items-center gap-2 mb-2\">\n              <TrendingUp className=\"h-4 w-4\" />\n              Cache Status\n            </h4>\n            <div className=\"grid grid-cols-2 gap-2 text-sm\">\n              <div>\n                <div className=\"text-xs text-gray-500\">LocalStorage</div>\n                <div className=\"font-mono\">\n                  {metrics?.cache.localStorage.items || 0} items\n                </div>\n                <div className=\"text-xs text-gray-400\">\n                  {Math.round((metrics?.cache.localStorage.size || 0) / 1024)}KB\n                </div>\n              </div>\n              <div>\n                <div className=\"text-xs text-gray-500\">Memory Cache</div>\n                <div className=\"font-mono\">\n                  {metrics?.cache.memory.items || 0} items\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Bundle Info */}\n          {metrics?.bundle && (\n            <div>\n              <h4 className=\"font-semibold flex items-center gap-2 mb-2\">\n                <Clock className=\"h-4 w-4\" />\n                Bundle Status\n              </h4>\n              <div className=\"text-sm space-y-1\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-500\">Loaded Components:</span>\n                  <span className=\"font-mono\">{metrics.bundle.loadedComponents.length}</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-500\">Cache Size:</span>\n                  <span className=\"font-mono\">{metrics.bundle.cacheSize}</span>\n                </div>\n                {metrics.bundle.memoryUsage && (\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-gray-500\">Memory Used:</span>\n                    <span className=\"font-mono\">\n                      {Math.round(metrics.bundle.memoryUsage.used / 1024 / 1024)}MB\n                    </span>\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n\n          {/* Actions */}\n          <div className=\"pt-2 border-t\">\n            <div className=\"flex gap-2\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => {\n                  BrowserCache.clearAll()\n                  collectMetrics()\n                }}\n                className=\"flex-1\"\n              >\n                Clear Cache\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => {\n                  console.log('Performance Report:', metrics)\n                }}\n                className=\"flex-1\"\n              >\n                Log Report\n              </Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n})\n\nexport default PerformanceMonitor\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;;;AAlBA;;;;;;;;;AA4CA,MAAM,mCAAqB,GAAA,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,UAAE,SAAS;;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,8BAA8B;IAC9B,MAAM,iBAAiB;QACrB,MAAM,gBAAgB,yIAAA,CAAA,sBAAmB,CAAC,oBAAoB;QAC9D,MAAM,YAAY,oIAAA,CAAA,eAAY,CAAC,YAAY;QAC3C,MAAM,aAAa,CAAA,GAAA,mIAAA,CAAA,gBAAa,AAAD;QAE/B,aAAa;QACb,MAAM,SAAuC,CAAC;QAE9C,IAAI,aAAkB,eAAe,iBAAiB,QAAQ;YAC5D,IAAI;gBACF,uEAAuE;gBACvE,IAAI,yBAAyB,QAAQ;oBACnC,4EAA4E;oBAC5E,MAAM,WAAW,IAAI,oBAAoB,CAAC;wBACxC,MAAM,UAAU,KAAK,UAAU;wBAC/B,IAAI,QAAQ,MAAM,GAAG,GAAG;4BACtB,OAAO,GAAG,GAAG,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE,CAAC,SAAS;wBACpD;oBACF;oBACA,IAAI;wBACF,SAAS,OAAO,CAAC;4BAAE,YAAY;gCAAC;6BAA2B;wBAAC;wBAC5D,qCAAqC;wBACrC,WAAW,IAAM,SAAS,UAAU,IAAI;oBAC1C,EAAE,OAAO,GAAG;wBACV,mCAAmC;wBACnC,MAAM,aAAa,YAAY,gBAAgB,CAAC;wBAChD,IAAI,WAAW,MAAM,GAAG,GAAG;4BACzB,OAAO,GAAG,GAAG,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,SAAS;wBAC1D;oBACF;gBACF;gBAEA,+BAA+B;gBAC/B,IAAI,YAAY,MAAM,EAAE;oBACtB,OAAO,GAAG,GAAG,YAAY,MAAM,CAAC,YAAY,GAAG,YAAY,MAAM,CAAC,cAAc;gBAClF;gBAEA,uCAAuC;gBACvC,OAAO,GAAG,GAAG,GAAE,cAAc;YAC/B,EAAE,OAAO,OAAO;gBACd,yCAAyC;gBACzC,QAAQ,KAAK,CAAC,iCAAiC;YACjD;QACF;QAEA,WAAW;YACT,SAAS,cAAc,YAAY;YACnC,OAAO;YACP,QAAQ;YACR;QACF;IACF;IAEA,uBAAuB;IACvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2DAAE;YACR,IAAI,CAAC,aAAa;YAElB,MAAM,WAAW,YAAY,gBAAgB,MAAM,kBAAkB;;YACrE,kBAAiB,qBAAqB;YAEtC;mEAAO,IAAM,cAAc;;QAC7B;0DAAG;QAAC;KAAY;IAEhB,gCAAgC;IAChC,MAAM,uBAAuB,CAAC;QAC5B,IAAI,UAAU,KAAK,OAAO;YAAE,OAAO;YAAS,MAAM,8NAAA,CAAA,cAAW;YAAE,OAAO;QAAY;QAClF,IAAI,UAAU,KAAK,OAAO;YAAE,OAAO;YAAU,MAAM,2NAAA,CAAA,gBAAa;YAAE,OAAO;QAAO;QAChF,OAAO;YAAE,OAAO;YAAO,MAAM,+MAAA,CAAA,UAAO;YAAE,OAAO;QAAoB;IACnE;IAEA,MAAM,kBAAkB,CAAC,QAAgB;QACvC,IAAI,CAAC,OAAO,OAAO;YAAE,OAAO;YAAQ,OAAO;QAAM;QAEjD,OAAQ;YACN,KAAK;gBACH,IAAI,QAAQ,MAAM,OAAO;oBAAE,OAAO;oBAAS,OAAO;gBAAO;gBACzD,IAAI,QAAQ,MAAM,OAAO;oBAAE,OAAO;oBAAU,OAAO;gBAAoB;gBACvE,OAAO;oBAAE,OAAO;oBAAO,OAAO;gBAAO;YACvC,KAAK;gBACH,IAAI,QAAQ,KAAK,OAAO;oBAAE,OAAO;oBAAS,OAAO;gBAAO;gBACxD,IAAI,QAAQ,KAAK,OAAO;oBAAE,OAAO;oBAAU,OAAO;gBAAoB;gBACtE,OAAO;oBAAE,OAAO;oBAAO,OAAO;gBAAO;YACvC,KAAK;gBACH,IAAI,QAAQ,KAAK,OAAO;oBAAE,OAAO;oBAAS,OAAO;gBAAO;gBACxD,IAAI,QAAQ,MAAM,OAAO;oBAAE,OAAO;oBAAU,OAAO;gBAAoB;gBACvE,OAAO;oBAAE,OAAO;oBAAO,OAAO;gBAAO;YACvC;gBACE,OAAO;oBAAE,OAAO;oBAAQ,OAAO;gBAAU;QAC7C;IACF;IAEA,IAAI,CAAC,WAAW;QACd,qBACE,6LAAC,qIAAA,CAAA,SAAM;YACL,SAAQ;YACR,MAAK;YACL,SAAS,IAAM,aAAa;YAC5B,WAAU;;8BAEV,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;gBAAiB;;;;;;;IAI3C;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGlC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe,CAAC;kDAE9B,cAAc,UAAU;;;;;;kDAE3B,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;kDACV;;;;;;kDAGD,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,aAAa;kDAC7B;;;;;;;;;;;;;;;;;;;;;;;8BAOP,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCAErB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAG7B,6LAAC;oCAAI,WAAU;8CACZ,OAAO,OAAO,CAAC,CAAA,oBAAA,8BAAA,QAAS,MAAM,KAAI,CAAC,GAAG,GAAG,CAAC;4CAAC,CAAC,KAAK,MAAM;wCACtD,MAAM,SAAS,gBAAgB,KAAK;wCACpC,qBACE,6LAAC;4CAAc,WAAU;;8DACvB,6LAAC;oDAAI,WAAU;8DAAmC;;;;;;8DAClD,6LAAC;oDAAI,WAAU;8DACZ,QAAQ,AAAC,GAAoB,OAAlB,KAAK,KAAK,CAAC,QAAO,QAAM;;;;;;8DAEtC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAS,OAAO,KAAK;oDAAS,WAAU;8DAC5C,OAAO,KAAK;;;;;;;2CANP;;;;;oCAUd;;;;;;;;;;;;sCAKJ,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGlC,6LAAC;oCAAI,WAAU;8CACZ,oBAAA,8BAAA,QAAS,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO;wCACxC,MAAM,SAAS,qBAAqB,MAAM,OAAO;wCACjD,MAAM,aAAa,OAAO,IAAI;wCAC9B,qBACE,6LAAC;4CAAgB,WAAU;;8DACzB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAW,WAAW,AAAC,gBAA4B,OAAb,OAAO,KAAK,EAAC;;;;;;sEACpD,6LAAC;4DAAK,WAAU;sEACb,MAAM,KAAK;;;;;;;;;;;;8DAGhB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;gEAAiB,MAAM,KAAK;gEAAC;;;;;;;sEAC7C,6LAAC;4DAAK,WAAU;;gEAAa,KAAK,KAAK,CAAC,MAAM,OAAO;gEAAE;;;;;;;;;;;;;;2CATjD;;;;;oCAad;;;;;;;;;;;;sCAKJ,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGpC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;8DACvC,6LAAC;oDAAI,WAAU;;wDACZ,CAAA,oBAAA,8BAAA,QAAS,KAAK,CAAC,YAAY,CAAC,KAAK,KAAI;wDAAE;;;;;;;8DAE1C,6LAAC;oDAAI,WAAU;;wDACZ,KAAK,KAAK,CAAC,CAAC,CAAA,oBAAA,8BAAA,QAAS,KAAK,CAAC,YAAY,CAAC,IAAI,KAAI,CAAC,IAAI;wDAAM;;;;;;;;;;;;;sDAGhE,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;8DACvC,6LAAC;oDAAI,WAAU;;wDACZ,CAAA,oBAAA,8BAAA,QAAS,KAAK,CAAC,MAAM,CAAC,KAAK,KAAI;wDAAE;;;;;;;;;;;;;;;;;;;;;;;;;wBAOzC,CAAA,oBAAA,8BAAA,QAAS,MAAM,mBACd,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAG/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAK,WAAU;8DAAa,QAAQ,MAAM,CAAC,gBAAgB,CAAC,MAAM;;;;;;;;;;;;sDAErE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAK,WAAU;8DAAa,QAAQ,MAAM,CAAC,SAAS;;;;;;;;;;;;wCAEtD,QAAQ,MAAM,CAAC,WAAW,kBACzB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAK,WAAU;;wDACb,KAAK,KAAK,CAAC,QAAQ,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,OAAO;wDAAM;;;;;;;;;;;;;;;;;;;;;;;;;sCASvE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;4CACP,oIAAA,CAAA,eAAY,CAAC,QAAQ;4CACrB;wCACF;wCACA,WAAU;kDACX;;;;;;kDAGD,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;4CACP,QAAQ,GAAG,CAAC,uBAAuB;wCACrC;wCACA,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;;uCAEe", "debugId": null}}]}
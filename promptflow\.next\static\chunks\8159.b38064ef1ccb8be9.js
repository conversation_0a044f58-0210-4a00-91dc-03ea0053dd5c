"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[832,8159,8451],{7801:(e,t,s)=>{s.d(t,{X1:()=>o,Xm:()=>c,v8:()=>d});var a=s(32960),r=s(26715),l=s(5041),n=s(70478),i=s(56671);function o(){return(0,a.I)({queryKey:["shared-prompts","user"],queryFn:async()=>{let{data:e}=await n.L.auth.getUser();if(!e.user)throw Error("Not authenticated");let{data:t,error:s}=await n.L.from("shared_prompts").select("\n          *,\n          prompt:prompts(\n            id,\n            prompt_text,\n            title,\n            description,\n            category,\n            tags,\n            task_code,\n            created_at,\n            project:projects(name)\n          )\n        ").eq("user_id",e.user.id).eq("is_active",!0).order("created_at",{ascending:!1});if(s)throw s;return t.map(e=>{var t,s;return{...e,project_name:(null==(s=e.prompt)||null==(t=s.project)?void 0:t.name)||"Unknown Project"}})},staleTime:3e5})}function c(){let e=(0,r.jE)();return(0,l.n)({mutationFn:async e=>{var t;let{data:s}=await n.L.auth.getUser();if(!s.user)throw Error("Not authenticated");let{data:a,error:r}=await n.L.from("prompts").select("id, user_id").eq("id",e.prompt_id).eq("user_id",s.user.id).single();if(r||!a)throw Error("Prompt not found or access denied");let l=function(){let e=new Uint8Array(16);return crypto.getRandomValues(e),Array.from(e,e=>e.toString(16).padStart(2,"0")).join("")}(),i=null;e.password&&(i=await m(e.password));let{data:o,error:c}=await n.L.from("shared_prompts").insert({prompt_id:e.prompt_id,user_id:s.user.id,share_token:l,title:e.title||null,description:e.description||null,is_public:null==(t=e.is_public)||t,password_hash:i,expires_at:e.expires_at||null}).select().single();if(c)throw c;let d="".concat(window.location.origin,"/share/").concat(l);return{id:o.id,share_token:l,share_url:d,created_at:o.created_at}},onSuccess:t=>{e.invalidateQueries({queryKey:["shared-prompts"]}),i.oR.success("Paylaşım linki oluşturuldu!",{description:"Link panoya kopyalandı"}),navigator.clipboard.writeText(t.share_url).catch(console.error)},onError:e=>{console.error("Share creation error:",e),i.oR.error("Paylaşım oluşturulamadı",{description:e instanceof Error?e.message:"Bilinmeyen hata"})}})}function d(e,t){return(0,a.I)({queryKey:["shared-prompt",e,t],queryFn:async()=>{var s,a;let{data:r,error:l}=await n.L.from("shared_prompts").select("\n          *,\n          prompt:prompts(\n            id,\n            prompt_text,\n            title,\n            description,\n            category,\n            tags,\n            task_code,\n            created_at,\n            project:projects(name)\n          )\n        ").eq("share_token",e).eq("is_active",!0).single();if(l)throw Error("Paylaşım bulunamadı veya s\xfcresi dolmuş");if(r.password_hash&&t){if(!await u(t,r.password_hash))throw Error("Şifre yanlış")}else if(r.password_hash&&!t)throw Error("Şifre gerekli");if(r.expires_at&&new Date(r.expires_at)<new Date)throw Error("Paylaşımın s\xfcresi dolmuş");return n.L.from("shared_prompts").update({view_count:r.view_count+1}).eq("id",r.id).then(()=>{fetch("/api/shared-prompts/record-view",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({share_token:e,viewer_ip:null,viewer_user_agent:navigator.userAgent,referrer:document.referrer||null,session_id:"session_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),viewed_at:new Date().toISOString()})}).catch(console.error)}),{...r,project_name:(null==(a=r.prompt)||null==(s=a.project)?void 0:s.name)||"Unknown Project",author_email:"Unknown Author"}},enabled:!!e,staleTime:0,retry:!1})}async function m(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t)),e=>e.toString(16).padStart(2,"0")).join("")}async function u(e,t){return await m(e)===t}},8159:(e,t,s)=>{s.r(t),s.d(t,{PromptWorkspace:()=>eA,default:()=>eE});var a=s(95155),r=s(12115),l=s(30285),n=s(66695),i=s(88539),o=s(62523),c=s(66424),d=s(26126),m=s(4884),u=s(59434);let h=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(m.bL,{className:(0,u.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",s),...r,ref:t,children:(0,a.jsx)(m.zi,{className:(0,u.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});h.displayName=m.bL.displayName;var x=s(47863),p=s(66474),g=s(48021),f=s(19145),j=s(18979),v=s(40646),b=s(9428),y=s(5196),w=s(54416),N=s(56287),k=s(24357),_=s(21380),C=s(15448),A=s(57434),E=s(74783),S=s(381),z=s(47924),D=s(84616),L=s(42103),P=s(91788),K=s(93654),$=s(41978),F=s(15501),T=s(67238),q=s(32960),M=s(70478);function R(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20;return(0,q.I)({queryKey:["popular-hashtags",e,t],queryFn:async()=>{if(!e)return[];let{data:s,error:a}=await M.L.from("prompts").select("tags").eq("project_id",e);if(a)throw Error(a.message);let r={};return null==s||s.forEach(e=>{e.tags&&Array.isArray(e.tags)&&e.tags.forEach(e=>{if("string"==typeof e&&e.trim()){let t=e.startsWith("#")?e.toLowerCase():"#".concat(e.toLowerCase());r[t]=(r[t]||0)+1}})}),Object.entries(r).map(e=>{let[t,s]=e;return{hashtag:t,count:s}}).sort((e,t)=>t.count-e.count).slice(0,t)},enabled:!!e})}function H(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;return(0,q.I)({queryKey:["popular-categories",e,t],queryFn:async()=>{if(!e)return[];let{data:s,error:a}=await M.L.from("prompts").select("category").eq("project_id",e).not("category","is",null);if(a)throw Error(a.message);let r={};return null==s||s.forEach(e=>{if(e.category){let t=e.category.toLowerCase();r[t]=(r[t]||0)+1}}),Object.entries(r).map(e=>{let[t,s]=e;return{category:t,count:s}}).sort((e,t)=>t.count-e.count).slice(0,t)},enabled:!!e})}var I=s(74178),Q=s(26042),O=s(56671);function U(e){return e.replace(/^#/,"").trim().toLowerCase()}function G(e){let t=e.trim().toLowerCase();return t.startsWith("/")||(t="/"+t),(t=t.replace(/\/+/g,"/")).length>1&&t.endsWith("/")&&(t=t.slice(0,-1)),t}function B(e){let t=function(e){let t=e.match(/#[\w\u00C0-\u017F]+/g);return t?t.map(e=>e.toLowerCase()):[]}(e),s=function(e){let t=e.match(/\/[\w\u00C0-\u017F\/]+/g);return t?t.map(e=>e.toLowerCase()):[]}(e);return{hashtags:t.map(U),folderPaths:s.map(G),originalText:e}}function W(e,t){return[...new Set([...e,...t].filter(e=>e.trim().length>0))]}function Z(e){let{hashtags:t,onHashtagsChange:s,suggestions:n=[],placeholder:i="Etiket ekleyin... (\xf6rn: #frontend, #api)",className:c,maxTags:m=10,disabled:h=!1}=e,[x,p]=(0,r.useState)(""),[g,f]=(0,r.useState)(!1),[j,v]=(0,r.useState)(-1),b=(0,r.useRef)(null),y=(0,r.useRef)(null),N=(function(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,a=U(e);return a?t.filter(e=>e.toLowerCase().includes(a)).slice(0,s):[]})(x,n,5).filter(e=>!t.includes(e)),k=e=>{let a=U(e);if(!a||!function(e){let t=U(e);return/^[\w\u00C0-\u017F]+$/.test(t)&&t.length>0}(a))return;let r=function(e){let t=U(e);return t?"#".concat(t):""}(a);t.includes(r)||t.length>=m||(s([...t,r]),p(""),f(!1),v(-1))},_=e=>{s(t.filter((t,s)=>s!==e))};return(0,r.useEffect)(()=>{let e=e=>{var t;!y.current||y.current.contains(e.target)||(null==(t=b.current)?void 0:t.contains(e.target))||f(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]),(0,a.jsxs)("div",{className:(0,u.cn)("space-y-2",c),children:[t.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:t.map((e,t)=>(0,a.jsxs)(d.E,{variant:"secondary",className:"flex items-center gap-1 text-xs bg-blue-100 text-blue-800 hover:bg-blue-200",children:[(0,a.jsx)(C.A,{className:"w-3 h-3"}),e.replace("#",""),!h&&(0,a.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-0 w-3 h-3 hover:bg-transparent",onClick:()=>_(t),children:(0,a.jsx)(w.A,{className:"w-2 h-2"})})]},t))}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(C.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),(0,a.jsx)(o.p,{ref:b,type:"text",value:x,onChange:e=>{let t=e.target.value;p(t),f(t.length>0),v(-1)},onKeyDown:e=>{"Enter"===e.key?(e.preventDefault(),j>=0&&N[j]?k(N[j]):x.trim()&&k(x.trim())):"ArrowDown"===e.key?(e.preventDefault(),v(e=>e<N.length-1?e+1:e)):"ArrowUp"===e.key?(e.preventDefault(),v(e=>e>0?e-1:-1)):"Escape"===e.key?(f(!1),v(-1)):"Backspace"===e.key&&!x&&t.length>0&&_(t.length-1)},onFocus:()=>f(x.length>0),placeholder:t.length>=m?"Maksimum ".concat(m," etiket"):i,disabled:h||t.length>=m,className:"pl-10"})]}),x.trim()&&(0,a.jsx)(l.$,{type:"button",size:"sm",onClick:()=>k(x.trim()),disabled:h||t.length>=m,className:"shrink-0",children:(0,a.jsx)(D.A,{className:"w-4 h-4"})})]}),g&&N.length>0&&(0,a.jsx)("div",{ref:y,className:"absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-40 overflow-y-auto",children:N.map((e,t)=>(0,a.jsxs)("button",{type:"button",className:(0,u.cn)("w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center gap-2",j===t&&"bg-blue-50"),onClick:()=>{var t;k(e),null==(t=b.current)||t.focus()},children:[(0,a.jsx)(C.A,{className:"w-3 h-3 text-gray-400"}),e.replace("#","")]},e))})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[t.length,"/",m," etiket • Eklemek i\xe7in Enter'a basın • # \xf6neki kullanın"]})]})}var J=s(13052),V=s(57340),X=s(14395);function Y(e){let{category:t,onCategoryChange:s,suggestions:n=[],placeholder:i="Klas\xf6r se\xe7in... (\xf6rn: /frontend, /admin/users)",className:c,disabled:d=!1}=e,[m,h]=(0,r.useState)(""),[x,p]=(0,r.useState)(!1),[g,f]=(0,r.useState)(-1),[j,v]=(0,r.useState)(!1),b=(0,r.useRef)(null),y=(0,r.useRef)(null),w=function(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,a=e.toLowerCase();return a?t.filter(e=>e.toLowerCase().includes(a)).slice(0,s):[]}(m,n,8),N=t?function(e){if(!e||"/"===e)return["/"];let t=e.split("/").filter(e=>e.length>0),s=["/"],a="";return t.forEach(e=>{a+="/"+e,s.push(a)}),s}(t):[],k=e=>{let t=G(e);t&&function(e){let t=G(e);return/^\/[\w\u00C0-\u017F\/]*$/.test(t)}(t)&&(s("/"===t?null:t),h(""),p(!1),f(-1),v(!1))},C=()=>{s(null),v(!1)};return(0,r.useEffect)(()=>{let e=e=>{var t;!y.current||y.current.contains(e.target)||(null==(t=b.current)?void 0:t.contains(e.target))||p(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]),(0,a.jsxs)("div",{className:(0,u.cn)("space-y-2",c),children:[t&&!j&&(0,a.jsxs)("div",{className:"flex items-center gap-1 p-2 bg-gray-50 rounded-md border",children:[(0,a.jsx)("div",{className:"flex items-center gap-1 flex-1 min-w-0",children:N.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[t>0&&(0,a.jsx)(J.A,{className:"w-3 h-3 text-gray-400"}),(0,a.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-1 text-xs hover:bg-gray-200",onClick:()=>{"/"===e?C():s(e)},children:0===t?(0,a.jsx)(V.A,{className:"w-3 h-3"}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(_.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{className:"ml-1",children:function(e){if(!e||"/"===e)return"Root";let t=e.lastIndexOf("/");return e.substring(t+1)||"Root"}(e)})]})})]},e))}),!d&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-1",onClick:()=>{v(!0),h(t||""),setTimeout(()=>{var e;return null==(e=b.current)?void 0:e.focus()},0)},children:"Edit"}),(0,a.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-1",onClick:C,children:"Clear"})]})]}),(!t||j)&&(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(_.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),(0,a.jsx)(o.p,{ref:b,type:"text",value:m,onChange:e=>{let t=e.target.value;h(t),p(t.length>0),f(-1)},onKeyDown:e=>{"Enter"===e.key?(e.preventDefault(),g>=0&&w[g]?k(w[g]):m.trim()&&k(m.trim())):"ArrowDown"===e.key?(e.preventDefault(),f(e=>e<w.length-1?e+1:e)):"ArrowUp"===e.key?(e.preventDefault(),f(e=>e>0?e-1:-1)):"Escape"===e.key&&(p(!1),f(-1),v(!1),h(""))},onFocus:()=>p(m.length>0),placeholder:i,disabled:d,className:"pl-10"})]}),m.trim()&&(0,a.jsx)(l.$,{type:"button",size:"sm",onClick:()=>k(m.trim()),disabled:d,className:"shrink-0",children:(0,a.jsx)(D.A,{className:"w-4 h-4"})})]}),x&&w.length>0&&(0,a.jsx)("div",{ref:y,className:"absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-40 overflow-y-auto",children:w.map((e,t)=>(0,a.jsxs)("button",{type:"button",className:(0,u.cn)("w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center gap-2",g===t&&"bg-blue-50"),onClick:()=>{var t;k(e),null==(t=b.current)||t.focus()},children:[(0,a.jsx)(X.A,{className:"w-3 h-3 text-gray-400"}),e]},e))})]}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Klas\xf6rler i\xe7in eğik \xe7izgi kullanın (\xf6rn: /frontend/components)"})]})}var ee=s(22346),et=s(33109),es=s(24944),ea=s(78749),er=s(92657),el=s(72713),en=s(16785);function ei(e){let{projectId:t,className:s}=e,[i,o]=(0,r.useState)(!1),{data:c=[]}=(0,F.F$)(t),{data:m=[]}=R(t,10),{data:u=[]}=H(t,5),h=c.length,x=c.filter(e=>e.category||e.tags&&e.tags.length>0).length,p=h-x,g=h>0?x/h*100:0,f=c.filter(e=>e.tags&&e.tags.length>0).length,j=c.filter(e=>e.category).length;return t?(0,a.jsxs)("div",{className:s,children:[(0,a.jsx)(l.$,{type:"button",variant:"outline",size:"sm",onClick:()=>o(!i),className:"mb-4",children:i?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ea.A,{className:"w-4 h-4 mr-2"}),"Analitikleri Gizle"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(er.A,{className:"w-4 h-4 mr-2"}),"Analitikleri G\xf6ster"]})}),i&&(0,a.jsxs)("div",{className:"space-y-3 w-full overflow-hidden",children:[(0,a.jsxs)(n.Zp,{className:"w-full bg-white shadow-sm border border-gray-200",children:[(0,a.jsx)(n.aR,{className:"pb-2 px-3 pt-3",children:(0,a.jsxs)(n.ZB,{className:"text-xs font-medium flex items-center gap-1.5",children:[(0,a.jsx)(el.A,{className:"w-3 h-3 shrink-0"}),(0,a.jsx)("span",{className:"truncate",children:"Genel Bakış"})]})}),(0,a.jsxs)(n.Wu,{className:"space-y-3 px-3 pb-3",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-xs text-gray-600 truncate",children:"Kategorizasyon"}),(0,a.jsxs)("span",{className:"text-xs font-medium shrink-0",children:[g.toFixed(0),"%"]})]}),(0,a.jsx)(es.k,{value:g,className:"h-1.5 w-full"}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 leading-tight",children:[x,"/",h," kategorili"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,a.jsxs)("div",{className:"text-center p-2 bg-blue-50 rounded",children:[(0,a.jsx)("div",{className:"text-sm font-semibold text-blue-700",children:f}),(0,a.jsx)("div",{className:"text-xs text-blue-600",children:"Etiket"})]}),(0,a.jsxs)("div",{className:"text-center p-2 bg-green-50 rounded",children:[(0,a.jsx)("div",{className:"text-sm font-semibold text-green-700",children:j}),(0,a.jsx)("div",{className:"text-xs text-green-600",children:"Klas\xf6r"})]})]}),p>0&&(0,a.jsx)("div",{className:"p-2 bg-orange-50 border border-orange-200 rounded",children:(0,a.jsxs)("div",{className:"flex items-start gap-1.5 text-orange-700",children:[(0,a.jsx)(en.A,{className:"w-3 h-3 shrink-0 mt-0.5"}),(0,a.jsxs)("div",{className:"min-w-0",children:[(0,a.jsxs)("div",{className:"text-xs font-medium leading-tight",children:[p," kategorisiz"]}),(0,a.jsx)("div",{className:"text-xs text-orange-600 leading-tight",children:"Etiket/klas\xf6r ekleyin"})]})]})})]})]}),m.length>0&&(0,a.jsxs)(n.Zp,{className:"w-full bg-white shadow-sm border border-gray-200",children:[(0,a.jsx)(n.aR,{className:"pb-2 px-3 pt-3",children:(0,a.jsxs)(n.ZB,{className:"text-xs font-medium flex items-center gap-1.5",children:[(0,a.jsx)(C.A,{className:"w-3 h-3 shrink-0"}),(0,a.jsx)("span",{className:"truncate",children:"Pop\xfcler Etiketler"})]})}),(0,a.jsx)(n.Wu,{className:"px-3 pb-3",children:(0,a.jsx)("div",{className:"space-y-1.5",children:m.slice(0,3).map((e,t)=>{let{hashtag:s,count:r}=e,l=h>0?r/h*100:0;return(0,a.jsxs)("div",{className:"flex items-center justify-between gap-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1.5 min-w-0 flex-1",children:[(0,a.jsxs)("span",{className:"text-xs text-gray-500 w-3 shrink-0",children:["#",t+1]}),(0,a.jsx)(d.E,{variant:"secondary",className:"text-xs px-1.5 py-0.5 truncate max-w-[80px]",children:s.replace("#","")})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1.5 shrink-0",children:[(0,a.jsx)("div",{className:"w-8 h-1 bg-gray-200 rounded-full overflow-hidden",children:(0,a.jsx)("div",{className:"h-full bg-blue-500 rounded-full",style:{width:"".concat(Math.max(l,10),"%")}})}),(0,a.jsx)("span",{className:"text-xs text-gray-600 w-4 text-right",children:r})]})]},s)})})})]}),u.length>0&&(0,a.jsxs)(n.Zp,{className:"w-full bg-white shadow-sm border border-gray-200",children:[(0,a.jsx)(n.aR,{className:"pb-2 px-3 pt-3",children:(0,a.jsxs)(n.ZB,{className:"text-xs font-medium flex items-center gap-1.5",children:[(0,a.jsx)(_.A,{className:"w-3 h-3 shrink-0"}),(0,a.jsx)("span",{className:"truncate",children:"Pop\xfcler Klas\xf6rler"})]})}),(0,a.jsx)(n.Wu,{className:"px-3 pb-3",children:(0,a.jsx)("div",{className:"space-y-1.5",children:u.slice(0,3).map((e,t)=>{let{category:s,count:r}=e,l=h>0?r/h*100:0;return(0,a.jsxs)("div",{className:"flex items-center justify-between gap-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1.5 min-w-0 flex-1",children:[(0,a.jsxs)("span",{className:"text-xs text-gray-500 w-3 shrink-0",children:["#",t+1]}),(0,a.jsxs)(d.E,{variant:"outline",className:"text-xs px-1.5 py-0.5 flex items-center gap-1 truncate max-w-[80px]",children:[(0,a.jsx)(_.A,{className:"w-2.5 h-2.5 shrink-0"}),(0,a.jsx)("span",{className:"truncate",children:s})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1.5 shrink-0",children:[(0,a.jsx)("div",{className:"w-8 h-1 bg-gray-200 rounded-full overflow-hidden",children:(0,a.jsx)("div",{className:"h-full bg-green-500 rounded-full",style:{width:"".concat(Math.max(l,10),"%")}})}),(0,a.jsx)("span",{className:"text-xs text-gray-600 w-4 text-right",children:r})]})]},s)})})})]}),h>0&&(0,a.jsxs)(n.Zp,{className:"w-full bg-white shadow-sm border border-gray-200",children:[(0,a.jsx)(n.aR,{className:"pb-2 px-3 pt-3",children:(0,a.jsxs)(n.ZB,{className:"text-xs font-medium flex items-center gap-1.5",children:[(0,a.jsx)(et.A,{className:"w-3 h-3 shrink-0"}),(0,a.jsx)("span",{className:"truncate",children:"\xd6neriler"})]})}),(0,a.jsx)(n.Wu,{className:"px-3 pb-3",children:(0,a.jsxs)("div",{className:"space-y-1.5 text-xs text-gray-600",children:[g<50&&(0,a.jsxs)("div",{className:"flex items-start gap-1.5",children:[(0,a.jsx)("span",{className:"text-orange-500 shrink-0",children:"•"}),(0,a.jsx)("span",{className:"leading-tight",children:"Etiket ekleyin"})]}),0===j&&(0,a.jsxs)("div",{className:"flex items-start gap-1.5",children:[(0,a.jsx)("span",{className:"text-blue-500 shrink-0",children:"•"}),(0,a.jsx)("span",{className:"leading-tight",children:"Klas\xf6r kullanın"})]}),m.length>10&&(0,a.jsxs)("div",{className:"flex items-start gap-1.5",children:[(0,a.jsx)("span",{className:"text-green-500 shrink-0",children:"•"}),(0,a.jsx)("span",{className:"leading-tight",children:"İyi etiket kullanımı!"})]}),h>20&&g>80&&(0,a.jsxs)("div",{className:"flex items-start gap-1.5",children:[(0,a.jsx)("span",{className:"text-green-500 shrink-0",children:"•"}),(0,a.jsx)("span",{className:"leading-tight",children:"M\xfckemmel organizasyon!"})]})]})})]})]})]}):null}function eo(e){let{projectId:t,onHashtagClick:s,onCategoryClick:n,selectedHashtags:i=[],selectedCategory:m=null,className:h}=e,[x,p]=(0,r.useState)(""),[g,f]=(0,r.useState)(!0),[j,v]=(0,r.useState)(!0),{data:b=[],isLoading:y}=R(t),{data:N=[],isLoading:k}=H(t),A=b.filter(e=>{let{hashtag:t}=e;return t.toLowerCase().includes(x.toLowerCase())}),E=N.filter(e=>{let{category:t}=e;return t.toLowerCase().includes(x.toLowerCase())});return(0,a.jsxs)("div",{className:(0,u.cn)("w-64 bg-white border-l border-gray-200 flex flex-col relative z-50",h),children:[(0,a.jsxs)("div",{className:"p-4 border-b border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,a.jsx)(et.A,{className:"w-4 h-4 text-blue-600"}),(0,a.jsx)("h3",{className:"font-medium text-sm",children:"AI Tags"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(z.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),(0,a.jsx)(o.p,{type:"text",placeholder:"Etiket ve klas\xf6r ara...",value:x,onChange:e=>p(e.target.value),className:"pl-10 text-sm"}),x&&(0,a.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0",onClick:()=>{p("")},children:(0,a.jsx)(w.A,{className:"w-3 h-3"})})]})]}),(0,a.jsx)(c.F,{className:"flex-1 overflow-hidden",children:(0,a.jsxs)("div",{className:"p-4 space-y-4 max-w-full pb-24 sm:pb-20 lg:pb-24",children:[g&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(_.A,{className:"w-3 h-3 text-gray-500"}),(0,a.jsx)("span",{className:"text-xs font-medium text-gray-700 uppercase tracking-wide",children:"Klas\xf6rler"})]}),(0,a.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-0 text-xs",onClick:()=>f(!g),children:g?"Gizle":"G\xf6ster"})]}),k?(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Klas\xf6rler y\xfckleniyor..."}):E.length>0?(0,a.jsx)("div",{className:"space-y-1",children:E.map(e=>{let{category:t,count:s}=e;return(0,a.jsxs)(l.$,{type:"button",variant:"ghost",size:"sm",className:(0,u.cn)("w-full justify-between h-auto p-2 text-xs hover:bg-gray-100",m===t&&"bg-blue-50 text-blue-700"),onClick:()=>{null==n||n(t)},children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 min-w-0",children:[(0,a.jsx)(_.A,{className:"w-3 h-3 shrink-0"}),(0,a.jsx)("span",{className:"truncate",children:t})]}),(0,a.jsx)(d.E,{variant:"secondary",className:"text-xs",children:s})]},t)})}):(0,a.jsx)("div",{className:"text-xs text-gray-500",children:x?"Eşleşen klas\xf6r bulunamadı":"Hen\xfcz klas\xf6r yok"})]}),g&&j&&(0,a.jsx)(ee.w,{}),j&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(C.A,{className:"w-3 h-3 text-gray-500"}),(0,a.jsx)("span",{className:"text-xs font-medium text-gray-700 uppercase tracking-wide",children:"Pop\xfcler Etiketler"})]}),(0,a.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-0 text-xs",onClick:()=>v(!j),children:j?"Gizle":"G\xf6ster"})]}),y?(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Etiketler y\xfckleniyor..."}):A.length>0?(0,a.jsx)("div",{className:"space-y-1",children:A.map(e=>{let{hashtag:t,count:r}=e;return(0,a.jsxs)(l.$,{type:"button",variant:"ghost",size:"sm",className:(0,u.cn)("w-full justify-between h-auto p-2 text-xs hover:bg-gray-100",i.includes(t)&&"bg-blue-50 text-blue-700"),onClick:()=>{null==s||s(t)},children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 min-w-0",children:[(0,a.jsx)(C.A,{className:"w-3 h-3 shrink-0"}),(0,a.jsx)("span",{className:"truncate",children:t.replace("#","")})]}),(0,a.jsx)(d.E,{variant:"secondary",className:"text-xs",children:r})]},t)})}):(0,a.jsx)("div",{className:"text-xs text-gray-500",children:x?"Eşleşen etiket bulunamadı":"Hen\xfcz etiket yok"})]}),t&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ee.w,{}),(0,a.jsx)("div",{className:"w-full overflow-hidden",children:(0,a.jsx)(ei,{projectId:t,className:"w-full max-w-none"})})]})]})}),(0,a.jsx)("div",{className:"p-4 border-t border-gray-200",children:(0,a.jsx)("div",{className:"text-xs text-gray-500 text-center",children:"Filtrelemek i\xe7in tıklayın • Sayılar kullanım sayısını g\xf6sterir"})})]})}var ec=s(88172);let ed=function(e){let{value:t,onChange:s,onKeyDown:l,placeholder:n,className:i,suggestions:o={hashtags:[],folders:[]},disabled:c=!1,enableDynamicHeight:d=!0,heightConfig:m={}}=e,[u,h]=(0,r.useState)(!1),[x,p]=(0,r.useState)([]),[g,f]=(0,r.useState)(-1),[j,v]=(0,r.useState)(null),[b,y]=(0,r.useState)(-1),[,w]=(0,r.useState)(""),N=(0,r.useRef)(null),k=(0,r.useRef)(null),{heightStyle:A}=function(e,t,s){let{heightStyle:a,maxHeight:l,minHeight:n}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{minHeight:t=44,maxHeightFraction:s=.33,baseHeight:a=56,enableTransitions:l=!0}=e,[n,i]=(0,r.useState)(0),[o,c]=(0,r.useState)(200),d=(0,r.useCallback)(()=>{let e=window.innerHeight,a=Math.max(Math.floor(e*s),t+50);i(e),c(a)},[s,t]);(0,r.useEffect)(()=>{let e,t=()=>{clearTimeout(e),e=setTimeout(d,150)};return d(),window.addEventListener("resize",t),window.addEventListener("orientationchange",t),()=>{clearTimeout(e),window.removeEventListener("resize",t),window.removeEventListener("orientationchange",t)}},[d]),(0,r.useEffect)(()=>{let e=()=>{if(window.visualViewport){let e=window.visualViewport.height,a=Math.max(Math.floor(e*s),t+50);i(e),c(a)}};if(window.visualViewport)return window.visualViewport.addEventListener("resize",e),()=>{var t;null==(t=window.visualViewport)||t.removeEventListener("resize",e)}},[s,t]);let m={minHeight:"".concat(t,"px"),maxHeight:"".concat(o,"px"),height:"auto",resize:"none",...l&&{transition:"max-height 0.2s ease-out, min-height 0.2s ease-out"}};return{maxHeight:o,minHeight:t,heightStyle:m,recalculateHeight:d,viewportHeight:n}}(s),[i,o]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let t=e.current;if(!t)return;let s=t.offsetHeight;t.style.height="auto";let a=Math.min(Math.max(t.scrollHeight,n),l);Math.abs(a-s)>10&&(o(!0),t.classList.add("expanding"),setTimeout(()=>{t.classList.remove("expanding"),o(!1)},200)),t.style.height="".concat(a,"px")},[t,l,n,e]),{heightStyle:{...a,overflow:"hidden"},maxHeight:l,minHeight:n,isExpanding:i}}(N,t,d?m:void 0),E=(0,r.useCallback)((e,t)=>{if(0===t)return null;for(let s=t-1;s>=0;s--){let a=e[s];if("#"===a||"/"===a){let r=s>0?e[s-1]:" ";if(" "===r||"\n"===r||0===s){let r=e.slice(s+1,t);if(!r.includes(" ")&&!r.includes("\n"))return{char:a,position:s,searchTerm:r}}}if(" "===a||"\n"===a)break}return null},[]),S=(0,r.useCallback)((e,t)=>{let s="hashtag"===t?(null==o?void 0:o.hashtags)||[]:(null==o?void 0:o.folders)||[];return s&&0!==s.length?s.filter(t=>!!t&&"string"==typeof t&&t.replace(/^[#/]/,"").toLowerCase().includes(e.toLowerCase())).slice(0,8).map((e,s)=>({id:"".concat(t,"-").concat(s),value:e,type:t,usage_count:Math.floor(100*Math.random())})).sort((e,t)=>(t.usage_count||0)-(e.usage_count||0)):[]},[o]),z=(0,r.useCallback)(e=>{var a;if(-1===b)return;let r=t.slice(0,b),l=t.slice((null==(a=N.current)?void 0:a.selectionStart)||0),n="hashtag"===e.type?"#":"/",i=e.value.startsWith(n)?e.value:"".concat(n).concat(e.value);s(r+i+" "+l),setTimeout(()=>{if(N.current){let e=r.length+i.length+1;N.current.setSelectionRange(e,e),N.current.focus()}},0),h(!1),f(-1)},[t,b,s]),D=(0,r.useMemo)(()=>(0,ec.A)((e,t)=>{try{let s=E(e,t);if(s){v(s.char),y(s.position),w(s.searchTerm);let e="#"===s.char?"hashtag":"folder",t=S(s.searchTerm,e);p(t||[]),h(t&&t.length>0),f(-1)}else h(!1),v(null),y(-1),w(""),p([]),f(-1)}catch(e){console.error("Error in trigger detection:",e),h(!1),v(null),y(-1),w(""),p([]),f(-1)}},100),[E,S]),L=(0,r.useCallback)(e=>{s(e),N.current&&D(e,N.current.selectionStart||0)},[s,D]);(0,r.useEffect)(()=>()=>{D.cancel()},[D]);let P=(0,r.useCallback)(e=>{if(u&&x.length>0)switch(e.key){case"ArrowDown":e.preventDefault(),f(e=>e<x.length-1?e+1:0);break;case"ArrowUp":e.preventDefault(),f(e=>e>0?e-1:x.length-1);break;case"Enter":case"Tab":g>=0&&(e.preventDefault(),z(x[g]));break;case"Escape":e.preventDefault(),h(!1),f(-1)}null==l||l(e)},[u,x,g,l,z]);return(0,r.useEffect)(()=>{let e=e=>{k.current&&!k.current.contains(e.target)&&h(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("textarea",{ref:N,value:t||"",onChange:e=>L(e.target.value),onKeyDown:P,placeholder:n,className:"".concat(i," ").concat(d?"dynamic-textarea":""),disabled:c,style:d?A:{height:"auto"}}),u&&x&&x.length>0&&(0,a.jsxs)("div",{ref:k,className:"absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto",children:[(0,a.jsxs)("div",{className:"p-2 text-xs text-gray-500 border-b border-gray-100",children:["#"===j?"Hashtags":"Folders"," - Use ↑↓ to navigate, Enter to select"]}),x.map((e,t)=>e&&e.id&&e.value?(0,a.jsxs)("div",{className:"flex items-center gap-2 px-3 py-2 cursor-pointer transition-colors ".concat(t===g?"bg-blue-50 text-blue-700":"hover:bg-gray-50"),onClick:()=>z(e),children:["hashtag"===e.type?(0,a.jsx)(C.A,{className:"h-4 w-4 text-blue-500"}):(0,a.jsx)(_.A,{className:"h-4 w-4 text-orange-500"}),(0,a.jsx)("span",{className:"flex-1 text-sm",children:e.value}),e.usage_count&&(0,a.jsxs)("span",{className:"text-xs text-gray-400",children:[e.usage_count," uses"]})]},e.id):null)]})]})};function em(){return(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 animate-pulse",children:[1,2,3,4,5,6].map(e=>(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mb-3"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-full"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-5/6"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-4/6"})]}),(0,a.jsxs)("div",{className:"flex gap-2 mt-4",children:[(0,a.jsx)("div",{className:"h-5 bg-blue-100 rounded-full w-16"}),(0,a.jsx)("div",{className:"h-5 bg-purple-100 rounded-full w-20"})]})]},e))})}var eu=s(66516),eh=s(74575),ex=s(34869),ep=s(32919),eg=s(54165),ef=s(85057),ej=s(7801);function ev(e){let{promptId:t,promptTitle:s,promptText:n,taskCode:c,className:d,variant:m="ghost",size:u="sm"}=e,[x,p]=(0,r.useState)(!1),[g,f]=(0,r.useState)(s||c||""),[j,v]=(0,r.useState)(""),[b,y]=(0,r.useState)(!0),[w,N]=(0,r.useState)(""),[_,C]=(0,r.useState)(""),[A,E]=(0,r.useState)(!1),S=(0,ej.Xm)(),{data:z}=(0,ej.X1)(),D=null==z?void 0:z.find(e=>e.prompt_id===t),L=async()=>{try{await S.mutateAsync({prompt_id:t,title:g.trim()||void 0,description:j.trim()||void 0,is_public:b,password:w.trim()||void 0,expires_at:_||void 0}),p(!1),P()}catch(e){console.error("Share error:",e)}},P=()=>{f(s||c||""),v(""),y(!0),N(""),C(""),E(!1)},K=async()=>{if(D){let e="".concat(window.location.origin,"/share/").concat(D.share_token);try{await navigator.clipboard.writeText(e),O.oR.success("Link panoya kopyalandı!")}catch(e){O.oR.error("Link kopyalanamadı")}}},$=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100;return e.length<=t?e:e.substring(0,t)+"..."};return(0,a.jsxs)(eg.lG,{open:x,onOpenChange:p,children:[(0,a.jsx)(eg.zM,{asChild:!0,children:(0,a.jsx)(l.$,{variant:m,size:u,className:d,onClick:()=>p(!0),children:(0,a.jsx)(eu.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(eg.Cf,{className:"sm:max-w-md",children:[(0,a.jsxs)(eg.c7,{children:[(0,a.jsxs)(eg.L3,{className:"flex items-center gap-2",children:[(0,a.jsx)(eu.A,{className:"h-5 w-5"}),"Prompt Paylaş"]}),(0,a.jsx)(eg.rr,{children:"Bu prompt'u başkalarıyla paylaşmak i\xe7in bir link oluşturun"})]}),D?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-green-50 border border-green-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(eh.A,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)("span",{className:"text-sm font-medium text-green-800",children:"Bu prompt zaten paylaşılmış"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(o.p,{value:"".concat(window.location.origin,"/share/").concat(D.share_token),readOnly:!0,className:"text-xs bg-white"}),(0,a.jsx)(l.$,{size:"sm",onClick:K,className:"shrink-0",children:(0,a.jsx)(k.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)("div",{className:"mt-2 flex items-center gap-4 text-xs text-green-700",children:[(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(er.A,{className:"h-3 w-3"}),D.view_count," g\xf6r\xfcnt\xfclenme"]}),(0,a.jsx)("span",{children:new Date(D.created_at).toLocaleDateString("tr-TR")})]})]}),(0,a.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-2",children:"Prompt \xd6nizleme"}),(0,a.jsx)("p",{className:"text-xs text-gray-600 font-mono",children:$(n)})]})]}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-2",children:"Prompt \xd6nizleme"}),(0,a.jsx)("p",{className:"text-xs text-gray-600 font-mono",children:$(n)})]}),(0,a.jsx)(ee.w,{}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(ef.J,{htmlFor:"share-title",children:"Başlık (Opsiyonel)"}),(0,a.jsx)(o.p,{id:"share-title",value:g,onChange:e=>f(e.target.value),placeholder:"Paylaşım i\xe7in \xf6zel başlık",className:"mt-1"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(ef.J,{htmlFor:"share-description",children:"A\xe7ıklama (Opsiyonel)"}),(0,a.jsx)(i.T,{id:"share-description",value:j,onChange:e=>v(e.target.value),placeholder:"Bu prompt hakkında kısa a\xe7ıklama",className:"mt-1 resize-none",rows:2})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[b?(0,a.jsx)(ex.A,{className:"h-4 w-4 text-green-600"}):(0,a.jsx)(ep.A,{className:"h-4 w-4 text-orange-600"}),(0,a.jsx)(ef.J,{htmlFor:"is-public",children:"Herkese A\xe7ık"})]}),(0,a.jsx)(h,{id:"is-public",checked:b,onCheckedChange:y})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(ef.J,{htmlFor:"password",children:"Şifre Koruması (Opsiyonel)"}),(0,a.jsxs)("div",{className:"flex gap-2 mt-1",children:[(0,a.jsx)(o.p,{id:"password",type:A?"text":"password",value:w,onChange:e=>N(e.target.value),placeholder:"Şifre belirleyin"}),(0,a.jsx)(l.$,{type:"button",variant:"outline",size:"icon",onClick:()=>E(!A),children:A?(0,a.jsx)(ea.A,{className:"h-4 w-4"}):(0,a.jsx)(er.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(ef.J,{htmlFor:"expires-at",children:"Son Kullanma Tarihi (Opsiyonel)"}),(0,a.jsx)(o.p,{id:"expires-at",type:"datetime-local",value:_,onChange:e=>C(e.target.value),className:"mt-1",min:new Date().toISOString().slice(0,16)})]})]}),(0,a.jsx)(ee.w,{}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(l.$,{onClick:L,disabled:S.isPending,className:"flex-1",children:S.isPending?"Oluşturuluyor...":(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(eu.A,{className:"h-4 w-4 mr-2"}),"Paylaşım Linki Oluştur"]})}),(0,a.jsx)(l.$,{variant:"outline",onClick:()=>p(!1),children:"İptal"})]})]})]})]})}var eb=s(75143),ey=s(50402),ew=s(78266);let eN=(0,r.lazy)(()=>Promise.all([s.e(2123),s.e(8611),s.e(8779),s.e(8650),s.e(8523),s.e(8405),s.e(9420),s.e(4939),s.e(1486),s.e(2662),s.e(3240),s.e(8669),s.e(3189),s.e(622),s.e(4696),s.e(1277),s.e(7979),s.e(1899),s.e(7098),s.e(4450),s.e(9744),s.e(4495),s.e(9592),s.e(433),s.e(2652),s.e(5030),s.e(465),s.e(2903),s.e(2663),s.e(9173),s.e(408),s.e(558),s.e(1356),s.e(6475),s.e(2130),s.e(4207),s.e(4191),s.e(6489),s.e(5230),s.e(7339),s.e(957),s.e(5677),s.e(6691),s.e(1151),s.e(7114),s.e(5803),s.e(3976),s.e(3492),s.e(2608),s.e(5644),s.e(2789),s.e(9824),s.e(4075),s.e(9473),s.e(7530),s.e(6759),s.e(0),s.e(6325),s.e(6077),s.e(4409),s.e(9184),s.e(7650),s.e(1595),s.e(4532),s.e(1911),s.e(5565),s.e(1038),s.e(6772),s.e(6285)]).then(s.bind(s,96285)).then(e=>({default:e.EnhancedContextGalleryModal}))),ek="Yapılan canlı testlerde Console log ekranında hatalar karşımıza \xe7ıkmıştır. Proje yapımıza uygun olarak d\xfczenleme yap. Consol logları şu şekildedir:",e_=(0,r.memo)(function(e){let{promptText:t,className:s,onDoubleClick:l,title:n}=e,[i,o]=(0,r.useState)(!1);if(!t.startsWith(ek))return(0,a.jsx)("p",{className:s,onDoubleClick:l,title:n,children:t});let c=t.substring(ek.length).trim();return(0,a.jsxs)("div",{className:"space-y-2 w-full max-w-full overflow-hidden",children:[(0,a.jsx)("p",{className:s,onDoubleClick:l,title:n,children:ek}),(0,a.jsxs)("div",{className:"border-2 border-red-300 rounded-md bg-red-50 p-3 w-full max-w-full overflow-hidden box-border",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-red-700 truncate",children:"Console Log İ\xe7eriği"}),(0,a.jsx)("button",{onClick:()=>o(!i),className:"text-red-600 hover:text-red-800 text-sm font-medium flex items-center gap-1 flex-shrink-0",children:i?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.A,{className:"h-4 w-4"}),"Daralt"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),"Genişlet"]})})]}),i?(0,a.jsx)("pre",{className:"text-sm text-red-800 whitespace-pre-wrap bg-white p-2 rounded border border-red-200 max-h-40 overflow-y-auto w-full max-w-full overflow-x-hidden break-words",children:c}):(0,a.jsx)("p",{className:"text-sm text-red-700 w-full max-w-full overflow-hidden break-words whitespace-pre-wrap",children:c.split("\n")[0].length>100?c.split("\n")[0].substring(0,100)+"...":c.split("\n")[0]})]})]})}),eC=(0,r.memo)(function(e){let{prompt:t,isMultiSelectMode:s,selectedPrompts:r,editingPromptId:o,editingText:c,editTextareaRef:m,onSelectPrompt:u,onEditPrompt:h,onSaveEdit:x,onCancelEdit:p,onCopyPrompt:A,setEditingText:E,onHashtagFilter:S}=e,{attributes:z,listeners:D,setNodeRef:L,transform:P,transition:K,isDragging:$}=(0,ey.gl)({id:t.id}),F={transform:ew.Ks.Transform.toString(P),transition:K,opacity:$?.5:1};return(0,a.jsxs)(n.Zp,{ref:L,style:F,className:"transition-all duration-200 ".concat(t.is_used?"bg-gray-50 border-gray-300":"bg-white border-gray-200 hover:border-blue-300 hover:shadow-md"," ").concat(r.has(t.id)?"ring-2 ring-blue-500 border-blue-500":""),onClick:()=>s?u(t.id):void 0,children:[(0,a.jsxs)(n.aR,{className:"pb-3",children:[(0,a.jsxs)("div",{className:"block lg:hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[!s&&(0,a.jsx)("div",{className:"flex items-center justify-center w-8 h-8 cursor-grab active:cursor-grabbing",...z,...D,children:(0,a.jsx)(g.A,{className:"h-5 w-5 text-gray-400"})}),s&&(0,a.jsx)("div",{className:"flex items-center justify-center w-8 h-8 cursor-pointer",onClick:e=>{e.stopPropagation(),u(t.id)},children:r.has(t.id)?(0,a.jsx)(f.A,{className:"h-6 w-6 text-blue-600"}):(0,a.jsx)(j.A,{className:"h-6 w-6 text-gray-400"})}),(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-full text-base font-medium ".concat(t.is_used?"bg-green-100 text-green-700":"bg-blue-100 text-blue-700"),children:t.order_index}),(0,a.jsx)("div",{className:"flex flex-col",children:(0,a.jsx)("span",{className:"text-sm font-mono px-2 py-1 rounded ".concat(t.is_used?"bg-gray-100 text-gray-500":"bg-blue-50 text-blue-600"),children:t.task_code||"task-".concat(t.order_index)})})]}),(0,a.jsx)("div",{className:"flex items-center gap-2",children:t.is_used?(0,a.jsx)(v.A,{className:"h-5 w-5 text-green-600"}):(0,a.jsx)(b.A,{className:"h-5 w-5 text-gray-400"})})]}),!s&&(0,a.jsx)("div",{className:"flex items-center gap-2 justify-end",children:o===t.id?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(l.$,{variant:"default",size:"sm",onClick:x,className:"bg-green-600 hover:bg-green-700 text-white border-green-600 hover:border-green-700 min-h-[44px] px-4",children:[(0,a.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Kaydet"]}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:p,className:"text-red-600 hover:text-red-700 min-h-[44px] px-4",children:[(0,a.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"İptal"]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>h(t),className:"text-gray-600 hover:text-gray-700 min-h-[44px] px-4",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"D\xfczenle"]}),(0,a.jsx)(ev,{promptId:t.id,promptTitle:t.title||void 0,promptText:t.prompt_text,taskCode:t.task_code||void 0,variant:"outline",size:"sm",className:"min-h-[44px] px-4"}),(0,a.jsxs)(l.$,{variant:t.is_used?"secondary":"default",size:"sm",onClick:()=>A(t),className:"min-h-[44px] px-4 ".concat(t.is_used?"opacity-60":""),children:[(0,a.jsx)(k.A,{className:"h-4 w-4 mr-2"}),t.is_used?"Kopyalandı":"Kopyala"]})]})})]}),(0,a.jsxs)("div",{className:"hidden lg:flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[!s&&(0,a.jsx)("div",{className:"flex items-center justify-center w-6 h-6 cursor-grab active:cursor-grabbing",...z,...D,children:(0,a.jsx)(g.A,{className:"h-4 w-4 text-gray-400"})}),s&&(0,a.jsx)("div",{className:"flex items-center justify-center w-6 h-6 cursor-pointer",onClick:e=>{e.stopPropagation(),u(t.id)},children:r.has(t.id)?(0,a.jsx)(f.A,{className:"h-5 w-5 text-blue-600"}):(0,a.jsx)(j.A,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("div",{className:"flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ".concat(t.is_used?"bg-green-100 text-green-700":"bg-blue-100 text-blue-700"),children:t.order_index}),(0,a.jsx)("div",{className:"flex flex-col",children:(0,a.jsx)("span",{className:"text-xs font-mono px-2 py-1 rounded ".concat(t.is_used?"bg-gray-100 text-gray-500":"bg-blue-50 text-blue-600"),children:t.task_code||"task-".concat(t.order_index)})}),(0,a.jsx)("div",{className:"flex items-center gap-2",children:t.is_used?(0,a.jsx)(v.A,{className:"h-4 w-4 text-green-600"}):(0,a.jsx)(b.A,{className:"h-4 w-4 text-gray-400"})})]}),!s&&(0,a.jsx)("div",{className:"flex items-center gap-2",children:o===t.id?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(l.$,{variant:"default",size:"sm",onClick:x,className:"bg-green-600 hover:bg-green-700 text-white border-green-600 hover:border-green-700",children:[(0,a.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Kaydet"]}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:p,className:"text-red-600 hover:text-red-700",children:[(0,a.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"İptal"]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>h(t),className:"text-gray-600 hover:text-gray-700",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"D\xfczenle"]}),(0,a.jsx)(ev,{promptId:t.id,promptTitle:t.title||void 0,promptText:t.prompt_text,taskCode:t.task_code||void 0,variant:"outline",size:"sm"}),(0,a.jsxs)(l.$,{variant:t.is_used?"secondary":"default",size:"sm",onClick:()=>A(t),className:t.is_used?"opacity-60":"",children:[(0,a.jsx)(k.A,{className:"h-4 w-4 mr-2"}),t.is_used?"Kopyalandı":"Kopyala"]})]})})]})]}),(0,a.jsx)(n.Wu,{className:"pt-0",children:o===t.id?(0,a.jsx)(i.T,{ref:m,value:c,onChange:e=>E(e.target.value),className:"min-h-[60px] max-h-[200px] resize-none",style:{height:"auto"},onKeyDown:e=>{"Enter"===e.key&&e.ctrlKey?x():"Escape"===e.key&&p()},autoFocus:!0}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(e_,{promptText:t.prompt_text,className:"text-sm leading-relaxed cursor-pointer whitespace-pre-wrap break-words ".concat(t.is_used?"text-gray-500":"text-gray-700"),onDoubleClick:()=>h(t),title:"D\xfczenlemek i\xe7in \xe7ift tıklayın"}),(t.tags&&t.tags.length>0||t.category)&&(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-1 pt-2 sm:gap-1.5",children:[t.category&&(0,a.jsxs)(d.E,{variant:"outline",className:"text-xs sm:text-xs bg-gray-50 text-gray-700 border-gray-300 hover:bg-gray-100 transition-colors cursor-pointer",onClick:e=>{e.preventDefault(),e.stopPropagation()},children:[(0,a.jsx)(_.A,{className:"w-3 h-3 mr-1 text-gray-500"}),t.category]}),t.tags&&Array.isArray(t.tags)&&t.tags.map((e,t)=>{let s="string"==typeof e?e:String(e);return s&&""!==s.trim()?(0,a.jsxs)(d.E,{variant:"secondary",className:"text-xs sm:text-xs bg-blue-50 text-blue-700 border-blue-200 cursor-pointer hover:bg-blue-100 transition-colors",onClick:e=>{e.preventDefault(),e.stopPropagation(),S(s)},children:[(0,a.jsx)(C.A,{className:"w-3 h-3 mr-1 text-blue-500"}),s.replace("#","")]},t):null})]})]})})]})});function eA(){let{isContextGalleryOpen:e=!1,onToggleContextGallery:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[s,n]=(0,r.useState)(""),[i,m]=(0,r.useState)(null),[u,g]=(0,r.useState)(""),[f,j]=(0,r.useState)(new Set),[y,N]=(0,r.useState)(!1),[R,H]=(0,r.useState)(""),[U,G]=(0,r.useState)([]),[J,V]=(0,r.useState)(null),[X,ee]=(0,r.useState)(""),[et,es]=(0,r.useState)(!1),[ea,er]=(0,r.useState)(!0),[el,en]=(0,r.useState)([]),[ei,ec]=(0,r.useState)(null),[eu,eh]=(0,r.useState)(!1),[ex,ep]=(0,r.useState)(1);(0,r.useEffect)(()=>{ep(1)},[R]);let eg=(0,r.useRef)(null),{activeProjectId:ef,isContextEnabled:ej}=(0,$.C)(),{data:ev}=(0,I.p$)(),ew=(0,eb.FR)((0,eb.MS)(eb.AN),(0,eb.MS)(eb.uN,{coordinateGetter:ey.JR})),{data:ek=[]}=(0,F.F$)(ef),{data:e_}=(0,T.By)(ef),{data:eA=[]}=(0,q.I)({queryKey:["all-hashtags",ef],queryFn:async()=>{if(!ef)return[];let{data:e,error:t}=await M.L.from("prompts").select("tags").eq("project_id",ef);if(t)throw Error(t.message);let s=new Set;return null==e||e.forEach(e=>{e.tags&&Array.isArray(e.tags)&&e.tags.forEach(e=>{if("string"==typeof e&&e.trim()){let t=e.startsWith("#")?e.toLowerCase():"#".concat(e.toLowerCase());s.add(t)}})}),Array.from(s).sort()},enabled:!!ef}),{data:eE=[]}=(0,q.I)({queryKey:["all-categories",ef],queryFn:async()=>{if(!ef)return[];let{data:e,error:t}=await M.L.from("prompts").select("category").eq("project_id",ef).not("category","is",null);if(t)throw Error(t.message);let s=new Set;return null==e||e.forEach(e=>{e.category&&s.add(e.category.toLowerCase())}),Array.from(s).sort()},enabled:!!ef}),eS=(0,F.sW)(),ez=(0,F.$I)(),eD=(0,F.GQ)(),eL=(0,F.Qu)(),eP=ek.filter(e=>{try{let t=""===R.trim()||e.prompt_text.toLowerCase().includes(R.toLowerCase())||e.title&&e.title.toLowerCase().includes(R.toLowerCase()),s=!ei||e.category===ei,a=0===el.length||e.tags&&Array.isArray(e.tags)&&el.some(t=>e.tags.some(e=>("string"==typeof e?e:String(e)).toLowerCase()===t.toLowerCase()));return t&&s&&a}catch(t){return console.error("Error filtering prompt:",t,e),!0}});(0,r.useEffect)(()=>{var e;eg.current&&((e=eg.current).style.height="auto",e.style.height=Math.min(e.scrollHeight,200)+"px")},[u]);let eK=async()=>{if(s.trim()&&ef){if(ev&&!ev.can_create_prompt)return void O.oR.error("Prompt oluşturma limitinize ulaştınız (".concat(ev.current_prompts,"/").concat(ev.max_prompts_per_project,"). Planınızı y\xfckseltin."));let e=s;eu&&(e="Yapılan canlı testlerde Console log ekranında hatalar karşımıza \xe7ıkmıştır. Proje yapımıza uygun olarak d\xfczenleme yap. Consol logları şu şekildedir:\n\n"+s);let t=e;n(""),ee(""),G([]),V(null),es(!1);try{let{hashtags:e,folderPaths:s,originalText:a}=B(t),r=W(U||[],e||[]),l=s&&s.length>0?s[0]:J,n=ek.length>0?Math.max(...ek.map(e=>e.order_index||0)):0;await eS.mutateAsync({project_id:ef,prompt_text:a,title:(null==X?void 0:X.trim())||void 0,category:l||void 0,tags:r||[],order_index:n+1,is_used:!1}),O.oR.success("Prompt başarıyla eklendi!")}catch(e){console.error("Prompt ekleme hatası:",e),n(t),ee(X),G(U),V(J),es(et),O.oR.error("Prompt eklenirken bir hata oluştu. L\xfctfen tekrar deneyin.")}}},e$=(0,r.useCallback)(e=>{try{let t="string"==typeof e?e:String(e);if(!t||""===t.trim())return void console.warn("Invalid hashtag provided to handleHashtagFilter:",e);el.includes(t)?en(el.filter(e=>e!==t)):en([...el,t])}catch(t){console.error("Error in handleHashtagFilter:",t,e)}},[el]),eF=(0,r.useCallback)(e=>{ec(ei===e?null:e)},[ei]),eT=(0,r.useCallback)(async e=>{try{let t=ej&&(null==e_?void 0:e_.context_text)||"",s=e.task_code||"task-".concat(e.order_index),a="";a=t?"".concat(t,"\n\n").concat(s,"\n").concat(e.prompt_text):"".concat(s,"\n").concat(e.prompt_text),await navigator.clipboard.writeText(a),await eL.mutateAsync(e.id)}catch(e){console.error("Kopyalama hatası:",e)}},[ej,null==e_?void 0:e_.context_text,eL]),eq=async()=>{if(ef)try{let e=ek.filter(e=>e.is_used).sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime())[0];e&&await ez.mutateAsync({id:e.id,is_used:!1})}catch(e){console.error("Geri alma hatası:",e)}},eM=(0,r.useCallback)(e=>{m(e.id),g(e.prompt_text)},[]),eR=async()=>{if(i&&u.trim())try{let{hashtags:e,folderPaths:t,originalText:s}=B(u),a=ek.find(e=>e.id===i),r=(null==a?void 0:a.tags)||[],l=null==a?void 0:a.category,n=W(r,e||[]),o=t&&t.length>0?t[0]:l;await ez.mutateAsync({id:i,prompt_text:s,tags:n,category:o||void 0}),m(null),g("")}catch(e){console.error("Prompt g\xfcncelleme hatası:",e),alert("Prompt g\xfcncellenirken bir hata oluştu. L\xfctfen tekrar deneyin.")}},eH=(0,r.useCallback)(()=>{m(null),g("")},[]),eI=(0,r.useCallback)(()=>{N(!y),j(new Set)},[y]),eQ=(0,r.useCallback)(e=>{let t=new Set(f);t.has(e)?t.delete(e):t.add(e),j(t)},[f]),eO=async()=>{if(0!==f.size)try{let e=ek.filter(e=>f.has(e.id)).sort((e,t)=>e.order_index-t.order_index),t=ej&&(null==e_?void 0:e_.context_text)||"",s=t?"".concat(t,"\n\n"):"";for(let t of(e.forEach((t,a)=>{let r=t.task_code||"task-".concat(t.order_index);s+="".concat(a+1,". ").concat(r,"\n").concat(t.prompt_text),a<e.length-1&&(s+="\n\n")}),await navigator.clipboard.writeText(s),f))await eL.mutateAsync(t);j(new Set),N(!1)}catch(e){console.error("\xc7oklu kopyalama hatası:",e)}},eU=()=>{let e=ek.filter(e=>!e.is_used).sort((e,t)=>e.order_index-t.order_index);if(0===e.length)return void alert("Kopyalanmamış prompt bulunamadı!");let t=ej&&(null==e_?void 0:e_.context_text)||"",s="# ".concat((null==e_?void 0:e_.name)||"Prompt Listesi","\n\n");t&&(s+="## Context\n\n".concat(t,"\n\n")),s+="## Kopyalanmamış Prompt'lar\n\n",e.forEach((e,t)=>{let a=e.task_code||"task-".concat(e.order_index);s+="### ".concat(t+1,". ").concat(a,"\n\n"),s+="".concat(e.prompt_text,"\n\n"),s+="---\n\n"});let a=new Blob([s],{type:"text/markdown"}),r=URL.createObjectURL(a),l=document.createElement("a");l.href=r,l.download="".concat((null==e_?void 0:e_.name)||"prompts","-unused.md"),document.body.appendChild(l),l.click(),document.body.removeChild(l),URL.revokeObjectURL(r)},eG=(0,r.useCallback)(async e=>{let{active:t,over:s}=e;if(!s||t.id===s.id)return;let a=ek.sort((e,t)=>{if(e.is_used!==t.is_used)return e.is_used?1:-1;if(!e.is_used&&!t.is_used)return e.order_index-t.order_index;if(e.is_used&&t.is_used){let s=e.last_used_at?new Date(e.last_used_at).getTime():0;return(t.last_used_at?new Date(t.last_used_at).getTime():0)-s}return 0}),r=a.findIndex(e=>e.id===t.id),l=a.findIndex(e=>e.id===s.id),n=(0,ey.be)(a,r,l),i=[];for(let e=0;e<n.length;e++){let t=n[e],s=e+1,a="task-".concat(s);(t.order_index!==s||t.task_code!==a)&&i.push({id:t.id,order_index:s,task_code:a})}if(i.length>0)try{await eD.mutateAsync(i)}catch(e){console.error("Sıralama g\xfcncelleme hatası:",e)}},[ek,eD]);return ef?(0,a.jsxs)("div",{className:"flex h-full",children:[(0,a.jsxs)("div",{className:"flex flex-col flex-1",children:[(0,a.jsxs)("div",{className:"p-3 sm:p-4 lg:p-6 border-b border-gray-200 bg-white",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3 sm:mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 sm:gap-3",children:[(0,a.jsxs)("div",{className:"flex md:hidden gap-1 sm:gap-2",children:[(0,a.jsx)(l.$,{variant:"outline",size:"sm",className:"h-8 w-8 sm:h-10 sm:w-10 p-0 touch-manipulation",children:(0,a.jsx)(E.A,{className:"h-4 w-4 sm:h-5 sm:w-5"})}),(0,a.jsx)(l.$,{variant:"outline",size:"sm",className:"h-8 w-8 sm:h-10 sm:w-10 p-0 touch-manipulation",children:(0,a.jsx)(S.A,{className:"h-4 w-4 sm:h-5 sm:w-5"})})]}),(0,a.jsx)("h2",{className:"text-base sm:text-lg lg:text-xl font-semibold text-gray-900 truncate",children:(null==e_?void 0:e_.name)||"Prompt Listesi"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1 sm:gap-2 overflow-x-auto",children:[(el.length>0||ei)&&(0,a.jsxs)("div",{className:"flex items-center gap-1 flex-shrink-0",children:[el.slice(0,2).map(e=>(0,a.jsxs)(d.E,{variant:"outline",className:"text-xs whitespace-nowrap",children:[(0,a.jsx)(C.A,{className:"w-3 h-3 mr-1"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:e.replace("#","")}),(0,a.jsx)("span",{className:"sm:hidden",children:e.replace("#","").slice(0,3)}),(0,a.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-0 w-3 h-3 ml-1 hover:bg-transparent touch-manipulation",onClick:t=>{t.preventDefault(),t.stopPropagation(),e$(e)},children:(0,a.jsx)(w.A,{className:"w-2 h-2"})})]},e)),el.length>2&&(0,a.jsxs)(d.E,{variant:"outline",className:"text-xs",children:["+",el.length-2]}),ei&&(0,a.jsxs)(d.E,{variant:"outline",className:"text-xs whitespace-nowrap",children:[(0,a.jsx)(_.A,{className:"w-3 h-3 mr-1"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:ei}),(0,a.jsx)("span",{className:"sm:hidden",children:ei.slice(0,3)}),(0,a.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-0 w-3 h-3 ml-1 hover:bg-transparent",onClick:()=>ec(null),children:(0,a.jsx)(w.A,{className:"w-2 h-2"})})]})]}),(0,a.jsxs)(l.$,{type:"button",variant:"outline",size:"sm",onClick:()=>er(!ea),className:"hidden lg:flex",children:[(0,a.jsx)(C.A,{className:"w-4 h-4 mr-1"}),"AI Tag ",ea?"Sakla":"G\xf6ster"]}),(0,a.jsxs)(d.E,{variant:"secondary",className:"text-xs lg:text-sm",children:[R?eP.filter(e=>!e.is_used).length:ek.filter(e=>!e.is_used).length," / ",R?eP.length:ek.length]})]})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(z.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,a.jsx)("input",{type:"text",placeholder:"Prompt'larda ara...",value:R,onChange:e=>H(e.target.value),className:"w-full pl-10 pr-10 py-2.5 sm:py-2 border border-gray-300 rounded-lg text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50 hover:bg-white transition-colors touch-manipulation"}),R&&(0,a.jsx)("button",{onClick:()=>H(""),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 touch-manipulation p-1",children:(0,a.jsx)(w.A,{className:"h-4 w-4"})})]})]}),(0,a.jsx)(c.F,{className:"flex-1 p-2 sm:p-3 lg:p-6 pb-32 sm:pb-28 lg:pb-24",children:(0,a.jsxs)("div",{className:"space-y-2 sm:space-y-3",children:[0===ek.length?(0,a.jsxs)("div",{className:"text-center py-8 sm:py-12",children:[(0,a.jsx)(D.A,{className:"h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-3 sm:mb-4"}),(0,a.jsx)("h3",{className:"text-base sm:text-lg font-medium text-gray-900 mb-2",children:"Hen\xfcz Prompt Yok"}),(0,a.jsx)("p",{className:"text-sm sm:text-base text-gray-500 px-4",children:"Aşağıdaki alandan ilk promptunuzu ekleyin"})]}):0===eP.length?(0,a.jsxs)("div",{className:"text-center py-8 sm:py-12",children:[(0,a.jsx)(z.A,{className:"h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-3 sm:mb-4"}),(0,a.jsx)("h3",{className:"text-base sm:text-lg font-medium text-gray-900 mb-2",children:"Arama Sonucu Bulunamadı"}),(0,a.jsxs)("p",{className:"text-sm sm:text-base text-gray-500 px-4",children:['"',R,'" i\xe7in eşleşen prompt bulunamadı']}),(0,a.jsx)(l.$,{variant:"outline",className:"mt-3 sm:mt-4 touch-manipulation",onClick:()=>H(""),children:"Aramayı Temizle"})]}):(0,a.jsx)(eb.Mp,{sensors:ew,collisionDetection:eb.fp,onDragEnd:eG,children:(0,a.jsx)(ey.gB,{items:eP.map(e=>e.id),strategy:ey._G,children:(()=>{let e=eP.sort((e,t)=>{if(e.is_used!==t.is_used)return e.is_used?1:-1;if(!e.is_used&&!t.is_used)return e.order_index-t.order_index;if(e.is_used&&t.is_used){let s=e.last_used_at?new Date(e.last_used_at).getTime():0;return(t.last_used_at?new Date(t.last_used_at).getTime():0)-s}return 0}),t=R.trim().length>0,s=e;if(!t){let t=(ex-1)*20;s=e.slice(t,t+20)}let r=s.filter(e=>!e.is_used),l=s.filter(e=>e.is_used),n=r.length>0,o=l.length>0;return e.length,(0,a.jsxs)(a.Fragment,{children:[r.map(e=>(0,a.jsx)(eC,{prompt:e,isMultiSelectMode:y,selectedPrompts:f,editingPromptId:i,editingText:u,editTextareaRef:eg,onSelectPrompt:eQ,onEditPrompt:eM,onSaveEdit:eR,onCancelEdit:eH,onCopyPrompt:eT,setEditingText:g,onHashtagFilter:e$},e.id)),n&&o&&(0,a.jsxs)("div",{className:"relative my-8",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"w-full border-t-2 border-green-300 shadow-md"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,a.jsxs)("span",{className:"bg-gradient-to-r from-green-50 to-green-100 px-6 py-2 text-green-700 font-semibold rounded-full border border-green-200 shadow-sm",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 inline mr-2"}),"Kullanılan Promptlar"]})})]}),l.map(e=>(0,a.jsx)(eC,{prompt:e,isMultiSelectMode:y,selectedPrompts:f,editingPromptId:i,editingText:u,editTextareaRef:eg,onSelectPrompt:eQ,onEditPrompt:eM,onSaveEdit:eR,onCancelEdit:eH,onCopyPrompt:eT,setEditingText:g,onHashtagFilter:e$},e.id))]})})()})}),(()=>{let e=eP.sort((e,t)=>{if(e.is_used!==t.is_used)return e.is_used?1:-1;if(!e.is_used&&!t.is_used)return e.order_index-t.order_index;if(e.is_used&&t.is_used){let s=e.last_used_at?new Date(e.last_used_at).getTime():0;return(t.last_used_at?new Date(t.last_used_at).getTime():0)-s}return 0}),t=R.trim().length>0,s=e.length,r=Math.ceil(s/20);return!t&&r>1?(0,a.jsxs)("div",{className:"flex items-center justify-between px-4 py-3 border-t border-gray-200 bg-gray-50",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Toplam ",s," prompt, sayfa ",ex," / ",r]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>ep(Math.max(1,ex-1)),disabled:1===ex,children:"\xd6nceki"}),(0,a.jsx)("div",{className:"flex items-center gap-1",children:Array.from({length:Math.min(5,r)},(e,t)=>{let s;return s=r<=5||ex<=3?t+1:ex>=r-2?r-4+t:ex-2+t,(0,a.jsx)(l.$,{variant:ex===s?"default":"outline",size:"sm",onClick:()=>ep(s),className:"w-8 h-8 p-0",children:s},s)})}),(0,a.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>ep(Math.min(r,ex+1)),disabled:ex===r,children:"Sonraki"})]})]}):null})()]})}),(0,a.jsx)(r.Suspense,{fallback:e?(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,a.jsx)("div",{className:"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden",children:(0,a.jsx)(em,{})})}):null,children:(0,a.jsx)(eN,{open:e,onOpenChange:t||(()=>{}),onSelectContext:e=>{n(e.content),null==t||t()}})}),(0,a.jsx)("div",{className:"fixed bottom-0 left-0 right-0 bg-gradient-to-t from-white via-white to-white/95 border-t border-gray-200 shadow-xl safe-area-bottom z-[60] backdrop-blur-sm",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsxs)("div",{className:"block lg:hidden",children:[(0,a.jsxs)("div",{className:"p-3 border-b border-gray-100 bg-gray-50/50",children:[(0,a.jsx)("div",{className:"flex justify-center mb-2",children:(0,a.jsxs)(l.$,{variant:y?"default":"outline",size:"sm",onClick:eI,className:"min-h-[44px] px-3 transition-all duration-200 ".concat(y?"text-white bg-blue-600 hover:bg-blue-700 shadow-md":"text-blue-600 hover:text-blue-700 border-blue-200 hover:border-blue-300 hover:bg-blue-50"),children:[(0,a.jsx)(L.A,{className:"h-4 w-4 mr-1"}),(0,a.jsx)("span",{className:"text-xs font-medium",children:y?"Se\xe7im İptal":"\xc7oklu Se\xe7"})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[y&&f.size>0&&(0,a.jsxs)(l.$,{variant:"default",size:"sm",onClick:eO,className:"text-white bg-green-600 hover:bg-green-700 min-h-[40px] px-2 shadow-md transition-all duration-200",children:[(0,a.jsx)(k.A,{className:"h-4 w-4 mr-1"}),(0,a.jsxs)("span",{className:"text-xs font-medium",children:["Kopyala (",f.size,")"]})]}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:eU,className:"text-purple-600 hover:text-purple-700 border-purple-200 hover:border-purple-300 hover:bg-purple-50 min-h-[40px] px-2 transition-all duration-200",children:[(0,a.jsx)(P.A,{className:"h-4 w-4 mr-1"}),(0,a.jsx)("span",{className:"text-xs font-medium",children:"Export"})]}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:eq,className:"text-orange-600 hover:text-orange-700 border-orange-200 hover:border-orange-300 hover:bg-orange-50 min-h-[40px] px-2 transition-all duration-200",children:[(0,a.jsx)(K.A,{className:"h-4 w-4 mr-1"}),(0,a.jsx)("span",{className:"text-xs font-medium",children:"Geri Al"})]})]})]}),(0,a.jsxs)("div",{className:"p-2 sm:p-3 relative z-[60]",children:[(0,a.jsxs)("div",{className:"block sm:hidden space-y-2",children:[(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)(ed,{value:s,onChange:n,onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),eK())},placeholder:"Prompt yazın... (/ klas\xf6r, # etiket)",className:"resize-none text-base border-2 focus:border-blue-500 transition-colors duration-200 pr-16 w-full rounded-lg p-3 touch-manipulation",suggestions:{hashtags:eA||[],folders:eE||[]},enableDynamicHeight:!0,heightConfig:{minHeight:48,maxHeightFraction:.25,baseHeight:48}})}),(0,a.jsxs)("div",{className:"flex gap-1",children:[(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:eI,className:"flex-1 min-h-[44px] transition-all duration-200 touch-manipulation ".concat(y?"text-white bg-blue-600 hover:bg-blue-700":"text-blue-600 hover:text-blue-700 border-blue-200"),children:[(0,a.jsx)(L.A,{className:"h-4 w-4 mr-1"}),(0,a.jsx)("span",{className:"text-xs",children:"Se\xe7"})]}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:eU,className:"flex-1 text-purple-600 hover:text-purple-700 border-purple-200 hover:border-purple-300 hover:bg-purple-50 min-h-[44px] touch-manipulation",children:[(0,a.jsx)(P.A,{className:"h-4 w-4 mr-1"}),(0,a.jsx)("span",{className:"text-xs",children:"Export"})]}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:eq,className:"flex-1 text-orange-600 hover:text-orange-700 border-orange-200 hover:border-orange-300 hover:bg-orange-50 min-h-[44px] touch-manipulation",children:[(0,a.jsx)(K.A,{className:"h-4 w-4 mr-1"}),(0,a.jsx)("span",{className:"text-xs",children:"Geri"})]}),(0,a.jsxs)(l.$,{onClick:eK,disabled:!s.trim(),size:"default",className:"flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white min-h-[44px] shadow-lg hover:shadow-xl transition-all duration-200 font-medium touch-manipulation",children:[(0,a.jsx)(D.A,{className:"h-4 w-4 mr-1"}),(0,a.jsx)("span",{className:"text-xs",children:"Ekle"})]})]})]}),(0,a.jsxs)("div",{className:"hidden sm:flex gap-2 items-end relative z-[60]",children:[(0,a.jsx)("div",{className:"flex-1 relative",children:(0,a.jsx)(ed,{value:s,onChange:n,onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),eK())},placeholder:"Prompt metninizi buraya yazın... (/ i\xe7in klas\xf6r, # i\xe7in etiket)",className:"resize-none text-base border-2 focus:border-blue-500 transition-colors duration-200 pr-16 w-full rounded-lg p-3",suggestions:{hashtags:eA||[],folders:eE||[]},enableDynamicHeight:!0,heightConfig:{minHeight:44,maxHeightFraction:.33,baseHeight:44}})}),(0,a.jsxs)("div",{className:"flex gap-1",children:[(0,a.jsx)(l.$,{variant:"outline",size:"sm",onClick:eI,className:"min-h-[44px] px-2 transition-all duration-200 ".concat(y?"text-white bg-blue-600 hover:bg-blue-700":"text-blue-600 hover:text-blue-700 border-blue-200"),children:(0,a.jsx)(L.A,{className:"h-4 w-4"})}),(0,a.jsx)(l.$,{variant:"outline",size:"sm",onClick:eU,className:"text-purple-600 hover:text-purple-700 border-purple-200 hover:border-purple-300 hover:bg-purple-50 min-h-[44px] px-2",children:(0,a.jsx)(P.A,{className:"h-4 w-4"})}),(0,a.jsx)(l.$,{variant:"outline",size:"sm",onClick:eq,className:"text-orange-600 hover:text-orange-700 border-orange-200 hover:border-orange-300 hover:bg-orange-50 min-h-[44px] px-2",children:(0,a.jsx)(K.A,{className:"h-4 w-4"})}),(0,a.jsxs)(l.$,{onClick:eK,disabled:!s.trim(),size:"default",className:"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white px-4 min-h-[44px] shrink-0 shadow-lg hover:shadow-xl transition-all duration-200 font-medium",children:[(0,a.jsx)(D.A,{className:"h-4 w-4 mr-1"}),"Ekle"]})]})]})]})]}),(0,a.jsx)("div",{className:"hidden lg:block p-4 bg-gradient-to-r from-gray-50 to-white border-t border-gray-100 relative z-[60]",children:(0,a.jsxs)("div",{className:"bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 p-4",children:[(0,a.jsxs)("div",{className:"flex gap-4 items-end",children:[(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsx)(ed,{value:s,onChange:n,onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),eK())},placeholder:"Prompt metninizi buraya yazın... (/ i\xe7in klas\xf6r, # i\xe7in etiket)",className:"resize-none border-0 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg bg-gray-50 hover:bg-white transition-all duration-200 text-base pr-20 w-full p-3",suggestions:{hashtags:eA||[],folders:eE||[]},enableDynamicHeight:!0,heightConfig:{minHeight:56,maxHeightFraction:.33,baseHeight:56}}),et&&(0,a.jsxs)("div",{className:"space-y-4 pt-4 border-t border-gray-200 bg-gray-50 rounded-lg p-4 mt-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("div",{className:"w-1 h-4 bg-purple-500 rounded-full"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Kategorilendirme"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Başlık (İsteğe bağlı)"}),(0,a.jsx)(o.p,{type:"text",placeholder:"Bu prompt i\xe7in kısa bir başlık...",value:X,onChange:e=>ee(e.target.value),className:"border-gray-300 focus:border-purple-500 focus:ring-purple-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Etiketler"}),ef&&(0,a.jsx)(Z,{hashtags:U,onHashtagsChange:G,suggestions:eA||[],placeholder:"Etiket ekleyin... (\xf6rn: #frontend, #api)",maxTags:5})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Klas\xf6r/Kategori"}),ef&&(0,a.jsx)(Y,{category:J,onCategoryChange:V,suggestions:eE||[],placeholder:"Klas\xf6r se\xe7in... (\xf6rn: /frontend, /admin)"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-center gap-3 py-3 border-t border-gray-200 mt-4 pt-4",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Console Log Fix"}),(0,a.jsx)(h,{checked:eu,onCheckedChange:eh,className:"scale-90"})]}),ev&&(0,a.jsx)(Q.LD,{type:"prompt",current:ev.current_prompts,max:ev.max_prompts_per_project,onUpgrade:()=>{}}),(0,a.jsxs)(l.$,{onClick:eK,disabled:!s.trim()||ev&&!ev.can_create_prompt,size:"lg",className:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:from-gray-300 disabled:to-gray-400 text-white px-8 py-3 min-h-[56px] shadow-lg hover:shadow-xl transition-all duration-200 font-semibold rounded-lg",children:[(0,a.jsx)(D.A,{className:"h-5 w-5 mr-2"}),"Prompt Ekle"]})]}),(0,a.jsx)("div",{className:"space-y-3",children:(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(l.$,{variant:y?"default":"outline",size:"sm",onClick:eI,className:"transition-all duration-200 rounded-lg font-medium ".concat(y?"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-md":"border-blue-200 text-blue-600 hover:text-blue-700 hover:border-blue-300 hover:bg-blue-50"),children:[(0,a.jsx)(L.A,{className:"h-4 w-4 mr-2"}),"\xc7oklu Se\xe7"]}),y&&f.size>0&&(0,a.jsxs)(l.$,{variant:"default",size:"sm",onClick:eO,className:"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white shadow-md transition-all duration-200 rounded-lg font-medium",children:[(0,a.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"Kopyala (",f.size,")"]}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:eU,className:"border-purple-200 text-purple-600 hover:text-purple-700 hover:border-purple-300 hover:bg-purple-50 transition-all duration-200 rounded-lg font-medium",children:[(0,a.jsx)(P.A,{className:"h-4 w-4 mr-2"}),"Export"]}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:eq,className:"border-orange-200 text-orange-600 hover:text-orange-700 hover:border-orange-300 hover:bg-orange-50 transition-all duration-200 rounded-lg font-medium",children:[(0,a.jsx)(K.A,{className:"h-4 w-4 mr-2"}),"Geri Al"]}),!s.trim()&&(0,a.jsx)(l.$,{type:"button",variant:"outline",size:"sm",onClick:()=>es(!et),className:"border-gray-300 text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition-all duration-200",children:et?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Kategorileri Gizle"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"w-4 h-4 mr-2"}),"Kategori Ekle"]})})]})})]})})]})})]}),ea&&ef&&(0,a.jsx)(eo,{projectId:ef,onHashtagClick:e$,onCategoryClick:eF,selectedHashtags:el,selectedCategory:ei,className:"hidden lg:flex"})]}):(0,a.jsxs)("div",{className:"flex flex-col h-full",children:[(0,a.jsx)("div",{className:"h-16 sm:h-20 lg:h-24"}),(0,a.jsx)("div",{className:"flex-1 flex items-center justify-center px-4",children:(0,a.jsx)("div",{className:"max-w-2xl w-full",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(b.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-6"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Proje Se\xe7in"}),(0,a.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Başlamak i\xe7in sol panelden bir proje se\xe7in veya yeni bir proje oluşturun"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(A.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-6"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Context Alanı"}),(0,a.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Proje se\xe7tikten sonra sağ panelden context metninizi yazabilirsiniz"})]})]})})}),(0,a.jsx)("div",{className:"h-32 sm:h-40 lg:h-48"})]})}let eE=eA},15501:(e,t,s)=>{s.d(t,{$I:()=>d,F$:()=>o,GQ:()=>u,Qu:()=>m,sW:()=>c});var a=s(32960),r=s(26715),l=s(5041),n=s(70478),i=s(86489);function o(e){return(0,a.I)({queryKey:["prompts",e],queryFn:async()=>{if(!e)return console.log("\uD83D\uDCDD [USE_PROMPTS] No project ID provided"),[];console.log("\uD83D\uDCDD [USE_PROMPTS] Fetching prompts for project:",e);try{var t;let{data:{session:s},error:a}=await n.L.auth.getSession();console.log("\uD83D\uDCDD [USE_PROMPTS] Session check:",{hasSession:!!s,sessionError:null==a?void 0:a.message,userId:null==s||null==(t=s.user)?void 0:t.id});let{data:r,error:l}=await n.L.from("prompts").select("*").eq("project_id",e).order("order_index",{ascending:!0});if(l)throw console.error("❌ [USE_PROMPTS] Error fetching prompts:",l),Error(l.message);return console.log("✅ [USE_PROMPTS] Prompts fetched:",(null==r?void 0:r.length)||0,"prompts"),r||[]}catch(e){throw console.error("\uD83D\uDCA5 [USE_PROMPTS] Exception:",e),e}},enabled:!!e})}function c(){let e=(0,r.jE)();return(0,l.n)({mutationFn:async e=>{let t=await (0,i.vw)(e.project_id);if(!t.allowed)throw Error(t.reason||i.x5.PROMPT_LIMIT_REACHED);let{data:{user:s},error:a}=await n.L.auth.getUser();if(a||!s)throw Error("Kullanıcı oturumu bulunamadı");let r=e.task_code||"task-".concat(e.order_index),{data:l,error:o}=await n.L.from("prompts").insert({...e,user_id:s.id,task_code:r,tags:e.tags||[],is_favorite:e.is_favorite||!1,usage_count:e.usage_count||0}).select().single();if(o)throw Error(o.message);return l},onMutate:async t=>{await e.cancelQueries({queryKey:["prompts",t.project_id]});let s=e.getQueryData(["prompts",t.project_id]),{data:{user:a}}=await n.L.auth.getUser();if(!a)return{previousPrompts:s};let r=e.getQueryData(["prompts",t.project_id])||[],l=(r.length>0?Math.max(...r.map(e=>e.order_index||0)):0)+1,i={id:"temp-".concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9)),...t,user_id:a.id,order_index:l,task_code:t.task_code||"task-".concat(l),tags:t.tags||[],is_favorite:t.is_favorite||!1,usage_count:0,created_at:new Date().toISOString(),updated_at:new Date().toISOString()};return e.setQueryData(["prompts",t.project_id],e=>Array.isArray(e)?[i,...e]:[i]),{previousPrompts:s,optimisticPrompt:i}},onError:(t,s,a)=>{(null==a?void 0:a.previousPrompts)&&e.setQueryData(["prompts",s.project_id],a.previousPrompts)},onSuccess:async(t,s,a)=>{e.setQueryData(["prompts",t.project_id],e=>{if(!Array.isArray(e))return[t];let s=e.filter(e=>{var s;return e.id!==(null==a||null==(s=a.optimisticPrompt)?void 0:s.id)&&e.id!==t.id});return[t,...s]}),setTimeout(()=>{e.invalidateQueries({queryKey:["popular-hashtags",t.project_id]}),e.invalidateQueries({queryKey:["all-hashtags",t.project_id]}),e.invalidateQueries({queryKey:["popular-categories",t.project_id]}),e.invalidateQueries({queryKey:["all-categories",t.project_id]}),(0,i.O3)().then(()=>{e.invalidateQueries({queryKey:["user-limits"]}),e.invalidateQueries({queryKey:["usage-stats"]})}).catch(e=>{console.warn("Kullanım istatistikleri g\xfcncellenemedi:",e)})},100)}})}function d(){let e=(0,r.jE)();return(0,l.n)({mutationFn:async e=>{let{id:t,...s}=e,{data:a,error:r}=await n.L.from("prompts").update({...s,updated_at:new Date().toISOString()}).eq("id",t).select().single();if(r)throw Error(r.message);return a},onSuccess:t=>{e.invalidateQueries({queryKey:["prompts",t.project_id]}),e.invalidateQueries({queryKey:["popular-hashtags",t.project_id]}),e.invalidateQueries({queryKey:["all-hashtags",t.project_id]}),e.invalidateQueries({queryKey:["popular-categories",t.project_id]}),e.invalidateQueries({queryKey:["all-categories",t.project_id]})}})}function m(){let e=(0,r.jE)();return(0,l.n)({mutationFn:async e=>{let{data:t,error:s}=await n.L.from("prompts").update({is_used:!0}).eq("id",e).select().single();if(s)throw Error(s.message);return t},onMutate:async t=>{let s=["prompts"];await e.cancelQueries({queryKey:s});let a=e.getQueriesData({queryKey:s});return e.setQueriesData({queryKey:s},e=>e&&Array.isArray(e)?e.map(e=>e.id===t?{...e,is_used:!0}:e):e),{previousPrompts:a}},onError:(t,s,a)=>{(null==a?void 0:a.previousPrompts)&&a.previousPrompts.forEach(t=>{let[s,a]=t;e.setQueryData(s,a)})},onSettled:t=>{t&&e.invalidateQueries({queryKey:["prompts",t.project_id]})}})}function u(){let e=(0,r.jE)();return(0,l.n)({mutationFn:async e=>{let t=e.filter(e=>void 0!==e.order_index||void 0!==e.task_code),s=e.filter(e=>void 0===e.order_index&&void 0===e.task_code),a=[];if(t.length>0)try{let{data:e,error:s}=await n.L.rpc("bulk_update_prompts_order",{prompt_updates:t});if(s){console.warn("RPC function failed, falling back to individual updates:",s);let e=t.map(async e=>{let{data:t,error:s}=await n.L.from("prompts").update({...e,updated_at:new Date().toISOString()}).eq("id",e.id).select().single();if(s)throw Error("Failed to update prompt ".concat(e.id,": ").concat(s.message));return t}),r=await Promise.all(e);a.push(...r)}else if(e){let t=e.map(e=>e.id),{data:s,error:r}=await n.L.from("prompts").select("*").in("id",t);if(r)throw Error("Failed to fetch updated prompts: ".concat(r.message));a.push(...s||[])}}catch(e){throw console.error("Bulk update error:",e),e}if(s.length>0){let e=s.map(async e=>{let{data:t,error:s}=await n.L.from("prompts").update({...e,updated_at:new Date().toISOString()}).eq("id",e.id).select().single();if(s)throw Error("Failed to update prompt ".concat(e.id,": ").concat(s.message));return t}),t=await Promise.all(e);a.push(...t)}return a},onMutate:async t=>{let s=["prompts"];await e.cancelQueries({queryKey:s});let a=e.getQueriesData({queryKey:s});return e.setQueriesData({queryKey:s},e=>e&&Array.isArray(e)?e.map(e=>{let s=t.find(t=>t.id===e.id);return s?{...e,...s}:e}):e),{previousPrompts:a}},onError:(t,s,a)=>{(null==a?void 0:a.previousPrompts)&&a.previousPrompts.forEach(t=>{let[s,a]=t;e.setQueryData(s,a)})},onSettled:t=>{t&&t.length>0&&e.invalidateQueries({queryKey:["prompts",t[0].project_id]})}})}},88539:(e,t,s)=>{s.d(t,{T:()=>l});var a=s(95155);s(12115);var r=s(59434);function l(e){let{className:t,...s}=e;return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...s})}}}]);
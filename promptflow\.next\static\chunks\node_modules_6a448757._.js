(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/highlight.js/lib/languages/1c.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/1c.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/abnf.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/abnf.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/accesslog.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/accesslog.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/actionscript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/actionscript.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/ada.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/ada.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/angelscript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/angelscript.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/apache.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/apache.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/applescript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/applescript.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/arcade.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/arcade.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/arduino.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/arduino.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/armasm.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/armasm.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/asciidoc.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/asciidoc.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/aspectj.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/aspectj.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/autohotkey.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/autohotkey.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/autoit.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/autoit.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/avrasm.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/avrasm.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/awk.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/awk.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/axapta.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/axapta.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/bash.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/bash.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/basic.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/basic.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/bnf.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/bnf.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/brainfuck.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/brainfuck.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/c-like.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/c-like.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/c.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/c.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/cal.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/cal.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/capnproto.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/capnproto.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/ceylon.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/ceylon.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/clean.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/clean.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/clojure-repl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/clojure-repl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/clojure.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/clojure.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/cmake.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/cmake.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/coffeescript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/coffeescript.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/coq.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/coq.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/cos.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/cos.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/cpp.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/cpp.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/crmsh.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/crmsh.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/crystal.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/crystal.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/csharp.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/csharp.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/csp.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/csp.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/css.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/css.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/d.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/d.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/dart.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/dart.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/delphi.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/delphi.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/diff.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/diff.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/django.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/django.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/dns.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/dns.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/dockerfile.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/dockerfile.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/dos.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/dos.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/dsconfig.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/dsconfig.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/dts.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/dts.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/dust.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/dust.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/ebnf.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/ebnf.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/elixir.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/elixir.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/elm.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/elm.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/erb.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/erb.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/erlang-repl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/erlang-repl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/erlang.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/erlang.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/excel.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/excel.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/fix.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/fix.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/flix.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/flix.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/fortran.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/fortran.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/fsharp.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/fsharp.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/gams.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/gams.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/gauss.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/gauss.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/gcode.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/gcode.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/gherkin.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/gherkin.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/glsl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/glsl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/gml.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/gml.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/go.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/go.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/golo.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/golo.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/gradle.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/gradle.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/groovy.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/groovy.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/haml.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/haml.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/handlebars.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/handlebars.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/haskell.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/haskell.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/haxe.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/haxe.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/hsp.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/hsp.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/htmlbars.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/htmlbars.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/http.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/http.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/hy.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/hy.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/inform7.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/inform7.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/ini.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/ini.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/irpf90.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/irpf90.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/isbl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/isbl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/java.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/java.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/javascript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/javascript.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/jboss-cli.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/jboss-cli.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/json.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/json.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/julia-repl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/julia-repl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/julia.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/julia.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/kotlin.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/kotlin.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/lasso.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/lasso.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/latex.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/latex.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/ldif.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/ldif.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/leaf.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/leaf.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/less.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/less.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/lisp.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/lisp.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/livecodeserver.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/livecodeserver.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/livescript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/livescript.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/llvm.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/llvm.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/lsl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/lsl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/lua.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/lua.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/makefile.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/makefile.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/markdown.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/markdown.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/mathematica.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/mathematica.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/matlab.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/matlab.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/maxima.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/maxima.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/mel.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/mel.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/mercury.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/mercury.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/mipsasm.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/mipsasm.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/mizar.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/mizar.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/mojolicious.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/mojolicious.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/monkey.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/monkey.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/moonscript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/moonscript.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/n1ql.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/n1ql.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/nginx.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/nginx.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/nim.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/nim.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/nix.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/nix.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/node-repl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/node-repl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/nsis.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/nsis.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/objectivec.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/objectivec.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/ocaml.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/ocaml.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/openscad.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/openscad.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/oxygene.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/oxygene.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/parser3.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/parser3.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/perl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/perl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/pf.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/pf.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/pgsql.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/pgsql.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/php-template.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/php-template.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/php.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/php.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/plaintext.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/plaintext.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/pony.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/pony.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/powershell.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/powershell.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/processing.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/processing.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/profile.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/profile.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/prolog.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/prolog.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/properties.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/properties.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/protobuf.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/protobuf.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/puppet.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/puppet.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/purebasic.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/purebasic.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/python-repl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/python-repl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/python.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/python.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/q.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/q.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/qml.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/qml.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/r.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/r.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/reasonml.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/reasonml.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/rib.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/rib.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/roboconf.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/roboconf.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/routeros.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/routeros.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/rsl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/rsl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/ruby.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/ruby.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/ruleslanguage.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/ruleslanguage.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/rust.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/rust.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/sas.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/sas.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/scala.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/scala.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/scheme.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/scheme.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/scilab.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/scilab.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/scss.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/scss.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/shell.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/shell.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/smali.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/smali.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/smalltalk.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/smalltalk.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/sml.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/sml.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/sqf.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/sqf.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/sql.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/sql.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/sql_more.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/sql_more.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/stan.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/stan.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/stata.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/stata.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/step21.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/step21.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/stylus.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/stylus.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/subunit.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/subunit.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/swift.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/swift.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/taggerscript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/taggerscript.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/tap.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/tap.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/tcl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/tcl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/thrift.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/thrift.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/tp.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/tp.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/twig.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/twig.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/typescript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/typescript.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/vala.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/vala.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/vbnet.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/vbnet.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/vbscript-html.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/vbscript-html.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/vbscript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/vbscript.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/verilog.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/verilog.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/vhdl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/vhdl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/vim.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/vim.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/x86asm.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/x86asm.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/xl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/xl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/xml.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/xml.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/xquery.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/xquery.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/yaml.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/yaml.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highlight.js/lib/languages/zephir.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/highlight.js/lib/languages/zephir.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/lowlight/lib/core.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/lowlight/lib/core.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/abap.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/abap.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/abnf.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/abnf.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/actionscript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/actionscript.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/ada.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/ada.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/agda.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/agda.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/al.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/al.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/antlr4.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/antlr4.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/apacheconf.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/apacheconf.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/apex.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/apex.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/apl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/apl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/applescript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/applescript.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/aql.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/aql.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/arduino.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/arduino.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/arff.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/arff.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/asciidoc.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/asciidoc.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/asm6502.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/asm6502.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/asmatmel.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/asmatmel.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/aspnet.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/aspnet.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/autohotkey.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/autohotkey.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/autoit.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/autoit.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/avisynth.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/avisynth.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/avro-idl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/avro-idl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/bash.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/bash.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/basic.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/basic.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/batch.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/batch.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/bbcode.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/bbcode.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/bicep.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/bicep.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/birb.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/birb.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/bison.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/bison.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/bnf.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/bnf.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/brainfuck.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/brainfuck.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/brightscript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/brightscript.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/bro.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/bro.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/bsl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/bsl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/c.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/c.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/cfscript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/cfscript.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/chaiscript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/chaiscript.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/cil.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/cil.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/clike.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/clike.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/clojure.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/clojure.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/cmake.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/cmake.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/cobol.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/cobol.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/coffeescript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/coffeescript.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/concurnas.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/concurnas.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/coq.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/coq.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/cpp.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/cpp.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/crystal.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/crystal.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/csharp.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/csharp.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/cshtml.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/cshtml.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/csp.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/csp.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/css-extras.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/css-extras.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/css.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/css.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/csv.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/csv.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/cypher.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/cypher.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/d.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/d.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/dart.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/dart.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/dataweave.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/dataweave.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/dax.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/dax.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/dhall.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/dhall.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/diff.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/diff.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/django.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/django.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/dns-zone-file.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/dns-zone-file.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/docker.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/docker.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/dot.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/dot.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/ebnf.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/ebnf.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/editorconfig.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/editorconfig.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/eiffel.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/eiffel.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/ejs.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/ejs.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/elixir.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/elixir.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/elm.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/elm.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/erb.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/erb.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/erlang.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/erlang.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/etlua.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/etlua.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/excel-formula.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/excel-formula.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/factor.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/factor.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/false.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/false.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/firestore-security-rules.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/firestore-security-rules.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/flow.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/flow.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/fortran.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/fortran.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/fsharp.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/fsharp.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/ftl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/ftl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/gap.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/gap.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/gcode.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/gcode.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/gdscript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/gdscript.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/gedcom.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/gedcom.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/gherkin.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/gherkin.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/git.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/git.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/glsl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/glsl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/gml.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/gml.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/gn.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/gn.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/go-module.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/go-module.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/go.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/go.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/graphql.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/graphql.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/groovy.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/groovy.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/haml.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/haml.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/handlebars.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/handlebars.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/haskell.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/haskell.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/haxe.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/haxe.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/hcl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/hcl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/hlsl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/hlsl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/hoon.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/hoon.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/hpkp.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/hpkp.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/hsts.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/hsts.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/http.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/http.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/ichigojam.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/ichigojam.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/icon.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/icon.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/icu-message-format.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/icu-message-format.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/idris.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/idris.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/iecst.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/iecst.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/ignore.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/ignore.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/inform7.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/inform7.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/ini.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/ini.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/io.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/io.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/j.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/j.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/java.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/java.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/javadoc.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/javadoc.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/javadoclike.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/javadoclike.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/javascript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/javascript.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/javastacktrace.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/javastacktrace.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/jexl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/jexl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/jolie.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/jolie.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/jq.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/jq.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/js-extras.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/js-extras.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/js-templates.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/js-templates.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/jsdoc.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/jsdoc.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/json.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/json.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/json5.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/json5.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/jsonp.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/jsonp.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/jsstacktrace.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/jsstacktrace.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/jsx.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/jsx.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/julia.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/julia.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/keepalived.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/keepalived.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/keyman.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/keyman.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/kotlin.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/kotlin.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/kumir.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/kumir.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/kusto.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/kusto.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/latex.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/latex.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/latte.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/latte.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/less.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/less.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/lilypond.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/lilypond.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/liquid.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/liquid.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/lisp.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/lisp.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/livescript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/livescript.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/llvm.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/llvm.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/log.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/log.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/lolcode.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/lolcode.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/lua.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/lua.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/magma.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/magma.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/makefile.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/makefile.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/markdown.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/markdown.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/markup-templating.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/markup-templating.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/markup.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/markup.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/matlab.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/matlab.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/maxscript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/maxscript.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/mel.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/mel.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/mermaid.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/mermaid.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/mizar.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/mizar.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/mongodb.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/mongodb.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/monkey.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/monkey.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/moonscript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/moonscript.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/n1ql.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/n1ql.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/n4js.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/n4js.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/nand2tetris-hdl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/nand2tetris-hdl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/naniscript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/naniscript.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/nasm.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/nasm.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/neon.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/neon.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/nevod.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/nevod.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/nginx.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/nginx.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/nim.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/nim.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/nix.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/nix.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/nsis.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/nsis.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/objectivec.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/objectivec.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/ocaml.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/ocaml.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/opencl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/opencl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/openqasm.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/openqasm.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/oz.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/oz.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/parigp.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/parigp.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/parser.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/parser.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/pascal.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/pascal.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/pascaligo.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/pascaligo.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/pcaxis.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/pcaxis.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/peoplecode.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/peoplecode.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/perl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/perl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/php-extras.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/php-extras.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/php.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/php.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/phpdoc.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/phpdoc.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/plsql.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/plsql.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/powerquery.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/powerquery.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/powershell.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/powershell.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/processing.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/processing.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/prolog.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/prolog.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/promql.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/promql.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/properties.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/properties.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/protobuf.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/protobuf.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/psl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/psl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/pug.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/pug.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/puppet.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/puppet.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/pure.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/pure.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/purebasic.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/purebasic.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/purescript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/purescript.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/python.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/python.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/q.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/q.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/qml.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/qml.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/qore.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/qore.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/qsharp.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/qsharp.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/r.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/r.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/racket.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/racket.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/reason.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/reason.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/regex.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/regex.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/rego.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/rego.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/renpy.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/renpy.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/rest.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/rest.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/rip.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/rip.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/roboconf.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/roboconf.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/robotframework.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/robotframework.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/ruby.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/ruby.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/rust.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/rust.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/sas.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/sas.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/sass.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/sass.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/scala.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/scala.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/scheme.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/scheme.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/scss.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/scss.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/shell-session.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/shell-session.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/smali.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/smali.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/smalltalk.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/smalltalk.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/smarty.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/smarty.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/sml.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/sml.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/solidity.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/solidity.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/solution-file.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/solution-file.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/soy.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/soy.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/sparql.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/sparql.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/splunk-spl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/splunk-spl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/sqf.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/sqf.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/sql.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/sql.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/squirrel.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/squirrel.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/stan.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/stan.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/stylus.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/stylus.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/swift.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/swift.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/systemd.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/systemd.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/t4-cs.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/t4-cs.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/t4-templating.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/t4-templating.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/t4-vb.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/t4-vb.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/tap.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/tap.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/tcl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/tcl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/textile.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/textile.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/toml.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/toml.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/tremor.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/tremor.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/tsx.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/tsx.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/tt2.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/tt2.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/turtle.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/turtle.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/twig.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/twig.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/typescript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/typescript.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/typoscript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/typoscript.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/unrealscript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/unrealscript.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/uorazor.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/uorazor.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/uri.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/uri.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/v.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/v.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/vala.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/vala.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/vbnet.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/vbnet.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/velocity.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/velocity.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/verilog.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/verilog.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/vhdl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/vhdl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/vim.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/vim.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/visual-basic.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/visual-basic.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/warpscript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/warpscript.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/wasm.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/wasm.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/web-idl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/web-idl.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/wiki.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/wiki.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/wolfram.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/wolfram.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/wren.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/wren.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/xeora.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/xeora.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/xml-doc.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/xml-doc.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/xojo.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/xojo.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/xquery.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/xquery.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/yaml.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/yaml.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/yang.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/yang.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/lang/zig.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/lang/zig.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/core.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/core.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/refractor/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/refractor/index.js [app-client] (ecmascript)");
    });
});
}),
}]);
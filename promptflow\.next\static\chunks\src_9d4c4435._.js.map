{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,KAAyD;QAAzD,EAAE,SAAS,EAAE,GAAG,OAAyC,GAAzD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction ScrollArea({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>) {\n  return (\n    <ScrollAreaPrimitive.Root\n      data-slot=\"scroll-area\"\n      className={cn(\"relative\", className)}\n      {...props}\n    >\n      <ScrollAreaPrimitive.Viewport\n        data-slot=\"scroll-area-viewport\"\n        className=\"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1\"\n      >\n        {children}\n      </ScrollAreaPrimitive.Viewport>\n      <ScrollBar />\n      <ScrollAreaPrimitive.Corner />\n    </ScrollAreaPrimitive.Root>\n  )\n}\n\nfunction ScrollBar({\n  className,\n  orientation = \"vertical\",\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {\n  return (\n    <ScrollAreaPrimitive.ScrollAreaScrollbar\n      data-slot=\"scroll-area-scrollbar\"\n      orientation={orientation}\n      className={cn(\n        \"flex touch-none p-px transition-colors select-none\",\n        orientation === \"vertical\" &&\n          \"h-full w-2.5 border-l border-l-transparent\",\n        orientation === \"horizontal\" &&\n          \"h-2.5 flex-col border-t border-t-transparent\",\n        className\n      )}\n      {...props}\n    >\n      <ScrollAreaPrimitive.ScrollAreaThumb\n        data-slot=\"scroll-area-thumb\"\n        className=\"bg-border relative flex-1 rounded-full\"\n      />\n    </ScrollAreaPrimitive.ScrollAreaScrollbar>\n  )\n}\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,WAAW,KAIoC;QAJpC,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OACmD,GAJpC;IAKlB,qBACE,6LAAC,6KAAA,CAAA,OAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;0BAET,6LAAC,6KAAA,CAAA,WAA4B;gBAC3B,aAAU;gBACV,WAAU;0BAET;;;;;;0BAEH,6LAAC;;;;;0BACD,6LAAC,6KAAA,CAAA,SAA0B;;;;;;;;;;;AAGjC;KArBS;AAuBT,SAAS,UAAU,KAIoD;QAJpD,EACjB,SAAS,EACT,cAAc,UAAU,EACxB,GAAG,OACkE,GAJpD;IAKjB,qBACE,6LAAC,6KAAA,CAAA,sBAAuC;QACtC,aAAU;QACV,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA,gBAAgB,cACd,8CACF,gBAAgB,gBACd,gDACF;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,6KAAA,CAAA,kBAAmC;YAClC,aAAU;YACV,WAAU;;;;;;;;;;;AAIlB;MAzBS", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Switch = React.forwardRef<\n  React.ElementRef<typeof SwitchPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\n>(({ className, ...props }, ref) => (\n  <SwitchPrimitives.Root\n    className={cn(\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  >\n    <SwitchPrimitives.Thumb\n      className={cn(\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\n      )}\n    />\n  </SwitchPrimitives.Root>\n))\nSwitch.displayName = SwitchPrimitives.Root.displayName\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAG7B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sXACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,6LAAC,qKAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/store/app-store.ts"], "sourcesContent": ["import { create } from 'zustand'\r\nimport { persist } from 'zustand/middleware'\r\n\r\ninterface AppState {\r\n  activeProjectId: string | null\r\n  setActiveProjectId: (projectId: string | null) => void\r\n  isContextEnabled: boolean\r\n  setIsContextEnabled: (enabled: boolean) => void\r\n  // Sidebar collapse states\r\n  isProjectSidebarCollapsed: boolean\r\n  setIsProjectSidebarCollapsed: (collapsed: boolean) => void\r\n  isContextSidebarCollapsed: boolean\r\n  setIsContextSidebarCollapsed: (collapsed: boolean) => void\r\n}\r\n\r\nexport const useAppStore = create<AppState>()(\r\n  persist(\r\n    (set) => ({\r\n      activeProjectId: null,\r\n      setActiveProjectId: (projectId) => set({ activeProjectId: projectId }),\r\n      isContextEnabled: true,\r\n      setIsContextEnabled: (enabled) => set({ isContextEnabled: enabled }),\r\n      // Sidebar collapse states\r\n      isProjectSidebarCollapsed: false,\r\n      setIsProjectSidebarCollapsed: (collapsed) => set({ isProjectSidebarCollapsed: collapsed }),\r\n      isContextSidebarCollapsed: false,\r\n      setIsContextSidebarCollapsed: (collapsed) => set({ isContextSidebarCollapsed: collapsed }),\r\n    }),\r\n    {\r\n      name: 'promptflow-app-store',\r\n    }\r\n  )\r\n)"], "names": [], "mappings": ";;;AAAA;AACA;;;AAcO,MAAM,cAAc,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC9B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,iBAAiB;QACjB,oBAAoB,CAAC,YAAc,IAAI;gBAAE,iBAAiB;YAAU;QACpE,kBAAkB;QAClB,qBAAqB,CAAC,UAAY,IAAI;gBAAE,kBAAkB;YAAQ;QAClE,0BAA0B;QAC1B,2BAA2B;QAC3B,8BAA8B,CAAC,YAAc,IAAI;gBAAE,2BAA2B;YAAU;QACxF,2BAA2B;QAC3B,8BAA8B,CAAC,YAAc,IAAI;gBAAE,2BAA2B;YAAU;IAC1F,CAAC,GACD;IACE,MAAM;AACR", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/plan-limits.ts"], "sourcesContent": ["// Plan Limitleri Kontrol Middleware ve Utility Fonksiyonları\n\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\n\nexport interface PlanLimits {\n  current_projects: number\n  current_prompts: number\n  max_projects: number\n  max_prompts_per_project: number\n  can_create_project: boolean\n  can_create_prompt: boolean\n}\n\nexport interface UserPlan {\n  plan_id: string\n  plan_name: string\n  display_name: string\n  max_projects: number\n  max_prompts_per_project: number\n  features: Record<string, boolean | string | number>\n  status: string\n  expires_at: string | null\n}\n\n// Plan limitlerini kontrol et\nexport async function checkUserLimits(): Promise<PlanLimits | null> {\n  try {\n    const { data: user } = await supabase.auth.getUser()\n    if (!user.user) {\n      throw new Error('Kullanıcı oturumu bulunamadı')\n    }\n\n    const { data, error } = await supabase.rpc('check_user_limits', {\n      user_uuid: user.user.id\n    })\n\n    if (error) {\n      console.error('Plan limitleri kontrol edilemedi:', error)\n      throw new Error(error.message)\n    }\n\n    return data?.[0] || null\n  } catch (error) {\n    console.error('Plan limitleri kontrol hatası:', error)\n    return null\n  }\n}\n\n// Kullanıcının aktif planını getir\nexport async function getUserActivePlan(): Promise<UserPlan | null> {\n  try {\n    const { data: user } = await supabase.auth.getUser()\n    if (!user.user) {\n      throw new Error('Kullanıcı oturumu bulunamadı')\n    }\n\n    const { data, error } = await supabase.rpc('get_user_active_plan', {\n      user_uuid: user.user.id\n    })\n\n    if (error) {\n      console.error('Aktif plan getirilemedi:', error)\n      throw new Error(error.message)\n    }\n\n    return data?.[0] || null\n  } catch (error) {\n    console.error('Aktif plan getirme hatası:', error)\n    return null\n  }\n}\n\n// Proje oluşturma limiti kontrol et\nexport async function canCreateProject(): Promise<{ allowed: boolean; reason?: string }> {\n  const limits = await checkUserLimits()\n  \n  if (!limits) {\n    return { allowed: false, reason: 'Plan bilgileri alınamadı' }\n  }\n\n  if (!limits.can_create_project) {\n    return { \n      allowed: false, \n      reason: `Proje limiti aşıldı (${limits.current_projects}/${limits.max_projects})` \n    }\n  }\n\n  return { allowed: true }\n}\n\n// Prompt oluşturma limiti kontrol et\nexport async function canCreatePrompt(projectId?: string): Promise<{ allowed: boolean; reason?: string }> {\n  const limits = await checkUserLimits()\n  \n  if (!limits) {\n    return { allowed: false, reason: 'Plan bilgileri alınamadı' }\n  }\n\n  if (!limits.can_create_prompt) {\n    return { \n      allowed: false, \n      reason: `Prompt limiti aşıldı (${limits.current_prompts}/${limits.max_prompts_per_project})` \n    }\n  }\n\n  // Eğer belirli bir proje için kontrol ediliyorsa, o projenin prompt sayısını kontrol et\n  if (projectId) {\n    try {\n      const { count, error } = await supabase\n        .from('prompts')\n        .select('*', { count: 'exact', head: true })\n        .eq('project_id', projectId)\n\n      if (error) {\n        console.error('Proje prompt sayısı kontrol edilemedi:', error)\n        return { allowed: false, reason: 'Proje bilgileri alınamadı' }\n      }\n\n      const currentPrompts = count || 0\n      if (limits.max_prompts_per_project !== -1 && currentPrompts >= limits.max_prompts_per_project) {\n        return { \n          allowed: false, \n          reason: `Bu proje için prompt limiti aşıldı (${currentPrompts}/${limits.max_prompts_per_project})` \n        }\n      }\n    } catch (error) {\n      console.error('Proje prompt sayısı kontrol hatası:', error)\n      return { allowed: false, reason: 'Proje bilgileri kontrol edilemedi' }\n    }\n  }\n\n  return { allowed: true }\n}\n\n// Plan özelliği kontrol et\nexport async function hasPlanFeature(featureName: string): Promise<boolean> {\n  const plan = await getUserActivePlan()\n  return plan?.features?.[featureName] === true\n}\n\n// Kullanım istatistiklerini güncelle\nexport async function updateUsageStats(): Promise<void> {\n  try {\n    const { data: user } = await supabase.auth.getUser()\n    if (!user.user) {\n      throw new Error('Kullanıcı oturumu bulunamadı')\n    }\n\n    const { error } = await supabase.rpc('update_usage_stats', {\n      user_uuid: user.user.id\n    })\n\n    if (error) {\n      console.error('Kullanım istatistikleri güncellenemedi:', error)\n      throw new Error(error.message)\n    }\n  } catch (error) {\n    console.error('Kullanım istatistikleri güncelleme hatası:', error)\n    throw error\n  }\n}\n\n// Plan upgrade önerisi\nexport function getPlanUpgradeSuggestion(limits: PlanLimits): {\n  shouldUpgrade: boolean\n  reason: string\n  suggestedPlan: string\n} {\n  // Proje limiti %80'e ulaştıysa\n  if (limits.max_projects !== -1 && limits.current_projects / limits.max_projects >= 0.8) {\n    return {\n      shouldUpgrade: true,\n      reason: 'Proje limitinizin %80\\'ine ulaştınız',\n      suggestedPlan: 'professional'\n    }\n  }\n\n  // Prompt limiti %80'e ulaştıysa\n  if (limits.max_prompts_per_project !== -1 && limits.current_prompts / limits.max_prompts_per_project >= 0.8) {\n    return {\n      shouldUpgrade: true,\n      reason: 'Prompt limitinizin %80\\'ine ulaştınız',\n      suggestedPlan: 'professional'\n    }\n  }\n\n  return {\n    shouldUpgrade: false,\n    reason: '',\n    suggestedPlan: ''\n  }\n}\n\n// Plan limiti hata mesajları\nexport const PLAN_LIMIT_MESSAGES = {\n  PROJECT_LIMIT_REACHED: 'Proje oluşturma limitinize ulaştınız. Daha fazla proje oluşturmak için planınızı yükseltin.',\n  PROMPT_LIMIT_REACHED: 'Prompt oluşturma limitinize ulaştınız. Daha fazla prompt oluşturmak için planınızı yükseltin.',\n  FEATURE_NOT_AVAILABLE: 'Bu özellik mevcut planınızda bulunmamaktadır. Planınızı yükseltin.',\n  PLAN_EXPIRED: 'Planınızın süresi dolmuştur. Lütfen planınızı yenileyin.',\n  PLAN_SUSPENDED: 'Hesabınız askıya alınmıştır. Lütfen destek ekibi ile iletişime geçin.'\n}\n\n// Plan durumu kontrol et\nexport async function checkPlanStatus(): Promise<{\n  isActive: boolean\n  status: string\n  message?: string\n}> {\n  const plan = await getUserActivePlan()\n  \n  if (!plan) {\n    return {\n      isActive: false,\n      status: 'no_plan',\n      message: 'Aktif plan bulunamadı'\n    }\n  }\n\n  if (plan.status !== 'active') {\n    return {\n      isActive: false,\n      status: plan.status,\n      message: PLAN_LIMIT_MESSAGES.PLAN_SUSPENDED\n    }\n  }\n\n  if (plan.expires_at && new Date(plan.expires_at) < new Date()) {\n    return {\n      isActive: false,\n      status: 'expired',\n      message: PLAN_LIMIT_MESSAGES.PLAN_EXPIRED\n    }\n  }\n\n  return {\n    isActive: true,\n    status: 'active'\n  }\n}\n"], "names": [], "mappings": "AAAA,6DAA6D;;;;;;;;;;;;AAE7D;;AAuBO,eAAe;IACpB,IAAI;QACF,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;QAClD,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,qBAAqB;YAC9D,WAAW,KAAK,IAAI,CAAC,EAAE;QACzB;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;QAEA,OAAO,CAAA,iBAAA,2BAAA,IAAM,CAAC,EAAE,KAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;IACT;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;QAClD,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,wBAAwB;YACjE,WAAW,KAAK,IAAI,CAAC,EAAE;QACzB;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;QAEA,OAAO,CAAA,iBAAA,2BAAA,IAAM,CAAC,EAAE,KAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF;AAGO,eAAe;IACpB,MAAM,SAAS,MAAM;IAErB,IAAI,CAAC,QAAQ;QACX,OAAO;YAAE,SAAS;YAAO,QAAQ;QAA2B;IAC9D;IAEA,IAAI,CAAC,OAAO,kBAAkB,EAAE;QAC9B,OAAO;YACL,SAAS;YACT,QAAQ,AAAC,wBAAkD,OAA3B,OAAO,gBAAgB,EAAC,KAAuB,OAApB,OAAO,YAAY,EAAC;QACjF;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,eAAe,gBAAgB,SAAkB;IACtD,MAAM,SAAS,MAAM;IAErB,IAAI,CAAC,QAAQ;QACX,OAAO;YAAE,SAAS;YAAO,QAAQ;QAA2B;IAC9D;IAEA,IAAI,CAAC,OAAO,iBAAiB,EAAE;QAC7B,OAAO;YACL,SAAS;YACT,QAAQ,AAAC,yBAAkD,OAA1B,OAAO,eAAe,EAAC,KAAkC,OAA/B,OAAO,uBAAuB,EAAC;QAC5F;IACF;IAEA,wFAAwF;IACxF,IAAI,WAAW;QACb,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACpC,IAAI,CAAC,WACL,MAAM,CAAC,KAAK;gBAAE,OAAO;gBAAS,MAAM;YAAK,GACzC,EAAE,CAAC,cAAc;YAEpB,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,0CAA0C;gBACxD,OAAO;oBAAE,SAAS;oBAAO,QAAQ;gBAA4B;YAC/D;YAEA,MAAM,iBAAiB,SAAS;YAChC,IAAI,OAAO,uBAAuB,KAAK,CAAC,KAAK,kBAAkB,OAAO,uBAAuB,EAAE;gBAC7F,OAAO;oBACL,SAAS;oBACT,QAAQ,AAAC,uCAAwD,OAAlB,gBAAe,KAAkC,OAA/B,OAAO,uBAAuB,EAAC;gBAClG;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,OAAO;gBAAE,SAAS;gBAAO,QAAQ;YAAoC;QACvE;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,eAAe,eAAe,WAAmB;QAE/C;IADP,MAAM,OAAO,MAAM;IACnB,OAAO,CAAA,iBAAA,4BAAA,iBAAA,KAAM,QAAQ,cAAd,qCAAA,cAAgB,CAAC,YAAY,MAAK;AAC3C;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;QAClD,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,sBAAsB;YACzD,WAAW,KAAK,IAAI,CAAC,EAAE;QACzB;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2CAA2C;YACzD,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,MAAM;IACR;AACF;AAGO,SAAS,yBAAyB,MAAkB;IAKzD,+BAA+B;IAC/B,IAAI,OAAO,YAAY,KAAK,CAAC,KAAK,OAAO,gBAAgB,GAAG,OAAO,YAAY,IAAI,KAAK;QACtF,OAAO;YACL,eAAe;YACf,QAAQ;YACR,eAAe;QACjB;IACF;IAEA,gCAAgC;IAChC,IAAI,OAAO,uBAAuB,KAAK,CAAC,KAAK,OAAO,eAAe,GAAG,OAAO,uBAAuB,IAAI,KAAK;QAC3G,OAAO;YACL,eAAe;YACf,QAAQ;YACR,eAAe;QACjB;IACF;IAEA,OAAO;QACL,eAAe;QACf,QAAQ;QACR,eAAe;IACjB;AACF;AAGO,MAAM,sBAAsB;IACjC,uBAAuB;IACvB,sBAAsB;IACtB,uBAAuB;IACvB,cAAc;IACd,gBAAgB;AAClB;AAGO,eAAe;IAKpB,MAAM,OAAO,MAAM;IAEnB,IAAI,CAAC,MAAM;QACT,OAAO;YACL,UAAU;YACV,QAAQ;YACR,SAAS;QACX;IACF;IAEA,IAAI,KAAK,MAAM,KAAK,UAAU;QAC5B,OAAO;YACL,UAAU;YACV,QAAQ,KAAK,MAAM;YACnB,SAAS,oBAAoB,cAAc;QAC7C;IACF;IAEA,IAAI,KAAK,UAAU,IAAI,IAAI,KAAK,KAAK,UAAU,IAAI,IAAI,QAAQ;QAC7D,OAAO;YACL,UAAU;YACV,QAAQ;YACR,SAAS,oBAAoB,YAAY;QAC3C;IACF;IAEA,OAAO;QACL,UAAU;QACV,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 442, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/hooks/use-prompts.ts"], "sourcesContent": ["'use client'\r\n\r\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\r\nimport { Database } from '@/lib/supabase'\r\nimport { canCreatePrompt, updateUsageStats, PLAN_LIMIT_MESSAGES } from '@/lib/plan-limits'\r\n// Advanced validation will be integrated in future updates\r\n// import { checkRateLimit, RateLimitError } from '@/lib/rate-limiter'\r\n// import { AdvancedValidator, COMMON_VALIDATION_RULES } from '@/lib/advanced-validation'\r\n\r\ntype Prompt = Database['public']['Tables']['prompts']['Row']\r\ntype PromptInsert = Database['public']['Tables']['prompts']['Insert']\r\ntype PromptUpdate = Database['public']['Tables']['prompts']['Update']\r\n\r\n// Proje prompt'larını getir\r\nexport function usePrompts(projectId: string | null) {\r\n  return useQuery({\r\n    queryKey: ['prompts', projectId],\r\n    queryFn: async (): Promise<Prompt[]> => {\r\n      if (!projectId) {\r\n        console.log(`📝 [USE_PROMPTS] No project ID provided`)\r\n        return []\r\n      }\r\n\r\n      console.log(`📝 [USE_PROMPTS] Fetching prompts for project:`, projectId)\r\n\r\n      try {\r\n        // Check if we have a session first\r\n        const { data: { session }, error: sessionError } = await supabase.auth.getSession()\r\n        console.log(`📝 [USE_PROMPTS] Session check:`, {\r\n          hasSession: !!session,\r\n          sessionError: sessionError?.message,\r\n          userId: session?.user?.id\r\n        })\r\n\r\n        const { data, error } = await supabase\r\n          .from('prompts')\r\n          .select('*')\r\n          .eq('project_id', projectId)\r\n          .order('order_index', { ascending: true })\r\n\r\n        if (error) {\r\n          console.error(`❌ [USE_PROMPTS] Error fetching prompts:`, error)\r\n          throw new Error(error.message)\r\n        }\r\n\r\n        console.log(`✅ [USE_PROMPTS] Prompts fetched:`, data?.length || 0, 'prompts')\r\n        return data || []\r\n      } catch (err) {\r\n        console.error(`💥 [USE_PROMPTS] Exception:`, err)\r\n        throw err\r\n      }\r\n    },\r\n    enabled: !!projectId,\r\n  })\r\n}\r\n\r\n// Prompt oluştur (Optimized with Optimistic Updates)\r\nexport function useCreatePrompt() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async (prompt: Omit<PromptInsert, 'user_id'>): Promise<Prompt> => {\r\n      // Plan limiti kontrol et\r\n      const limitCheck = await canCreatePrompt(prompt.project_id)\r\n      if (!limitCheck.allowed) {\r\n        throw new Error(limitCheck.reason || PLAN_LIMIT_MESSAGES.PROMPT_LIMIT_REACHED)\r\n      }\r\n\r\n      const { data: { user }, error: userError } = await supabase.auth.getUser()\r\n\r\n      if (userError || !user) {\r\n        throw new Error('Kullanıcı oturumu bulunamadı')\r\n      }\r\n\r\n      // Task code otomatik oluştur\r\n      const taskCode = prompt.task_code || `task-${prompt.order_index}`\r\n\r\n      const { data, error } = await supabase\r\n        .from('prompts')\r\n        .insert({\r\n          ...prompt,\r\n          user_id: user.id,\r\n          task_code: taskCode,\r\n          tags: prompt.tags || [],\r\n          is_favorite: prompt.is_favorite || false,\r\n          usage_count: prompt.usage_count || 0,\r\n        })\r\n        .select()\r\n        .single()\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n\r\n      return data\r\n    },\r\n    onMutate: async (newPrompt) => {\r\n      // Cancel outgoing refetches\r\n      await queryClient.cancelQueries({ queryKey: ['prompts', newPrompt.project_id] })\r\n\r\n      // Snapshot the previous value\r\n      const previousPrompts = queryClient.getQueryData(['prompts', newPrompt.project_id])\r\n\r\n      // Get current user for optimistic update\r\n      const { data: { user } } = await supabase.auth.getUser()\r\n      if (!user) return { previousPrompts }\r\n\r\n      // Get current prompts to calculate proper order_index\r\n      const currentPrompts = queryClient.getQueryData(['prompts', newPrompt.project_id]) as any[] || []\r\n      const maxOrderIndex = currentPrompts.length > 0\r\n        ? Math.max(...currentPrompts.map(p => p.order_index || 0))\r\n        : 0\r\n      const nextOrderIndex = maxOrderIndex + 1\r\n\r\n      // Create optimistic prompt with unique ID and proper order\r\n      const optimisticId = `temp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`\r\n      const optimisticPrompt = {\r\n        id: optimisticId,\r\n        ...newPrompt,\r\n        user_id: user.id,\r\n        order_index: nextOrderIndex,\r\n        task_code: newPrompt.task_code || `task-${nextOrderIndex}`,\r\n        tags: newPrompt.tags || [],\r\n        is_favorite: newPrompt.is_favorite || false,\r\n        usage_count: 0,\r\n        created_at: new Date().toISOString(),\r\n        updated_at: new Date().toISOString(),\r\n      }\r\n\r\n      // Optimistically update the cache\r\n      queryClient.setQueryData(['prompts', newPrompt.project_id], (old: any) => {\r\n        if (!Array.isArray(old)) return [optimisticPrompt]\r\n        // Add to beginning for immediate visibility\r\n        return [optimisticPrompt, ...old]\r\n      })\r\n\r\n      return { previousPrompts, optimisticPrompt }\r\n    },\r\n    onError: (err, newPrompt, context) => {\r\n      // Rollback optimistic update on error\r\n      if (context?.previousPrompts) {\r\n        queryClient.setQueryData(['prompts', newPrompt.project_id], context.previousPrompts)\r\n      }\r\n    },\r\n    onSuccess: async (data, variables, context) => {\r\n      // Replace optimistic prompt with real data and ensure no duplicates\r\n      queryClient.setQueryData(['prompts', data.project_id], (old: any) => {\r\n        if (!Array.isArray(old)) return [data]\r\n\r\n        // Remove optimistic prompt and any potential duplicates\r\n        const filteredOld = old.filter((prompt: any) =>\r\n          prompt.id !== context?.optimisticPrompt?.id && prompt.id !== data.id\r\n        )\r\n\r\n        // Add real data at the beginning (newest first)\r\n        return [data, ...filteredOld]\r\n      })\r\n\r\n      // Batch invalidate related queries (non-blocking)\r\n      setTimeout(() => {\r\n        queryClient.invalidateQueries({ queryKey: ['popular-hashtags', data.project_id] })\r\n        queryClient.invalidateQueries({ queryKey: ['all-hashtags', data.project_id] })\r\n        queryClient.invalidateQueries({ queryKey: ['popular-categories', data.project_id] })\r\n        queryClient.invalidateQueries({ queryKey: ['all-categories', data.project_id] })\r\n\r\n        // Update usage stats in background\r\n        updateUsageStats().then(() => {\r\n          queryClient.invalidateQueries({ queryKey: ['user-limits'] })\r\n          queryClient.invalidateQueries({ queryKey: ['usage-stats'] })\r\n        }).catch(error => {\r\n          console.warn('Kullanım istatistikleri güncellenemedi:', error)\r\n        })\r\n      }, 100)\r\n    },\r\n  })\r\n}\r\n\r\n// Prompt güncelle\r\nexport function useUpdatePrompt() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async ({ id, ...updates }: PromptUpdate & { id: string }): Promise<Prompt> => {\r\n      const { data, error } = await supabase\r\n        .from('prompts')\r\n        .update({\r\n          ...updates,\r\n          updated_at: new Date().toISOString(),\r\n        })\r\n        .eq('id', id)\r\n        .select()\r\n        .single()\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n\r\n      return data\r\n    },\r\n    onSuccess: (data) => {\r\n      queryClient.invalidateQueries({ queryKey: ['prompts', data.project_id] })\r\n      // Invalidate hashtag and category queries if tags or category were updated\r\n      queryClient.invalidateQueries({ queryKey: ['popular-hashtags', data.project_id] })\r\n      queryClient.invalidateQueries({ queryKey: ['all-hashtags', data.project_id] })\r\n      queryClient.invalidateQueries({ queryKey: ['popular-categories', data.project_id] })\r\n      queryClient.invalidateQueries({ queryKey: ['all-categories', data.project_id] })\r\n    },\r\n  })\r\n}\r\n\r\n// Prompt sil\r\nexport function useDeletePrompt() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async ({ id }: { id: string; projectId: string }): Promise<void> => {\r\n      const { error } = await supabase\r\n        .from('prompts')\r\n        .delete()\r\n        .eq('id', id)\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n    },\r\n    onSuccess: (_, { projectId }) => {\r\n      queryClient.invalidateQueries({ queryKey: ['prompts', projectId] })\r\n    },\r\n  })\r\n}\r\n\r\n// Prompt'u kullanıldı olarak işaretle (optimistic update ile)\r\nexport function useMarkPromptAsUsed() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async (promptId: string): Promise<Prompt> => {\r\n      const { data, error } = await supabase\r\n        .from('prompts')\r\n        .update({ is_used: true })\r\n        .eq('id', promptId)\r\n        .select()\r\n        .single()\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n\r\n      return data\r\n    },\r\n    onMutate: async (promptId) => {\r\n      // Optimistic update\r\n      const queryKey = ['prompts']\r\n      await queryClient.cancelQueries({ queryKey })\r\n\r\n      const previousPrompts = queryClient.getQueriesData({ queryKey })\r\n\r\n      queryClient.setQueriesData({ queryKey }, (old: unknown) => {\r\n        if (!old || !Array.isArray(old)) return old\r\n        return old.map((prompt: Prompt) =>\r\n          prompt.id === promptId ? { ...prompt, is_used: true } : prompt\r\n        )\r\n      })\r\n\r\n      return { previousPrompts }\r\n    },\r\n    onError: (err, _promptId, context) => {\r\n      // Hata durumunda geri al\r\n      if (context?.previousPrompts) {\r\n        context.previousPrompts.forEach(([queryKey, data]) => {\r\n          queryClient.setQueryData(queryKey, data)\r\n        })\r\n      }\r\n    },\r\n    onSettled: (data) => {\r\n      // Her durumda cache'i yenile\r\n      if (data) {\r\n        queryClient.invalidateQueries({ queryKey: ['prompts', data.project_id] })\r\n      }\r\n    },\r\n  })\r\n}\r\n\r\n// Proje prompt'larını sıfırla\r\nexport function useResetPrompts() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async (projectId: string): Promise<void> => {\r\n      const { error } = await supabase\r\n        .from('prompts')\r\n        .update({ is_used: false })\r\n        .eq('project_id', projectId)\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n    },\r\n    onSuccess: (_, projectId) => {\r\n      queryClient.invalidateQueries({ queryKey: ['prompts', projectId] })\r\n    },\r\n  })\r\n}\r\n\r\n// Toplu prompt güncelleme (optimistic update ile)\r\nexport function useBulkUpdatePrompts() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async (updates: Array<{ id: string; order_index?: number; task_code?: string; is_used?: boolean }>): Promise<Prompt[]> => {\r\n      // Try to use RPC function for order updates, fallback to individual updates for other fields\r\n      const orderUpdates = updates.filter(u => u.order_index !== undefined || u.task_code !== undefined)\r\n      const otherUpdates = updates.filter(u => u.order_index === undefined && u.task_code === undefined)\r\n\r\n      const results: Prompt[] = []\r\n\r\n      // Use RPC function for order/task_code updates (more efficient)\r\n      if (orderUpdates.length > 0) {\r\n        try {\r\n          const { data: rpcData, error: rpcError } = await supabase.rpc('bulk_update_prompts_order', {\r\n            prompt_updates: orderUpdates\r\n          })\r\n\r\n          if (rpcError) {\r\n            console.warn('RPC function failed, falling back to individual updates:', rpcError)\r\n            // Fallback to individual updates\r\n            const fallbackPromises = orderUpdates.map(async (update) => {\r\n              const { data, error } = await supabase\r\n                .from('prompts')\r\n                .update({\r\n                  ...update,\r\n                  updated_at: new Date().toISOString(),\r\n                })\r\n                .eq('id', update.id)\r\n                .select()\r\n                .single()\r\n\r\n              if (error) {\r\n                throw new Error(`Failed to update prompt ${update.id}: ${error.message}`)\r\n              }\r\n              return data\r\n            })\r\n\r\n            const fallbackResults = await Promise.all(fallbackPromises)\r\n            results.push(...fallbackResults)\r\n          } else if (rpcData) {\r\n            // Get full prompt data for RPC results\r\n            const rpcIds = rpcData.map((r: { id: string }) => r.id)\r\n            const { data: fullPrompts, error: selectError } = await supabase\r\n              .from('prompts')\r\n              .select('*')\r\n              .in('id', rpcIds)\r\n\r\n            if (selectError) {\r\n              throw new Error(`Failed to fetch updated prompts: ${selectError.message}`)\r\n            }\r\n\r\n            results.push(...(fullPrompts || []))\r\n          }\r\n        } catch (error) {\r\n          console.error('Bulk update error:', error)\r\n          throw error\r\n        }\r\n      }\r\n\r\n      // Handle other updates (is_used, etc.) with individual calls\r\n      if (otherUpdates.length > 0) {\r\n        const updatePromises = otherUpdates.map(async (update) => {\r\n          const { data, error } = await supabase\r\n            .from('prompts')\r\n            .update({\r\n              ...update,\r\n              updated_at: new Date().toISOString(),\r\n            })\r\n            .eq('id', update.id)\r\n            .select()\r\n            .single()\r\n\r\n          if (error) {\r\n            throw new Error(`Failed to update prompt ${update.id}: ${error.message}`)\r\n          }\r\n\r\n          return data\r\n        })\r\n\r\n        const updatedPrompts = await Promise.all(updatePromises)\r\n        results.push(...updatedPrompts)\r\n      }\r\n\r\n      return results\r\n    },\r\n    onMutate: async (updates) => {\r\n      // Optimistic update\r\n      const queryKey = ['prompts']\r\n      await queryClient.cancelQueries({ queryKey })\r\n\r\n      const previousPrompts = queryClient.getQueriesData({ queryKey })\r\n\r\n      queryClient.setQueriesData({ queryKey }, (old: unknown) => {\r\n        if (!old || !Array.isArray(old)) return old\r\n\r\n        return old.map((prompt: Prompt) => {\r\n          const update = updates.find(u => u.id === prompt.id)\r\n          return update ? { ...prompt, ...update } : prompt\r\n        })\r\n      })\r\n\r\n      return { previousPrompts }\r\n    },\r\n    onError: (err, _updates, context) => {\r\n      // Hata durumunda geri al\r\n      if (context?.previousPrompts) {\r\n        context.previousPrompts.forEach(([queryKey, data]) => {\r\n          queryClient.setQueryData(queryKey, data)\r\n        })\r\n      }\r\n    },\r\n    onSettled: (data) => {\r\n      // Her durumda cache'i yenile\r\n      if (data && data.length > 0) {\r\n        queryClient.invalidateQueries({ queryKey: ['prompts', data[0].project_id] })\r\n      }\r\n    },\r\n  })\r\n}"], "names": [], "mappings": ";;;;;;;;;AAEA;AAAA;AAAA;AACA;AAEA;;AALA;;;;AAeO,SAAS,WAAW,SAAwB;;IACjD,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;SAAU;QAChC,OAAO;mCAAE;gBACP,IAAI,CAAC,WAAW;oBACd,QAAQ,GAAG,CAAE;oBACb,OAAO,EAAE;gBACX;gBAEA,QAAQ,GAAG,CAAE,kDAAiD;gBAE9D,IAAI;wBAMQ;oBALV,mCAAmC;oBACnC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,UAAU;oBACjF,QAAQ,GAAG,CAAE,mCAAkC;wBAC7C,YAAY,CAAC,CAAC;wBACd,YAAY,EAAE,yBAAA,mCAAA,aAAc,OAAO;wBACnC,MAAM,EAAE,oBAAA,+BAAA,gBAAA,QAAS,IAAI,cAAb,oCAAA,cAAe,EAAE;oBAC3B;oBAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,cAAc,WACjB,KAAK,CAAC,eAAe;wBAAE,WAAW;oBAAK;oBAE1C,IAAI,OAAO;wBACT,QAAQ,KAAK,CAAE,2CAA0C;wBACzD,MAAM,IAAI,MAAM,MAAM,OAAO;oBAC/B;oBAEA,QAAQ,GAAG,CAAE,oCAAmC,CAAA,iBAAA,2BAAA,KAAM,MAAM,KAAI,GAAG;oBACnE,OAAO,QAAQ,EAAE;gBACnB,EAAE,OAAO,KAAK;oBACZ,QAAQ,KAAK,CAAE,+BAA8B;oBAC7C,MAAM;gBACR;YACF;;QACA,SAAS,CAAC,CAAC;IACb;AACF;GAxCgB;;QACP,8KAAA,CAAA,WAAQ;;;AA0CV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;2CAAE,OAAO;gBACjB,yBAAyB;gBACzB,MAAM,aAAa,MAAM,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,UAAU;gBAC1D,IAAI,CAAC,WAAW,OAAO,EAAE;oBACvB,MAAM,IAAI,MAAM,WAAW,MAAM,IAAI,+HAAA,CAAA,sBAAmB,CAAC,oBAAoB;gBAC/E;gBAEA,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;gBAExE,IAAI,aAAa,CAAC,MAAM;oBACtB,MAAM,IAAI,MAAM;gBAClB;gBAEA,6BAA6B;gBAC7B,MAAM,WAAW,OAAO,SAAS,IAAI,AAAC,QAA0B,OAAnB,OAAO,WAAW;gBAE/D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC;oBACN,GAAG,MAAM;oBACT,SAAS,KAAK,EAAE;oBAChB,WAAW;oBACX,MAAM,OAAO,IAAI,IAAI,EAAE;oBACvB,aAAa,OAAO,WAAW,IAAI;oBACnC,aAAa,OAAO,WAAW,IAAI;gBACrC,GACC,MAAM,GACN,MAAM;gBAET,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,OAAO;YACT;;QACA,QAAQ;2CAAE,OAAO;gBACf,4BAA4B;gBAC5B,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU;wBAAC;wBAAW,UAAU,UAAU;qBAAC;gBAAC;gBAE9E,8BAA8B;gBAC9B,MAAM,kBAAkB,YAAY,YAAY,CAAC;oBAAC;oBAAW,UAAU,UAAU;iBAAC;gBAElF,yCAAyC;gBACzC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;gBACtD,IAAI,CAAC,MAAM,OAAO;oBAAE;gBAAgB;gBAEpC,sDAAsD;gBACtD,MAAM,iBAAiB,YAAY,YAAY,CAAC;oBAAC;oBAAW,UAAU,UAAU;iBAAC,KAAc,EAAE;gBACjG,MAAM,gBAAgB,eAAe,MAAM,GAAG,IAC1C,KAAK,GAAG,IAAI,eAAe,GAAG;mDAAC,CAAA,IAAK,EAAE,WAAW,IAAI;qDACrD;gBACJ,MAAM,iBAAiB,gBAAgB;gBAEvC,2DAA2D;gBAC3D,MAAM,eAAe,AAAC,QAAqB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;gBAChF,MAAM,mBAAmB;oBACvB,IAAI;oBACJ,GAAG,SAAS;oBACZ,SAAS,KAAK,EAAE;oBAChB,aAAa;oBACb,WAAW,UAAU,SAAS,IAAI,AAAC,QAAsB,OAAf;oBAC1C,MAAM,UAAU,IAAI,IAAI,EAAE;oBAC1B,aAAa,UAAU,WAAW,IAAI;oBACtC,aAAa;oBACb,YAAY,IAAI,OAAO,WAAW;oBAClC,YAAY,IAAI,OAAO,WAAW;gBACpC;gBAEA,kCAAkC;gBAClC,YAAY,YAAY,CAAC;oBAAC;oBAAW,UAAU,UAAU;iBAAC;mDAAE,CAAC;wBAC3D,IAAI,CAAC,MAAM,OAAO,CAAC,MAAM,OAAO;4BAAC;yBAAiB;wBAClD,4CAA4C;wBAC5C,OAAO;4BAAC;+BAAqB;yBAAI;oBACnC;;gBAEA,OAAO;oBAAE;oBAAiB;gBAAiB;YAC7C;;QACA,OAAO;2CAAE,CAAC,KAAK,WAAW;gBACxB,sCAAsC;gBACtC,IAAI,oBAAA,8BAAA,QAAS,eAAe,EAAE;oBAC5B,YAAY,YAAY,CAAC;wBAAC;wBAAW,UAAU,UAAU;qBAAC,EAAE,QAAQ,eAAe;gBACrF;YACF;;QACA,SAAS;2CAAE,OAAO,MAAM,WAAW;gBACjC,oEAAoE;gBACpE,YAAY,YAAY,CAAC;oBAAC;oBAAW,KAAK,UAAU;iBAAC;mDAAE,CAAC;wBACtD,IAAI,CAAC,MAAM,OAAO,CAAC,MAAM,OAAO;4BAAC;yBAAK;wBAEtC,wDAAwD;wBACxD,MAAM,cAAc,IAAI,MAAM;uEAAC,CAAC;oCAChB;uCAAd,OAAO,EAAE,MAAK,oBAAA,+BAAA,4BAAA,QAAS,gBAAgB,cAAzB,gDAAA,0BAA2B,EAAE,KAAI,OAAO,EAAE,KAAK,KAAK,EAAE;;;wBAGtE,gDAAgD;wBAChD,OAAO;4BAAC;+BAAS;yBAAY;oBAC/B;;gBAEA,kDAAkD;gBAClD;mDAAW;wBACT,YAAY,iBAAiB,CAAC;4BAAE,UAAU;gCAAC;gCAAoB,KAAK,UAAU;6BAAC;wBAAC;wBAChF,YAAY,iBAAiB,CAAC;4BAAE,UAAU;gCAAC;gCAAgB,KAAK,UAAU;6BAAC;wBAAC;wBAC5E,YAAY,iBAAiB,CAAC;4BAAE,UAAU;gCAAC;gCAAsB,KAAK,UAAU;6BAAC;wBAAC;wBAClF,YAAY,iBAAiB,CAAC;4BAAE,UAAU;gCAAC;gCAAkB,KAAK,UAAU;6BAAC;wBAAC;wBAE9E,mCAAmC;wBACnC,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,IAAI,IAAI;2DAAC;gCACtB,YAAY,iBAAiB,CAAC;oCAAE,UAAU;wCAAC;qCAAc;gCAAC;gCAC1D,YAAY,iBAAiB,CAAC;oCAAE,UAAU;wCAAC;qCAAc;gCAAC;4BAC5D;0DAAG,KAAK;2DAAC,CAAA;gCACP,QAAQ,IAAI,CAAC,2CAA2C;4BAC1D;;oBACF;kDAAG;YACL;;IACF;AACF;IAtHgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAsHb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;2CAAE;oBAAO,EAAE,EAAE,EAAE,GAAG,SAAwC;gBAClE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC;oBACN,GAAG,OAAO;oBACV,YAAY,IAAI,OAAO,WAAW;gBACpC,GACC,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;gBAET,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,OAAO;YACT;;QACA,SAAS;2CAAE,CAAC;gBACV,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAW,KAAK,UAAU;qBAAC;gBAAC;gBACvE,2EAA2E;gBAC3E,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAoB,KAAK,UAAU;qBAAC;gBAAC;gBAChF,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAgB,KAAK,UAAU;qBAAC;gBAAC;gBAC5E,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAsB,KAAK,UAAU;qBAAC;gBAAC;gBAClF,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAkB,KAAK,UAAU;qBAAC;gBAAC;YAChF;;IACF;AACF;IA9BgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AA8Bb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;2CAAE;oBAAO,EAAE,EAAE,EAAqC;gBAC1D,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAC7B,IAAI,CAAC,WACL,MAAM,GACN,EAAE,CAAC,MAAM;gBAEZ,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;YACF;;QACA,SAAS;2CAAE,CAAC;oBAAG,EAAE,SAAS,EAAE;gBAC1B,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAW;qBAAU;gBAAC;YACnE;;IACF;AACF;IAlBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAkBb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;+CAAE,OAAO;gBACjB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC;oBAAE,SAAS;gBAAK,GACvB,EAAE,CAAC,MAAM,UACT,MAAM,GACN,MAAM;gBAET,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,OAAO;YACT;;QACA,QAAQ;+CAAE,OAAO;gBACf,oBAAoB;gBACpB,MAAM,WAAW;oBAAC;iBAAU;gBAC5B,MAAM,YAAY,aAAa,CAAC;oBAAE;gBAAS;gBAE3C,MAAM,kBAAkB,YAAY,cAAc,CAAC;oBAAE;gBAAS;gBAE9D,YAAY,cAAc,CAAC;oBAAE;gBAAS;uDAAG,CAAC;wBACxC,IAAI,CAAC,OAAO,CAAC,MAAM,OAAO,CAAC,MAAM,OAAO;wBACxC,OAAO,IAAI,GAAG;+DAAC,CAAC,SACd,OAAO,EAAE,KAAK,WAAW;oCAAE,GAAG,MAAM;oCAAE,SAAS;gCAAK,IAAI;;oBAE5D;;gBAEA,OAAO;oBAAE;gBAAgB;YAC3B;;QACA,OAAO;+CAAE,CAAC,KAAK,WAAW;gBACxB,yBAAyB;gBACzB,IAAI,oBAAA,8BAAA,QAAS,eAAe,EAAE;oBAC5B,QAAQ,eAAe,CAAC,OAAO;2DAAC;gCAAC,CAAC,UAAU,KAAK;4BAC/C,YAAY,YAAY,CAAC,UAAU;wBACrC;;gBACF;YACF;;QACA,SAAS;+CAAE,CAAC;gBACV,6BAA6B;gBAC7B,IAAI,MAAM;oBACR,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;4BAAW,KAAK,UAAU;yBAAC;oBAAC;gBACzE;YACF;;IACF;AACF;IAjDgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAiDb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;2CAAE,OAAO;gBACjB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAC7B,IAAI,CAAC,WACL,MAAM,CAAC;oBAAE,SAAS;gBAAM,GACxB,EAAE,CAAC,cAAc;gBAEpB,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;YACF;;QACA,SAAS;2CAAE,CAAC,GAAG;gBACb,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAW;qBAAU;gBAAC;YACnE;;IACF;AACF;IAlBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAkBb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;gDAAE,OAAO;gBACjB,6FAA6F;gBAC7F,MAAM,eAAe,QAAQ,MAAM;qEAAC,CAAA,IAAK,EAAE,WAAW,KAAK,aAAa,EAAE,SAAS,KAAK;;gBACxF,MAAM,eAAe,QAAQ,MAAM;qEAAC,CAAA,IAAK,EAAE,WAAW,KAAK,aAAa,EAAE,SAAS,KAAK;;gBAExF,MAAM,UAAoB,EAAE;gBAE5B,gEAAgE;gBAChE,IAAI,aAAa,MAAM,GAAG,GAAG;oBAC3B,IAAI;wBACF,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,QAAQ,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,6BAA6B;4BACzF,gBAAgB;wBAClB;wBAEA,IAAI,UAAU;4BACZ,QAAQ,IAAI,CAAC,4DAA4D;4BACzE,iCAAiC;4BACjC,MAAM,mBAAmB,aAAa,GAAG;qFAAC,OAAO;oCAC/C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC;wCACN,GAAG,MAAM;wCACT,YAAY,IAAI,OAAO,WAAW;oCACpC,GACC,EAAE,CAAC,MAAM,OAAO,EAAE,EAClB,MAAM,GACN,MAAM;oCAET,IAAI,OAAO;wCACT,MAAM,IAAI,MAAM,AAAC,2BAAwC,OAAd,OAAO,EAAE,EAAC,MAAkB,OAAd,MAAM,OAAO;oCACxE;oCACA,OAAO;gCACT;;4BAEA,MAAM,kBAAkB,MAAM,QAAQ,GAAG,CAAC;4BAC1C,QAAQ,IAAI,IAAI;wBAClB,OAAO,IAAI,SAAS;4BAClB,uCAAuC;4BACvC,MAAM,SAAS,QAAQ,GAAG;2EAAC,CAAC,IAAsB,EAAE,EAAE;;4BACtD,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAC7D,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM;4BAEZ,IAAI,aAAa;gCACf,MAAM,IAAI,MAAM,AAAC,oCAAuD,OAApB,YAAY,OAAO;4BACzE;4BAEA,QAAQ,IAAI,IAAK,eAAe,EAAE;wBACpC;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,sBAAsB;wBACpC,MAAM;oBACR;gBACF;gBAEA,6DAA6D;gBAC7D,IAAI,aAAa,MAAM,GAAG,GAAG;oBAC3B,MAAM,iBAAiB,aAAa,GAAG;2EAAC,OAAO;4BAC7C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC;gCACN,GAAG,MAAM;gCACT,YAAY,IAAI,OAAO,WAAW;4BACpC,GACC,EAAE,CAAC,MAAM,OAAO,EAAE,EAClB,MAAM,GACN,MAAM;4BAET,IAAI,OAAO;gCACT,MAAM,IAAI,MAAM,AAAC,2BAAwC,OAAd,OAAO,EAAE,EAAC,MAAkB,OAAd,MAAM,OAAO;4BACxE;4BAEA,OAAO;wBACT;;oBAEA,MAAM,iBAAiB,MAAM,QAAQ,GAAG,CAAC;oBACzC,QAAQ,IAAI,IAAI;gBAClB;gBAEA,OAAO;YACT;;QACA,QAAQ;gDAAE,OAAO;gBACf,oBAAoB;gBACpB,MAAM,WAAW;oBAAC;iBAAU;gBAC5B,MAAM,YAAY,aAAa,CAAC;oBAAE;gBAAS;gBAE3C,MAAM,kBAAkB,YAAY,cAAc,CAAC;oBAAE;gBAAS;gBAE9D,YAAY,cAAc,CAAC;oBAAE;gBAAS;wDAAG,CAAC;wBACxC,IAAI,CAAC,OAAO,CAAC,MAAM,OAAO,CAAC,MAAM,OAAO;wBAExC,OAAO,IAAI,GAAG;gEAAC,CAAC;gCACd,MAAM,SAAS,QAAQ,IAAI;+EAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,EAAE;;gCACnD,OAAO,SAAS;oCAAE,GAAG,MAAM;oCAAE,GAAG,MAAM;gCAAC,IAAI;4BAC7C;;oBACF;;gBAEA,OAAO;oBAAE;gBAAgB;YAC3B;;QACA,OAAO;gDAAE,CAAC,KAAK,UAAU;gBACvB,yBAAyB;gBACzB,IAAI,oBAAA,8BAAA,QAAS,eAAe,EAAE;oBAC5B,QAAQ,eAAe,CAAC,OAAO;4DAAC;gCAAC,CAAC,UAAU,KAAK;4BAC/C,YAAY,YAAY,CAAC,UAAU;wBACrC;;gBACF;YACF;;QACA,SAAS;gDAAE,CAAC;gBACV,6BAA6B;gBAC7B,IAAI,QAAQ,KAAK,MAAM,GAAG,GAAG;oBAC3B,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;4BAAW,IAAI,CAAC,EAAE,CAAC,UAAU;yBAAC;oBAAC;gBAC5E;YACF;;IACF;AACF;IAvHgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 1050, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/project-validation.ts"], "sourcesContent": ["/**\n * Proje adı validation ve güvenlik utilities\n * Güvenlik odaklı input validation ve sanitization\n */\n\n// Proje adı limitleri ve kuralları\nexport const PROJECT_NAME_RULES = {\n  minLength: 3,\n  maxLength: 50,\n  allowedCharsRegex: /^[a-zA-Z0-9\\s\\-_.]+$/,\n  debounceMs: 300,\n  rateLimit: {\n    maxRequests: 10,\n    windowMinutes: 1\n  }\n} as const;\n\n// Validation sonuç tipi\nexport interface ValidationResult {\n  isValid: boolean;\n  error?: string;\n  sanitizedValue?: string;\n}\n\n// Rate limiting için local storage key\nconst RATE_LIMIT_KEY = 'project_name_update_rate_limit';\n\n/**\n * Proje adını sanitize eder\n */\nexport function sanitizeProjectName(name: string): string {\n  if (!name) return '';\n  \n  // Trim ve normalize\n  let sanitized = name.trim();\n  \n  // Çoklu boşlukları tek boşluğa çevir\n  sanitized = sanitized.replace(/\\s+/g, ' ');\n  \n  // Başlangıç ve bitiş boşluklarını kaldır\n  sanitized = sanitized.trim();\n  \n  return sanitized;\n}\n\n/**\n * Proje adını validate eder\n */\nexport function validateProjectName(name: string): ValidationResult {\n  const sanitized = sanitizeProjectName(name);\n  \n  // Boş kontrol\n  if (!sanitized) {\n    return {\n      isValid: false,\n      error: 'Proje adı boş olamaz'\n    };\n  }\n  \n  // Uzunluk kontrol\n  if (sanitized.length < PROJECT_NAME_RULES.minLength) {\n    return {\n      isValid: false,\n      error: `Proje adı en az ${PROJECT_NAME_RULES.minLength} karakter olmalıdır`\n    };\n  }\n  \n  if (sanitized.length > PROJECT_NAME_RULES.maxLength) {\n    return {\n      isValid: false,\n      error: `Proje adı en fazla ${PROJECT_NAME_RULES.maxLength} karakter olabilir`\n    };\n  }\n  \n  // Karakter kontrol\n  if (!PROJECT_NAME_RULES.allowedCharsRegex.test(sanitized)) {\n    return {\n      isValid: false,\n      error: 'Proje adı sadece harf, rakam, boşluk ve -_. karakterlerini içerebilir'\n    };\n  }\n  \n  // Sadece boşluk kontrolü\n  if (sanitized.replace(/\\s/g, '').length === 0) {\n    return {\n      isValid: false,\n      error: 'Proje adı sadece boşluk karakterlerinden oluşamaz'\n    };\n  }\n  \n  return {\n    isValid: true,\n    sanitizedValue: sanitized\n  };\n}\n\n/**\n * XSS koruması için HTML encode\n */\nexport function escapeHtml(text: string): string {\n  const div = document.createElement('div');\n  div.textContent = text;\n  return div.innerHTML;\n}\n\n/**\n * Client-side rate limiting kontrolü\n */\nexport function checkClientRateLimit(): boolean {\n  try {\n    const stored = localStorage.getItem(RATE_LIMIT_KEY);\n    const now = Date.now();\n    \n    if (!stored) {\n      // İlk istek\n      localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify({\n        count: 1,\n        windowStart: now\n      }));\n      return true;\n    }\n    \n    const data = JSON.parse(stored);\n    const windowDuration = PROJECT_NAME_RULES.rateLimit.windowMinutes * 60 * 1000; // ms\n    \n    // Pencere süresi dolmuş mu?\n    if (now - data.windowStart > windowDuration) {\n      // Yeni pencere başlat\n      localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify({\n        count: 1,\n        windowStart: now\n      }));\n      return true;\n    }\n    \n    // Limit kontrolü\n    if (data.count >= PROJECT_NAME_RULES.rateLimit.maxRequests) {\n      return false;\n    }\n    \n    // Sayacı artır\n    localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify({\n      count: data.count + 1,\n      windowStart: data.windowStart\n    }));\n    \n    return true;\n  } catch (error) {\n    console.warn('Rate limit check failed:', error);\n    return true; // Hata durumunda izin ver\n  }\n}\n\n/**\n * Rate limit reset\n */\nexport function resetClientRateLimit(): void {\n  try {\n    localStorage.removeItem(RATE_LIMIT_KEY);\n  } catch (error) {\n    console.warn('Rate limit reset failed:', error);\n  }\n}\n\n/**\n * Debounced validation hook için utility\n */\nexport function createDebouncedValidator(\n  validator: (value: string) => ValidationResult,\n  delay: number = PROJECT_NAME_RULES.debounceMs\n) {\n  let timeoutId: NodeJS.Timeout;\n  \n  return (value: string, callback: (result: ValidationResult) => void) => {\n    clearTimeout(timeoutId);\n    timeoutId = setTimeout(() => {\n      const result = validator(value);\n      callback(result);\n    }, delay);\n  };\n}\n\n/**\n * Güvenli proje adı karşılaştırması\n */\nexport function isSameProjectName(name1: string, name2: string): boolean {\n  const sanitized1 = sanitizeProjectName(name1).toLowerCase();\n  const sanitized2 = sanitizeProjectName(name2).toLowerCase();\n  return sanitized1 === sanitized2;\n}\n\n/**\n * Duplicate name kontrolü için async validation\n */\nexport async function validateProjectNameUnique(\n  name: string,\n  currentProjectId: string,\n  projects: Array<{ id: string; name: string }>\n): Promise<ValidationResult> {\n  const sanitized = sanitizeProjectName(name);\n\n  // Önce temel validation\n  const basicValidation = validateProjectName(name);\n  if (!basicValidation.isValid) {\n    return basicValidation;\n  }\n\n  // Duplicate kontrolü (case-insensitive)\n  const isDuplicate = projects.some(project =>\n    project.id !== currentProjectId &&\n    isSameProjectName(project.name, sanitized)\n  );\n\n  if (isDuplicate) {\n    return {\n      isValid: false,\n      error: 'Bu isimde bir proje zaten mevcut'\n    };\n  }\n\n  return {\n    isValid: true,\n    sanitizedValue: sanitized\n  };\n}\n\n/**\n * Error mesajlarını kullanıcı dostu hale getir\n */\nexport function formatValidationError(error: string): string {\n  // Teknik hataları kullanıcı dostu mesajlara çevir\n  const errorMap: Record<string, string> = {\n    'unique_violation': 'Bu isimde bir proje zaten mevcut',\n    'check_violation': 'Proje adı geçersiz karakterler içeriyor',\n    'not_null_violation': 'Proje adı boş olamaz',\n    'foreign_key_violation': 'Geçersiz proje referansı',\n    'RateLimitExceeded': 'Çok fazla güncelleme isteği. Lütfen bekleyin.',\n    'InvalidInput': 'Geçersiz proje adı',\n    'DuplicateName': 'Bu isimde bir proje zaten mevcut',\n    'NotFound': 'Proje bulunamadı',\n    'Unauthorized': 'Bu işlem için yetkiniz yok'\n  };\n\n  return errorMap[error] || error;\n}\n\n/**\n * Advanced debounced validator with duplicate check\n */\nexport function createAdvancedDebouncedValidator(\n  projects: Array<{ id: string; name: string }>,\n  currentProjectId: string,\n  delay: number = PROJECT_NAME_RULES.debounceMs\n) {\n  let timeoutId: NodeJS.Timeout;\n\n  return (value: string, callback: (result: ValidationResult) => void) => {\n    clearTimeout(timeoutId);\n    timeoutId = setTimeout(async () => {\n      const result = await validateProjectNameUnique(value, currentProjectId, projects);\n      callback(result);\n    }, delay);\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,mCAAmC;;;;;;;;;;;;;;AAC5B,MAAM,qBAAqB;IAChC,WAAW;IACX,WAAW;IACX,mBAAmB;IACnB,YAAY;IACZ,WAAW;QACT,aAAa;QACb,eAAe;IACjB;AACF;AASA,uCAAuC;AACvC,MAAM,iBAAiB;AAKhB,SAAS,oBAAoB,IAAY;IAC9C,IAAI,CAAC,MAAM,OAAO;IAElB,oBAAoB;IACpB,IAAI,YAAY,KAAK,IAAI;IAEzB,qCAAqC;IACrC,YAAY,UAAU,OAAO,CAAC,QAAQ;IAEtC,yCAAyC;IACzC,YAAY,UAAU,IAAI;IAE1B,OAAO;AACT;AAKO,SAAS,oBAAoB,IAAY;IAC9C,MAAM,YAAY,oBAAoB;IAEtC,cAAc;IACd,IAAI,CAAC,WAAW;QACd,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,kBAAkB;IAClB,IAAI,UAAU,MAAM,GAAG,mBAAmB,SAAS,EAAE;QACnD,OAAO;YACL,SAAS;YACT,OAAO,AAAC,mBAA+C,OAA7B,mBAAmB,SAAS,EAAC;QACzD;IACF;IAEA,IAAI,UAAU,MAAM,GAAG,mBAAmB,SAAS,EAAE;QACnD,OAAO;YACL,SAAS;YACT,OAAO,AAAC,sBAAkD,OAA7B,mBAAmB,SAAS,EAAC;QAC5D;IACF;IAEA,mBAAmB;IACnB,IAAI,CAAC,mBAAmB,iBAAiB,CAAC,IAAI,CAAC,YAAY;QACzD,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,yBAAyB;IACzB,IAAI,UAAU,OAAO,CAAC,OAAO,IAAI,MAAM,KAAK,GAAG;QAC7C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,OAAO;QACL,SAAS;QACT,gBAAgB;IAClB;AACF;AAKO,SAAS,WAAW,IAAY;IACrC,MAAM,MAAM,SAAS,aAAa,CAAC;IACnC,IAAI,WAAW,GAAG;IAClB,OAAO,IAAI,SAAS;AACtB;AAKO,SAAS;IACd,IAAI;QACF,MAAM,SAAS,aAAa,OAAO,CAAC;QACpC,MAAM,MAAM,KAAK,GAAG;QAEpB,IAAI,CAAC,QAAQ;YACX,YAAY;YACZ,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;gBAClD,OAAO;gBACP,aAAa;YACf;YACA,OAAO;QACT;QAEA,MAAM,OAAO,KAAK,KAAK,CAAC;QACxB,MAAM,iBAAiB,mBAAmB,SAAS,CAAC,aAAa,GAAG,KAAK,MAAM,KAAK;QAEpF,4BAA4B;QAC5B,IAAI,MAAM,KAAK,WAAW,GAAG,gBAAgB;YAC3C,sBAAsB;YACtB,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;gBAClD,OAAO;gBACP,aAAa;YACf;YACA,OAAO;QACT;QAEA,iBAAiB;QACjB,IAAI,KAAK,KAAK,IAAI,mBAAmB,SAAS,CAAC,WAAW,EAAE;YAC1D,OAAO;QACT;QAEA,eAAe;QACf,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;YAClD,OAAO,KAAK,KAAK,GAAG;YACpB,aAAa,KAAK,WAAW;QAC/B;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,4BAA4B;QACzC,OAAO,MAAM,0BAA0B;IACzC;AACF;AAKO,SAAS;IACd,IAAI;QACF,aAAa,UAAU,CAAC;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,4BAA4B;IAC3C;AACF;AAKO,SAAS,yBACd,SAA8C;QAC9C,QAAA,iEAAgB,mBAAmB,UAAU;IAE7C,IAAI;IAEJ,OAAO,CAAC,OAAe;QACrB,aAAa;QACb,YAAY,WAAW;YACrB,MAAM,SAAS,UAAU;YACzB,SAAS;QACX,GAAG;IACL;AACF;AAKO,SAAS,kBAAkB,KAAa,EAAE,KAAa;IAC5D,MAAM,aAAa,oBAAoB,OAAO,WAAW;IACzD,MAAM,aAAa,oBAAoB,OAAO,WAAW;IACzD,OAAO,eAAe;AACxB;AAKO,eAAe,0BACpB,IAAY,EACZ,gBAAwB,EACxB,QAA6C;IAE7C,MAAM,YAAY,oBAAoB;IAEtC,wBAAwB;IACxB,MAAM,kBAAkB,oBAAoB;IAC5C,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAC5B,OAAO;IACT;IAEA,wCAAwC;IACxC,MAAM,cAAc,SAAS,IAAI,CAAC,CAAA,UAChC,QAAQ,EAAE,KAAK,oBACf,kBAAkB,QAAQ,IAAI,EAAE;IAGlC,IAAI,aAAa;QACf,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,OAAO;QACL,SAAS;QACT,gBAAgB;IAClB;AACF;AAKO,SAAS,sBAAsB,KAAa;IACjD,kDAAkD;IAClD,MAAM,WAAmC;QACvC,oBAAoB;QACpB,mBAAmB;QACnB,sBAAsB;QACtB,yBAAyB;QACzB,qBAAqB;QACrB,gBAAgB;QAChB,iBAAiB;QACjB,YAAY;QACZ,gBAAgB;IAClB;IAEA,OAAO,QAAQ,CAAC,MAAM,IAAI;AAC5B;AAKO,SAAS,iCACd,QAA6C,EAC7C,gBAAwB;QACxB,QAAA,iEAAgB,mBAAmB,UAAU;IAE7C,IAAI;IAEJ,OAAO,CAAC,OAAe;QACrB,aAAa;QACb,YAAY,WAAW;YACrB,MAAM,SAAS,MAAM,0BAA0B,OAAO,kBAAkB;YACxE,SAAS;QACX,GAAG;IACL;AACF", "debugId": null}}, {"offset": {"line": 1251, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/rate-limiter.ts"], "sourcesContent": ["'use client'\n\n/**\n * Advanced Rate Limiting System for PromptFlow\n * Provides both client-side and server-side rate limiting\n */\n\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\n\n// Rate limiting configurations for different actions\nexport const RATE_LIMITS = {\n  // Authentication actions\n  login: { maxRequests: 5, windowMinutes: 15 },\n  signup: { maxRequests: 3, windowMinutes: 60 },\n  password_reset: { maxRequests: 3, windowMinutes: 60 },\n  \n  // Project actions\n  project_create: { maxRequests: 10, windowMinutes: 60 },\n  project_update: { maxRequests: 20, windowMinutes: 10 },\n  project_delete: { maxRequests: 5, windowMinutes: 60 },\n  \n  // Prompt actions\n  prompt_create: { maxRequests: 50, windowMinutes: 10 },\n  prompt_update: { maxRequests: 100, windowMinutes: 10 },\n  prompt_delete: { maxRequests: 20, windowMinutes: 10 },\n  \n  // Context actions\n  context_create: { maxRequests: 20, windowMinutes: 10 },\n  context_batch_add: { maxRequests: 5, windowMinutes: 10 },\n  \n  // Plan actions\n  plan_change: { maxRequests: 3, windowMinutes: 60 },\n  \n  // General API\n  api_general: { maxRequests: 200, windowMinutes: 10 },\n} as const\n\nexport type RateLimitAction = keyof typeof RATE_LIMITS\n\ninterface RateLimitData {\n  count: number\n  windowStart: number\n  lastRequest: number\n}\n\n/**\n * Client-side rate limiting with localStorage\n */\nexport class ClientRateLimiter {\n  private getStorageKey(action: RateLimitAction, userId?: string): string {\n    const userSuffix = userId ? `_${userId}` : ''\n    return `rate_limit_${action}${userSuffix}`\n  }\n\n  /**\n   * Check if action is allowed based on rate limits\n   */\n  async checkLimit(action: RateLimitAction, userId?: string): Promise<{\n    allowed: boolean\n    remainingRequests: number\n    resetTime: number\n    retryAfter?: number\n  }> {\n    try {\n      const config = RATE_LIMITS[action]\n      const storageKey = this.getStorageKey(action, userId)\n      const now = Date.now()\n      const windowMs = config.windowMinutes * 60 * 1000\n\n      // Get current data\n      const stored = localStorage.getItem(storageKey)\n      let data: RateLimitData\n\n      if (!stored) {\n        // First request\n        data = {\n          count: 1,\n          windowStart: now,\n          lastRequest: now\n        }\n        localStorage.setItem(storageKey, JSON.stringify(data))\n        \n        return {\n          allowed: true,\n          remainingRequests: config.maxRequests - 1,\n          resetTime: now + windowMs\n        }\n      }\n\n      data = JSON.parse(stored)\n\n      // Check if window has expired\n      if (now - data.windowStart > windowMs) {\n        // Reset window\n        data = {\n          count: 1,\n          windowStart: now,\n          lastRequest: now\n        }\n        localStorage.setItem(storageKey, JSON.stringify(data))\n        \n        return {\n          allowed: true,\n          remainingRequests: config.maxRequests - 1,\n          resetTime: now + windowMs\n        }\n      }\n\n      // Check if limit exceeded\n      if (data.count >= config.maxRequests) {\n        const resetTime = data.windowStart + windowMs\n        const retryAfter = Math.ceil((resetTime - now) / 1000)\n        \n        return {\n          allowed: false,\n          remainingRequests: 0,\n          resetTime,\n          retryAfter\n        }\n      }\n\n      // Increment counter\n      data.count++\n      data.lastRequest = now\n      localStorage.setItem(storageKey, JSON.stringify(data))\n\n      return {\n        allowed: true,\n        remainingRequests: config.maxRequests - data.count,\n        resetTime: data.windowStart + windowMs\n      }\n    } catch (error) {\n      console.warn(`Rate limit check failed for ${action}:`, error)\n      // On error, allow the request but log it\n      return {\n        allowed: true,\n        remainingRequests: 0,\n        resetTime: Date.now() + 60000 // 1 minute fallback\n      }\n    }\n  }\n\n  /**\n   * Reset rate limit for specific action\n   */\n  resetLimit(action: RateLimitAction, userId?: string): void {\n    try {\n      const storageKey = this.getStorageKey(action, userId)\n      localStorage.removeItem(storageKey)\n    } catch (error) {\n      console.warn(`Rate limit reset failed for ${action}:`, error)\n    }\n  }\n\n  /**\n   * Get current rate limit status\n   */\n  getStatus(action: RateLimitAction, userId?: string): {\n    count: number\n    remaining: number\n    resetTime: number\n  } | null {\n    try {\n      const config = RATE_LIMITS[action]\n      const storageKey = this.getStorageKey(action, userId)\n      const stored = localStorage.getItem(storageKey)\n      \n      if (!stored) {\n        return {\n          count: 0,\n          remaining: config.maxRequests,\n          resetTime: Date.now() + config.windowMinutes * 60 * 1000\n        }\n      }\n\n      const data: RateLimitData = JSON.parse(stored)\n      const windowMs = config.windowMinutes * 60 * 1000\n      const now = Date.now()\n\n      // Check if window expired\n      if (now - data.windowStart > windowMs) {\n        return {\n          count: 0,\n          remaining: config.maxRequests,\n          resetTime: now + windowMs\n        }\n      }\n\n      return {\n        count: data.count,\n        remaining: Math.max(0, config.maxRequests - data.count),\n        resetTime: data.windowStart + windowMs\n      }\n    } catch (error) {\n      console.warn(`Rate limit status check failed for ${action}:`, error)\n      return null\n    }\n  }\n}\n\n/**\n * Server-side rate limiting with Supabase\n */\nexport class ServerRateLimiter {\n  /**\n   * Check server-side rate limit using Supabase function\n   */\n  async checkLimit(\n    action: RateLimitAction,\n    userId?: string\n  ): Promise<{\n    allowed: boolean\n    remainingRequests: number\n    resetTime: number\n    retryAfter?: number\n  }> {\n    try {\n      const config = RATE_LIMITS[action]\n      \n      // Get current user if not provided\n      let targetUserId = userId\n      if (!targetUserId) {\n        const { data: { user } } = await supabase.auth.getUser()\n        if (!user) {\n          throw new Error('User not authenticated')\n        }\n        targetUserId = user.id\n      }\n\n      // Call Supabase rate limiting function\n      const { data, error } = await supabase.rpc('check_rate_limit', {\n        p_user_id: targetUserId,\n        p_action_type: action,\n        p_max_requests: config.maxRequests,\n        p_window_minutes: config.windowMinutes\n      })\n\n      if (error) {\n        console.error(`Server rate limit check failed for ${action}:`, error)\n        // On error, allow but log\n        return {\n          allowed: true,\n          remainingRequests: 0,\n          resetTime: Date.now() + config.windowMinutes * 60 * 1000\n        }\n      }\n\n      const allowed = data === true\n      const windowMs = config.windowMinutes * 60 * 1000\n      const resetTime = Date.now() + windowMs\n\n      if (!allowed) {\n        return {\n          allowed: false,\n          remainingRequests: 0,\n          resetTime,\n          retryAfter: Math.ceil(windowMs / 1000)\n        }\n      }\n\n      return {\n        allowed: true,\n        remainingRequests: config.maxRequests - 1, // Approximate\n        resetTime\n      }\n    } catch (error) {\n      console.error(`Server rate limit error for ${action}:`, error)\n      // On error, allow but log\n      return {\n        allowed: true,\n        remainingRequests: 0,\n        resetTime: Date.now() + 60000\n      }\n    }\n  }\n}\n\n// Global instances\nexport const clientRateLimiter = new ClientRateLimiter()\nexport const serverRateLimiter = new ServerRateLimiter()\n\n/**\n * Combined rate limiting check (client + server)\n */\nexport async function checkRateLimit(\n  action: RateLimitAction,\n  options: {\n    clientOnly?: boolean\n    serverOnly?: boolean\n    userId?: string\n  } = {}\n): Promise<{\n  allowed: boolean\n  remainingRequests: number\n  resetTime: number\n  retryAfter?: number\n  source: 'client' | 'server' | 'both'\n}> {\n  const { clientOnly = false, serverOnly = false, userId } = options\n\n  try {\n    // Client-side check\n    if (!serverOnly) {\n      const clientResult = await clientRateLimiter.checkLimit(action, userId)\n      if (!clientResult.allowed) {\n        return {\n          ...clientResult,\n          source: 'client'\n        }\n      }\n      \n      if (clientOnly) {\n        return {\n          ...clientResult,\n          source: 'client'\n        }\n      }\n    }\n\n    // Server-side check\n    if (!clientOnly) {\n      const serverResult = await serverRateLimiter.checkLimit(action, userId)\n      return {\n        ...serverResult,\n        source: serverOnly ? 'server' : 'both'\n      }\n    }\n\n    // Fallback (shouldn't reach here)\n    return {\n      allowed: true,\n      remainingRequests: 0,\n      resetTime: Date.now() + 60000,\n      source: 'client'\n    }\n  } catch (error) {\n    console.error(`Rate limit check failed for ${action}:`, error)\n    return {\n      allowed: true,\n      remainingRequests: 0,\n      resetTime: Date.now() + 60000,\n      source: 'client'\n    }\n  }\n}\n\n/**\n * Rate limit error class\n */\nexport class RateLimitError extends Error {\n  constructor(\n    public action: RateLimitAction,\n    public retryAfter: number,\n    public resetTime: number\n  ) {\n    super(`Rate limit exceeded for ${action}. Try again in ${retryAfter} seconds.`)\n    this.name = 'RateLimitError'\n  }\n}\n\n/**\n * Rate limiting hook for React components\n */\nexport function useRateLimit(action: RateLimitAction) {\n  return {\n    checkLimit: (options?: { clientOnly?: boolean; serverOnly?: boolean }) =>\n      checkRateLimit(action, options),\n    resetLimit: () => clientRateLimiter.resetLimit(action),\n    getStatus: () => clientRateLimiter.getStatus(action)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;;;CAGC,GAED;AAPA;;;AAUO,MAAM,cAAc;IACzB,yBAAyB;IACzB,OAAO;QAAE,aAAa;QAAG,eAAe;IAAG;IAC3C,QAAQ;QAAE,aAAa;QAAG,eAAe;IAAG;IAC5C,gBAAgB;QAAE,aAAa;QAAG,eAAe;IAAG;IAEpD,kBAAkB;IAClB,gBAAgB;QAAE,aAAa;QAAI,eAAe;IAAG;IACrD,gBAAgB;QAAE,aAAa;QAAI,eAAe;IAAG;IACrD,gBAAgB;QAAE,aAAa;QAAG,eAAe;IAAG;IAEpD,iBAAiB;IACjB,eAAe;QAAE,aAAa;QAAI,eAAe;IAAG;IACpD,eAAe;QAAE,aAAa;QAAK,eAAe;IAAG;IACrD,eAAe;QAAE,aAAa;QAAI,eAAe;IAAG;IAEpD,kBAAkB;IAClB,gBAAgB;QAAE,aAAa;QAAI,eAAe;IAAG;IACrD,mBAAmB;QAAE,aAAa;QAAG,eAAe;IAAG;IAEvD,eAAe;IACf,aAAa;QAAE,aAAa;QAAG,eAAe;IAAG;IAEjD,cAAc;IACd,aAAa;QAAE,aAAa;QAAK,eAAe;IAAG;AACrD;AAaO,MAAM;IACH,cAAc,MAAuB,EAAE,MAAe,EAAU;QACtE,MAAM,aAAa,SAAS,AAAC,IAAU,OAAP,UAAW;QAC3C,OAAO,AAAC,cAAsB,OAAT,QAAoB,OAAX;IAChC;IAEA;;GAEC,GACD,MAAM,WAAW,MAAuB,EAAE,MAAe,EAKtD;QACD,IAAI;YACF,MAAM,SAAS,WAAW,CAAC,OAAO;YAClC,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC,QAAQ;YAC9C,MAAM,MAAM,KAAK,GAAG;YACpB,MAAM,WAAW,OAAO,aAAa,GAAG,KAAK;YAE7C,mBAAmB;YACnB,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,IAAI;YAEJ,IAAI,CAAC,QAAQ;gBACX,gBAAgB;gBAChB,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,aAAa;gBACf;gBACA,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;gBAEhD,OAAO;oBACL,SAAS;oBACT,mBAAmB,OAAO,WAAW,GAAG;oBACxC,WAAW,MAAM;gBACnB;YACF;YAEA,OAAO,KAAK,KAAK,CAAC;YAElB,8BAA8B;YAC9B,IAAI,MAAM,KAAK,WAAW,GAAG,UAAU;gBACrC,eAAe;gBACf,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,aAAa;gBACf;gBACA,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;gBAEhD,OAAO;oBACL,SAAS;oBACT,mBAAmB,OAAO,WAAW,GAAG;oBACxC,WAAW,MAAM;gBACnB;YACF;YAEA,0BAA0B;YAC1B,IAAI,KAAK,KAAK,IAAI,OAAO,WAAW,EAAE;gBACpC,MAAM,YAAY,KAAK,WAAW,GAAG;gBACrC,MAAM,aAAa,KAAK,IAAI,CAAC,CAAC,YAAY,GAAG,IAAI;gBAEjD,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB;oBACA;gBACF;YACF;YAEA,oBAAoB;YACpB,KAAK,KAAK;YACV,KAAK,WAAW,GAAG;YACnB,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;YAEhD,OAAO;gBACL,SAAS;gBACT,mBAAmB,OAAO,WAAW,GAAG,KAAK,KAAK;gBAClD,WAAW,KAAK,WAAW,GAAG;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,AAAC,+BAAqC,OAAP,QAAO,MAAI;YACvD,yCAAyC;YACzC,OAAO;gBACL,SAAS;gBACT,mBAAmB;gBACnB,WAAW,KAAK,GAAG,KAAK,MAAM,oBAAoB;YACpD;QACF;IACF;IAEA;;GAEC,GACD,WAAW,MAAuB,EAAE,MAAe,EAAQ;QACzD,IAAI;YACF,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC,QAAQ;YAC9C,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,AAAC,+BAAqC,OAAP,QAAO,MAAI;QACzD;IACF;IAEA;;GAEC,GACD,UAAU,MAAuB,EAAE,MAAe,EAIzC;QACP,IAAI;YACF,MAAM,SAAS,WAAW,CAAC,OAAO;YAClC,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC,QAAQ;YAC9C,MAAM,SAAS,aAAa,OAAO,CAAC;YAEpC,IAAI,CAAC,QAAQ;gBACX,OAAO;oBACL,OAAO;oBACP,WAAW,OAAO,WAAW;oBAC7B,WAAW,KAAK,GAAG,KAAK,OAAO,aAAa,GAAG,KAAK;gBACtD;YACF;YAEA,MAAM,OAAsB,KAAK,KAAK,CAAC;YACvC,MAAM,WAAW,OAAO,aAAa,GAAG,KAAK;YAC7C,MAAM,MAAM,KAAK,GAAG;YAEpB,0BAA0B;YAC1B,IAAI,MAAM,KAAK,WAAW,GAAG,UAAU;gBACrC,OAAO;oBACL,OAAO;oBACP,WAAW,OAAO,WAAW;oBAC7B,WAAW,MAAM;gBACnB;YACF;YAEA,OAAO;gBACL,OAAO,KAAK,KAAK;gBACjB,WAAW,KAAK,GAAG,CAAC,GAAG,OAAO,WAAW,GAAG,KAAK,KAAK;gBACtD,WAAW,KAAK,WAAW,GAAG;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,AAAC,sCAA4C,OAAP,QAAO,MAAI;YAC9D,OAAO;QACT;IACF;AACF;AAKO,MAAM;IACX;;GAEC,GACD,MAAM,WACJ,MAAuB,EACvB,MAAe,EAMd;QACD,IAAI;YACF,MAAM,SAAS,WAAW,CAAC,OAAO;YAElC,mCAAmC;YACnC,IAAI,eAAe;YACnB,IAAI,CAAC,cAAc;gBACjB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;gBACtD,IAAI,CAAC,MAAM;oBACT,MAAM,IAAI,MAAM;gBAClB;gBACA,eAAe,KAAK,EAAE;YACxB;YAEA,uCAAuC;YACvC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,oBAAoB;gBAC7D,WAAW;gBACX,eAAe;gBACf,gBAAgB,OAAO,WAAW;gBAClC,kBAAkB,OAAO,aAAa;YACxC;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,AAAC,sCAA4C,OAAP,QAAO,MAAI;gBAC/D,0BAA0B;gBAC1B,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB,WAAW,KAAK,GAAG,KAAK,OAAO,aAAa,GAAG,KAAK;gBACtD;YACF;YAEA,MAAM,UAAU,SAAS;YACzB,MAAM,WAAW,OAAO,aAAa,GAAG,KAAK;YAC7C,MAAM,YAAY,KAAK,GAAG,KAAK;YAE/B,IAAI,CAAC,SAAS;gBACZ,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB;oBACA,YAAY,KAAK,IAAI,CAAC,WAAW;gBACnC;YACF;YAEA,OAAO;gBACL,SAAS;gBACT,mBAAmB,OAAO,WAAW,GAAG;gBACxC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,AAAC,+BAAqC,OAAP,QAAO,MAAI;YACxD,0BAA0B;YAC1B,OAAO;gBACL,SAAS;gBACT,mBAAmB;gBACnB,WAAW,KAAK,GAAG,KAAK;YAC1B;QACF;IACF;AACF;AAGO,MAAM,oBAAoB,IAAI;AAC9B,MAAM,oBAAoB,IAAI;AAK9B,eAAe,eACpB,MAAuB;QACvB,UAAA,iEAII,CAAC;IAQL,MAAM,EAAE,aAAa,KAAK,EAAE,aAAa,KAAK,EAAE,MAAM,EAAE,GAAG;IAE3D,IAAI;QACF,oBAAoB;QACpB,IAAI,CAAC,YAAY;YACf,MAAM,eAAe,MAAM,kBAAkB,UAAU,CAAC,QAAQ;YAChE,IAAI,CAAC,aAAa,OAAO,EAAE;gBACzB,OAAO;oBACL,GAAG,YAAY;oBACf,QAAQ;gBACV;YACF;YAEA,IAAI,YAAY;gBACd,OAAO;oBACL,GAAG,YAAY;oBACf,QAAQ;gBACV;YACF;QACF;QAEA,oBAAoB;QACpB,IAAI,CAAC,YAAY;YACf,MAAM,eAAe,MAAM,kBAAkB,UAAU,CAAC,QAAQ;YAChE,OAAO;gBACL,GAAG,YAAY;gBACf,QAAQ,aAAa,WAAW;YAClC;QACF;QAEA,kCAAkC;QAClC,OAAO;YACL,SAAS;YACT,mBAAmB;YACnB,WAAW,KAAK,GAAG,KAAK;YACxB,QAAQ;QACV;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,AAAC,+BAAqC,OAAP,QAAO,MAAI;QACxD,OAAO;YACL,SAAS;YACT,mBAAmB;YACnB,WAAW,KAAK,GAAG,KAAK;YACxB,QAAQ;QACV;IACF;AACF;AAKO,MAAM,uBAAuB;IAClC,YACE,AAAO,MAAuB,EAC9B,AAAO,UAAkB,EACzB,AAAO,SAAiB,CACxB;QACA,KAAK,CAAC,AAAC,2BAAkD,OAAxB,QAAO,mBAA4B,OAAX,YAAW,imBAJ7D,SAAA,aACA,aAAA,iBACA,YAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAKO,SAAS,aAAa,MAAuB;IAClD,OAAO;QACL,YAAY,CAAC,UACX,eAAe,QAAQ;QACzB,YAAY,IAAM,kBAAkB,UAAU,CAAC;QAC/C,WAAW,IAAM,kBAAkB,SAAS,CAAC;IAC/C;AACF", "debugId": null}}, {"offset": {"line": 1578, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/hooks/use-projects.ts"], "sourcesContent": ["'use client'\r\n\r\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\r\nimport { Database } from '@/lib/supabase'\r\nimport { canCreateProject, updateUsageStats, PLAN_LIMIT_MESSAGES } from '@/lib/plan-limits'\r\nimport {\r\n  validateProjectName,\r\n  formatValidationError\r\n} from '@/lib/project-validation'\r\nimport { checkRateLimit, RateLimitError } from '@/lib/rate-limiter'\r\nimport { OptimizedQueries, QueryPerformanceMonitor } from '@/lib/database-optimization'\r\nimport { CACHE_CONFIGS } from '@/lib/cache-strategies'\r\n\r\ntype Project = Database['public']['Tables']['projects']['Row']\r\ntype ProjectInsert = Database['public']['Tables']['projects']['Insert']\r\ntype ProjectUpdate = Database['public']['Tables']['projects']['Update']\r\n\r\n// Projeleri getir\r\nexport function useProjects() {\r\n  return useQuery({\r\n    queryKey: ['projects'],\r\n    queryFn: async (): Promise<Project[]> => {\r\n      console.log(`📁 [USE_PROJECTS] Fetching projects...`)\r\n\r\n      try {\r\n        // Check if we have a session first\r\n        const { data: { session }, error: sessionError } = await supabase.auth.getSession()\r\n        console.log(`📁 [USE_PROJECTS] Session check:`, {\r\n          hasSession: !!session,\r\n          sessionError: sessionError?.message,\r\n          userId: session?.user?.id\r\n        })\r\n\r\n        const { data, error } = await supabase\r\n          .from('projects')\r\n          .select('*')\r\n          .order('created_at', { ascending: false })\r\n\r\n        if (error) {\r\n          console.error(`❌ [USE_PROJECTS] Error fetching projects:`, error)\r\n          throw new Error(error.message)\r\n        }\r\n\r\n        console.log(`✅ [USE_PROJECTS] Projects fetched:`, data?.length || 0, 'projects')\r\n        return data || []\r\n      } catch (err) {\r\n        console.error(`💥 [USE_PROJECTS] Exception:`, err)\r\n        throw err\r\n      }\r\n    },\r\n  })\r\n}\r\n\r\n// Tek proje getir\r\nexport function useProject(projectId: string | null) {\r\n  return useQuery({\r\n    queryKey: ['project', projectId],\r\n    queryFn: async (): Promise<Project | null> => {\r\n      if (!projectId) return null\r\n\r\n      const { data, error } = await supabase\r\n        .from('projects')\r\n        .select('*')\r\n        .eq('id', projectId)\r\n        .single()\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n\r\n      return data\r\n    },\r\n    enabled: !!projectId,\r\n  })\r\n}\r\n\r\n// Proje oluştur\r\nexport function useCreateProject() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async (project: Omit<ProjectInsert, 'user_id'>): Promise<Project> => {\r\n      // Plan limiti kontrol et\r\n      const limitCheck = await canCreateProject()\r\n      if (!limitCheck.allowed) {\r\n        throw new Error(limitCheck.reason || PLAN_LIMIT_MESSAGES.PROJECT_LIMIT_REACHED)\r\n      }\r\n\r\n      const { data: { user }, error: userError } = await supabase.auth.getUser()\r\n\r\n      if (userError || !user) {\r\n        throw new Error('Kullanıcı oturumu bulunamadı')\r\n      }\r\n\r\n      const { data, error } = await supabase\r\n        .from('projects')\r\n        .insert({\r\n          ...project,\r\n          user_id: user.id,\r\n        })\r\n        .select()\r\n        .single()\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n\r\n      return data\r\n    },\r\n    onSuccess: async () => {\r\n      queryClient.invalidateQueries({ queryKey: ['projects'] })\r\n      // Kullanım istatistiklerini güncelle\r\n      try {\r\n        await updateUsageStats()\r\n        queryClient.invalidateQueries({ queryKey: ['user-limits'] })\r\n        queryClient.invalidateQueries({ queryKey: ['usage-stats'] })\r\n      } catch (error) {\r\n        console.warn('Kullanım istatistikleri güncellenemedi:', error)\r\n      }\r\n    },\r\n  })\r\n}\r\n\r\n// Proje güncelle\r\nexport function useUpdateProject() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async ({ id, ...updates }: ProjectUpdate & { id: string }): Promise<Project> => {\r\n      console.log('🔄 [UPDATE_PROJECT] Starting update:', { id, updates })\r\n\r\n      // Önce mevcut kullanıcıyı kontrol et\r\n      const { data: user } = await supabase.auth.getUser()\r\n      if (!user.user) {\r\n        throw new Error('Kullanıcı girişi gerekli')\r\n      }\r\n\r\n      // Güncelleme işlemini yap - RLS politikaları otomatik olarak kontrol edecek\r\n      const { data, error } = await supabase\r\n        .from('projects')\r\n        .update({\r\n          ...updates,\r\n          updated_at: new Date().toISOString()\r\n        })\r\n        .eq('id', id)\r\n        .eq('user_id', user.user.id) // Ekstra güvenlik için user_id kontrolü\r\n        .select()\r\n        .single()\r\n\r\n      if (error) {\r\n        console.error('❌ [UPDATE_PROJECT] Database error:', error)\r\n        throw new Error(`Proje güncellenirken hata oluştu: ${error.message}`)\r\n      }\r\n\r\n      if (!data) {\r\n        throw new Error('Proje bulunamadı veya güncelleme yetkisi yok')\r\n      }\r\n\r\n      console.log('✅ [UPDATE_PROJECT] Success:', data)\r\n      return data\r\n    },\r\n    onSuccess: (data) => {\r\n      queryClient.invalidateQueries({ queryKey: ['projects'] })\r\n      queryClient.invalidateQueries({ queryKey: ['project', data.id] })\r\n    },\r\n    onError: (error) => {\r\n      console.error('❌ [UPDATE_PROJECT] Mutation error:', error)\r\n    }\r\n  })\r\n}\r\n\r\n// Güvenli proje adı güncelleme\r\nexport function useUpdateProjectNameSecure() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async ({\r\n      projectId,\r\n      newName\r\n    }: {\r\n      projectId: string;\r\n      newName: string\r\n    }): Promise<Project> => {\r\n      console.log(`🔒 [UPDATE_PROJECT_NAME] Starting secure update:`, { projectId, newName })\r\n\r\n      // Advanced rate limiting check\r\n      const rateLimitResult = await checkRateLimit('project_update')\r\n      if (!rateLimitResult.allowed) {\r\n        throw new RateLimitError(\r\n          'project_update',\r\n          rateLimitResult.retryAfter || 60,\r\n          rateLimitResult.resetTime\r\n        )\r\n      }\r\n\r\n      // Client-side validation\r\n      const validation = validateProjectName(newName)\r\n      if (!validation.isValid) {\r\n        throw new Error(validation.error || 'Geçersiz proje adı')\r\n      }\r\n\r\n      const sanitizedName = validation.sanitizedValue!\r\n\r\n      try {\r\n        // Güvenli database function'ını çağır\r\n        const { data, error } = await supabase.rpc('update_project_name_secure', {\r\n          p_project_id: projectId,\r\n          p_new_name: sanitizedName\r\n        })\r\n\r\n        if (error) {\r\n          console.error(`❌ [UPDATE_PROJECT_NAME] Database error:`, error)\r\n          throw new Error(formatValidationError(error.message))\r\n        }\r\n\r\n        // Function response kontrolü\r\n        if (!data?.success) {\r\n          console.error(`❌ [UPDATE_PROJECT_NAME] Function error:`, data)\r\n          throw new Error(data?.message || 'Proje adı güncellenemedi')\r\n        }\r\n\r\n        console.log(`✅ [UPDATE_PROJECT_NAME] Success:`, data.data)\r\n        return data.data as Project\r\n\r\n      } catch (err) {\r\n        console.error(`💥 [UPDATE_PROJECT_NAME] Exception:`, err)\r\n\r\n        // Error mesajını kullanıcı dostu hale getir\r\n        let errorMessage = err instanceof Error ? err.message : 'Bilinmeyen hata'\r\n\r\n        if (errorMessage.includes('unique_violation') || errorMessage.includes('DuplicateName')) {\r\n          errorMessage = 'Bu isimde bir proje zaten mevcut'\r\n        } else if (errorMessage.includes('check_violation') || errorMessage.includes('InvalidInput')) {\r\n          errorMessage = 'Proje adı geçersiz karakterler içeriyor'\r\n        } else if (errorMessage.includes('RateLimitExceeded')) {\r\n          errorMessage = 'Çok fazla güncelleme isteği. Lütfen bekleyin.'\r\n        }\r\n\r\n        throw new Error(errorMessage)\r\n      }\r\n    },\r\n    onMutate: async ({ projectId, newName }) => {\r\n      console.log(`🚀 [UPDATE_PROJECT_NAME] Starting optimistic update:`, { projectId, newName })\r\n\r\n      // Cancel outgoing refetches\r\n      await queryClient.cancelQueries({ queryKey: ['projects'] })\r\n      await queryClient.cancelQueries({ queryKey: ['project', projectId] })\r\n\r\n      // Snapshot previous values\r\n      const previousProjects = queryClient.getQueryData<Project[]>(['projects'])\r\n      const previousProject = queryClient.getQueryData<Project>(['project', projectId])\r\n\r\n      // Optimistically update projects list\r\n      if (previousProjects) {\r\n        queryClient.setQueryData<Project[]>(['projects'], (old) => {\r\n          if (!old) return old\r\n          return old.map(project =>\r\n            project.id === projectId\r\n              ? { ...project, name: newName.trim(), updated_at: new Date().toISOString() }\r\n              : project\r\n          )\r\n        })\r\n      }\r\n\r\n      // Optimistically update single project\r\n      if (previousProject) {\r\n        queryClient.setQueryData<Project>(['project', projectId], {\r\n          ...previousProject,\r\n          name: newName.trim(),\r\n          updated_at: new Date().toISOString()\r\n        })\r\n      }\r\n\r\n      return { previousProjects, previousProject }\r\n    },\r\n    onSuccess: (data) => {\r\n      console.log(`✅ [UPDATE_PROJECT_NAME] Success, updating cache with server data:`, data.id)\r\n\r\n      // Update with actual server data\r\n      queryClient.setQueryData<Project[]>(['projects'], (old) => {\r\n        if (!old) return old\r\n        return old.map(project =>\r\n          project.id === data.id ? { ...project, ...data } : project\r\n        )\r\n      })\r\n\r\n      queryClient.setQueryData<Project>(['project', data.id], data)\r\n    },\r\n    onError: (error, variables, context) => {\r\n      console.error(`💥 [UPDATE_PROJECT_NAME] Error, rolling back:`, error)\r\n\r\n      // Rollback optimistic updates\r\n      if (context?.previousProjects) {\r\n        queryClient.setQueryData(['projects'], context.previousProjects)\r\n      }\r\n      if (context?.previousProject) {\r\n        queryClient.setQueryData(['project', variables.projectId], context.previousProject)\r\n      }\r\n    },\r\n    onSettled: () => {\r\n      // Always refetch to ensure consistency\r\n      queryClient.invalidateQueries({ queryKey: ['projects'] })\r\n    }\r\n  })\r\n}\r\n\r\n// Proje sil\r\nexport function useDeleteProject() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async (projectId: string): Promise<void> => {\r\n      const { error } = await supabase\r\n        .from('projects')\r\n        .delete()\r\n        .eq('id', projectId)\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n    },\r\n    onSuccess: async () => {\r\n      queryClient.invalidateQueries({ queryKey: ['projects'] })\r\n      // Kullanım istatistiklerini güncelle\r\n      try {\r\n        await updateUsageStats()\r\n        queryClient.invalidateQueries({ queryKey: ['user-limits'] })\r\n        queryClient.invalidateQueries({ queryKey: ['usage-stats'] })\r\n      } catch (error) {\r\n        console.warn('Kullanım istatistikleri güncellenemedi:', error)\r\n      }\r\n    },\r\n  })\r\n} "], "names": [], "mappings": ";;;;;;;;AAEA;AAAA;AAAA;AACA;AAEA;AACA;AAIA;;AAVA;;;;;;AAmBO,SAAS;;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAW;QACtB,OAAO;oCAAE;gBACP,QAAQ,GAAG,CAAE;gBAEb,IAAI;wBAMQ;oBALV,mCAAmC;oBACnC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,UAAU;oBACjF,QAAQ,GAAG,CAAE,oCAAmC;wBAC9C,YAAY,CAAC,CAAC;wBACd,YAAY,EAAE,yBAAA,mCAAA,aAAc,OAAO;wBACnC,MAAM,EAAE,oBAAA,+BAAA,gBAAA,QAAS,IAAI,cAAb,oCAAA,cAAe,EAAE;oBAC3B;oBAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;wBAAE,WAAW;oBAAM;oBAE1C,IAAI,OAAO;wBACT,QAAQ,KAAK,CAAE,6CAA4C;wBAC3D,MAAM,IAAI,MAAM,MAAM,OAAO;oBAC/B;oBAEA,QAAQ,GAAG,CAAE,sCAAqC,CAAA,iBAAA,2BAAA,KAAM,MAAM,KAAI,GAAG;oBACrE,OAAO,QAAQ,EAAE;gBACnB,EAAE,OAAO,KAAK;oBACZ,QAAQ,KAAK,CAAE,gCAA+B;oBAC9C,MAAM;gBACR;YACF;;IACF;AACF;GAjCgB;;QACP,8KAAA,CAAA,WAAQ;;;AAmCV,SAAS,WAAW,SAAwB;;IACjD,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;SAAU;QAChC,OAAO;mCAAE;gBACP,IAAI,CAAC,WAAW,OAAO;gBAEvB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,WACT,MAAM;gBAET,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,OAAO;YACT;;QACA,SAAS,CAAC,CAAC;IACb;AACF;IApBgB;;QACP,8KAAA,CAAA,WAAQ;;;AAsBV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;4CAAE,OAAO;gBACjB,yBAAyB;gBACzB,MAAM,aAAa,MAAM,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD;gBACxC,IAAI,CAAC,WAAW,OAAO,EAAE;oBACvB,MAAM,IAAI,MAAM,WAAW,MAAM,IAAI,+HAAA,CAAA,sBAAmB,CAAC,qBAAqB;gBAChF;gBAEA,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;gBAExE,IAAI,aAAa,CAAC,MAAM;oBACtB,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC;oBACN,GAAG,OAAO;oBACV,SAAS,KAAK,EAAE;gBAClB,GACC,MAAM,GACN,MAAM;gBAET,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,OAAO;YACT;;QACA,SAAS;4CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,qCAAqC;gBACrC,IAAI;oBACF,MAAM,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD;oBACrB,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;yBAAc;oBAAC;oBAC1D,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;yBAAc;oBAAC;gBAC5D,EAAE,OAAO,OAAO;oBACd,QAAQ,IAAI,CAAC,2CAA2C;gBAC1D;YACF;;IACF;AACF;IA5CgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AA4Cb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;4CAAE;oBAAO,EAAE,EAAE,EAAE,GAAG,SAAyC;gBACnE,QAAQ,GAAG,CAAC,wCAAwC;oBAAE;oBAAI;gBAAQ;gBAElE,qCAAqC;gBACrC,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;gBAClD,IAAI,CAAC,KAAK,IAAI,EAAE;oBACd,MAAM,IAAI,MAAM;gBAClB;gBAEA,4EAA4E;gBAC5E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC;oBACN,GAAG,OAAO;oBACV,YAAY,IAAI,OAAO,WAAW;gBACpC,GACC,EAAE,CAAC,MAAM,IACT,EAAE,CAAC,WAAW,KAAK,IAAI,CAAC,EAAE,EAAE,wCAAwC;iBACpE,MAAM,GACN,MAAM;gBAET,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,sCAAsC;oBACpD,MAAM,IAAI,MAAM,AAAC,qCAAkD,OAAd,MAAM,OAAO;gBACpE;gBAEA,IAAI,CAAC,MAAM;oBACT,MAAM,IAAI,MAAM;gBAClB;gBAEA,QAAQ,GAAG,CAAC,+BAA+B;gBAC3C,OAAO;YACT;;QACA,SAAS;4CAAE,CAAC;gBACV,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAW,KAAK,EAAE;qBAAC;gBAAC;YACjE;;QACA,OAAO;4CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,sCAAsC;YACtD;;IACF;AACF;IA7CgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AA6Cb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;sDAAE;oBAAO,EACjB,SAAS,EACT,OAAO,EAIR;gBACC,QAAQ,GAAG,CAAE,oDAAmD;oBAAE;oBAAW;gBAAQ;gBAErF,+BAA+B;gBAC/B,MAAM,kBAAkB,MAAM,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD,EAAE;gBAC7C,IAAI,CAAC,gBAAgB,OAAO,EAAE;oBAC5B,MAAM,IAAI,gIAAA,CAAA,iBAAc,CACtB,kBACA,gBAAgB,UAAU,IAAI,IAC9B,gBAAgB,SAAS;gBAE7B;gBAEA,yBAAyB;gBACzB,MAAM,aAAa,CAAA,GAAA,sIAAA,CAAA,sBAAmB,AAAD,EAAE;gBACvC,IAAI,CAAC,WAAW,OAAO,EAAE;oBACvB,MAAM,IAAI,MAAM,WAAW,KAAK,IAAI;gBACtC;gBAEA,MAAM,gBAAgB,WAAW,cAAc;gBAE/C,IAAI;oBACF,sCAAsC;oBACtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,8BAA8B;wBACvE,cAAc;wBACd,YAAY;oBACd;oBAEA,IAAI,OAAO;wBACT,QAAQ,KAAK,CAAE,2CAA0C;wBACzD,MAAM,IAAI,MAAM,CAAA,GAAA,sIAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM,OAAO;oBACrD;oBAEA,6BAA6B;oBAC7B,IAAI,EAAC,iBAAA,2BAAA,KAAM,OAAO,GAAE;wBAClB,QAAQ,KAAK,CAAE,2CAA0C;wBACzD,MAAM,IAAI,MAAM,CAAA,iBAAA,2BAAA,KAAM,OAAO,KAAI;oBACnC;oBAEA,QAAQ,GAAG,CAAE,oCAAmC,KAAK,IAAI;oBACzD,OAAO,KAAK,IAAI;gBAElB,EAAE,OAAO,KAAK;oBACZ,QAAQ,KAAK,CAAE,uCAAsC;oBAErD,4CAA4C;oBAC5C,IAAI,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;oBAExD,IAAI,aAAa,QAAQ,CAAC,uBAAuB,aAAa,QAAQ,CAAC,kBAAkB;wBACvF,eAAe;oBACjB,OAAO,IAAI,aAAa,QAAQ,CAAC,sBAAsB,aAAa,QAAQ,CAAC,iBAAiB;wBAC5F,eAAe;oBACjB,OAAO,IAAI,aAAa,QAAQ,CAAC,sBAAsB;wBACrD,eAAe;oBACjB;oBAEA,MAAM,IAAI,MAAM;gBAClB;YACF;;QACA,QAAQ;sDAAE;oBAAO,EAAE,SAAS,EAAE,OAAO,EAAE;gBACrC,QAAQ,GAAG,CAAE,wDAAuD;oBAAE;oBAAW;gBAAQ;gBAEzF,4BAA4B;gBAC5B,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACzD,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU;wBAAC;wBAAW;qBAAU;gBAAC;gBAEnE,2BAA2B;gBAC3B,MAAM,mBAAmB,YAAY,YAAY,CAAY;oBAAC;iBAAW;gBACzE,MAAM,kBAAkB,YAAY,YAAY,CAAU;oBAAC;oBAAW;iBAAU;gBAEhF,sCAAsC;gBACtC,IAAI,kBAAkB;oBACpB,YAAY,YAAY,CAAY;wBAAC;qBAAW;kEAAE,CAAC;4BACjD,IAAI,CAAC,KAAK,OAAO;4BACjB,OAAO,IAAI,GAAG;0EAAC,CAAA,UACb,QAAQ,EAAE,KAAK,YACX;wCAAE,GAAG,OAAO;wCAAE,MAAM,QAAQ,IAAI;wCAAI,YAAY,IAAI,OAAO,WAAW;oCAAG,IACzE;;wBAER;;gBACF;gBAEA,uCAAuC;gBACvC,IAAI,iBAAiB;oBACnB,YAAY,YAAY,CAAU;wBAAC;wBAAW;qBAAU,EAAE;wBACxD,GAAG,eAAe;wBAClB,MAAM,QAAQ,IAAI;wBAClB,YAAY,IAAI,OAAO,WAAW;oBACpC;gBACF;gBAEA,OAAO;oBAAE;oBAAkB;gBAAgB;YAC7C;;QACA,SAAS;sDAAE,CAAC;gBACV,QAAQ,GAAG,CAAE,qEAAoE,KAAK,EAAE;gBAExF,iCAAiC;gBACjC,YAAY,YAAY,CAAY;oBAAC;iBAAW;8DAAE,CAAC;wBACjD,IAAI,CAAC,KAAK,OAAO;wBACjB,OAAO,IAAI,GAAG;sEAAC,CAAA,UACb,QAAQ,EAAE,KAAK,KAAK,EAAE,GAAG;oCAAE,GAAG,OAAO;oCAAE,GAAG,IAAI;gCAAC,IAAI;;oBAEvD;;gBAEA,YAAY,YAAY,CAAU;oBAAC;oBAAW,KAAK,EAAE;iBAAC,EAAE;YAC1D;;QACA,OAAO;sDAAE,CAAC,OAAO,WAAW;gBAC1B,QAAQ,KAAK,CAAE,iDAAgD;gBAE/D,8BAA8B;gBAC9B,IAAI,oBAAA,8BAAA,QAAS,gBAAgB,EAAE;oBAC7B,YAAY,YAAY,CAAC;wBAAC;qBAAW,EAAE,QAAQ,gBAAgB;gBACjE;gBACA,IAAI,oBAAA,8BAAA,QAAS,eAAe,EAAE;oBAC5B,YAAY,YAAY,CAAC;wBAAC;wBAAW,UAAU,SAAS;qBAAC,EAAE,QAAQ,eAAe;gBACpF;YACF;;QACA,SAAS;sDAAE;gBACT,uCAAuC;gBACvC,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;YACzD;;IACF;AACF;IApIgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAoIb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;4CAAE,OAAO;gBACjB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAC7B,IAAI,CAAC,YACL,MAAM,GACN,EAAE,CAAC,MAAM;gBAEZ,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;YACF;;QACA,SAAS;4CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,qCAAqC;gBACrC,IAAI;oBACF,MAAM,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD;oBACrB,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;yBAAc;oBAAC;oBAC1D,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;yBAAc;oBAAC;gBAC5D,EAAE,OAAO,OAAO;oBACd,QAAQ,IAAI,CAAC,2CAA2C;gBAC1D;YACF;;IACF;AACF;IA1BgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 2011, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/hooks/use-hashtags.ts"], "sourcesContent": ["'use client'\n\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\n\n// Get popular hashtags with usage count\nexport function usePopularHashtags(projectId: string | null, limit: number = 20) {\n  return useQuery({\n    queryKey: ['popular-hashtags', projectId, limit],\n    queryFn: async (): Promise<{ hashtag: string; count: number }[]> => {\n      if (!projectId) return []\n\n      // Get all prompts for the project with their tags\n      const { data: prompts, error } = await supabase\n        .from('prompts')\n        .select('tags')\n        .eq('project_id', projectId)\n\n      if (error) {\n        throw new Error(error.message)\n      }\n\n      // Count hashtag occurrences\n      const hashtagCounts: Record<string, number> = {}\n\n      prompts?.forEach(prompt => {\n        if (prompt.tags && Array.isArray(prompt.tags)) {\n          prompt.tags.forEach(tag => {\n            if (typeof tag === 'string' && tag.trim()) {\n              // Add # prefix if not present, then normalize to lowercase\n              const hashtag = tag.startsWith('#') ? tag.toLowerCase() : `#${tag.toLowerCase()}`\n              hashtagCounts[hashtag] = (hashtagCounts[hashtag] || 0) + 1\n            }\n          })\n        }\n      })\n\n      // Convert to array and sort by count\n      return Object.entries(hashtagCounts)\n        .map(([hashtag, count]) => ({ hashtag, count }))\n        .sort((a, b) => b.count - a.count)\n        .slice(0, limit)\n    },\n    enabled: !!projectId,\n  })\n}\n\n// Get popular categories with usage count\nexport function usePopularCategories(projectId: string | null, limit: number = 10) {\n  return useQuery({\n    queryKey: ['popular-categories', projectId, limit],\n    queryFn: async (): Promise<{ category: string; count: number }[]> => {\n      if (!projectId) return []\n\n      // Get category counts using SQL aggregation\n      const { data, error } = await supabase\n        .from('prompts')\n        .select('category')\n        .eq('project_id', projectId)\n        .not('category', 'is', null)\n\n      if (error) {\n        throw new Error(error.message)\n      }\n\n      // Count category occurrences\n      const categoryCounts: Record<string, number> = {}\n      \n      data?.forEach(prompt => {\n        if (prompt.category) {\n          const category = prompt.category.toLowerCase()\n          categoryCounts[category] = (categoryCounts[category] || 0) + 1\n        }\n      })\n\n      // Convert to array and sort by count\n      return Object.entries(categoryCounts)\n        .map(([category, count]) => ({ category, count }))\n        .sort((a, b) => b.count - a.count)\n        .slice(0, limit)\n    },\n    enabled: !!projectId,\n  })\n}\n\n// Get all unique hashtags for a project (for autocomplete)\nexport function useAllHashtags(projectId: string | null) {\n  return useQuery({\n    queryKey: ['all-hashtags', projectId],\n    queryFn: async (): Promise<string[]> => {\n      if (!projectId) return []\n\n      const { data: prompts, error } = await supabase\n        .from('prompts')\n        .select('tags')\n        .eq('project_id', projectId)\n\n      if (error) {\n        throw new Error(error.message)\n      }\n\n      // Extract all unique hashtags\n      const allHashtags = new Set<string>()\n      \n      prompts?.forEach(prompt => {\n        if (prompt.tags && Array.isArray(prompt.tags)) {\n          prompt.tags.forEach(tag => {\n            if (typeof tag === 'string' && tag.trim()) {\n              // Add # prefix if not present, then normalize to lowercase\n              const hashtag = tag.startsWith('#') ? tag.toLowerCase() : `#${tag.toLowerCase()}`\n              allHashtags.add(hashtag)\n            }\n          })\n        }\n      })\n\n      return Array.from(allHashtags).sort()\n    },\n    enabled: !!projectId,\n  })\n}\n\n// Get all unique categories for a project (for autocomplete)\nexport function useAllCategories(projectId: string | null) {\n  return useQuery({\n    queryKey: ['all-categories', projectId],\n    queryFn: async (): Promise<string[]> => {\n      if (!projectId) return []\n\n      const { data, error } = await supabase\n        .from('prompts')\n        .select('category')\n        .eq('project_id', projectId)\n        .not('category', 'is', null)\n\n      if (error) {\n        throw new Error(error.message)\n      }\n\n      // Extract all unique categories\n      const allCategories = new Set<string>()\n      \n      data?.forEach(prompt => {\n        if (prompt.category) {\n          allCategories.add(prompt.category.toLowerCase())\n        }\n      })\n\n      return Array.from(allCategories).sort()\n    },\n    enabled: !!projectId,\n  })\n}\n\n// Get prompts filtered by hashtags and/or category\nexport function useFilteredPrompts(\n  projectId: string | null,\n  filters: {\n    hashtags?: string[]\n    category?: string\n    searchQuery?: string\n  }\n) {\n  return useQuery({\n    queryKey: ['filtered-prompts', projectId, filters],\n    queryFn: async () => {\n      if (!projectId) return []\n\n      let query = supabase\n        .from('prompts')\n        .select('*')\n        .eq('project_id', projectId)\n        .order('order_index', { ascending: true })\n\n      // Filter by category\n      if (filters.category) {\n        query = query.eq('category', filters.category)\n      }\n\n      // Filter by search query\n      if (filters.searchQuery) {\n        query = query.ilike('prompt_text', `%${filters.searchQuery}%`)\n      }\n\n      const { data: prompts, error } = await query\n\n      if (error) {\n        throw new Error(error.message)\n      }\n\n      // Filter by hashtags (client-side since we need to check array contents)\n      let filteredPrompts = prompts || []\n      \n      if (filters.hashtags && filters.hashtags.length > 0) {\n        filteredPrompts = filteredPrompts.filter(prompt => {\n          if (!prompt.tags || !Array.isArray(prompt.tags)) return false\n          \n          return filters.hashtags!.some(hashtag => \n            prompt.tags.some((tag: string) => \n              tag.toLowerCase() === hashtag.toLowerCase()\n            )\n          )\n        })\n      }\n\n      return filteredPrompts\n    },\n    enabled: !!projectId,\n  })\n}\n\n// Update hashtag usage statistics\nexport function useUpdateHashtagStats() {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async ({ \n      projectId, \n      hashtags \n    }: { \n      projectId: string\n      hashtags: string[] \n    }) => {\n      // This could be implemented as a database function for better performance\n      // For now, we'll rely on the query invalidation to refresh the popular hashtags\n      return { projectId, hashtags }\n    },\n    onSuccess: (data) => {\n      // Invalidate popular hashtags query to refresh counts\n      queryClient.invalidateQueries({ \n        queryKey: ['popular-hashtags', data.projectId] \n      })\n      queryClient.invalidateQueries({ \n        queryKey: ['all-hashtags', data.projectId] \n      })\n    },\n  })\n}\n\n// Update category usage statistics\nexport function useUpdateCategoryStats() {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async ({ \n      projectId, \n      category \n    }: { \n      projectId: string\n      category: string \n    }) => {\n      return { projectId, category }\n    },\n    onSuccess: (data) => {\n      // Invalidate popular categories query to refresh counts\n      queryClient.invalidateQueries({ \n        queryKey: ['popular-categories', data.projectId] \n      })\n      queryClient.invalidateQueries({ \n        queryKey: ['all-categories', data.projectId] \n      })\n    },\n  })\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AAAA;AAAA;AACA;;AAHA;;;AAMO,SAAS,mBAAmB,SAAwB;QAAE,QAAA,iEAAgB;;IAC3E,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAoB;YAAW;SAAM;QAChD,OAAO;2CAAE;gBACP,IAAI,CAAC,WAAW,OAAO,EAAE;gBAEzB,kDAAkD;gBAClD,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAC5C,IAAI,CAAC,WACL,MAAM,CAAC,QACP,EAAE,CAAC,cAAc;gBAEpB,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,4BAA4B;gBAC5B,MAAM,gBAAwC,CAAC;gBAE/C,oBAAA,8BAAA,QAAS,OAAO;mDAAC,CAAA;wBACf,IAAI,OAAO,IAAI,IAAI,MAAM,OAAO,CAAC,OAAO,IAAI,GAAG;4BAC7C,OAAO,IAAI,CAAC,OAAO;+DAAC,CAAA;oCAClB,IAAI,OAAO,QAAQ,YAAY,IAAI,IAAI,IAAI;wCACzC,2DAA2D;wCAC3D,MAAM,UAAU,IAAI,UAAU,CAAC,OAAO,IAAI,WAAW,KAAK,AAAC,IAAqB,OAAlB,IAAI,WAAW;wCAC7E,aAAa,CAAC,QAAQ,GAAG,CAAC,aAAa,CAAC,QAAQ,IAAI,CAAC,IAAI;oCAC3D;gCACF;;wBACF;oBACF;;gBAEA,qCAAqC;gBACrC,OAAO,OAAO,OAAO,CAAC,eACnB,GAAG;mDAAC;4BAAC,CAAC,SAAS,MAAM;+BAAM;4BAAE;4BAAS;wBAAM;;kDAC5C,IAAI;mDAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;kDAChC,KAAK,CAAC,GAAG;YACd;;QACA,SAAS,CAAC,CAAC;IACb;AACF;GAvCgB;;QACP,8KAAA,CAAA,WAAQ;;;AAyCV,SAAS,qBAAqB,SAAwB;QAAE,QAAA,iEAAgB;;IAC7E,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAsB;YAAW;SAAM;QAClD,OAAO;6CAAE;gBACP,IAAI,CAAC,WAAW,OAAO,EAAE;gBAEzB,4CAA4C;gBAC5C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC,YACP,EAAE,CAAC,cAAc,WACjB,GAAG,CAAC,YAAY,MAAM;gBAEzB,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,6BAA6B;gBAC7B,MAAM,iBAAyC,CAAC;gBAEhD,iBAAA,2BAAA,KAAM,OAAO;qDAAC,CAAA;wBACZ,IAAI,OAAO,QAAQ,EAAE;4BACnB,MAAM,WAAW,OAAO,QAAQ,CAAC,WAAW;4BAC5C,cAAc,CAAC,SAAS,GAAG,CAAC,cAAc,CAAC,SAAS,IAAI,CAAC,IAAI;wBAC/D;oBACF;;gBAEA,qCAAqC;gBACrC,OAAO,OAAO,OAAO,CAAC,gBACnB,GAAG;qDAAC;4BAAC,CAAC,UAAU,MAAM;+BAAM;4BAAE;4BAAU;wBAAM;;oDAC9C,IAAI;qDAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;oDAChC,KAAK,CAAC,GAAG;YACd;;QACA,SAAS,CAAC,CAAC;IACb;AACF;IAnCgB;;QACP,8KAAA,CAAA,WAAQ;;;AAqCV,SAAS,eAAe,SAAwB;;IACrD,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAgB;SAAU;QACrC,OAAO;uCAAE;gBACP,IAAI,CAAC,WAAW,OAAO,EAAE;gBAEzB,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAC5C,IAAI,CAAC,WACL,MAAM,CAAC,QACP,EAAE,CAAC,cAAc;gBAEpB,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,8BAA8B;gBAC9B,MAAM,cAAc,IAAI;gBAExB,oBAAA,8BAAA,QAAS,OAAO;+CAAC,CAAA;wBACf,IAAI,OAAO,IAAI,IAAI,MAAM,OAAO,CAAC,OAAO,IAAI,GAAG;4BAC7C,OAAO,IAAI,CAAC,OAAO;2DAAC,CAAA;oCAClB,IAAI,OAAO,QAAQ,YAAY,IAAI,IAAI,IAAI;wCACzC,2DAA2D;wCAC3D,MAAM,UAAU,IAAI,UAAU,CAAC,OAAO,IAAI,WAAW,KAAK,AAAC,IAAqB,OAAlB,IAAI,WAAW;wCAC7E,YAAY,GAAG,CAAC;oCAClB;gCACF;;wBACF;oBACF;;gBAEA,OAAO,MAAM,IAAI,CAAC,aAAa,IAAI;YACrC;;QACA,SAAS,CAAC,CAAC;IACb;AACF;IAlCgB;;QACP,8KAAA,CAAA,WAAQ;;;AAoCV,SAAS,iBAAiB,SAAwB;;IACvD,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAkB;SAAU;QACvC,OAAO;yCAAE;gBACP,IAAI,CAAC,WAAW,OAAO,EAAE;gBAEzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC,YACP,EAAE,CAAC,cAAc,WACjB,GAAG,CAAC,YAAY,MAAM;gBAEzB,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,gCAAgC;gBAChC,MAAM,gBAAgB,IAAI;gBAE1B,iBAAA,2BAAA,KAAM,OAAO;iDAAC,CAAA;wBACZ,IAAI,OAAO,QAAQ,EAAE;4BACnB,cAAc,GAAG,CAAC,OAAO,QAAQ,CAAC,WAAW;wBAC/C;oBACF;;gBAEA,OAAO,MAAM,IAAI,CAAC,eAAe,IAAI;YACvC;;QACA,SAAS,CAAC,CAAC;IACb;AACF;IA7BgB;;QACP,8KAAA,CAAA,WAAQ;;;AA+BV,SAAS,mBACd,SAAwB,EACxB,OAIC;;IAED,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAoB;YAAW;SAAQ;QAClD,OAAO;2CAAE;gBACP,IAAI,CAAC,WAAW,OAAO,EAAE;gBAEzB,IAAI,QAAQ,oIAAA,CAAA,kBAAQ,CACjB,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,cAAc,WACjB,KAAK,CAAC,eAAe;oBAAE,WAAW;gBAAK;gBAE1C,qBAAqB;gBACrB,IAAI,QAAQ,QAAQ,EAAE;oBACpB,QAAQ,MAAM,EAAE,CAAC,YAAY,QAAQ,QAAQ;gBAC/C;gBAEA,yBAAyB;gBACzB,IAAI,QAAQ,WAAW,EAAE;oBACvB,QAAQ,MAAM,KAAK,CAAC,eAAe,AAAC,IAAuB,OAApB,QAAQ,WAAW,EAAC;gBAC7D;gBAEA,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM;gBAEvC,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,yEAAyE;gBACzE,IAAI,kBAAkB,WAAW,EAAE;gBAEnC,IAAI,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,GAAG;oBACnD,kBAAkB,gBAAgB,MAAM;uDAAC,CAAA;4BACvC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO,IAAI,GAAG,OAAO;4BAExD,OAAO,QAAQ,QAAQ,CAAE,IAAI;+DAAC,CAAA,UAC5B,OAAO,IAAI,CAAC,IAAI;uEAAC,CAAC,MAChB,IAAI,WAAW,OAAO,QAAQ,WAAW;;;wBAG/C;;gBACF;gBAEA,OAAO;YACT;;QACA,SAAS,CAAC,CAAC;IACb;AACF;IAtDgB;;QAQP,8KAAA,CAAA,WAAQ;;;AAiDV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;iDAAE;oBAAO,EACjB,SAAS,EACT,QAAQ,EAIT;gBACC,0EAA0E;gBAC1E,gFAAgF;gBAChF,OAAO;oBAAE;oBAAW;gBAAS;YAC/B;;QACA,SAAS;iDAAE,CAAC;gBACV,sDAAsD;gBACtD,YAAY,iBAAiB,CAAC;oBAC5B,UAAU;wBAAC;wBAAoB,KAAK,SAAS;qBAAC;gBAChD;gBACA,YAAY,iBAAiB,CAAC;oBAC5B,UAAU;wBAAC;wBAAgB,KAAK,SAAS;qBAAC;gBAC5C;YACF;;IACF;AACF;IAzBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAyBb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;kDAAE;oBAAO,EACjB,SAAS,EACT,QAAQ,EAIT;gBACC,OAAO;oBAAE;oBAAW;gBAAS;YAC/B;;QACA,SAAS;kDAAE,CAAC;gBACV,wDAAwD;gBACxD,YAAY,iBAAiB,CAAC;oBAC5B,UAAU;wBAAC;wBAAsB,KAAK,SAAS;qBAAC;gBAClD;gBACA,YAAY,iBAAiB,CAAC;oBAC5B,UAAU;wBAAC;wBAAkB,KAAK,SAAS;qBAAC;gBAC9C;YACF;;IACF;AACF;IAvBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 2347, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/hooks/use-plans.ts"], "sourcesContent": ["'use client'\n\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\nimport { Database } from '@/lib/supabase'\n\ntype PlanType = Database['public']['Tables']['plan_types']['Row']\ntype UserPlan = Database['public']['Tables']['user_plans']['Row']\ntype UsageStats = Database['public']['Tables']['usage_stats']['Row']\n\n// Plan türlerini getir\nexport function usePlanTypes() {\n  return useQuery({\n    queryKey: ['plan-types'],\n    queryFn: async (): Promise<PlanType[]> => {\n      console.log(`📋 [USE_PLANS] Fetching plan types...`)\n      \n      const { data, error } = await supabase\n        .from('plan_types')\n        .select('*')\n        .eq('is_active', true)\n        .order('sort_order')\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] Plan types fetch failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] Plan types fetched:`, data?.length)\n      return data || []\n    },\n    staleTime: 5 * 60 * 1000, // 5 dakika\n  })\n}\n\n// Kullanıcının aktif planını getir\nexport function useUserActivePlan() {\n  return useQuery({\n    queryKey: ['user-active-plan'],\n    queryFn: async () => {\n      console.log(`👤 [USE_PLANS] Fetching user active plan...`)\n      \n      const { data, error } = await supabase.rpc('get_user_active_plan', {\n        user_uuid: (await supabase.auth.getUser()).data.user?.id\n      })\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] User active plan fetch failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] User active plan fetched:`, data?.[0])\n      return data?.[0] || null\n    },\n    staleTime: 2 * 60 * 1000, // 2 dakika\n  })\n}\n\n// Kullanıcının limitlerini kontrol et\nexport function useUserLimits() {\n  return useQuery({\n    queryKey: ['user-limits'],\n    queryFn: async () => {\n      console.log(`🔍 [USE_PLANS] Checking user limits...`)\n      \n      const { data, error } = await supabase.rpc('check_user_limits', {\n        user_uuid: (await supabase.auth.getUser()).data.user?.id\n      })\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] User limits check failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] User limits checked:`, data?.[0])\n      return data?.[0] || null\n    },\n    staleTime: 30 * 1000, // 30 saniye\n  })\n}\n\n// Kullanıcının plan geçmişini getir\nexport function useUserPlanHistory() {\n  return useQuery({\n    queryKey: ['user-plan-history'],\n    queryFn: async (): Promise<UserPlan[]> => {\n      console.log(`📜 [USE_PLANS] Fetching user plan history...`)\n      \n      const { data, error } = await supabase\n        .from('user_plans')\n        .select(`\n          *,\n          plan_types (\n            name,\n            display_name,\n            price_monthly,\n            price_yearly\n          )\n        `)\n        .order('created_at', { ascending: false })\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] User plan history fetch failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] User plan history fetched:`, data?.length)\n      return data || []\n    },\n    staleTime: 5 * 60 * 1000, // 5 dakika\n  })\n}\n\n// Kullanıcının kullanım istatistiklerini getir\nexport function useUsageStats(days: number = 30) {\n  return useQuery({\n    queryKey: ['usage-stats', days],\n    queryFn: async (): Promise<UsageStats[]> => {\n      console.log(`📊 [USE_PLANS] Fetching usage stats for ${days} days...`)\n      \n      const startDate = new Date()\n      startDate.setDate(startDate.getDate() - days)\n      \n      const { data, error } = await supabase\n        .from('usage_stats')\n        .select('*')\n        .gte('stat_date', startDate.toISOString().split('T')[0])\n        .order('stat_date', { ascending: false })\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] Usage stats fetch failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] Usage stats fetched:`, data?.length)\n      return data || []\n    },\n    staleTime: 5 * 60 * 1000, // 5 dakika\n  })\n}\n\n// Plan değiştir\nexport function useChangePlan() {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async ({ \n      planName, \n      billingCycle = 'monthly',\n      paymentReference \n    }: { \n      planName: string\n      billingCycle?: string\n      paymentReference?: string \n    }) => {\n      console.log(`🔄 [USE_PLANS] Changing plan to: ${planName}`)\n      \n      const { data, error } = await supabase.rpc('change_user_plan', {\n        user_uuid: (await supabase.auth.getUser()).data.user?.id,\n        new_plan_name: planName,\n        billing_cycle_param: billingCycle,\n        payment_reference_param: paymentReference\n      })\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] Plan change failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] Plan changed successfully:`, data)\n      return data\n    },\n    onSuccess: () => {\n      console.log(`🎉 [USE_PLANS] Plan change successful, invalidating queries`)\n      queryClient.invalidateQueries({ queryKey: ['user-active-plan'] })\n      queryClient.invalidateQueries({ queryKey: ['user-limits'] })\n      queryClient.invalidateQueries({ queryKey: ['user-plan-history'] })\n      queryClient.invalidateQueries({ queryKey: ['usage-stats'] })\n    },\n    onError: (error) => {\n      console.error(`💥 [USE_PLANS] Plan change error:`, error)\n    }\n  })\n}\n\n// Kullanım istatistiklerini güncelle\nexport function useUpdateUsageStats() {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async () => {\n      console.log(`📈 [USE_PLANS] Updating usage stats...`)\n      \n      const { error } = await supabase.rpc('update_usage_stats', {\n        user_uuid: (await supabase.auth.getUser()).data.user?.id\n      })\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] Usage stats update failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] Usage stats updated successfully`)\n    },\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: ['usage-stats'] })\n      queryClient.invalidateQueries({ queryKey: ['user-limits'] })\n    }\n  })\n}\n\n// Plan limiti kontrol fonksiyonu\nexport async function checkPlanLimit(action: 'create_project' | 'create_prompt'): Promise<boolean> {\n  try {\n    const { data, error } = await supabase.rpc('check_user_limits', {\n      user_uuid: (await supabase.auth.getUser()).data.user?.id\n    })\n\n    if (error) {\n      console.error(`❌ [USE_PLANS] Limit check failed:`, error)\n      return false\n    }\n\n    const limits = data?.[0]\n    if (!limits) return false\n\n    switch (action) {\n      case 'create_project':\n        return limits.can_create_project\n      case 'create_prompt':\n        return limits.can_create_prompt\n      default:\n        return false\n    }\n  } catch (error) {\n    console.error(`❌ [USE_PLANS] Limit check error:`, error)\n    return false\n  }\n}\n\n// Plan özelliği kontrol fonksiyonu\nexport function usePlanFeature(featureName: string) {\n  const { data: activePlan } = useUserActivePlan()\n\n  return {\n    hasFeature: activePlan?.features?.[featureName] === true,\n    featureValue: activePlan?.features?.[featureName],\n    planName: activePlan?.plan_name,\n    displayName: activePlan?.display_name\n  }\n}\n\n// Kullanıcının deneme süresi bilgilerini getir\nexport function useUserTrialInfo() {\n  return useQuery({\n    queryKey: ['user-trial-info'],\n    queryFn: async () => {\n      console.log(`🔍 [USE_PLANS] Fetching user trial info...`)\n\n      const { data, error } = await supabase.rpc('get_user_trial_info', {\n        user_uuid: (await supabase.auth.getUser()).data.user?.id\n      })\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] User trial info fetch failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] User trial info fetched:`, data?.[0])\n      return data?.[0] || null\n    },\n    staleTime: 30 * 1000, // 30 saniye\n  })\n}\n\n// İptal uygunluğunu kontrol et\nexport function useCancellationEligibility() {\n  return useQuery({\n    queryKey: ['cancellation-eligibility'],\n    queryFn: async () => {\n      console.log(`🔍 [USE_PLANS] Checking cancellation eligibility...`)\n\n      const { data, error } = await supabase.rpc('get_cancellation_eligibility', {\n        user_uuid: (await supabase.auth.getUser()).data.user?.id\n      })\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] Cancellation eligibility check failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] Cancellation eligibility checked:`, data?.[0])\n      return data?.[0] || null\n    },\n    staleTime: 30 * 1000, // 30 saniye\n  })\n}\n\n// Planı iptal et\nexport function useCancelPlan() {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async ({\n      cancellationReason,\n      requestRefund = false\n    }: {\n      cancellationReason?: string\n      requestRefund?: boolean\n    }) => {\n      console.log(`🚫 [USE_PLANS] Cancelling plan with reason: ${cancellationReason}`)\n\n      const { data, error } = await supabase.rpc('cancel_user_plan', {\n        user_uuid: (await supabase.auth.getUser()).data.user?.id,\n        cancellation_reason_param: cancellationReason,\n        request_refund: requestRefund\n      })\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] Plan cancellation failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] Plan cancelled successfully:`, data?.[0])\n      return data?.[0]\n    },\n    onSuccess: (data) => {\n      console.log(`🎉 [USE_PLANS] Plan cancellation successful, invalidating queries`)\n      queryClient.invalidateQueries({ queryKey: ['user-active-plan'] })\n      queryClient.invalidateQueries({ queryKey: ['user-limits'] })\n      queryClient.invalidateQueries({ queryKey: ['user-plan-history'] })\n      queryClient.invalidateQueries({ queryKey: ['user-trial-info'] })\n      queryClient.invalidateQueries({ queryKey: ['cancellation-eligibility'] })\n      queryClient.invalidateQueries({ queryKey: ['usage-stats'] })\n    },\n    onError: (error) => {\n      console.error(`💥 [USE_PLANS] Plan cancellation error:`, error)\n    }\n  })\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA;AAAA;AAAA;AACA;;AAHA;;;AAWO,SAAS;;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAa;QACxB,OAAO;qCAAE;gBACP,QAAQ,GAAG,CAAE;gBAEb,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,MAChB,KAAK,CAAC;gBAET,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAE,0CAAyC;oBACxD,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,QAAQ,GAAG,CAAE,qCAAoC,iBAAA,2BAAA,KAAM,MAAM;gBAC7D,OAAO,QAAQ,EAAE;YACnB;;QACA,WAAW,IAAI,KAAK;IACtB;AACF;GAtBgB;;QACP,8KAAA,CAAA,WAAQ;;;AAwBV,SAAS;;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAmB;QAC9B,OAAO;0CAAE;oBAIM;gBAHb,QAAQ,GAAG,CAAE;gBAEb,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,wBAAwB;oBACjE,SAAS,GAAE,aAAA,CAAC,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,IAAI,cAAzC,iCAAA,WAA2C,EAAE;gBAC1D;gBAEA,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAE,gDAA+C;oBAC9D,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,QAAQ,GAAG,CAAE,2CAA0C,iBAAA,2BAAA,IAAM,CAAC,EAAE;gBAChE,OAAO,CAAA,iBAAA,2BAAA,IAAM,CAAC,EAAE,KAAI;YACtB;;QACA,WAAW,IAAI,KAAK;IACtB;AACF;IApBgB;;QACP,8KAAA,CAAA,WAAQ;;;AAsBV,SAAS;;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAc;QACzB,OAAO;sCAAE;oBAIM;gBAHb,QAAQ,GAAG,CAAE;gBAEb,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,qBAAqB;oBAC9D,SAAS,GAAE,aAAA,CAAC,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,IAAI,cAAzC,iCAAA,WAA2C,EAAE;gBAC1D;gBAEA,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAE,2CAA0C;oBACzD,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,QAAQ,GAAG,CAAE,sCAAqC,iBAAA,2BAAA,IAAM,CAAC,EAAE;gBAC3D,OAAO,CAAA,iBAAA,2BAAA,IAAM,CAAC,EAAE,KAAI;YACtB;;QACA,WAAW,KAAK;IAClB;AACF;IApBgB;;QACP,8KAAA,CAAA,WAAQ;;;AAsBV,SAAS;;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAoB;QAC/B,OAAO;2CAAE;gBACP,QAAQ,GAAG,CAAE;gBAEb,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAE,qKASR,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAM;gBAE1C,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAE,iDAAgD;oBAC/D,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,QAAQ,GAAG,CAAE,4CAA2C,iBAAA,2BAAA,KAAM,MAAM;gBACpE,OAAO,QAAQ,EAAE;YACnB;;QACA,WAAW,IAAI,KAAK;IACtB;AACF;IA7BgB;;QACP,8KAAA,CAAA,WAAQ;;;AA+BV,SAAS;QAAc,OAAA,iEAAe;;IAC3C,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAe;SAAK;QAC/B,OAAO;sCAAE;gBACP,QAAQ,GAAG,CAAC,AAAC,2CAA+C,OAAL,MAAK;gBAE5D,MAAM,YAAY,IAAI;gBACtB,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;gBAExC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAC,KACP,GAAG,CAAC,aAAa,UAAU,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EACtD,KAAK,CAAC,aAAa;oBAAE,WAAW;gBAAM;gBAEzC,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAE,2CAA0C;oBACzD,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,QAAQ,GAAG,CAAE,sCAAqC,iBAAA,2BAAA,KAAM,MAAM;gBAC9D,OAAO,QAAQ,EAAE;YACnB;;QACA,WAAW,IAAI,KAAK;IACtB;AACF;IAzBgB;;QACP,8KAAA,CAAA,WAAQ;;;AA2BV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;yCAAE;oBAAO,EACjB,QAAQ,EACR,eAAe,SAAS,EACxB,gBAAgB,EAKjB;oBAIc;gBAHb,QAAQ,GAAG,CAAC,AAAC,oCAA4C,OAAT;gBAEhD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,oBAAoB;oBAC7D,SAAS,GAAE,aAAA,CAAC,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,IAAI,cAAzC,iCAAA,WAA2C,EAAE;oBACxD,eAAe;oBACf,qBAAqB;oBACrB,yBAAyB;gBAC3B;gBAEA,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAE,qCAAoC;oBACnD,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,QAAQ,GAAG,CAAE,4CAA2C;gBACxD,OAAO;YACT;;QACA,SAAS;yCAAE;gBACT,QAAQ,GAAG,CAAE;gBACb,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAmB;gBAAC;gBAC/D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAc;gBAAC;gBAC1D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAoB;gBAAC;gBAChE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAc;gBAAC;YAC5D;;QACA,OAAO;yCAAE,CAAC;gBACR,QAAQ,KAAK,CAAE,qCAAoC;YACrD;;IACF;AACF;IAzCgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAyCb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;+CAAE;oBAIG;gBAHb,QAAQ,GAAG,CAAE;gBAEb,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,sBAAsB;oBACzD,SAAS,GAAE,aAAA,CAAC,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,IAAI,cAAzC,iCAAA,WAA2C,EAAE;gBAC1D;gBAEA,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAE,4CAA2C;oBAC1D,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,QAAQ,GAAG,CAAE;YACf;;QACA,SAAS;+CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAc;gBAAC;gBAC1D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAc;gBAAC;YAC5D;;IACF;AACF;IAvBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAuBb,eAAe,eAAe,MAA0C;IAC7E,IAAI;YAEW;QADb,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,qBAAqB;YAC9D,SAAS,GAAE,aAAA,CAAC,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,IAAI,cAAzC,iCAAA,WAA2C,EAAE;QAC1D;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAE,qCAAoC;YACnD,OAAO;QACT;QAEA,MAAM,SAAS,iBAAA,2BAAA,IAAM,CAAC,EAAE;QACxB,IAAI,CAAC,QAAQ,OAAO;QAEpB,OAAQ;YACN,KAAK;gBACH,OAAO,OAAO,kBAAkB;YAClC,KAAK;gBACH,OAAO,OAAO,iBAAiB;YACjC;gBACE,OAAO;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAE,oCAAmC;QAClD,OAAO;IACT;AACF;AAGO,SAAS,eAAe,WAAmB;QAIlC,sBACE;;IAJhB,MAAM,EAAE,MAAM,UAAU,EAAE,GAAG;IAE7B,OAAO;QACL,YAAY,CAAA,uBAAA,kCAAA,uBAAA,WAAY,QAAQ,cAApB,2CAAA,oBAAsB,CAAC,YAAY,MAAK;QACpD,YAAY,EAAE,uBAAA,kCAAA,wBAAA,WAAY,QAAQ,cAApB,4CAAA,qBAAsB,CAAC,YAAY;QACjD,QAAQ,EAAE,uBAAA,iCAAA,WAAY,SAAS;QAC/B,WAAW,EAAE,uBAAA,iCAAA,WAAY,YAAY;IACvC;AACF;IATgB;;QACe;;;AAWxB,SAAS;;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAkB;QAC7B,OAAO;yCAAE;oBAIM;gBAHb,QAAQ,GAAG,CAAE;gBAEb,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,uBAAuB;oBAChE,SAAS,GAAE,aAAA,CAAC,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,IAAI,cAAzC,iCAAA,WAA2C,EAAE;gBAC1D;gBAEA,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAE,+CAA8C;oBAC7D,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,QAAQ,GAAG,CAAE,0CAAyC,iBAAA,2BAAA,IAAM,CAAC,EAAE;gBAC/D,OAAO,CAAA,iBAAA,2BAAA,IAAM,CAAC,EAAE,KAAI;YACtB;;QACA,WAAW,KAAK;IAClB;AACF;IApBgB;;QACP,8KAAA,CAAA,WAAQ;;;AAsBV,SAAS;;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAA2B;QACtC,OAAO;mDAAE;oBAIM;gBAHb,QAAQ,GAAG,CAAE;gBAEb,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,gCAAgC;oBACzE,SAAS,GAAE,aAAA,CAAC,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,IAAI,cAAzC,iCAAA,WAA2C,EAAE;gBAC1D;gBAEA,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAE,wDAAuD;oBACtE,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,QAAQ,GAAG,CAAE,mDAAkD,iBAAA,2BAAA,IAAM,CAAC,EAAE;gBACxE,OAAO,CAAA,iBAAA,2BAAA,IAAM,CAAC,EAAE,KAAI;YACtB;;QACA,WAAW,KAAK;IAClB;AACF;IApBgB;;QACP,8KAAA,CAAA,WAAQ;;;AAsBV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;yCAAE;oBAAO,EACjB,kBAAkB,EAClB,gBAAgB,KAAK,EAItB;oBAIc;gBAHb,QAAQ,GAAG,CAAC,AAAC,+CAAiE,OAAnB;gBAE3D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,oBAAoB;oBAC7D,SAAS,GAAE,aAAA,CAAC,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,IAAI,cAAzC,iCAAA,WAA2C,EAAE;oBACxD,2BAA2B;oBAC3B,gBAAgB;gBAClB;gBAEA,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAE,2CAA0C;oBACzD,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,QAAQ,GAAG,CAAE,8CAA6C,iBAAA,2BAAA,IAAM,CAAC,EAAE;gBACnE,OAAO,iBAAA,2BAAA,IAAM,CAAC,EAAE;YAClB;;QACA,SAAS;yCAAE,CAAC;gBACV,QAAQ,GAAG,CAAE;gBACb,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAmB;gBAAC;gBAC/D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAc;gBAAC;gBAC1D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAoB;gBAAC;gBAChE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAkB;gBAAC;gBAC9D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAA2B;gBAAC;gBACvE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAc;gBAAC;YAC5D;;QACA,OAAO;yCAAE,CAAC;gBACR,QAAQ,KAAK,CAAE,2CAA0C;YAC3D;;IACF;AACF;KAxCgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 2792, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/limit-warning.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON><PERSON><PERSON>riangle, Crown, TrendingUp, X } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { useState } from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface LimitWarningProps {\n  type: 'project' | 'prompt'\n  current: number\n  max: number\n  planName: string\n  onUpgrade?: () => void\n  className?: string\n}\n\nexport function LimitWarning({ \n  type, \n  current, \n  max, \n  planName, \n  onUpgrade,\n  className \n}: LimitWarningProps) {\n  const [dismissed, setDismissed] = useState(false)\n\n  if (dismissed || max === -1) return null\n\n  const percentage = (current / max) * 100\n  const isNearLimit = percentage >= 80\n  const isAtLimit = current >= max\n\n  if (!isNearLimit) return null\n\n  const getWarningLevel = () => {\n    if (isAtLimit) return 'error'\n    if (percentage >= 90) return 'warning'\n    return 'info'\n  }\n\n  const getWarningColors = () => {\n    const level = getWarningLevel()\n    switch (level) {\n      case 'error':\n        return 'bg-red-50 border-red-200 text-red-800'\n      case 'warning':\n        return 'bg-yellow-50 border-yellow-200 text-yellow-800'\n      case 'info':\n        return 'bg-blue-50 border-blue-200 text-blue-800'\n      default:\n        return 'bg-gray-50 border-gray-200 text-gray-800'\n    }\n  }\n\n  const getIconColors = () => {\n    const level = getWarningLevel()\n    switch (level) {\n      case 'error':\n        return 'text-red-500'\n      case 'warning':\n        return 'text-yellow-500'\n      case 'info':\n        return 'text-blue-500'\n      default:\n        return 'text-gray-500'\n    }\n  }\n\n  const getTitle = () => {\n    if (isAtLimit) {\n      return type === 'project' ? 'Proje limiti doldu!' : 'Prompt limiti doldu!'\n    }\n    return type === 'project' ? 'Proje limitine yaklaşıyorsunuz' : 'Prompt limitine yaklaşıyorsunuz'\n  }\n\n  const getDescription = () => {\n    if (isAtLimit) {\n      return type === 'project' \n        ? 'Yeni proje oluşturmak için planınızı yükseltin.'\n        : 'Bu projede yeni prompt oluşturmak için planınızı yükseltin.'\n    }\n    \n    const remaining = max - current\n    return type === 'project'\n      ? `${remaining} proje hakkınız kaldı. Daha fazla proje için planınızı yükseltin.`\n      : `Bu projede ${remaining} prompt hakkınız kaldı. Daha fazla prompt için planınızı yükseltin.`\n  }\n\n  const getSuggestedPlan = () => {\n    if (planName === 'free') return 'Profesyonel'\n    if (planName === 'professional') return 'Kurumsal'\n    return null\n  }\n\n  return (\n    <Card className={cn('border-l-4', getWarningColors(), className)}>\n      <CardContent className=\"p-4\">\n        <div className=\"flex items-start gap-3\">\n          <AlertTriangle className={cn('h-5 w-5 mt-0.5 flex-shrink-0', getIconColors())} />\n          \n          <div className=\"flex-1 space-y-2\">\n            <div className=\"flex items-center justify-between\">\n              <h4 className=\"font-medium text-sm\">\n                {getTitle()}\n              </h4>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"h-6 w-6 p-0 hover:bg-transparent\"\n                onClick={() => setDismissed(true)}\n              >\n                <X className=\"h-4 w-4\" />\n              </Button>\n            </div>\n            \n            <p className=\"text-sm opacity-90\">\n              {getDescription()}\n            </p>\n\n            <div className=\"flex items-center justify-between pt-2\">\n              <div className=\"flex items-center gap-2\">\n                <Badge variant=\"outline\" className=\"text-xs\">\n                  {current}/{max} kullanıldı\n                </Badge>\n                <Badge variant=\"outline\" className=\"text-xs\">\n                  %{Math.round(percentage)} dolu\n                </Badge>\n              </div>\n\n              {onUpgrade && getSuggestedPlan() && (\n                <Button\n                  size=\"sm\"\n                  onClick={onUpgrade}\n                  className=\"h-7 text-xs\"\n                >\n                  <Crown className=\"h-3 w-3 mr-1\" />\n                  {getSuggestedPlan()} Plan\n                </Button>\n              )}\n            </div>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n\n// Hızlı kullanım için özel bileşenler\nexport function ProjectLimitWarning({ \n  current, \n  max, \n  planName, \n  onUpgrade,\n  className \n}: Omit<LimitWarningProps, 'type'>) {\n  return (\n    <LimitWarning\n      type=\"project\"\n      current={current}\n      max={max}\n      planName={planName}\n      onUpgrade={onUpgrade}\n      className={className}\n    />\n  )\n}\n\nexport function PromptLimitWarning({ \n  current, \n  max, \n  planName, \n  onUpgrade,\n  className \n}: Omit<LimitWarningProps, 'type'>) {\n  return (\n    <LimitWarning\n      type=\"prompt\"\n      current={current}\n      max={max}\n      planName={planName}\n      onUpgrade={onUpgrade}\n      className={className}\n    />\n  )\n}\n\n// Inline uyarı bileşeni (daha kompakt)\nexport function InlineLimitWarning({ \n  type, \n  current, \n  max, \n  onUpgrade \n}: Pick<LimitWarningProps, 'type' | 'current' | 'max' | 'onUpgrade'>) {\n  if (max === -1 || current < max) return null\n\n  return (\n    <div className=\"flex items-center gap-2 p-2 bg-red-50 border border-red-200 rounded text-red-800 text-sm\">\n      <AlertTriangle className=\"h-4 w-4 text-red-500\" />\n      <span className=\"flex-1\">\n        {type === 'project' ? 'Proje limiti doldu' : 'Prompt limiti doldu'}\n      </span>\n      {onUpgrade && (\n        <Button size=\"sm\" variant=\"outline\" onClick={onUpgrade} className=\"h-6 text-xs\">\n          <TrendingUp className=\"h-3 w-3 mr-1\" />\n          Yükselt\n        </Button>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAkBO,SAAS,aAAa,KAOT;QAPS,EAC3B,IAAI,EACJ,OAAO,EACP,GAAG,EACH,QAAQ,EACR,SAAS,EACT,SAAS,EACS,GAPS;;IAQ3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,IAAI,aAAa,QAAQ,CAAC,GAAG,OAAO;IAEpC,MAAM,aAAa,AAAC,UAAU,MAAO;IACrC,MAAM,cAAc,cAAc;IAClC,MAAM,YAAY,WAAW;IAE7B,IAAI,CAAC,aAAa,OAAO;IAEzB,MAAM,kBAAkB;QACtB,IAAI,WAAW,OAAO;QACtB,IAAI,cAAc,IAAI,OAAO;QAC7B,OAAO;IACT;IAEA,MAAM,mBAAmB;QACvB,MAAM,QAAQ;QACd,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,MAAM,QAAQ;QACd,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,WAAW;QACf,IAAI,WAAW;YACb,OAAO,SAAS,YAAY,wBAAwB;QACtD;QACA,OAAO,SAAS,YAAY,mCAAmC;IACjE;IAEA,MAAM,iBAAiB;QACrB,IAAI,WAAW;YACb,OAAO,SAAS,YACZ,oDACA;QACN;QAEA,MAAM,YAAY,MAAM;QACxB,OAAO,SAAS,YACZ,AAAC,GAAY,OAAV,WAAU,uEACb,AAAC,cAAuB,OAAV,WAAU;IAC9B;IAEA,MAAM,mBAAmB;QACvB,IAAI,aAAa,QAAQ,OAAO;QAChC,IAAI,aAAa,gBAAgB,OAAO;QACxC,OAAO;IACT;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc,oBAAoB;kBACpD,cAAA,6LAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,2NAAA,CAAA,gBAAa;wBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;;;;;;kCAE7D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDACX;;;;;;kDAEH,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,aAAa;kDAE5B,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIjB,6LAAC;gCAAE,WAAU;0CACV;;;;;;0CAGH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;;oDAChC;oDAAQ;oDAAE;oDAAI;;;;;;;0DAEjB,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;;oDAAU;oDACzC,KAAK,KAAK,CAAC;oDAAY;;;;;;;;;;;;;oCAI5B,aAAa,oCACZ,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAChB;4CAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStC;GAjIgB;KAAA;AAoIT,SAAS,oBAAoB,KAMF;QANE,EAClC,OAAO,EACP,GAAG,EACH,QAAQ,EACR,SAAS,EACT,SAAS,EACuB,GANE;IAOlC,qBACE,6LAAC;QACC,MAAK;QACL,SAAS;QACT,KAAK;QACL,UAAU;QACV,WAAW;QACX,WAAW;;;;;;AAGjB;MAjBgB;AAmBT,SAAS,mBAAmB,KAMD;QANC,EACjC,OAAO,EACP,GAAG,EACH,QAAQ,EACR,SAAS,EACT,SAAS,EACuB,GANC;IAOjC,qBACE,6LAAC;QACC,MAAK;QACL,SAAS;QACT,KAAK;QACL,UAAU;QACV,WAAW;QACX,WAAW;;;;;;AAGjB;MAjBgB;AAoBT,SAAS,mBAAmB,KAKiC;QALjC,EACjC,IAAI,EACJ,OAAO,EACP,GAAG,EACH,SAAS,EACyD,GALjC;IAMjC,IAAI,QAAQ,CAAC,KAAK,UAAU,KAAK,OAAO;IAExC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,2NAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;0BACzB,6LAAC;gBAAK,WAAU;0BACb,SAAS,YAAY,uBAAuB;;;;;;YAE9C,2BACC,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAK;gBAAK,SAAQ;gBAAU,SAAS;gBAAW,WAAU;;kCAChE,6LAAC,qNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;;;;;;;AAMjD;MAtBgB", "debugId": null}}, {"offset": {"line": 3119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/hashtag-utils.ts"], "sourcesContent": ["/**\n * Utility functions for hashtag and category management\n */\n\n// Simple memoization cache for performance optimization\nconst parseCache = new Map<string, { category: string | null; tags: string[] }>();\nconst CACHE_SIZE_LIMIT = 100; // Prevent memory leaks\n\n// Extract hashtags from text (words starting with #)\nexport function extractHashtags(text: string): string[] {\n  const hashtagRegex = /#[\\w\\u00C0-\\u017F]+/g;\n  const matches = text.match(hashtagRegex);\n  return matches ? matches.map(tag => tag.toLowerCase()) : [];\n}\n\n// Extract folder paths from text (words starting with /)\nexport function extractFolderPaths(text: string): string[] {\n  const folderRegex = /\\/[\\w\\u00C0-\\u017F\\/]+/g;\n  const matches = text.match(folderRegex);\n  return matches ? matches.map(path => path.toLowerCase()) : [];\n}\n\n// Clean and validate hashtag\nexport function cleanHashtag(hashtag: string): string {\n  // Remove # if present, clean whitespace, convert to lowercase\n  return hashtag.replace(/^#/, '').trim().toLowerCase();\n}\n\n// Clean and validate folder path\nexport function cleanFolderPath(path: string): string {\n  // Ensure starts with /, clean multiple slashes, convert to lowercase\n  let cleaned = path.trim().toLowerCase();\n  if (!cleaned.startsWith('/')) {\n    cleaned = '/' + cleaned;\n  }\n  // Remove multiple consecutive slashes\n  cleaned = cleaned.replace(/\\/+/g, '/');\n  // Remove trailing slash unless it's root\n  if (cleaned.length > 1 && cleaned.endsWith('/')) {\n    cleaned = cleaned.slice(0, -1);\n  }\n  return cleaned;\n}\n\n// Format hashtag for display (with #)\nexport function formatHashtag(hashtag: string): string {\n  const cleaned = cleanHashtag(hashtag);\n  return cleaned ? `#${cleaned}` : '';\n}\n\n// Parse hashtags and folder paths from prompt text (LEGACY - removes text)\nexport function parsePromptCategories(text: string): {\n  hashtags: string[];\n  folderPaths: string[];\n  cleanText: string;\n} {\n  const hashtags = extractHashtags(text);\n  const folderPaths = extractFolderPaths(text);\n\n  // Remove hashtags and folder paths from text\n  let cleanText = text;\n  hashtags.forEach(tag => {\n    cleanText = cleanText.replace(new RegExp(tag, 'gi'), '');\n  });\n  folderPaths.forEach(path => {\n    cleanText = cleanText.replace(new RegExp(path.replace(/\\//g, '\\\\/'), 'gi'), '');\n  });\n\n  // Clean up extra whitespace\n  cleanText = cleanText.replace(/\\s+/g, ' ').trim();\n\n  return {\n    hashtags: hashtags.map(cleanHashtag),\n    folderPaths: folderPaths.map(cleanFolderPath),\n    cleanText\n  };\n}\n\n// NEW: Parse hashtags and folder paths while preserving original text\nexport function parsePromptCategoriesPreserveText(text: string): {\n  hashtags: string[];\n  folderPaths: string[];\n  originalText: string;\n} {\n  const hashtags = extractHashtags(text);\n  const folderPaths = extractFolderPaths(text);\n\n  return {\n    hashtags: hashtags.map(cleanHashtag),\n    folderPaths: folderPaths.map(cleanFolderPath),\n    originalText: text // Preserve original text exactly as typed\n  };\n}\n\n// NEW: Extract only categories and tags for database storage (OPTIMIZED with MEMOIZATION)\nexport function extractCategoriesOnly(text: string): {\n  category: string | null;\n  tags: string[];\n} {\n  // Early return for empty text\n  if (!text || text.trim().length === 0) {\n    return { category: null, tags: [] };\n  }\n\n  // Check cache first\n  const cacheKey = text.trim();\n  if (parseCache.has(cacheKey)) {\n    return parseCache.get(cacheKey)!;\n  }\n\n  // Use more efficient regex with single pass\n  const hashtagMatches = text.match(/#[\\w\\u00C0-\\u017F]+/g);\n  const folderMatches = text.match(/\\/[\\w\\u00C0-\\u017F\\/]+/g);\n\n  // Use first folder path as category, or null if none\n  const category = folderMatches && folderMatches.length > 0\n    ? cleanFolderPath(folderMatches[0])\n    : null;\n\n  // Clean hashtags for tags (avoid unnecessary map if no hashtags)\n  const tags = hashtagMatches\n    ? hashtagMatches.map(cleanHashtag)\n    : [];\n\n  const result = { category, tags };\n\n  // Cache the result (with size limit)\n  if (parseCache.size >= CACHE_SIZE_LIMIT) {\n    // Remove oldest entry (simple LRU)\n    const firstKey = parseCache.keys().next().value;\n    if (firstKey !== undefined) {\n      parseCache.delete(firstKey);\n    }\n  }\n  parseCache.set(cacheKey, result);\n\n  return result;\n}\n\n// Merge hashtags with existing tags\nexport function mergeHashtags(existingTags: string[], newHashtags: string[]): string[] {\n  const allTags = [...existingTags, ...newHashtags];\n  // Remove duplicates and empty tags\n  return [...new Set(allTags.filter(tag => tag.trim().length > 0))];\n}\n\n// Get folder hierarchy from path\nexport function getFolderHierarchy(path: string): string[] {\n  if (!path || path === '/') return ['/'];\n  \n  const parts = path.split('/').filter(part => part.length > 0);\n  const hierarchy = ['/'];\n  \n  let currentPath = '';\n  parts.forEach(part => {\n    currentPath += '/' + part;\n    hierarchy.push(currentPath);\n  });\n  \n  return hierarchy;\n}\n\n// Get parent folder from path\nexport function getParentFolder(path: string): string {\n  if (!path || path === '/') return '/';\n  \n  const lastSlashIndex = path.lastIndexOf('/');\n  if (lastSlashIndex <= 0) return '/';\n  \n  return path.substring(0, lastSlashIndex) || '/';\n}\n\n// Get folder name from path\nexport function getFolderName(path: string): string {\n  if (!path || path === '/') return 'Root';\n  \n  const lastSlashIndex = path.lastIndexOf('/');\n  return path.substring(lastSlashIndex + 1) || 'Root';\n}\n\n// Validate hashtag format\nexport function isValidHashtag(hashtag: string): boolean {\n  const cleaned = cleanHashtag(hashtag);\n  return /^[\\w\\u00C0-\\u017F]+$/.test(cleaned) && cleaned.length > 0;\n}\n\n// Validate folder path format\nexport function isValidFolderPath(path: string): boolean {\n  const cleaned = cleanFolderPath(path);\n  return /^\\/[\\w\\u00C0-\\u017F\\/]*$/.test(cleaned);\n}\n\n// Get hashtag suggestions based on existing hashtags\nexport function getHashtagSuggestions(\n  input: string, \n  existingHashtags: string[], \n  limit: number = 5\n): string[] {\n  const cleanInput = cleanHashtag(input);\n  if (!cleanInput) return [];\n  \n  return existingHashtags\n    .filter(tag => tag.toLowerCase().includes(cleanInput))\n    .slice(0, limit);\n}\n\n// Get folder suggestions based on existing folders\nexport function getFolderSuggestions(\n  input: string, \n  existingFolders: string[], \n  limit: number = 5\n): string[] {\n  const cleanInput = input.toLowerCase();\n  if (!cleanInput) return [];\n  \n  return existingFolders\n    .filter(folder => folder.toLowerCase().includes(cleanInput))\n    .slice(0, limit);\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,wDAAwD;;;;;;;;;;;;;;;;;;;AACxD,MAAM,aAAa,IAAI;AACvB,MAAM,mBAAmB,KAAK,uBAAuB;AAG9C,SAAS,gBAAgB,IAAY;IAC1C,MAAM,eAAe;IACrB,MAAM,UAAU,KAAK,KAAK,CAAC;IAC3B,OAAO,UAAU,QAAQ,GAAG,CAAC,CAAA,MAAO,IAAI,WAAW,MAAM,EAAE;AAC7D;AAGO,SAAS,mBAAmB,IAAY;IAC7C,MAAM,cAAc;IACpB,MAAM,UAAU,KAAK,KAAK,CAAC;IAC3B,OAAO,UAAU,QAAQ,GAAG,CAAC,CAAA,OAAQ,KAAK,WAAW,MAAM,EAAE;AAC/D;AAGO,SAAS,aAAa,OAAe;IAC1C,8DAA8D;IAC9D,OAAO,QAAQ,OAAO,CAAC,MAAM,IAAI,IAAI,GAAG,WAAW;AACrD;AAGO,SAAS,gBAAgB,IAAY;IAC1C,qEAAqE;IACrE,IAAI,UAAU,KAAK,IAAI,GAAG,WAAW;IACrC,IAAI,CAAC,QAAQ,UAAU,CAAC,MAAM;QAC5B,UAAU,MAAM;IAClB;IACA,sCAAsC;IACtC,UAAU,QAAQ,OAAO,CAAC,QAAQ;IAClC,yCAAyC;IACzC,IAAI,QAAQ,MAAM,GAAG,KAAK,QAAQ,QAAQ,CAAC,MAAM;QAC/C,UAAU,QAAQ,KAAK,CAAC,GAAG,CAAC;IAC9B;IACA,OAAO;AACT;AAGO,SAAS,cAAc,OAAe;IAC3C,MAAM,UAAU,aAAa;IAC7B,OAAO,UAAU,AAAC,IAAW,OAAR,WAAY;AACnC;AAGO,SAAS,sBAAsB,IAAY;IAKhD,MAAM,WAAW,gBAAgB;IACjC,MAAM,cAAc,mBAAmB;IAEvC,6CAA6C;IAC7C,IAAI,YAAY;IAChB,SAAS,OAAO,CAAC,CAAA;QACf,YAAY,UAAU,OAAO,CAAC,IAAI,OAAO,KAAK,OAAO;IACvD;IACA,YAAY,OAAO,CAAC,CAAA;QAClB,YAAY,UAAU,OAAO,CAAC,IAAI,OAAO,KAAK,OAAO,CAAC,OAAO,QAAQ,OAAO;IAC9E;IAEA,4BAA4B;IAC5B,YAAY,UAAU,OAAO,CAAC,QAAQ,KAAK,IAAI;IAE/C,OAAO;QACL,UAAU,SAAS,GAAG,CAAC;QACvB,aAAa,YAAY,GAAG,CAAC;QAC7B;IACF;AACF;AAGO,SAAS,kCAAkC,IAAY;IAK5D,MAAM,WAAW,gBAAgB;IACjC,MAAM,cAAc,mBAAmB;IAEvC,OAAO;QACL,UAAU,SAAS,GAAG,CAAC;QACvB,aAAa,YAAY,GAAG,CAAC;QAC7B,cAAc,KAAK,0CAA0C;IAC/D;AACF;AAGO,SAAS,sBAAsB,IAAY;IAIhD,8BAA8B;IAC9B,IAAI,CAAC,QAAQ,KAAK,IAAI,GAAG,MAAM,KAAK,GAAG;QACrC,OAAO;YAAE,UAAU;YAAM,MAAM,EAAE;QAAC;IACpC;IAEA,oBAAoB;IACpB,MAAM,WAAW,KAAK,IAAI;IAC1B,IAAI,WAAW,GAAG,CAAC,WAAW;QAC5B,OAAO,WAAW,GAAG,CAAC;IACxB;IAEA,4CAA4C;IAC5C,MAAM,iBAAiB,KAAK,KAAK,CAAC;IAClC,MAAM,gBAAgB,KAAK,KAAK,CAAC;IAEjC,qDAAqD;IACrD,MAAM,WAAW,iBAAiB,cAAc,MAAM,GAAG,IACrD,gBAAgB,aAAa,CAAC,EAAE,IAChC;IAEJ,iEAAiE;IACjE,MAAM,OAAO,iBACT,eAAe,GAAG,CAAC,gBACnB,EAAE;IAEN,MAAM,SAAS;QAAE;QAAU;IAAK;IAEhC,qCAAqC;IACrC,IAAI,WAAW,IAAI,IAAI,kBAAkB;QACvC,mCAAmC;QACnC,MAAM,WAAW,WAAW,IAAI,GAAG,IAAI,GAAG,KAAK;QAC/C,IAAI,aAAa,WAAW;YAC1B,WAAW,MAAM,CAAC;QACpB;IACF;IACA,WAAW,GAAG,CAAC,UAAU;IAEzB,OAAO;AACT;AAGO,SAAS,cAAc,YAAsB,EAAE,WAAqB;IACzE,MAAM,UAAU;WAAI;WAAiB;KAAY;IACjD,mCAAmC;IACnC,OAAO;WAAI,IAAI,IAAI,QAAQ,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,GAAG,MAAM,GAAG;KAAI;AACnE;AAGO,SAAS,mBAAmB,IAAY;IAC7C,IAAI,CAAC,QAAQ,SAAS,KAAK,OAAO;QAAC;KAAI;IAEvC,MAAM,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;IAC3D,MAAM,YAAY;QAAC;KAAI;IAEvB,IAAI,cAAc;IAClB,MAAM,OAAO,CAAC,CAAA;QACZ,eAAe,MAAM;QACrB,UAAU,IAAI,CAAC;IACjB;IAEA,OAAO;AACT;AAGO,SAAS,gBAAgB,IAAY;IAC1C,IAAI,CAAC,QAAQ,SAAS,KAAK,OAAO;IAElC,MAAM,iBAAiB,KAAK,WAAW,CAAC;IACxC,IAAI,kBAAkB,GAAG,OAAO;IAEhC,OAAO,KAAK,SAAS,CAAC,GAAG,mBAAmB;AAC9C;AAGO,SAAS,cAAc,IAAY;IACxC,IAAI,CAAC,QAAQ,SAAS,KAAK,OAAO;IAElC,MAAM,iBAAiB,KAAK,WAAW,CAAC;IACxC,OAAO,KAAK,SAAS,CAAC,iBAAiB,MAAM;AAC/C;AAGO,SAAS,eAAe,OAAe;IAC5C,MAAM,UAAU,aAAa;IAC7B,OAAO,uBAAuB,IAAI,CAAC,YAAY,QAAQ,MAAM,GAAG;AAClE;AAGO,SAAS,kBAAkB,IAAY;IAC5C,MAAM,UAAU,gBAAgB;IAChC,OAAO,2BAA2B,IAAI,CAAC;AACzC;AAGO,SAAS,sBACd,KAAa,EACb,gBAA0B;QAC1B,QAAA,iEAAgB;IAEhB,MAAM,aAAa,aAAa;IAChC,IAAI,CAAC,YAAY,OAAO,EAAE;IAE1B,OAAO,iBACJ,MAAM,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,aACzC,KAAK,CAAC,GAAG;AACd;AAGO,SAAS,qBACd,KAAa,EACb,eAAyB;QACzB,QAAA,iEAAgB;IAEhB,MAAM,aAAa,MAAM,WAAW;IACpC,IAAI,CAAC,YAAY,OAAO,EAAE;IAE1B,OAAO,gBACJ,MAAM,CAAC,CAAA,SAAU,OAAO,WAAW,GAAG,QAAQ,CAAC,aAC/C,KAAK,CAAC,GAAG;AACd", "debugId": null}}, {"offset": {"line": 3302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/hashtag-input.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { Badge } from '@/components/ui/badge'\nimport { Input } from '@/components/ui/input'\nimport { Button } from '@/components/ui/button'\nimport { X, Hash, Plus } from 'lucide-react'\nimport { cn } from '@/lib/utils'\nimport { \n  cleanHashtag, \n  formatHashtag, \n  isValidHashtag,\n  getHashtagSuggestions \n} from '@/lib/hashtag-utils'\n\ninterface HashtagInputProps {\n  hashtags: string[]\n  onHashtagsChange: (hashtags: string[]) => void\n  suggestions?: string[]\n  placeholder?: string\n  className?: string\n  maxTags?: number\n  disabled?: boolean\n}\n\nexport function HashtagInput({\n  hashtags,\n  onHashtagsChange,\n  suggestions = [],\n  placeholder = \"Etiket ekleyin... (örn: #frontend, #api)\",\n  className,\n  maxTags = 10,\n  disabled = false\n}: HashtagInputProps) {\n  const [inputValue, setInputValue] = useState('')\n  const [showSuggestions, setShowSuggestions] = useState(false)\n  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1)\n  const inputRef = useRef<HTMLInputElement>(null)\n  const suggestionsRef = useRef<HTMLDivElement>(null)\n\n  // Filter suggestions based on input\n  const filteredSuggestions = getHashtagSuggestions(inputValue, suggestions, 5)\n    .filter(suggestion => !hashtags.includes(suggestion))\n\n  // Add hashtag\n  const addHashtag = (hashtag: string) => {\n    const cleaned = cleanHashtag(hashtag)\n    if (!cleaned || !isValidHashtag(cleaned)) return\n    \n    const formatted = formatHashtag(cleaned)\n    if (hashtags.includes(formatted) || hashtags.length >= maxTags) return\n    \n    onHashtagsChange([...hashtags, formatted])\n    setInputValue('')\n    setShowSuggestions(false)\n    setSelectedSuggestionIndex(-1)\n  }\n\n  // Remove hashtag\n  const removeHashtag = (index: number) => {\n    const newHashtags = hashtags.filter((_, i) => i !== index)\n    onHashtagsChange(newHashtags)\n  }\n\n  // Handle input change\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const value = e.target.value\n    setInputValue(value)\n    setShowSuggestions(value.length > 0)\n    setSelectedSuggestionIndex(-1)\n  }\n\n  // Handle key down\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {\n    if (e.key === 'Enter') {\n      e.preventDefault()\n      if (selectedSuggestionIndex >= 0 && filteredSuggestions[selectedSuggestionIndex]) {\n        addHashtag(filteredSuggestions[selectedSuggestionIndex])\n      } else if (inputValue.trim()) {\n        addHashtag(inputValue.trim())\n      }\n    } else if (e.key === 'ArrowDown') {\n      e.preventDefault()\n      setSelectedSuggestionIndex(prev => \n        prev < filteredSuggestions.length - 1 ? prev + 1 : prev\n      )\n    } else if (e.key === 'ArrowUp') {\n      e.preventDefault()\n      setSelectedSuggestionIndex(prev => prev > 0 ? prev - 1 : -1)\n    } else if (e.key === 'Escape') {\n      setShowSuggestions(false)\n      setSelectedSuggestionIndex(-1)\n    } else if (e.key === 'Backspace' && !inputValue && hashtags.length > 0) {\n      // Remove last hashtag if input is empty\n      removeHashtag(hashtags.length - 1)\n    }\n  }\n\n  // Handle suggestion click\n  const handleSuggestionClick = (suggestion: string) => {\n    addHashtag(suggestion)\n    inputRef.current?.focus()\n  }\n\n  // Close suggestions when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (\n        suggestionsRef.current && \n        !suggestionsRef.current.contains(event.target as Node) &&\n        !inputRef.current?.contains(event.target as Node)\n      ) {\n        setShowSuggestions(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => document.removeEventListener('mousedown', handleClickOutside)\n  }, [])\n\n  return (\n    <div className={cn(\"space-y-2\", className)}>\n      {/* Hashtag Display */}\n      {hashtags.length > 0 && (\n        <div className=\"flex flex-wrap gap-1\">\n          {hashtags.map((hashtag, index) => (\n            <Badge\n              key={index}\n              variant=\"secondary\"\n              className=\"flex items-center gap-1 text-xs bg-blue-100 text-blue-800 hover:bg-blue-200\"\n            >\n              <Hash className=\"w-3 h-3\" />\n              {hashtag.replace('#', '')}\n              {!disabled && (\n                <Button\n                  type=\"button\"\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  className=\"h-auto p-0 w-3 h-3 hover:bg-transparent\"\n                  onClick={() => removeHashtag(index)}\n                >\n                  <X className=\"w-2 h-2\" />\n                </Button>\n              )}\n            </Badge>\n          ))}\n        </div>\n      )}\n\n      {/* Input Field */}\n      <div className=\"relative\">\n        <div className=\"flex items-center gap-2\">\n          <div className=\"relative flex-1\">\n            <Hash className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\n            <Input\n              ref={inputRef}\n              type=\"text\"\n              value={inputValue}\n              onChange={handleInputChange}\n              onKeyDown={handleKeyDown}\n              onFocus={() => setShowSuggestions(inputValue.length > 0)}\n              placeholder={hashtags.length >= maxTags ? `Maksimum ${maxTags} etiket` : placeholder}\n              disabled={disabled || hashtags.length >= maxTags}\n              className=\"pl-10\"\n            />\n          </div>\n          {inputValue.trim() && (\n            <Button\n              type=\"button\"\n              size=\"sm\"\n              onClick={() => addHashtag(inputValue.trim())}\n              disabled={disabled || hashtags.length >= maxTags}\n              className=\"shrink-0\"\n            >\n              <Plus className=\"w-4 h-4\" />\n            </Button>\n          )}\n        </div>\n\n        {/* Suggestions Dropdown */}\n        {showSuggestions && filteredSuggestions.length > 0 && (\n          <div\n            ref={suggestionsRef}\n            className=\"absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-40 overflow-y-auto\"\n          >\n            {filteredSuggestions.map((suggestion, index) => (\n              <button\n                key={suggestion}\n                type=\"button\"\n                className={cn(\n                  \"w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center gap-2\",\n                  selectedSuggestionIndex === index && \"bg-blue-50\"\n                )}\n                onClick={() => handleSuggestionClick(suggestion)}\n              >\n                <Hash className=\"w-3 h-3 text-gray-400\" />\n                {suggestion.replace('#', '')}\n              </button>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Helper Text */}\n      <div className=\"text-xs text-gray-500\">\n        {hashtags.length}/{maxTags} etiket • Eklemek için Enter&apos;a basın • # öneki kullanın\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;;;AARA;;;;;;;;AAyBO,SAAS,aAAa,KAQT;QARS,EAC3B,QAAQ,EACR,gBAAgB,EAChB,cAAc,EAAE,EAChB,cAAc,0CAA0C,EACxD,SAAS,EACT,UAAU,EAAE,EACZ,WAAW,KAAK,EACE,GARS;;IAS3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACxE,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,oCAAoC;IACpC,MAAM,sBAAsB,CAAA,GAAA,iIAAA,CAAA,wBAAqB,AAAD,EAAE,YAAY,aAAa,GACxE,MAAM,CAAC,CAAA,aAAc,CAAC,SAAS,QAAQ,CAAC;IAE3C,cAAc;IACd,MAAM,aAAa,CAAC;QAClB,MAAM,UAAU,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD,EAAE;QAC7B,IAAI,CAAC,WAAW,CAAC,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;QAE1C,MAAM,YAAY,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD,EAAE;QAChC,IAAI,SAAS,QAAQ,CAAC,cAAc,SAAS,MAAM,IAAI,SAAS;QAEhE,iBAAiB;eAAI;YAAU;SAAU;QACzC,cAAc;QACd,mBAAmB;QACnB,2BAA2B,CAAC;IAC9B;IAEA,iBAAiB;IACjB,MAAM,gBAAgB,CAAC;QACrB,MAAM,cAAc,SAAS,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACpD,iBAAiB;IACnB;IAEA,sBAAsB;IACtB,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,cAAc;QACd,mBAAmB,MAAM,MAAM,GAAG;QAClC,2BAA2B,CAAC;IAC9B;IAEA,kBAAkB;IAClB,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB,EAAE,cAAc;YAChB,IAAI,2BAA2B,KAAK,mBAAmB,CAAC,wBAAwB,EAAE;gBAChF,WAAW,mBAAmB,CAAC,wBAAwB;YACzD,OAAO,IAAI,WAAW,IAAI,IAAI;gBAC5B,WAAW,WAAW,IAAI;YAC5B;QACF,OAAO,IAAI,EAAE,GAAG,KAAK,aAAa;YAChC,EAAE,cAAc;YAChB,2BAA2B,CAAA,OACzB,OAAO,oBAAoB,MAAM,GAAG,IAAI,OAAO,IAAI;QAEvD,OAAO,IAAI,EAAE,GAAG,KAAK,WAAW;YAC9B,EAAE,cAAc;YAChB,2BAA2B,CAAA,OAAQ,OAAO,IAAI,OAAO,IAAI,CAAC;QAC5D,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;YAC7B,mBAAmB;YACnB,2BAA2B,CAAC;QAC9B,OAAO,IAAI,EAAE,GAAG,KAAK,eAAe,CAAC,cAAc,SAAS,MAAM,GAAG,GAAG;YACtE,wCAAwC;YACxC,cAAc,SAAS,MAAM,GAAG;QAClC;IACF;IAEA,0BAA0B;IAC1B,MAAM,wBAAwB,CAAC;YAE7B;QADA,WAAW;SACX,oBAAA,SAAS,OAAO,cAAhB,wCAAA,kBAAkB,KAAK;IACzB;IAEA,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;6DAAqB,CAAC;wBAIvB;oBAHH,IACE,eAAe,OAAO,IACtB,CAAC,eAAe,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,KAC7C,GAAC,oBAAA,SAAS,OAAO,cAAhB,wCAAA,kBAAkB,QAAQ,CAAC,MAAM,MAAM,IACxC;wBACA,mBAAmB;oBACrB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;0CAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QACzD;iCAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;YAE7B,SAAS,MAAM,GAAG,mBACjB,6LAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,oIAAA,CAAA,QAAK;wBAEJ,SAAQ;wBACR,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BACf,QAAQ,OAAO,CAAC,KAAK;4BACrB,CAAC,0BACA,6LAAC,qIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE7B,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;uBAdZ;;;;;;;;;;0BAuBb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC,oIAAA,CAAA,QAAK;wCACJ,KAAK;wCACL,MAAK;wCACL,OAAO;wCACP,UAAU;wCACV,WAAW;wCACX,SAAS,IAAM,mBAAmB,WAAW,MAAM,GAAG;wCACtD,aAAa,SAAS,MAAM,IAAI,UAAU,AAAC,YAAmB,OAAR,SAAQ,aAAW;wCACzE,UAAU,YAAY,SAAS,MAAM,IAAI;wCACzC,WAAU;;;;;;;;;;;;4BAGb,WAAW,IAAI,oBACd,6LAAC,qIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,MAAK;gCACL,SAAS,IAAM,WAAW,WAAW,IAAI;gCACzC,UAAU,YAAY,SAAS,MAAM,IAAI;gCACzC,WAAU;0CAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAMrB,mBAAmB,oBAAoB,MAAM,GAAG,mBAC/C,6LAAC;wBACC,KAAK;wBACL,WAAU;kCAET,oBAAoB,GAAG,CAAC,CAAC,YAAY,sBACpC,6LAAC;gCAEC,MAAK;gCACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gFACA,4BAA4B,SAAS;gCAEvC,SAAS,IAAM,sBAAsB;;kDAErC,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCACf,WAAW,OAAO,CAAC,KAAK;;+BATpB;;;;;;;;;;;;;;;;0BAiBf,6LAAC;gBAAI,WAAU;;oBACZ,SAAS,MAAM;oBAAC;oBAAE;oBAAQ;;;;;;;;;;;;;AAInC;GAxLgB;KAAA", "debugId": null}}, {"offset": {"line": 3580, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/category-selector.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\n\nimport { Folder, FolderOpen, ChevronRight, Home, Plus } from 'lucide-react'\nimport { cn } from '@/lib/utils'\nimport {\n  cleanFolderPath,\n  isValidFolderPath,\n  getFolderHierarchy,\n  getFolderName,\n  getFolderSuggestions\n} from '@/lib/hashtag-utils'\n\ninterface CategorySelectorProps {\n  category: string | null\n  onCategoryChange: (category: string | null) => void\n  suggestions?: string[]\n  placeholder?: string\n  className?: string\n  disabled?: boolean\n}\n\nexport function CategorySelector({\n  category,\n  onCategoryChange,\n  suggestions = [],\n  placeholder = \"Klasör seçin... (örn: /frontend, /admin/users)\",\n  className,\n  disabled = false\n}: CategorySelectorProps) {\n  const [inputValue, setInputValue] = useState('')\n  const [showSuggestions, setShowSuggestions] = useState(false)\n  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1)\n  const [isEditing, setIsEditing] = useState(false)\n  const inputRef = useRef<HTMLInputElement>(null)\n  const suggestionsRef = useRef<HTMLDivElement>(null)\n\n  // Filter suggestions based on input\n  const filteredSuggestions = getFolderSuggestions(inputValue, suggestions, 8)\n\n  // Get folder hierarchy for breadcrumb display\n  const hierarchy = category ? getFolderHierarchy(category) : []\n\n  // Set category\n  const setCategory = (newCategory: string) => {\n    const cleaned = cleanFolderPath(newCategory)\n    if (!cleaned || !isValidFolderPath(cleaned)) return\n    \n    onCategoryChange(cleaned === '/' ? null : cleaned)\n    setInputValue('')\n    setShowSuggestions(false)\n    setSelectedSuggestionIndex(-1)\n    setIsEditing(false)\n  }\n\n  // Clear category\n  const clearCategory = () => {\n    onCategoryChange(null)\n    setIsEditing(false)\n  }\n\n  // Handle input change\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const value = e.target.value\n    setInputValue(value)\n    setShowSuggestions(value.length > 0)\n    setSelectedSuggestionIndex(-1)\n  }\n\n  // Handle key down\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {\n    if (e.key === 'Enter') {\n      e.preventDefault()\n      if (selectedSuggestionIndex >= 0 && filteredSuggestions[selectedSuggestionIndex]) {\n        setCategory(filteredSuggestions[selectedSuggestionIndex])\n      } else if (inputValue.trim()) {\n        setCategory(inputValue.trim())\n      }\n    } else if (e.key === 'ArrowDown') {\n      e.preventDefault()\n      setSelectedSuggestionIndex(prev => \n        prev < filteredSuggestions.length - 1 ? prev + 1 : prev\n      )\n    } else if (e.key === 'ArrowUp') {\n      e.preventDefault()\n      setSelectedSuggestionIndex(prev => prev > 0 ? prev - 1 : -1)\n    } else if (e.key === 'Escape') {\n      setShowSuggestions(false)\n      setSelectedSuggestionIndex(-1)\n      setIsEditing(false)\n      setInputValue('')\n    }\n  }\n\n  // Handle suggestion click\n  const handleSuggestionClick = (suggestion: string) => {\n    setCategory(suggestion)\n    inputRef.current?.focus()\n  }\n\n  // Handle breadcrumb click\n  const handleBreadcrumbClick = (path: string) => {\n    if (path === '/') {\n      clearCategory()\n    } else {\n      onCategoryChange(path)\n    }\n  }\n\n  // Start editing\n  const startEditing = () => {\n    setIsEditing(true)\n    setInputValue(category || '')\n    setTimeout(() => inputRef.current?.focus(), 0)\n  }\n\n  // Close suggestions when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (\n        suggestionsRef.current && \n        !suggestionsRef.current.contains(event.target as Node) &&\n        !inputRef.current?.contains(event.target as Node)\n      ) {\n        setShowSuggestions(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => document.removeEventListener('mousedown', handleClickOutside)\n  }, [])\n\n  return (\n    <div className={cn(\"space-y-2\", className)}>\n      {/* Category Display */}\n      {category && !isEditing && (\n        <div className=\"flex items-center gap-1 p-2 bg-gray-50 rounded-md border\">\n          <div className=\"flex items-center gap-1 flex-1 min-w-0\">\n            {hierarchy.map((path, index) => (\n              <div key={path} className=\"flex items-center gap-1\">\n                {index > 0 && <ChevronRight className=\"w-3 h-3 text-gray-400\" />}\n                <Button\n                  type=\"button\"\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  className=\"h-auto p-1 text-xs hover:bg-gray-200\"\n                  onClick={() => handleBreadcrumbClick(path)}\n                >\n                  {index === 0 ? (\n                    <Home className=\"w-3 h-3\" />\n                  ) : (\n                    <>\n                      <Folder className=\"w-3 h-3\" />\n                      <span className=\"ml-1\">{getFolderName(path)}</span>\n                    </>\n                  )}\n                </Button>\n              </div>\n            ))}\n          </div>\n          {!disabled && (\n            <div className=\"flex items-center gap-1\">\n              <Button\n                type=\"button\"\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"h-auto p-1\"\n                onClick={startEditing}\n              >\n                Edit\n              </Button>\n              <Button\n                type=\"button\"\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"h-auto p-1\"\n                onClick={clearCategory}\n              >\n                Clear\n              </Button>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Input Field */}\n      {(!category || isEditing) && (\n        <div className=\"relative\">\n          <div className=\"flex items-center gap-2\">\n            <div className=\"relative flex-1\">\n              <Folder className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\n              <Input\n                ref={inputRef}\n                type=\"text\"\n                value={inputValue}\n                onChange={handleInputChange}\n                onKeyDown={handleKeyDown}\n                onFocus={() => setShowSuggestions(inputValue.length > 0)}\n                placeholder={placeholder}\n                disabled={disabled}\n                className=\"pl-10\"\n              />\n            </div>\n            {inputValue.trim() && (\n              <Button\n                type=\"button\"\n                size=\"sm\"\n                onClick={() => setCategory(inputValue.trim())}\n                disabled={disabled}\n                className=\"shrink-0\"\n              >\n                <Plus className=\"w-4 h-4\" />\n              </Button>\n            )}\n          </div>\n\n          {/* Suggestions Dropdown */}\n          {showSuggestions && filteredSuggestions.length > 0 && (\n            <div\n              ref={suggestionsRef}\n              className=\"absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-40 overflow-y-auto\"\n            >\n              {filteredSuggestions.map((suggestion, index) => (\n                <button\n                  key={suggestion}\n                  type=\"button\"\n                  className={cn(\n                    \"w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center gap-2\",\n                    selectedSuggestionIndex === index && \"bg-blue-50\"\n                  )}\n                  onClick={() => handleSuggestionClick(suggestion)}\n                >\n                  <FolderOpen className=\"w-3 h-3 text-gray-400\" />\n                  {suggestion}\n                </button>\n              ))}\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Helper Text */}\n      <div className=\"text-xs text-gray-500\">\n        Klasörler için eğik çizgi kullanın (örn: /frontend/components)\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AARA;;;;;;;AAyBO,SAAS,iBAAiB,KAOT;QAPS,EAC/B,QAAQ,EACR,gBAAgB,EAChB,cAAc,EAAE,EAChB,cAAc,gDAAgD,EAC9D,SAAS,EACT,WAAW,KAAK,EACM,GAPS;;IAQ/B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACxE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,oCAAoC;IACpC,MAAM,sBAAsB,CAAA,GAAA,iIAAA,CAAA,uBAAoB,AAAD,EAAE,YAAY,aAAa;IAE1E,8CAA8C;IAC9C,MAAM,YAAY,WAAW,CAAA,GAAA,iIAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY,EAAE;IAE9D,eAAe;IACf,MAAM,cAAc,CAAC;QACnB,MAAM,UAAU,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD,EAAE;QAChC,IAAI,CAAC,WAAW,CAAC,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU;QAE7C,iBAAiB,YAAY,MAAM,OAAO;QAC1C,cAAc;QACd,mBAAmB;QACnB,2BAA2B,CAAC;QAC5B,aAAa;IACf;IAEA,iBAAiB;IACjB,MAAM,gBAAgB;QACpB,iBAAiB;QACjB,aAAa;IACf;IAEA,sBAAsB;IACtB,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,cAAc;QACd,mBAAmB,MAAM,MAAM,GAAG;QAClC,2BAA2B,CAAC;IAC9B;IAEA,kBAAkB;IAClB,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB,EAAE,cAAc;YAChB,IAAI,2BAA2B,KAAK,mBAAmB,CAAC,wBAAwB,EAAE;gBAChF,YAAY,mBAAmB,CAAC,wBAAwB;YAC1D,OAAO,IAAI,WAAW,IAAI,IAAI;gBAC5B,YAAY,WAAW,IAAI;YAC7B;QACF,OAAO,IAAI,EAAE,GAAG,KAAK,aAAa;YAChC,EAAE,cAAc;YAChB,2BAA2B,CAAA,OACzB,OAAO,oBAAoB,MAAM,GAAG,IAAI,OAAO,IAAI;QAEvD,OAAO,IAAI,EAAE,GAAG,KAAK,WAAW;YAC9B,EAAE,cAAc;YAChB,2BAA2B,CAAA,OAAQ,OAAO,IAAI,OAAO,IAAI,CAAC;QAC5D,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;YAC7B,mBAAmB;YACnB,2BAA2B,CAAC;YAC5B,aAAa;YACb,cAAc;QAChB;IACF;IAEA,0BAA0B;IAC1B,MAAM,wBAAwB,CAAC;YAE7B;QADA,YAAY;SACZ,oBAAA,SAAS,OAAO,cAAhB,wCAAA,kBAAkB,KAAK;IACzB;IAEA,0BAA0B;IAC1B,MAAM,wBAAwB,CAAC;QAC7B,IAAI,SAAS,KAAK;YAChB;QACF,OAAO;YACL,iBAAiB;QACnB;IACF;IAEA,gBAAgB;IAChB,MAAM,eAAe;QACnB,aAAa;QACb,cAAc,YAAY;QAC1B,WAAW;gBAAM;oBAAA,oBAAA,SAAS,OAAO,cAAhB,wCAAA,kBAAkB,KAAK;WAAI;IAC9C;IAEA,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;iEAAqB,CAAC;wBAIvB;oBAHH,IACE,eAAe,OAAO,IACtB,CAAC,eAAe,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,KAC7C,GAAC,oBAAA,SAAS,OAAO,cAAhB,wCAAA,kBAAkB,QAAQ,CAAC,MAAM,MAAM,IACxC;wBACA,mBAAmB;oBACrB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;8CAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QACzD;qCAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;YAE7B,YAAY,CAAC,2BACZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC;gCAAe,WAAU;;oCACvB,QAAQ,mBAAK,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACtC,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,sBAAsB;kDAEpC,UAAU,kBACT,6LAAC,sMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;iEAEhB;;8DACE,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDAAK,WAAU;8DAAQ,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD,EAAE;;;;;;;;;;;;;;+BAdpC;;;;;;;;;;oBAqBb,CAAC,0BACA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;0CACV;;;;;;0CAGD,6LAAC,qIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;0CACV;;;;;;;;;;;;;;;;;;YASR,CAAC,CAAC,YAAY,SAAS,mBACtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC,oIAAA,CAAA,QAAK;wCACJ,KAAK;wCACL,MAAK;wCACL,OAAO;wCACP,UAAU;wCACV,WAAW;wCACX,SAAS,IAAM,mBAAmB,WAAW,MAAM,GAAG;wCACtD,aAAa;wCACb,UAAU;wCACV,WAAU;;;;;;;;;;;;4BAGb,WAAW,IAAI,oBACd,6LAAC,qIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,MAAK;gCACL,SAAS,IAAM,YAAY,WAAW,IAAI;gCAC1C,UAAU;gCACV,WAAU;0CAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAMrB,mBAAmB,oBAAoB,MAAM,GAAG,mBAC/C,6LAAC;wBACC,KAAK;wBACL,WAAU;kCAET,oBAAoB,GAAG,CAAC,CAAC,YAAY,sBACpC,6LAAC;gCAEC,MAAK;gCACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gFACA,4BAA4B,SAAS;gCAEvC,SAAS,IAAM,sBAAsB;;kDAErC,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCACrB;;+BATI;;;;;;;;;;;;;;;;0BAkBjB,6LAAC;gBAAI,WAAU;0BAAwB;;;;;;;;;;;;AAK7C;GAjOgB;KAAA", "debugId": null}}, {"offset": {"line": 3926, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,KAKoC;QALpC,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD,GALpC;IAMjB,qBACE,6LAAC,wKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 3963, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root> & {\n    indicatorClassName?: string\n  }\n>(({ className, value, indicatorClassName, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-2 w-full overflow-hidden rounded-full bg-gray-100\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className={cn(\n        \"h-full w-full flex-1 bg-gray-900 transition-all\",\n        indicatorClassName\n      )}\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,6JAAA,CAAA,aAAgB,MAK/B,QAAqD;QAApD,EAAE,SAAS,EAAE,KAAK,EAAE,kBAAkB,EAAE,GAAG,OAAO;yBACnD,6LAAC,uKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mDACA;YAEF,OAAO;gBAAE,WAAW,AAAC,eAAiC,OAAnB,MAAM,CAAC,SAAS,CAAC,GAAE;YAAI;;;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,uKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 4012, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/categorization-analytics.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Button } from '@/components/ui/button'\nimport { Progress } from '@/components/ui/progress'\nimport {\n  <PERSON><PERSON>hart<PERSON>,\n  <PERSON><PERSON>,\n  Fold<PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  Eye,\n  EyeOff,\n  Target\n} from 'lucide-react'\nimport { usePopularHashtags, usePopularCategories } from '@/hooks/use-hashtags'\nimport { usePrompts } from '@/hooks/use-prompts'\n\ninterface CategorizationAnalyticsProps {\n  projectId: string | null\n  className?: string\n}\n\nexport function CategorizationAnalytics({ \n  projectId, \n  className \n}: CategorizationAnalyticsProps) {\n  const [isVisible, setIsVisible] = useState(false)\n  \n  const { data: prompts = [] } = usePrompts(projectId)\n  const { data: popularHashtags = [] } = usePopularHashtags(projectId, 10)\n  const { data: popularCategories = [] } = usePopularCategories(projectId, 5)\n\n  // Calculate statistics\n  const totalPrompts = prompts.length\n  const categorizedPrompts = prompts.filter(p => p.category || (p.tags && p.tags.length > 0)).length\n  const uncategorizedPrompts = totalPrompts - categorizedPrompts\n  const categorizationRate = totalPrompts > 0 ? (categorizedPrompts / totalPrompts) * 100 : 0\n\n  const promptsWithHashtags = prompts.filter(p => p.tags && p.tags.length > 0).length\n  const promptsWithCategories = prompts.filter(p => p.category).length\n\n  if (!projectId) return null\n\n  return (\n    <div className={className}>\n      {/* Toggle Button */}\n      <Button\n        type=\"button\"\n        variant=\"outline\"\n        size=\"sm\"\n        onClick={() => setIsVisible(!isVisible)}\n        className=\"mb-4\"\n      >\n        {isVisible ? (\n          <>\n            <EyeOff className=\"w-4 h-4 mr-2\" />\n            Analitikleri Gizle\n          </>\n        ) : (\n          <>\n            <Eye className=\"w-4 h-4 mr-2\" />\n            Analitikleri Göster\n          </>\n        )}\n      </Button>\n\n      {isVisible && (\n        <div className=\"space-y-3 w-full overflow-hidden\">\n          {/* Overview Card - Compact */}\n          <Card className=\"w-full bg-white shadow-sm border border-gray-200\">\n            <CardHeader className=\"pb-2 px-3 pt-3\">\n              <CardTitle className=\"text-xs font-medium flex items-center gap-1.5\">\n                <BarChart3 className=\"w-3 h-3 shrink-0\" />\n                <span className=\"truncate\">Genel Bakış</span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-3 px-3 pb-3\">\n              {/* Categorization Rate - Compact */}\n              <div className=\"space-y-1\">\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-xs text-gray-600 truncate\">Kategorizasyon</span>\n                  <span className=\"text-xs font-medium shrink-0\">{categorizationRate.toFixed(0)}%</span>\n                </div>\n                <Progress value={categorizationRate} className=\"h-1.5 w-full\" />\n                <div className=\"text-xs text-gray-500 leading-tight\">\n                  {categorizedPrompts}/{totalPrompts} kategorili\n                </div>\n              </div>\n\n              {/* Quick Stats - Compact Grid */}\n              <div className=\"grid grid-cols-2 gap-2\">\n                <div className=\"text-center p-2 bg-blue-50 rounded\">\n                  <div className=\"text-sm font-semibold text-blue-700\">{promptsWithHashtags}</div>\n                  <div className=\"text-xs text-blue-600\">Etiket</div>\n                </div>\n                <div className=\"text-center p-2 bg-green-50 rounded\">\n                  <div className=\"text-sm font-semibold text-green-700\">{promptsWithCategories}</div>\n                  <div className=\"text-xs text-green-600\">Klasör</div>\n                </div>\n              </div>\n\n              {uncategorizedPrompts > 0 && (\n                <div className=\"p-2 bg-orange-50 border border-orange-200 rounded\">\n                  <div className=\"flex items-start gap-1.5 text-orange-700\">\n                    <Target className=\"w-3 h-3 shrink-0 mt-0.5\" />\n                    <div className=\"min-w-0\">\n                      <div className=\"text-xs font-medium leading-tight\">\n                        {uncategorizedPrompts} kategorisiz\n                      </div>\n                      <div className=\"text-xs text-orange-600 leading-tight\">\n                        Etiket/klasör ekleyin\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n\n          {/* Popular Hashtags - Compact */}\n          {popularHashtags.length > 0 && (\n            <Card className=\"w-full bg-white shadow-sm border border-gray-200\">\n              <CardHeader className=\"pb-2 px-3 pt-3\">\n                <CardTitle className=\"text-xs font-medium flex items-center gap-1.5\">\n                  <Hash className=\"w-3 h-3 shrink-0\" />\n                  <span className=\"truncate\">Popüler Etiketler</span>\n                </CardTitle>\n              </CardHeader>\n              <CardContent className=\"px-3 pb-3\">\n                <div className=\"space-y-1.5\">\n                  {popularHashtags.slice(0, 3).map(({ hashtag, count }, index) => {\n                    const percentage = totalPrompts > 0 ? (count / totalPrompts) * 100 : 0\n                    return (\n                      <div key={hashtag} className=\"flex items-center justify-between gap-2\">\n                        <div className=\"flex items-center gap-1.5 min-w-0 flex-1\">\n                          <span className=\"text-xs text-gray-500 w-3 shrink-0\">#{index + 1}</span>\n                          <Badge variant=\"secondary\" className=\"text-xs px-1.5 py-0.5 truncate max-w-[80px]\">\n                            {hashtag.replace('#', '')}\n                          </Badge>\n                        </div>\n                        <div className=\"flex items-center gap-1.5 shrink-0\">\n                          <div className=\"w-8 h-1 bg-gray-200 rounded-full overflow-hidden\">\n                            <div\n                              className=\"h-full bg-blue-500 rounded-full\"\n                              style={{ width: `${Math.max(percentage, 10)}%` }}\n                            />\n                          </div>\n                          <span className=\"text-xs text-gray-600 w-4 text-right\">{count}</span>\n                        </div>\n                      </div>\n                    )\n                  })}\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {/* Popular Categories - Compact */}\n          {popularCategories.length > 0 && (\n            <Card className=\"w-full bg-white shadow-sm border border-gray-200\">\n              <CardHeader className=\"pb-2 px-3 pt-3\">\n                <CardTitle className=\"text-xs font-medium flex items-center gap-1.5\">\n                  <Folder className=\"w-3 h-3 shrink-0\" />\n                  <span className=\"truncate\">Popüler Klasörler</span>\n                </CardTitle>\n              </CardHeader>\n              <CardContent className=\"px-3 pb-3\">\n                <div className=\"space-y-1.5\">\n                  {popularCategories.slice(0, 3).map(({ category, count }, index) => {\n                    const percentage = totalPrompts > 0 ? (count / totalPrompts) * 100 : 0\n                    return (\n                      <div key={category} className=\"flex items-center justify-between gap-2\">\n                        <div className=\"flex items-center gap-1.5 min-w-0 flex-1\">\n                          <span className=\"text-xs text-gray-500 w-3 shrink-0\">#{index + 1}</span>\n                          <Badge variant=\"outline\" className=\"text-xs px-1.5 py-0.5 flex items-center gap-1 truncate max-w-[80px]\">\n                            <Folder className=\"w-2.5 h-2.5 shrink-0\" />\n                            <span className=\"truncate\">{category}</span>\n                          </Badge>\n                        </div>\n                        <div className=\"flex items-center gap-1.5 shrink-0\">\n                          <div className=\"w-8 h-1 bg-gray-200 rounded-full overflow-hidden\">\n                            <div\n                              className=\"h-full bg-green-500 rounded-full\"\n                              style={{ width: `${Math.max(percentage, 10)}%` }}\n                            />\n                          </div>\n                          <span className=\"text-xs text-gray-600 w-4 text-right\">{count}</span>\n                        </div>\n                      </div>\n                    )\n                  })}\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {/* Recommendations - Compact */}\n          {totalPrompts > 0 && (\n            <Card className=\"w-full bg-white shadow-sm border border-gray-200\">\n              <CardHeader className=\"pb-2 px-3 pt-3\">\n                <CardTitle className=\"text-xs font-medium flex items-center gap-1.5\">\n                  <TrendingUp className=\"w-3 h-3 shrink-0\" />\n                  <span className=\"truncate\">Öneriler</span>\n                </CardTitle>\n              </CardHeader>\n              <CardContent className=\"px-3 pb-3\">\n                <div className=\"space-y-1.5 text-xs text-gray-600\">\n                  {categorizationRate < 50 && (\n                    <div className=\"flex items-start gap-1.5\">\n                      <span className=\"text-orange-500 shrink-0\">•</span>\n                      <span className=\"leading-tight\">Etiket ekleyin</span>\n                    </div>\n                  )}\n                  {promptsWithCategories === 0 && (\n                    <div className=\"flex items-start gap-1.5\">\n                      <span className=\"text-blue-500 shrink-0\">•</span>\n                      <span className=\"leading-tight\">Klasör kullanın</span>\n                    </div>\n                  )}\n                  {popularHashtags.length > 10 && (\n                    <div className=\"flex items-start gap-1.5\">\n                      <span className=\"text-green-500 shrink-0\">•</span>\n                      <span className=\"leading-tight\">İyi etiket kullanımı!</span>\n                    </div>\n                  )}\n                  {totalPrompts > 20 && categorizationRate > 80 && (\n                    <div className=\"flex items-start gap-1.5\">\n                      <span className=\"text-green-500 shrink-0\">•</span>\n                      <span className=\"leading-tight\">Mükemmel organizasyon!</span>\n                    </div>\n                  )}\n                </div>\n              </CardContent>\n            </Card>\n          )}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;;;AAjBA;;;;;;;;;AAwBO,SAAS,wBAAwB,KAGT;QAHS,EACtC,SAAS,EACT,SAAS,EACoB,GAHS;;IAItC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,EAAE,MAAM,UAAU,EAAE,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,aAAU,AAAD,EAAE;IAC1C,MAAM,EAAE,MAAM,kBAAkB,EAAE,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW;IACrE,MAAM,EAAE,MAAM,oBAAoB,EAAE,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,uBAAoB,AAAD,EAAE,WAAW;IAEzE,uBAAuB;IACvB,MAAM,eAAe,QAAQ,MAAM;IACnC,MAAM,qBAAqB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,IAAK,EAAE,IAAI,IAAI,EAAE,IAAI,CAAC,MAAM,GAAG,GAAI,MAAM;IAClG,MAAM,uBAAuB,eAAe;IAC5C,MAAM,qBAAqB,eAAe,IAAI,AAAC,qBAAqB,eAAgB,MAAM;IAE1F,MAAM,sBAAsB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,IAAI,EAAE,IAAI,CAAC,MAAM,GAAG,GAAG,MAAM;IACnF,MAAM,wBAAwB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;IAEpE,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6LAAC;QAAI,WAAW;;0BAEd,6LAAC,qIAAA,CAAA,SAAM;gBACL,MAAK;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS,IAAM,aAAa,CAAC;gBAC7B,WAAU;0BAET,0BACC;;sCACE,6LAAC,6MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBAAiB;;iDAIrC;;sCACE,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;YAMrC,2BACC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,qNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;4CAAK,WAAU;sDAAW;;;;;;;;;;;;;;;;;0CAG/B,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDAErB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAiC;;;;;;kEACjD,6LAAC;wDAAK,WAAU;;4DAAgC,mBAAmB,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAEhF,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,OAAO;gDAAoB,WAAU;;;;;;0DAC/C,6LAAC;gDAAI,WAAU;;oDACZ;oDAAmB;oDAAE;oDAAa;;;;;;;;;;;;;kDAKvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAuC;;;;;;kEACtD,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAwC;;;;;;kEACvD,6LAAC;wDAAI,WAAU;kEAAyB;;;;;;;;;;;;;;;;;;oCAI3C,uBAAuB,mBACtB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;gEACZ;gEAAqB;;;;;;;sEAExB,6LAAC;4DAAI,WAAU;sEAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAWlE,gBAAgB,MAAM,GAAG,mBACxB,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;4CAAK,WAAU;sDAAW;;;;;;;;;;;;;;;;;0CAG/B,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;8CACZ,gBAAgB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,QAAqB;4CAApB,EAAE,OAAO,EAAE,KAAK,EAAE;wCAClD,MAAM,aAAa,eAAe,IAAI,AAAC,QAAQ,eAAgB,MAAM;wCACrE,qBACE,6LAAC;4CAAkB,WAAU;;8DAC3B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;gEAAqC;gEAAE,QAAQ;;;;;;;sEAC/D,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;sEAClC,QAAQ,OAAO,CAAC,KAAK;;;;;;;;;;;;8DAG1B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,OAAO,AAAC,GAA2B,OAAzB,KAAK,GAAG,CAAC,YAAY,KAAI;gEAAG;;;;;;;;;;;sEAGnD,6LAAC;4DAAK,WAAU;sEAAwC;;;;;;;;;;;;;2CAdlD;;;;;oCAkBd;;;;;;;;;;;;;;;;;oBAOP,kBAAkB,MAAM,GAAG,mBAC1B,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAK,WAAU;sDAAW;;;;;;;;;;;;;;;;;0CAG/B,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;8CACZ,kBAAkB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,QAAsB;4CAArB,EAAE,QAAQ,EAAE,KAAK,EAAE;wCACrD,MAAM,aAAa,eAAe,IAAI,AAAC,QAAQ,eAAgB,MAAM;wCACrE,qBACE,6LAAC;4CAAmB,WAAU;;8DAC5B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;gEAAqC;gEAAE,QAAQ;;;;;;;sEAC/D,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;;8EACjC,6LAAC,yMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,6LAAC;oEAAK,WAAU;8EAAY;;;;;;;;;;;;;;;;;;8DAGhC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,OAAO,AAAC,GAA2B,OAAzB,KAAK,GAAG,CAAC,YAAY,KAAI;gEAAG;;;;;;;;;;;sEAGnD,6LAAC;4DAAK,WAAU;sEAAwC;;;;;;;;;;;;;2CAflD;;;;;oCAmBd;;;;;;;;;;;;;;;;;oBAOP,eAAe,mBACd,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;4CAAK,WAAU;sDAAW;;;;;;;;;;;;;;;;;0CAG/B,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;wCACZ,qBAAqB,oBACpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAA2B;;;;;;8DAC3C,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;wCAGnC,0BAA0B,mBACzB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAyB;;;;;;8DACzC,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;wCAGnC,gBAAgB,MAAM,GAAG,oBACxB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAA0B;;;;;;8DAC1C,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;wCAGnC,eAAe,MAAM,qBAAqB,oBACzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAA0B;;;;;;8DAC1C,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtD;GAzNgB;;QAMiB,iIAAA,CAAA,aAAU;QACF,kIAAA,CAAA,qBAAkB;QAChB,kIAAA,CAAA,uBAAoB;;;KAR/C", "debugId": null}}, {"offset": {"line": 4777, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/popular-hashtags-sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Input } from '@/components/ui/input'\nimport { ScrollArea } from '@/components/ui/scroll-area'\nimport { Separator } from '@/components/ui/separator'\nimport {\n  Hash,\n  Search,\n  TrendingUp,\n  Folder,\n  X\n} from 'lucide-react'\nimport { cn } from '@/lib/utils'\nimport { usePopularHashtags, usePopularCategories } from '@/hooks/use-hashtags'\nimport { CategorizationAnalytics } from '@/components/categorization-analytics'\n\ninterface PopularHashtagsSidebarProps {\n  projectId: string | null\n  onHashtagClick?: (hashtag: string) => void\n  onCategoryClick?: (category: string) => void\n  selectedHashtags?: string[]\n  selectedCategory?: string | null\n  className?: string\n}\n\nexport function PopularHashtagsSidebar({\n  projectId,\n  onHashtagClick,\n  onCategoryClick,\n  selectedHashtags = [],\n  selectedCategory = null,\n  className\n}: PopularHashtagsSidebarProps) {\n  const [searchQuery, setSearchQuery] = useState('')\n  const [showCategories, setShowCategories] = useState(true)\n  const [showHashtags, setShowHashtags] = useState(true)\n\n  const { data: popularHashtags = [], isLoading: hashtagsLoading } = usePopularHashtags(projectId)\n  const { data: popularCategories = [], isLoading: categoriesLoading } = usePopularCategories(projectId)\n\n  // Filter hashtags based on search\n  const filteredHashtags = popularHashtags.filter(({ hashtag }) =>\n    hashtag.toLowerCase().includes(searchQuery.toLowerCase())\n  )\n\n  // Filter categories based on search\n  const filteredCategories = popularCategories.filter(({ category }) =>\n    category.toLowerCase().includes(searchQuery.toLowerCase())\n  )\n\n  const handleHashtagClick = (hashtag: string) => {\n    onHashtagClick?.(hashtag)\n  }\n\n  const handleCategoryClick = (category: string) => {\n    onCategoryClick?.(category)\n  }\n\n  const clearFilters = () => {\n    setSearchQuery('')\n  }\n\n  return (\n    <div className={cn(\"w-64 bg-white border-l border-gray-200 flex flex-col relative z-50\", className)}>\n      {/* Header */}\n      <div className=\"p-4 border-b border-gray-200\">\n        <div className=\"flex items-center gap-2 mb-3\">\n          <TrendingUp className=\"w-4 h-4 text-blue-600\" />\n          <h3 className=\"font-medium text-sm\">AI Tags</h3>\n        </div>\n\n        {/* Search */}\n        <div className=\"relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\n          <Input\n            type=\"text\"\n            placeholder=\"Etiket ve klasör ara...\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n            className=\"pl-10 text-sm\"\n          />\n          {searchQuery && (\n            <Button\n              type=\"button\"\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0\"\n              onClick={clearFilters}\n            >\n              <X className=\"w-3 h-3\" />\n            </Button>\n          )}\n        </div>\n      </div>\n\n      <ScrollArea className=\"flex-1 overflow-hidden\">\n        <div className=\"p-4 space-y-4 max-w-full pb-24 sm:pb-20 lg:pb-24\">\n          {/* Categories Section */}\n          {showCategories && (\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-2\">\n                  <Folder className=\"w-3 h-3 text-gray-500\" />\n                  <span className=\"text-xs font-medium text-gray-700 uppercase tracking-wide\">\n                    Klasörler\n                  </span>\n                </div>\n                <Button\n                  type=\"button\"\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  className=\"h-auto p-0 text-xs\"\n                  onClick={() => setShowCategories(!showCategories)}\n                >\n                  {showCategories ? 'Gizle' : 'Göster'}\n                </Button>\n              </div>\n\n              {categoriesLoading ? (\n                <div className=\"text-xs text-gray-500\">Klasörler yükleniyor...</div>\n              ) : filteredCategories.length > 0 ? (\n                <div className=\"space-y-1\">\n                  {filteredCategories.map(({ category, count }) => (\n                    <Button\n                      key={category}\n                      type=\"button\"\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      className={cn(\n                        \"w-full justify-between h-auto p-2 text-xs hover:bg-gray-100\",\n                        selectedCategory === category && \"bg-blue-50 text-blue-700\"\n                      )}\n                      onClick={() => handleCategoryClick(category)}\n                    >\n                      <div className=\"flex items-center gap-2 min-w-0\">\n                        <Folder className=\"w-3 h-3 shrink-0\" />\n                        <span className=\"truncate\">{category}</span>\n                      </div>\n                      <Badge variant=\"secondary\" className=\"text-xs\">\n                        {count}\n                      </Badge>\n                    </Button>\n                  ))}\n                </div>\n              ) : (\n                <div className=\"text-xs text-gray-500\">\n                  {searchQuery ? 'Eşleşen klasör bulunamadı' : 'Henüz klasör yok'}\n                </div>\n              )}\n            </div>\n          )}\n\n          {showCategories && showHashtags && (\n            <Separator />\n          )}\n\n          {/* Hashtags Section */}\n          {showHashtags && (\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-2\">\n                  <Hash className=\"w-3 h-3 text-gray-500\" />\n                  <span className=\"text-xs font-medium text-gray-700 uppercase tracking-wide\">\n                    Popüler Etiketler\n                  </span>\n                </div>\n                <Button\n                  type=\"button\"\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  className=\"h-auto p-0 text-xs\"\n                  onClick={() => setShowHashtags(!showHashtags)}\n                >\n                  {showHashtags ? 'Gizle' : 'Göster'}\n                </Button>\n              </div>\n\n              {hashtagsLoading ? (\n                <div className=\"text-xs text-gray-500\">Etiketler yükleniyor...</div>\n              ) : filteredHashtags.length > 0 ? (\n                <div className=\"space-y-1\">\n                  {filteredHashtags.map(({ hashtag, count }) => (\n                    <Button\n                      key={hashtag}\n                      type=\"button\"\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      className={cn(\n                        \"w-full justify-between h-auto p-2 text-xs hover:bg-gray-100\",\n                        selectedHashtags.includes(hashtag) && \"bg-blue-50 text-blue-700\"\n                      )}\n                      onClick={() => handleHashtagClick(hashtag)}\n                    >\n                      <div className=\"flex items-center gap-2 min-w-0\">\n                        <Hash className=\"w-3 h-3 shrink-0\" />\n                        <span className=\"truncate\">{hashtag.replace('#', '')}</span>\n                      </div>\n                      <Badge variant=\"secondary\" className=\"text-xs\">\n                        {count}\n                      </Badge>\n                    </Button>\n                  ))}\n                </div>\n              ) : (\n                <div className=\"text-xs text-gray-500\">\n                  {searchQuery ? 'Eşleşen etiket bulunamadı' : 'Henüz etiket yok'}\n                </div>\n              )}\n            </div>\n          )}\n\n\n\n          {/* Analytics */}\n          {projectId && (\n            <>\n              <Separator />\n              <div className=\"w-full overflow-hidden\">\n                <CategorizationAnalytics\n                  projectId={projectId}\n                  className=\"w-full max-w-none\"\n                />\n              </div>\n            </>\n          )}\n        </div>\n      </ScrollArea>\n\n      {/* Footer */}\n      <div className=\"p-4 border-t border-gray-200\">\n        <div className=\"text-xs text-gray-500 text-center\">\n          Filtrelemek için tıklayın • Sayılar kullanım sayısını gösterir\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AACA;AACA;;;AAjBA;;;;;;;;;;;AA4BO,SAAS,uBAAuB,KAOT;QAPS,EACrC,SAAS,EACT,cAAc,EACd,eAAe,EACf,mBAAmB,EAAE,EACrB,mBAAmB,IAAI,EACvB,SAAS,EACmB,GAPS;;IAQrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,EAAE,MAAM,kBAAkB,EAAE,EAAE,WAAW,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,qBAAkB,AAAD,EAAE;IACtF,MAAM,EAAE,MAAM,oBAAoB,EAAE,EAAE,WAAW,iBAAiB,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,uBAAoB,AAAD,EAAE;IAE5F,kCAAkC;IAClC,MAAM,mBAAmB,gBAAgB,MAAM,CAAC;YAAC,EAAE,OAAO,EAAE;eAC1D,QAAQ,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;;IAGxD,oCAAoC;IACpC,MAAM,qBAAqB,kBAAkB,MAAM,CAAC;YAAC,EAAE,QAAQ,EAAE;eAC/D,SAAS,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;;IAGzD,MAAM,qBAAqB,CAAC;QAC1B,2BAAA,qCAAA,eAAiB;IACnB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,4BAAA,sCAAA,gBAAkB;IACpB;IAEA,MAAM,eAAe;QACnB,eAAe;IACjB;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sEAAsE;;0BAEvF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,6LAAC;gCAAG,WAAU;0CAAsB;;;;;;;;;;;;kCAItC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC,oIAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;;;;;;4BAEX,6BACC,6LAAC,qIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMrB,6LAAC,6IAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,6LAAC;oBAAI,WAAU;;wBAEZ,gCACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDAAK,WAAU;8DAA4D;;;;;;;;;;;;sDAI9E,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,kBAAkB,CAAC;sDAEjC,iBAAiB,UAAU;;;;;;;;;;;;gCAI/B,kCACC,6LAAC;oCAAI,WAAU;8CAAwB;;;;;2CACrC,mBAAmB,MAAM,GAAG,kBAC9B,6LAAC;oCAAI,WAAU;8CACZ,mBAAmB,GAAG,CAAC;4CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE;6DAC1C,6LAAC,qIAAA,CAAA,SAAM;4CAEL,MAAK;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA,qBAAqB,YAAY;4CAEnC,SAAS,IAAM,oBAAoB;;8DAEnC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;4DAAK,WAAU;sEAAY;;;;;;;;;;;;8DAE9B,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAClC;;;;;;;2CAfE;;;;;;;;;;yDAqBX,6LAAC;oCAAI,WAAU;8CACZ,cAAc,8BAA8B;;;;;;;;;;;;wBAMpD,kBAAkB,8BACjB,6LAAC,wIAAA,CAAA,YAAS;;;;;wBAIX,8BACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDAAK,WAAU;8DAA4D;;;;;;;;;;;;sDAI9E,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,gBAAgB,CAAC;sDAE/B,eAAe,UAAU;;;;;;;;;;;;gCAI7B,gCACC,6LAAC;oCAAI,WAAU;8CAAwB;;;;;2CACrC,iBAAiB,MAAM,GAAG,kBAC5B,6LAAC;oCAAI,WAAU;8CACZ,iBAAiB,GAAG,CAAC;4CAAC,EAAE,OAAO,EAAE,KAAK,EAAE;6DACvC,6LAAC,qIAAA,CAAA,SAAM;4CAEL,MAAK;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA,iBAAiB,QAAQ,CAAC,YAAY;4CAExC,SAAS,IAAM,mBAAmB;;8DAElC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;sEAAY,QAAQ,OAAO,CAAC,KAAK;;;;;;;;;;;;8DAEnD,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAClC;;;;;;;2CAfE;;;;;;;;;;yDAqBX,6LAAC;oCAAI,WAAU;8CACZ,cAAc,8BAA8B;;;;;;;;;;;;wBASpD,2BACC;;8CACE,6LAAC,wIAAA,CAAA,YAAS;;;;;8CACV,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,oJAAA,CAAA,0BAAuB;wCACtB,WAAW;wCACX,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0BAStB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BAAoC;;;;;;;;;;;;;;;;;AAM3D;GAnNgB;;QAYqD,kIAAA,CAAA,qBAAkB;QACd,kIAAA,CAAA,uBAAoB;;;KAb7E", "debugId": null}}, {"offset": {"line": 5249, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/hooks/use-dynamic-height.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useCallback } from 'react'\n\ninterface DynamicHeightConfig {\n  /** Minimum height in pixels */\n  minHeight?: number\n  /** Maximum height as fraction of viewport height (0.33 = 1/3) */\n  maxHeightFraction?: number\n  /** Base height in pixels when content is minimal */\n  baseHeight?: number\n  /** Enable smooth transitions */\n  enableTransitions?: boolean\n}\n\ninterface DynamicHeightReturn {\n  /** Current maximum height in pixels */\n  maxHeight: number\n  /** Current minimum height in pixels */\n  minHeight: number\n  /** CSS style object for textarea */\n  heightStyle: React.CSSProperties\n  /** Function to manually trigger height recalculation */\n  recalculateHeight: () => void\n  /** Current viewport height */\n  viewportHeight: number\n}\n\n/**\n * Custom hook for dynamic textarea height management\n * Calculates optimal height based on viewport size and content\n */\nexport function useDynamicHeight(config: DynamicHeightConfig = {}): DynamicHeightReturn {\n  const {\n    minHeight = 44,\n    maxHeightFraction = 0.33, // 1/3 of viewport height\n    baseHeight: _baseHeight = 56, // Unused for now\n    enableTransitions = true\n  } = config\n\n  const [viewportHeight, setViewportHeight] = useState(0)\n  const [maxHeight, setMaxHeight] = useState(200) // Default fallback\n\n  // Calculate heights based on viewport\n  const calculateHeights = useCallback(() => {\n    if (typeof window === 'undefined') return\n\n    const vh = window.innerHeight\n    const calculatedMaxHeight = Math.floor(vh * maxHeightFraction)\n    \n    // Ensure minimum constraints\n    const finalMaxHeight = Math.max(calculatedMaxHeight, minHeight + 50)\n    \n    setViewportHeight(vh)\n    setMaxHeight(finalMaxHeight)\n  }, [maxHeightFraction, minHeight])\n\n  // Handle viewport resize with debouncing\n  useEffect(() => {\n    if (typeof window === 'undefined') return\n\n    let timeoutId: NodeJS.Timeout\n\n    const handleResize = () => {\n      clearTimeout(timeoutId)\n      timeoutId = setTimeout(calculateHeights, 150) // Debounce for performance\n    }\n\n    // Initial calculation\n    calculateHeights()\n\n    // Listen for resize events\n    window.addEventListener('resize', handleResize)\n    window.addEventListener('orientationchange', handleResize)\n\n    // Cleanup\n    return () => {\n      clearTimeout(timeoutId)\n      window.removeEventListener('resize', handleResize)\n      window.removeEventListener('orientationchange', handleResize)\n    }\n  }, [calculateHeights])\n\n  // Handle dynamic viewport height changes (mobile browsers)\n  useEffect(() => {\n    if (typeof window === 'undefined') return\n\n    const handleVisualViewportChange = () => {\n      if (window.visualViewport) {\n        const vh = window.visualViewport.height\n        const calculatedMaxHeight = Math.floor(vh * maxHeightFraction)\n        const finalMaxHeight = Math.max(calculatedMaxHeight, minHeight + 50)\n        \n        setViewportHeight(vh)\n        setMaxHeight(finalMaxHeight)\n      }\n    }\n\n    // Support for Visual Viewport API (better mobile support)\n    if (window.visualViewport) {\n      window.visualViewport.addEventListener('resize', handleVisualViewportChange)\n      return () => {\n        window.visualViewport?.removeEventListener('resize', handleVisualViewportChange)\n      }\n    }\n  }, [maxHeightFraction, minHeight])\n\n  // Generate CSS style object\n  const heightStyle: React.CSSProperties = {\n    minHeight: `${minHeight}px`,\n    maxHeight: `${maxHeight}px`,\n    height: 'auto',\n    resize: 'none' as const,\n    ...(enableTransitions && {\n      transition: 'max-height 0.2s ease-out, min-height 0.2s ease-out'\n    })\n  }\n\n  return {\n    maxHeight,\n    minHeight,\n    heightStyle,\n    recalculateHeight: calculateHeights,\n    viewportHeight\n  }\n}\n\n/**\n * Responsive breakpoint-aware dynamic height hook\n * Provides different height configurations for different screen sizes\n */\nexport function useResponsiveDynamicHeight(): DynamicHeightReturn {\n  const [breakpoint, setBreakpoint] = useState<'mobile' | 'tablet' | 'desktop'>('desktop')\n\n  // Detect current breakpoint\n  useEffect(() => {\n    if (typeof window === 'undefined') return\n\n    const updateBreakpoint = () => {\n      const width = window.innerWidth\n      if (width < 640) {\n        setBreakpoint('mobile')\n      } else if (width < 1024) {\n        setBreakpoint('tablet')\n      } else {\n        setBreakpoint('desktop')\n      }\n    }\n\n    updateBreakpoint()\n    window.addEventListener('resize', updateBreakpoint)\n    \n    return () => window.removeEventListener('resize', updateBreakpoint)\n  }, [])\n\n  // Configure height based on breakpoint with optimized values\n  const config: DynamicHeightConfig = {\n    minHeight: breakpoint === 'mobile' ? 48 : breakpoint === 'tablet' ? 44 : 56,\n    maxHeightFraction: breakpoint === 'mobile' ? 0.25 : 0.33, // 1/4 on mobile, 1/3 on tablet/desktop\n    baseHeight: breakpoint === 'mobile' ? 48 : breakpoint === 'tablet' ? 44 : 56,\n    enableTransitions: true\n  }\n\n  return useDynamicHeight(config)\n}\n\n/**\n * Auto-expanding textarea hook with dynamic height constraints\n * Automatically adjusts height based on content while respecting viewport limits\n */\nexport function useAutoExpandingHeight(\n  textareaRef: React.RefObject<HTMLTextAreaElement | null>,\n  content: string,\n  config?: DynamicHeightConfig\n) {\n  const { heightStyle, maxHeight, minHeight } = useDynamicHeight(config)\n  const [isExpanding, setIsExpanding] = useState(false)\n\n  // Auto-expand based on content with smooth animations\n  useEffect(() => {\n    const textarea = textareaRef.current\n    if (!textarea) return\n\n    // Get current height before changes\n    const currentHeight = textarea.offsetHeight\n\n    // Reset height to calculate scroll height\n    textarea.style.height = 'auto'\n\n    // Calculate optimal height\n    const scrollHeight = textarea.scrollHeight\n    const optimalHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight)\n\n    // Check if height is changing significantly\n    const heightDifference = Math.abs(optimalHeight - currentHeight)\n\n    if (heightDifference > 10) {\n      setIsExpanding(true)\n\n      // Add expanding class for animation\n      textarea.classList.add('expanding')\n\n      // Remove animation class after animation completes\n      setTimeout(() => {\n        textarea.classList.remove('expanding')\n        setIsExpanding(false)\n      }, 200)\n    }\n\n    // Apply calculated height\n    textarea.style.height = `${optimalHeight}px`\n  }, [content, maxHeight, minHeight, textareaRef])\n\n  return {\n    heightStyle: {\n      ...heightStyle,\n      overflow: 'hidden' as const\n    },\n    maxHeight,\n    minHeight,\n    isExpanding\n  }\n}\n"], "names": [], "mappings": ";;;;;AAEA;;AAFA;;AAgCO,SAAS;QAAiB,SAAA,iEAA8B,CAAC;;IAC9D,MAAM,EACJ,YAAY,EAAE,EACd,oBAAoB,IAAI,EACxB,YAAY,cAAc,EAAE,EAC5B,oBAAoB,IAAI,EACzB,GAAG;IAEJ,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,mBAAmB;;IAEnE,sCAAsC;IACtC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YACnC;;YAEA,MAAM,KAAK,OAAO,WAAW;YAC7B,MAAM,sBAAsB,KAAK,KAAK,CAAC,KAAK;YAE5C,6BAA6B;YAC7B,MAAM,iBAAiB,KAAK,GAAG,CAAC,qBAAqB,YAAY;YAEjE,kBAAkB;YAClB,aAAa;QACf;yDAAG;QAAC;QAAmB;KAAU;IAEjC,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;;YAEA,IAAI;YAEJ,MAAM;2DAAe;oBACnB,aAAa;oBACb,YAAY,WAAW,kBAAkB,MAAK,2BAA2B;gBAC3E;;YAEA,sBAAsB;YACtB;YAEA,2BAA2B;YAC3B,OAAO,gBAAgB,CAAC,UAAU;YAClC,OAAO,gBAAgB,CAAC,qBAAqB;YAE7C,UAAU;YACV;8CAAO;oBACL,aAAa;oBACb,OAAO,mBAAmB,CAAC,UAAU;oBACrC,OAAO,mBAAmB,CAAC,qBAAqB;gBAClD;;QACF;qCAAG;QAAC;KAAiB;IAErB,2DAA2D;IAC3D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;;YAEA,MAAM;yEAA6B;oBACjC,IAAI,OAAO,cAAc,EAAE;wBACzB,MAAM,KAAK,OAAO,cAAc,CAAC,MAAM;wBACvC,MAAM,sBAAsB,KAAK,KAAK,CAAC,KAAK;wBAC5C,MAAM,iBAAiB,KAAK,GAAG,CAAC,qBAAqB,YAAY;wBAEjE,kBAAkB;wBAClB,aAAa;oBACf;gBACF;;YAEA,0DAA0D;YAC1D,IAAI,OAAO,cAAc,EAAE;gBACzB,OAAO,cAAc,CAAC,gBAAgB,CAAC,UAAU;gBACjD;kDAAO;4BACL;yBAAA,yBAAA,OAAO,cAAc,cAArB,6CAAA,uBAAuB,mBAAmB,CAAC,UAAU;oBACvD;;YACF;QACF;qCAAG;QAAC;QAAmB;KAAU;IAEjC,4BAA4B;IAC5B,MAAM,cAAmC;QACvC,WAAW,AAAC,GAAY,OAAV,WAAU;QACxB,WAAW,AAAC,GAAY,OAAV,WAAU;QACxB,QAAQ;QACR,QAAQ;QACR,GAAI,qBAAqB;YACvB,YAAY;QACd,CAAC;IACH;IAEA,OAAO;QACL;QACA;QACA;QACA,mBAAmB;QACnB;IACF;AACF;GA7FgB;AAmGT,SAAS;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC;IAE9E,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gDAAE;YACR;;YAEA,MAAM;yEAAmB;oBACvB,MAAM,QAAQ,OAAO,UAAU;oBAC/B,IAAI,QAAQ,KAAK;wBACf,cAAc;oBAChB,OAAO,IAAI,QAAQ,MAAM;wBACvB,cAAc;oBAChB,OAAO;wBACL,cAAc;oBAChB;gBACF;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAElC;wDAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;+CAAG,EAAE;IAEL,6DAA6D;IAC7D,MAAM,SAA8B;QAClC,WAAW,eAAe,WAAW,KAAK,eAAe,WAAW,KAAK;QACzE,mBAAmB,eAAe,WAAW,OAAO;QACpD,YAAY,eAAe,WAAW,KAAK,eAAe,WAAW,KAAK;QAC1E,mBAAmB;IACrB;IAEA,OAAO,iBAAiB;AAC1B;IAjCgB;;QAgCP;;;AAOF,SAAS,uBACd,WAAwD,EACxD,OAAe,EACf,MAA4B;;IAE5B,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,iBAAiB;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,sDAAsD;IACtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,MAAM,WAAW,YAAY,OAAO;YACpC,IAAI,CAAC,UAAU;YAEf,oCAAoC;YACpC,MAAM,gBAAgB,SAAS,YAAY;YAE3C,0CAA0C;YAC1C,SAAS,KAAK,CAAC,MAAM,GAAG;YAExB,2BAA2B;YAC3B,MAAM,eAAe,SAAS,YAAY;YAC1C,MAAM,gBAAgB,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,cAAc,YAAY;YAElE,4CAA4C;YAC5C,MAAM,mBAAmB,KAAK,GAAG,CAAC,gBAAgB;YAElD,IAAI,mBAAmB,IAAI;gBACzB,eAAe;gBAEf,oCAAoC;gBACpC,SAAS,SAAS,CAAC,GAAG,CAAC;gBAEvB,mDAAmD;gBACnD;wDAAW;wBACT,SAAS,SAAS,CAAC,MAAM,CAAC;wBAC1B,eAAe;oBACjB;uDAAG;YACL;YAEA,0BAA0B;YAC1B,SAAS,KAAK,CAAC,MAAM,GAAG,AAAC,GAAgB,OAAd,eAAc;QAC3C;2CAAG;QAAC;QAAS;QAAW;QAAW;KAAY;IAE/C,OAAO;QACL,aAAa;YACX,GAAG,WAAW;YACd,UAAU;QACZ;QACA;QACA;QACA;IACF;AACF;IApDgB;;QAKgC", "debugId": null}}, {"offset": {"line": 5464, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/smart-autocomplete.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef, useCallback, useMemo } from 'react'\nimport { Hash, Folder } from 'lucide-react'\nimport { useAutoExpandingHeight } from '@/hooks/use-dynamic-height'\nimport { debounce } from 'lodash-es'\n\ninterface AutocompleteItem {\n  id: string\n  value: string\n  type: 'hashtag' | 'folder'\n  usage_count?: number\n}\n\ninterface SmartAutocompleteProps {\n  value: string\n  onChange: (value: string) => void\n  onKeyDown?: (e: React.KeyboardEvent) => void\n  placeholder?: string\n  className?: string\n  suggestions: {\n    hashtags: string[]\n    folders: string[]\n  }\n  disabled?: boolean\n  /** Enable dynamic height based on viewport */\n  enableDynamicHeight?: boolean\n  /** Custom height configuration */\n  heightConfig?: {\n    minHeight?: number\n    maxHeightFraction?: number\n    baseHeight?: number\n  }\n}\n\nexport function SmartAutocomplete({\n  value,\n  onChange,\n  onKeyDown,\n  placeholder,\n  className,\n  suggestions = { hashtags: [], folders: [] },\n  disabled = false,\n  enableDynamicHeight = true,\n  heightConfig = {}\n}: SmartAutocompleteProps) {\n  const [showDropdown, setShowDropdown] = useState(false)\n  const [filteredItems, setFilteredItems] = useState<AutocompleteItem[]>([])\n  const [selectedIndex, setSelectedIndex] = useState(-1)\n  const [triggerChar, setTriggerChar] = useState<'#' | '/' | null>(null)\n  const [triggerPosition, setTriggerPosition] = useState(-1)\n  const [, setSearchTerm] = useState('')\n\n  const textareaRef = useRef<HTMLTextAreaElement>(null)\n  const dropdownRef = useRef<HTMLDivElement>(null)\n\n  // Dynamic height management\n  const { heightStyle } = useAutoExpandingHeight(\n    textareaRef,\n    value,\n    enableDynamicHeight ? heightConfig : undefined\n  )\n\n  // Get cursor position and detect trigger characters\n  const detectTrigger = useCallback((text: string, cursorPos: number) => {\n    if (cursorPos === 0) return null\n\n    // Look backwards from cursor to find trigger character\n    for (let i = cursorPos - 1; i >= 0; i--) {\n      const char = text[i]\n      \n      if (char === '#' || char === '/') {\n        // Check if this is a valid trigger (not escaped or in middle of word)\n        const prevChar = i > 0 ? text[i - 1] : ' '\n        if (prevChar === ' ' || prevChar === '\\n' || i === 0) {\n          const searchText = text.slice(i + 1, cursorPos)\n          // Only trigger if search term doesn't contain spaces\n          if (!searchText.includes(' ') && !searchText.includes('\\n')) {\n            return {\n              char: char as '#' | '/',\n              position: i,\n              searchTerm: searchText\n            }\n          }\n        }\n      }\n      \n      // Stop if we hit a space or newline\n      if (char === ' ' || char === '\\n') {\n        break\n      }\n    }\n    \n    return null\n  }, [])\n\n  // Filter suggestions based on search term\n  const filterSuggestions = useCallback((searchTerm: string, type: 'hashtag' | 'folder') => {\n    const items = type === 'hashtag' ? (suggestions?.hashtags || []) : (suggestions?.folders || [])\n\n    if (!items || items.length === 0) {\n      return []\n    }\n\n    return items\n      .filter(item => {\n        if (!item || typeof item !== 'string') return false\n        const cleanItem = item.replace(/^[#/]/, '') // Remove prefix for matching\n        return cleanItem.toLowerCase().includes(searchTerm.toLowerCase())\n      })\n      .slice(0, 8) // Limit to 8 suggestions for performance\n      .map((item, index) => ({\n        id: `${type}-${index}`,\n        value: item,\n        type,\n        usage_count: Math.floor(Math.random() * 100) // Mock usage count\n      }))\n      .sort((a, b) => (b.usage_count || 0) - (a.usage_count || 0)) // Sort by usage\n  }, [suggestions])\n\n  // Insert selected suggestion\n  const insertSuggestion = useCallback((item: AutocompleteItem) => {\n    if (triggerPosition === -1) return\n\n    const beforeTrigger = value.slice(0, triggerPosition)\n    const afterCursor = value.slice(textareaRef.current?.selectionStart || 0)\n\n    // Ensure proper formatting\n    const prefix = item.type === 'hashtag' ? '#' : '/'\n    const suggestion = item.value.startsWith(prefix) ? item.value : `${prefix}${item.value}`\n\n    const newValue = beforeTrigger + suggestion + ' ' + afterCursor\n    onChange(newValue)\n\n    // Set cursor position after the inserted suggestion\n    setTimeout(() => {\n      if (textareaRef.current) {\n        const newCursorPos = beforeTrigger.length + suggestion.length + 1\n        textareaRef.current.setSelectionRange(newCursorPos, newCursorPos)\n        textareaRef.current.focus()\n      }\n    }, 0)\n\n    setShowDropdown(false)\n    setSelectedIndex(-1)\n  }, [value, triggerPosition, onChange])\n\n  // Debounced trigger detection for better performance\n  const debouncedTriggerDetection = useMemo(\n    () => debounce((newValue: string, cursorPos: number) => {\n      try {\n        const trigger = detectTrigger(newValue, cursorPos)\n\n        if (trigger) {\n          setTriggerChar(trigger.char)\n          setTriggerPosition(trigger.position)\n          setSearchTerm(trigger.searchTerm)\n\n          const type = trigger.char === '#' ? 'hashtag' : 'folder'\n          const filtered = filterSuggestions(trigger.searchTerm, type)\n          setFilteredItems(filtered || [])\n          setShowDropdown(filtered && filtered.length > 0)\n          setSelectedIndex(-1)\n        } else {\n          setShowDropdown(false)\n          setTriggerChar(null)\n          setTriggerPosition(-1)\n          setSearchTerm('')\n          setFilteredItems([])\n          setSelectedIndex(-1)\n        }\n      } catch (error) {\n        console.error('Error in trigger detection:', error)\n        // Reset state on error\n        setShowDropdown(false)\n        setTriggerChar(null)\n        setTriggerPosition(-1)\n        setSearchTerm('')\n        setFilteredItems([])\n        setSelectedIndex(-1)\n      }\n    }, 100), // Reduced debounce time for better responsiveness\n    [detectTrigger, filterSuggestions]\n  )\n\n  // Handle text change with immediate UI update and debounced processing\n  const handleTextChange = useCallback((newValue: string) => {\n    // Immediate onChange for UI responsiveness\n    onChange(newValue)\n\n    if (!textareaRef.current) return\n\n    const cursorPos = textareaRef.current.selectionStart || 0\n\n    // Debounced trigger detection to reduce processing\n    debouncedTriggerDetection(newValue, cursorPos)\n  }, [onChange, debouncedTriggerDetection])\n\n  // Cleanup debounced functions on unmount\n  useEffect(() => {\n    return () => {\n      debouncedTriggerDetection.cancel()\n    }\n  }, [debouncedTriggerDetection])\n\n  // Handle keyboard navigation\n  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {\n    if (showDropdown && filteredItems.length > 0) {\n      switch (e.key) {\n        case 'ArrowDown':\n          e.preventDefault()\n          setSelectedIndex(prev =>\n            prev < filteredItems.length - 1 ? prev + 1 : 0\n          )\n          break\n        case 'ArrowUp':\n          e.preventDefault()\n          setSelectedIndex(prev =>\n            prev > 0 ? prev - 1 : filteredItems.length - 1\n          )\n          break\n        case 'Enter':\n        case 'Tab':\n          if (selectedIndex >= 0) {\n            e.preventDefault()\n            insertSuggestion(filteredItems[selectedIndex])\n          }\n          break\n        case 'Escape':\n          e.preventDefault()\n          setShowDropdown(false)\n          setSelectedIndex(-1)\n          break\n      }\n    }\n\n    onKeyDown?.(e)\n  }, [showDropdown, filteredItems, selectedIndex, onKeyDown, insertSuggestion])\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setShowDropdown(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => document.removeEventListener('mousedown', handleClickOutside)\n  }, [])\n\n  return (\n    <div className=\"relative\">\n      <textarea\n        ref={textareaRef}\n        value={value || ''}\n        onChange={(e) => handleTextChange(e.target.value)}\n        onKeyDown={handleKeyDown}\n        placeholder={placeholder}\n        className={`${className} ${enableDynamicHeight ? 'dynamic-textarea' : ''}`}\n        disabled={disabled}\n        style={enableDynamicHeight ? heightStyle : { height: 'auto' }}\n      />\n\n      {showDropdown && filteredItems && filteredItems.length > 0 && (\n        <div\n          ref={dropdownRef}\n          className=\"absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto\"\n        >\n          <div className=\"p-2 text-xs text-gray-500 border-b border-gray-100\">\n            {triggerChar === '#' ? 'Hashtags' : 'Folders'} - Use ↑↓ to navigate, Enter to select\n          </div>\n          {filteredItems.map((item, index) => {\n            if (!item || !item.id || !item.value) return null\n\n            return (\n              <div\n                key={item.id}\n                className={`flex items-center gap-2 px-3 py-2 cursor-pointer transition-colors ${\n                  index === selectedIndex\n                    ? 'bg-blue-50 text-blue-700'\n                    : 'hover:bg-gray-50'\n                }`}\n                onClick={() => insertSuggestion(item)}\n              >\n                {item.type === 'hashtag' ? (\n                  <Hash className=\"h-4 w-4 text-blue-500\" />\n                ) : (\n                  <Folder className=\"h-4 w-4 text-orange-500\" />\n                )}\n                <span className=\"flex-1 text-sm\">\n                  {item.value}\n                </span>\n                {item.usage_count && (\n                  <span className=\"text-xs text-gray-400\">\n                    {item.usage_count} uses\n                  </span>\n                )}\n              </div>\n            )\n          })}\n        </div>\n      )}\n    </div>\n  )\n}\n\nexport default SmartAutocomplete\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AACA;AACA;;;AALA;;;;;AAmCO,SAAS,kBAAkB,KAUT;QAVS,EAChC,KAAK,EACL,QAAQ,EACR,SAAS,EACT,WAAW,EACX,SAAS,EACT,cAAc;QAAE,UAAU,EAAE;QAAE,SAAS,EAAE;IAAC,CAAC,EAC3C,WAAW,KAAK,EAChB,sBAAsB,IAAI,EAC1B,eAAe,CAAC,CAAC,EACM,GAVS;;IAWhC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACzE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACpD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IACjE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACxD,MAAM,GAAG,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAuB;IAChD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,4BAA4B;IAC5B,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,yBAAsB,AAAD,EAC3C,aACA,OACA,sBAAsB,eAAe;IAGvC,oDAAoD;IACpD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC,MAAc;YAC/C,IAAI,cAAc,GAAG,OAAO;YAE5B,uDAAuD;YACvD,IAAK,IAAI,IAAI,YAAY,GAAG,KAAK,GAAG,IAAK;gBACvC,MAAM,OAAO,IAAI,CAAC,EAAE;gBAEpB,IAAI,SAAS,OAAO,SAAS,KAAK;oBAChC,sEAAsE;oBACtE,MAAM,WAAW,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,GAAG;oBACvC,IAAI,aAAa,OAAO,aAAa,QAAQ,MAAM,GAAG;wBACpD,MAAM,aAAa,KAAK,KAAK,CAAC,IAAI,GAAG;wBACrC,qDAAqD;wBACrD,IAAI,CAAC,WAAW,QAAQ,CAAC,QAAQ,CAAC,WAAW,QAAQ,CAAC,OAAO;4BAC3D,OAAO;gCACL,MAAM;gCACN,UAAU;gCACV,YAAY;4BACd;wBACF;oBACF;gBACF;gBAEA,oCAAoC;gBACpC,IAAI,SAAS,OAAO,SAAS,MAAM;oBACjC;gBACF;YACF;YAEA,OAAO;QACT;uDAAG,EAAE;IAEL,0CAA0C;IAC1C,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE,CAAC,YAAoB;YACzD,MAAM,QAAQ,SAAS,YAAa,CAAA,wBAAA,kCAAA,YAAa,QAAQ,KAAI,EAAE,GAAK,CAAA,wBAAA,kCAAA,YAAa,OAAO,KAAI,EAAE;YAE9F,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;gBAChC,OAAO,EAAE;YACX;YAEA,OAAO,MACJ,MAAM;oEAAC,CAAA;oBACN,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU,OAAO;oBAC9C,MAAM,YAAY,KAAK,OAAO,CAAC,SAAS,IAAI,6BAA6B;;oBACzE,OAAO,UAAU,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;gBAChE;mEACC,KAAK,CAAC,GAAG,GAAG,yCAAyC;aACrD,GAAG;oEAAC,CAAC,MAAM,QAAU,CAAC;wBACrB,IAAI,AAAC,GAAU,OAAR,MAAK,KAAS,OAAN;wBACf,OAAO;wBACP;wBACA,aAAa,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,mBAAmB;oBAClE,CAAC;mEACA,IAAI;oEAAC,CAAC,GAAG,IAAM,CAAC,EAAE,WAAW,IAAI,CAAC,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC;mEAAG,gBAAgB;;QACjF;2DAAG;QAAC;KAAY;IAEhB,6BAA6B;IAC7B,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,CAAC;gBAIJ;YAHhC,IAAI,oBAAoB,CAAC,GAAG;YAE5B,MAAM,gBAAgB,MAAM,KAAK,CAAC,GAAG;YACrC,MAAM,cAAc,MAAM,KAAK,CAAC,EAAA,uBAAA,YAAY,OAAO,cAAnB,2CAAA,qBAAqB,cAAc,KAAI;YAEvE,2BAA2B;YAC3B,MAAM,SAAS,KAAK,IAAI,KAAK,YAAY,MAAM;YAC/C,MAAM,aAAa,KAAK,KAAK,CAAC,UAAU,CAAC,UAAU,KAAK,KAAK,GAAG,AAAC,GAAW,OAAT,QAAoB,OAAX,KAAK,KAAK;YAEtF,MAAM,WAAW,gBAAgB,aAAa,MAAM;YACpD,SAAS;YAET,oDAAoD;YACpD;mEAAW;oBACT,IAAI,YAAY,OAAO,EAAE;wBACvB,MAAM,eAAe,cAAc,MAAM,GAAG,WAAW,MAAM,GAAG;wBAChE,YAAY,OAAO,CAAC,iBAAiB,CAAC,cAAc;wBACpD,YAAY,OAAO,CAAC,KAAK;oBAC3B;gBACF;kEAAG;YAEH,gBAAgB;YAChB,iBAAiB,CAAC;QACpB;0DAAG;QAAC;QAAO;QAAiB;KAAS;IAErC,qDAAqD;IACrD,MAAM,4BAA4B,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;gEACtC,IAAM,CAAA,GAAA,kLAAA,CAAA,WAAQ,AAAD;wEAAE,CAAC,UAAkB;oBAChC,IAAI;wBACF,MAAM,UAAU,cAAc,UAAU;wBAExC,IAAI,SAAS;4BACX,eAAe,QAAQ,IAAI;4BAC3B,mBAAmB,QAAQ,QAAQ;4BACnC,cAAc,QAAQ,UAAU;4BAEhC,MAAM,OAAO,QAAQ,IAAI,KAAK,MAAM,YAAY;4BAChD,MAAM,WAAW,kBAAkB,QAAQ,UAAU,EAAE;4BACvD,iBAAiB,YAAY,EAAE;4BAC/B,gBAAgB,YAAY,SAAS,MAAM,GAAG;4BAC9C,iBAAiB,CAAC;wBACpB,OAAO;4BACL,gBAAgB;4BAChB,eAAe;4BACf,mBAAmB,CAAC;4BACpB,cAAc;4BACd,iBAAiB,EAAE;4BACnB,iBAAiB,CAAC;wBACpB;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,+BAA+B;wBAC7C,uBAAuB;wBACvB,gBAAgB;wBAChB,eAAe;wBACf,mBAAmB,CAAC;wBACpB,cAAc;wBACd,iBAAiB,EAAE;wBACnB,iBAAiB,CAAC;oBACpB;gBACF;uEAAG;+DACH;QAAC;QAAe;KAAkB;IAGpC,uEAAuE;IACvE,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,CAAC;YACpC,2CAA2C;YAC3C,SAAS;YAET,IAAI,CAAC,YAAY,OAAO,EAAE;YAE1B,MAAM,YAAY,YAAY,OAAO,CAAC,cAAc,IAAI;YAExD,mDAAmD;YACnD,0BAA0B,UAAU;QACtC;0DAAG;QAAC;QAAU;KAA0B;IAExC,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR;+CAAO;oBACL,0BAA0B,MAAM;gBAClC;;QACF;sCAAG;QAAC;KAA0B;IAE9B,6BAA6B;IAC7B,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;YACjC,IAAI,gBAAgB,cAAc,MAAM,GAAG,GAAG;gBAC5C,OAAQ,EAAE,GAAG;oBACX,KAAK;wBACH,EAAE,cAAc;wBAChB;4EAAiB,CAAA,OACf,OAAO,cAAc,MAAM,GAAG,IAAI,OAAO,IAAI;;wBAE/C;oBACF,KAAK;wBACH,EAAE,cAAc;wBAChB;4EAAiB,CAAA,OACf,OAAO,IAAI,OAAO,IAAI,cAAc,MAAM,GAAG;;wBAE/C;oBACF,KAAK;oBACL,KAAK;wBACH,IAAI,iBAAiB,GAAG;4BACtB,EAAE,cAAc;4BAChB,iBAAiB,aAAa,CAAC,cAAc;wBAC/C;wBACA;oBACF,KAAK;wBACH,EAAE,cAAc;wBAChB,gBAAgB;wBAChB,iBAAiB,CAAC;wBAClB;gBACJ;YACF;YAEA,sBAAA,gCAAA,UAAY;QACd;uDAAG;QAAC;QAAc;QAAe;QAAe;QAAW;KAAiB;IAE5E,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;kEAAqB,CAAC;oBAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC9E,gBAAgB;oBAClB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;+CAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QACzD;sCAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,KAAK;gBACL,OAAO,SAAS;gBAChB,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gBAChD,WAAW;gBACX,aAAa;gBACb,WAAW,AAAC,GAAe,OAAb,WAAU,KAAiD,OAA9C,sBAAsB,qBAAqB;gBACtE,UAAU;gBACV,OAAO,sBAAsB,cAAc;oBAAE,QAAQ;gBAAO;;;;;;YAG7D,gBAAgB,iBAAiB,cAAc,MAAM,GAAG,mBACvD,6LAAC;gBACC,KAAK;gBACL,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;;4BACZ,gBAAgB,MAAM,aAAa;4BAAU;;;;;;;oBAE/C,cAAc,GAAG,CAAC,CAAC,MAAM;wBACxB,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,KAAK,EAAE,OAAO;wBAE7C,qBACE,6LAAC;4BAEC,WAAW,AAAC,sEAIX,OAHC,UAAU,gBACN,6BACA;4BAEN,SAAS,IAAM,iBAAiB;;gCAE/B,KAAK,IAAI,KAAK,0BACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;yDAEhB,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAEpB,6LAAC;oCAAK,WAAU;8CACb,KAAK,KAAK;;;;;;gCAEZ,KAAK,WAAW,kBACf,6LAAC;oCAAK,WAAU;;wCACb,KAAK,WAAW;wCAAC;;;;;;;;2BAlBjB,KAAK,EAAE;;;;;oBAuBlB;;;;;;;;;;;;;AAKV;GA9QgB;;QAsBU,2IAAA,CAAA,yBAAsB;;;KAtBhC;uCAgRD", "debugId": null}}, {"offset": {"line": 5818, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/progressive-loader.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, ReactNode } from 'react'\n\ninterface ProgressiveLoaderProps {\n  children: ReactNode\n  fallback?: ReactNode\n  delay?: number\n  className?: string\n}\n\ninterface LoadingSkeletonProps {\n  className?: string\n}\n\nexport function LoadingSkeleton({ className = '' }: LoadingSkeletonProps) {\n  return (\n    <div className={`animate-pulse ${className}`}>\n      <div className=\"space-y-4\">\n        {/* Header skeleton */}\n        <div className=\"h-8 bg-gray-200 rounded-lg w-1/3\"></div>\n        \n        {/* Content skeleton */}\n        <div className=\"space-y-3\">\n          <div className=\"h-4 bg-gray-200 rounded w-full\"></div>\n          <div className=\"h-4 bg-gray-200 rounded w-5/6\"></div>\n          <div className=\"h-4 bg-gray-200 rounded w-4/6\"></div>\n        </div>\n        \n        {/* Card skeletons */}\n        <div className=\"grid gap-4 mt-6\">\n          {[1, 2, 3].map((i) => (\n            <div key={i} className=\"border border-gray-200 rounded-lg p-4\">\n              <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n              <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  )\n}\n\nexport function ProgressiveLoader({ \n  children, \n  fallback, \n  delay = 200,\n  className = '' \n}: ProgressiveLoaderProps) {\n  const [isLoaded, setIsLoaded] = useState(false)\n  const [showFallback, setShowFallback] = useState(false)\n\n  useEffect(() => {\n    // Delay gösterimi için timer\n    const delayTimer = setTimeout(() => {\n      setShowFallback(true)\n    }, delay)\n\n    // Component yüklendiğinde\n    const loadTimer = setTimeout(() => {\n      setIsLoaded(true)\n    }, delay + 100)\n\n    return () => {\n      clearTimeout(delayTimer)\n      clearTimeout(loadTimer)\n    }\n  }, [delay])\n\n  if (!isLoaded) {\n    if (!showFallback) {\n      return null // İlk delay sırasında hiçbir şey gösterme\n    }\n    \n    return (\n      <div className={className}>\n        {fallback || <LoadingSkeleton />}\n      </div>\n    )\n  }\n\n  return <>{children}</>\n}\n\n// Specialized loaders for different content types\nexport function PromptListSkeleton() {\n  return (\n    <div className=\"space-y-3\">\n      {[1, 2, 3, 4, 5].map((i) => (\n        <div key={i} className=\"border border-gray-200 rounded-lg p-4 animate-pulse\">\n          <div className=\"flex items-start justify-between\">\n            <div className=\"flex-1\">\n              <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n              <div className=\"h-3 bg-gray-200 rounded w-1/2 mb-3\"></div>\n              <div className=\"flex gap-2\">\n                <div className=\"h-5 bg-blue-100 rounded-full w-16\"></div>\n                <div className=\"h-5 bg-green-100 rounded-full w-20\"></div>\n              </div>\n            </div>\n            <div className=\"h-8 w-8 bg-gray-200 rounded\"></div>\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n\nexport function SidebarSkeleton() {\n  return (\n    <div className=\"w-full h-full bg-white border-r border-gray-200 animate-pulse\">\n      <div className=\"p-4\">\n        <div className=\"h-8 bg-gray-200 rounded w-3/4 mb-4\"></div>\n        <div className=\"space-y-2\">\n          {[1, 2, 3, 4].map((i) => (\n            <div key={i} className=\"h-10 bg-gray-100 rounded\"></div>\n          ))}\n        </div>\n      </div>\n    </div>\n  )\n}\n\nexport function ContextGallerySkeleton() {\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 animate-pulse\">\n      {[1, 2, 3, 4, 5, 6].map((i) => (\n        <div key={i} className=\"border border-gray-200 rounded-lg p-4\">\n          <div className=\"h-6 bg-gray-200 rounded w-3/4 mb-3\"></div>\n          <div className=\"space-y-2\">\n            <div className=\"h-3 bg-gray-200 rounded w-full\"></div>\n            <div className=\"h-3 bg-gray-200 rounded w-5/6\"></div>\n            <div className=\"h-3 bg-gray-200 rounded w-4/6\"></div>\n          </div>\n          <div className=\"flex gap-2 mt-4\">\n            <div className=\"h-5 bg-blue-100 rounded-full w-16\"></div>\n            <div className=\"h-5 bg-purple-100 rounded-full w-20\"></div>\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n\n// Hook for progressive loading with intersection observer\nexport function useProgressiveLoading(threshold = 0.1) {\n  const [isVisible, setIsVisible] = useState(false)\n  const [ref, setRef] = useState<HTMLElement | null>(null)\n\n  useEffect(() => {\n    if (!ref) return\n\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true)\n          observer.disconnect()\n        }\n      },\n      { threshold }\n    )\n\n    observer.observe(ref)\n\n    return () => observer.disconnect()\n  }, [ref, threshold])\n\n  return { isVisible, ref: setRef }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA;;;AAFA;;AAeO,SAAS,gBAAgB,KAAwC;QAAxC,EAAE,YAAY,EAAE,EAAwB,GAAxC;IAC9B,qBACE,6LAAC;QAAI,WAAW,AAAC,iBAA0B,OAAV;kBAC/B,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;;;;;8BAGf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;8BAIjB,6LAAC;oBAAI,WAAU;8BACZ;wBAAC;wBAAG;wBAAG;qBAAE,CAAC,GAAG,CAAC,CAAC,kBACd,6LAAC;4BAAY,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;2BAFP;;;;;;;;;;;;;;;;;;;;;AAStB;KA1BgB;AA4BT,SAAS,kBAAkB,KAKT;QALS,EAChC,QAAQ,EACR,QAAQ,EACR,QAAQ,GAAG,EACX,YAAY,EAAE,EACS,GALS;;IAMhC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,6BAA6B;YAC7B,MAAM,aAAa;0DAAW;oBAC5B,gBAAgB;gBAClB;yDAAG;YAEH,0BAA0B;YAC1B,MAAM,YAAY;yDAAW;oBAC3B,YAAY;gBACd;wDAAG,QAAQ;YAEX;+CAAO;oBACL,aAAa;oBACb,aAAa;gBACf;;QACF;sCAAG;QAAC;KAAM;IAEV,IAAI,CAAC,UAAU;QACb,IAAI,CAAC,cAAc;YACjB,OAAO,KAAK,0CAA0C;;QACxD;QAEA,qBACE,6LAAC;YAAI,WAAW;sBACb,0BAAY,6LAAC;;;;;;;;;;IAGpB;IAEA,qBAAO;kBAAG;;AACZ;GAvCgB;MAAA;AA0CT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACZ;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE,CAAC,GAAG,CAAC,CAAC,kBACpB,6LAAC;gBAAY,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;sCAGnB,6LAAC;4BAAI,WAAU;;;;;;;;;;;;eAVT;;;;;;;;;;AAgBlB;MApBgB;AAsBT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;8BACZ;wBAAC;wBAAG;wBAAG;wBAAG;qBAAE,CAAC,GAAG,CAAC,CAAC,kBACjB,6LAAC;4BAAY,WAAU;2BAAb;;;;;;;;;;;;;;;;;;;;;AAMtB;MAbgB;AAeT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACZ;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE,CAAC,GAAG,CAAC,CAAC,kBACvB,6LAAC;gBAAY,WAAU;;kCACrB,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;eATT;;;;;;;;;;AAelB;MAnBgB;AAsBT,SAAS;QAAsB,YAAA,iEAAY;;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,IAAI,CAAC,KAAK;YAEV,MAAM,WAAW,IAAI;mDACnB;wBAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;wBACb,SAAS,UAAU;oBACrB;gBACF;kDACA;gBAAE;YAAU;YAGd,SAAS,OAAO,CAAC;YAEjB;mDAAO,IAAM,SAAS,UAAU;;QAClC;0CAAG;QAAC;QAAK;KAAU;IAEnB,OAAO;QAAE;QAAW,KAAK;IAAO;AAClC;IAvBgB", "debugId": null}}, {"offset": {"line": 6241, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Dialog = DialogPrimitive.Root\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger\r\n\r\nconst DialogPortal = DialogPrimitive.Portal\r\n\r\nconst DialogClose = DialogPrimitive.Close\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Kapat</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n))\r\nDialogContent.displayName = DialogPrimitive.Content.displayName\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogHeader.displayName = \"DialogHeader\"\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogFooter.displayName = \"DialogFooter\"\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogClose,\r\n  DialogTrigger,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n} "], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,qKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,qKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,6JAAA,CAAA,aAAgB,CAGpC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;;KAVP;AAaN,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,6JAAA,CAAA,aAAgB,OAGpC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClC,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe;QAAC,EACpB,SAAS,EACT,GAAG,OACkC;yBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe;QAAC,EACpB,SAAS,EACT,GAAG,OACkC;yBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,6JAAA,CAAA,aAAgB,OAGxC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,kBAAkB,WAAW,GAAG,qKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 6410, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,KAGoC;QAHpC,EACb,SAAS,EACT,GAAG,OAC8C,GAHpC;IAIb,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 6445, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/hooks/use-shared-prompts.ts"], "sourcesContent": ["'use client'\n\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\nimport { Database } from '@/lib/supabase'\nimport { toast } from 'sonner'\n\n// Type definitions\nexport type SharedPrompt = {\n  id: string\n  prompt_id: string\n  user_id: string\n  share_token: string\n  title: string | null\n  description: string | null\n  is_public: boolean\n  password_hash: string | null\n  expires_at: string | null\n  view_count: number\n  copy_count: number\n  is_active: boolean\n  metadata: any\n  created_at: string\n  updated_at: string\n}\n\nexport type SharedPromptWithDetails = SharedPrompt & {\n  prompt: {\n    id: string\n    prompt_text: string\n    title: string | null\n    description: string | null\n    category: string | null\n    tags: string[]\n    task_code: string | null\n    created_at: string\n  }\n  project_name: string\n  author_email: string\n}\n\nexport type CreateSharedPromptParams = {\n  prompt_id: string\n  title?: string\n  description?: string\n  is_public?: boolean\n  password?: string\n  expires_at?: string\n}\n\nexport type ShareUrlResult = {\n  id: string\n  share_token: string\n  share_url: string\n  created_at: string\n}\n\n// Hook: Kullanıcının paylaştığı prompt'ları listele\nexport function useUserSharedPrompts() {\n  return useQuery({\n    queryKey: ['shared-prompts', 'user'],\n    queryFn: async () => {\n      const { data: user } = await supabase.auth.getUser()\n      if (!user.user) throw new Error('Not authenticated')\n\n      const { data, error } = await supabase\n        .from('shared_prompts')\n        .select(`\n          *,\n          prompt:prompts(\n            id,\n            prompt_text,\n            title,\n            description,\n            category,\n            tags,\n            task_code,\n            created_at,\n            project:projects(name)\n          )\n        `)\n        .eq('user_id', user.user.id)\n        .eq('is_active', true)\n        .order('created_at', { ascending: false })\n\n      if (error) throw error\n\n      return data.map(item => ({\n        ...item,\n        project_name: item.prompt?.project?.name || 'Unknown Project'\n      })) as (SharedPrompt & { \n        prompt: any\n        project_name: string \n      })[]\n    },\n    staleTime: 5 * 60 * 1000, // 5 dakika\n  })\n}\n\n// Hook: Paylaşım oluştur\nexport function useCreateSharedPrompt() {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async (params: CreateSharedPromptParams): Promise<ShareUrlResult> => {\n      const { data: user } = await supabase.auth.getUser()\n      if (!user.user) throw new Error('Not authenticated')\n\n      // Önce prompt'un kullanıcıya ait olduğunu kontrol et\n      const { data: prompt, error: promptError } = await supabase\n        .from('prompts')\n        .select('id, user_id')\n        .eq('id', params.prompt_id)\n        .eq('user_id', user.user.id)\n        .single()\n\n      if (promptError || !prompt) {\n        throw new Error('Prompt not found or access denied')\n      }\n\n      // Paylaşım token'ı oluştur\n      const shareToken = generateShareToken()\n      \n      // Şifre hash'le (eğer varsa)\n      let passwordHash = null\n      if (params.password) {\n        // Client-side'da basit hash (production'da server-side yapılmalı)\n        passwordHash = await hashPassword(params.password)\n      }\n\n      const { data, error } = await supabase\n        .from('shared_prompts')\n        .insert({\n          prompt_id: params.prompt_id,\n          user_id: user.user.id,\n          share_token: shareToken,\n          title: params.title || null,\n          description: params.description || null,\n          is_public: params.is_public ?? true,\n          password_hash: passwordHash,\n          expires_at: params.expires_at || null,\n        })\n        .select()\n        .single()\n\n      if (error) throw error\n\n      const shareUrl = `${window.location.origin}/share/${shareToken}`\n\n      return {\n        id: data.id,\n        share_token: shareToken,\n        share_url: shareUrl,\n        created_at: data.created_at\n      }\n    },\n    onSuccess: (data) => {\n      queryClient.invalidateQueries({ queryKey: ['shared-prompts'] })\n      toast.success('Paylaşım linki oluşturuldu!', {\n        description: 'Link panoya kopyalandı'\n      })\n      \n      // Linki panoya kopyala\n      navigator.clipboard.writeText(data.share_url).catch(console.error)\n    },\n    onError: (error) => {\n      console.error('Share creation error:', error)\n      toast.error('Paylaşım oluşturulamadı', {\n        description: error instanceof Error ? error.message : 'Bilinmeyen hata'\n      })\n    }\n  })\n}\n\n// Hook: Paylaşımı sil/deaktive et\nexport function useDeleteSharedPrompt() {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async (sharedPromptId: string) => {\n      const { data: user } = await supabase.auth.getUser()\n      if (!user.user) throw new Error('Not authenticated')\n\n      const { error } = await supabase\n        .from('shared_prompts')\n        .update({ is_active: false })\n        .eq('id', sharedPromptId)\n        .eq('user_id', user.user.id)\n\n      if (error) throw error\n    },\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: ['shared-prompts'] })\n      toast.success('Paylaşım kaldırıldı')\n    },\n    onError: (error) => {\n      console.error('Delete shared prompt error:', error)\n      toast.error('Paylaşım kaldırılamadı')\n    }\n  })\n}\n\n// Hook: Paylaşılan prompt'u görüntüle (public)\nexport function useSharedPrompt(shareToken: string, password?: string) {\n  return useQuery({\n    queryKey: ['shared-prompt', shareToken, password],\n    queryFn: async (): Promise<SharedPromptWithDetails> => {\n      // Önce paylaşımı al\n      const { data: sharedPrompt, error } = await supabase\n        .from('shared_prompts')\n        .select(`\n          *,\n          prompt:prompts(\n            id,\n            prompt_text,\n            title,\n            description,\n            category,\n            tags,\n            task_code,\n            created_at,\n            project:projects(name)\n          )\n        `)\n        .eq('share_token', shareToken)\n        .eq('is_active', true)\n        .single()\n\n      if (error) throw new Error('Paylaşım bulunamadı veya süresi dolmuş')\n\n      // Şifre kontrolü (basit client-side kontrol)\n      if (sharedPrompt.password_hash && password) {\n        const isPasswordValid = await verifyPassword(password, sharedPrompt.password_hash)\n        if (!isPasswordValid) {\n          throw new Error('Şifre yanlış')\n        }\n      } else if (sharedPrompt.password_hash && !password) {\n        throw new Error('Şifre gerekli')\n      }\n\n      // Son kullanma tarihi kontrolü\n      if (sharedPrompt.expires_at && new Date(sharedPrompt.expires_at) < new Date()) {\n        throw new Error('Paylaşımın süresi dolmuş')\n      }\n\n      // Görüntülenme sayısını artır\n      supabase\n        .from('shared_prompts')\n        .update({ view_count: sharedPrompt.view_count + 1 })\n        .eq('id', sharedPrompt.id)\n        .then(() => {\n          // Analytics kaydı ekle\n          recordView(shareToken)\n        })\n\n      return {\n        ...sharedPrompt,\n        project_name: sharedPrompt.prompt?.project?.name || 'Unknown Project',\n        author_email: 'Unknown Author' // Bu bilgiyi ayrı bir query ile alacağız\n      } as SharedPromptWithDetails\n    },\n    enabled: !!shareToken,\n    staleTime: 0, // Her zaman fresh data al\n    retry: false, // Hata durumunda tekrar deneme\n  })\n}\n\n// Utility functions\nfunction generateShareToken(): string {\n  // Güvenli token oluştur\n  const array = new Uint8Array(16)\n  crypto.getRandomValues(array)\n  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')\n}\n\nasync function hashPassword(password: string): Promise<string> {\n  // Basit client-side hash (production'da server-side yapılmalı)\n  const encoder = new TextEncoder()\n  const data = encoder.encode(password)\n  const hash = await crypto.subtle.digest('SHA-256', data)\n  return Array.from(new Uint8Array(hash), b => b.toString(16).padStart(2, '0')).join('')\n}\n\nasync function verifyPassword(password: string, hash: string): Promise<boolean> {\n  const passwordHash = await hashPassword(password)\n  return passwordHash === hash\n}\n\nfunction recordView(shareToken: string) {\n  // Analytics için view kaydı\n  const viewData = {\n    share_token: shareToken,\n    viewer_ip: null, // Server-side'da alınmalı\n    viewer_user_agent: navigator.userAgent,\n    referrer: document.referrer || null,\n    session_id: generateSessionId(),\n    viewed_at: new Date().toISOString()\n  }\n\n  // Bu veri server-side endpoint'e gönderilmeli\n  fetch('/api/shared-prompts/record-view', {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify(viewData)\n  }).catch(console.error)\n}\n\nfunction generateSessionId(): string {\n  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AAAA;AAAA;AACA;AAEA;;AALA;;;;AA0DO,SAAS;;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAkB;SAAO;QACpC,OAAO;6CAAE;gBACP,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;gBAClD,IAAI,CAAC,KAAK,IAAI,EAAE,MAAM,IAAI,MAAM;gBAEhC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,kBACL,MAAM,CAAE,4RAcR,EAAE,CAAC,WAAW,KAAK,IAAI,CAAC,EAAE,EAC1B,EAAE,CAAC,aAAa,MAChB,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAM;gBAE1C,IAAI,OAAO,MAAM;gBAEjB,OAAO,KAAK,GAAG;qDAAC,CAAA;4BAEA,sBAAA;+BAFS;4BACvB,GAAG,IAAI;4BACP,cAAc,EAAA,eAAA,KAAK,MAAM,cAAX,oCAAA,uBAAA,aAAa,OAAO,cAApB,2CAAA,qBAAsB,IAAI,KAAI;wBAC9C;;;YAIF;;QACA,WAAW,IAAI,KAAK;IACtB;AACF;GAvCgB;;QACP,8KAAA,CAAA,WAAQ;;;AAyCV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;iDAAE,OAAO;gBACjB,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;gBAClD,IAAI,CAAC,KAAK,IAAI,EAAE,MAAM,IAAI,MAAM;gBAEhC,qDAAqD;gBACrD,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACxD,IAAI,CAAC,WACL,MAAM,CAAC,eACP,EAAE,CAAC,MAAM,OAAO,SAAS,EACzB,EAAE,CAAC,WAAW,KAAK,IAAI,CAAC,EAAE,EAC1B,MAAM;gBAET,IAAI,eAAe,CAAC,QAAQ;oBAC1B,MAAM,IAAI,MAAM;gBAClB;gBAEA,2BAA2B;gBAC3B,MAAM,aAAa;gBAEnB,6BAA6B;gBAC7B,IAAI,eAAe;gBACnB,IAAI,OAAO,QAAQ,EAAE;oBACnB,kEAAkE;oBAClE,eAAe,MAAM,aAAa,OAAO,QAAQ;gBACnD;oBAUe;gBARf,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,kBACL,MAAM,CAAC;oBACN,WAAW,OAAO,SAAS;oBAC3B,SAAS,KAAK,IAAI,CAAC,EAAE;oBACrB,aAAa;oBACb,OAAO,OAAO,KAAK,IAAI;oBACvB,aAAa,OAAO,WAAW,IAAI;oBACnC,WAAW,CAAA,oBAAA,OAAO,SAAS,cAAhB,+BAAA,oBAAoB;oBAC/B,eAAe;oBACf,YAAY,OAAO,UAAU,IAAI;gBACnC,GACC,MAAM,GACN,MAAM;gBAET,IAAI,OAAO,MAAM;gBAEjB,MAAM,WAAW,AAAC,GAAkC,OAAhC,OAAO,QAAQ,CAAC,MAAM,EAAC,WAAoB,OAAX;gBAEpD,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,aAAa;oBACb,WAAW;oBACX,YAAY,KAAK,UAAU;gBAC7B;YACF;;QACA,SAAS;iDAAE,CAAC;gBACV,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAiB;gBAAC;gBAC7D,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,+BAA+B;oBAC3C,aAAa;gBACf;gBAEA,uBAAuB;gBACvB,UAAU,SAAS,CAAC,SAAS,CAAC,KAAK,SAAS,EAAE,KAAK,CAAC,QAAQ,KAAK;YACnE;;QACA,OAAO;iDAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,2BAA2B;oBACrC,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACxD;YACF;;IACF;AACF;IAxEgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAwEb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;iDAAE,OAAO;gBACjB,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;gBAClD,IAAI,CAAC,KAAK,IAAI,EAAE,MAAM,IAAI,MAAM;gBAEhC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAC7B,IAAI,CAAC,kBACL,MAAM,CAAC;oBAAE,WAAW;gBAAM,GAC1B,EAAE,CAAC,MAAM,gBACT,EAAE,CAAC,WAAW,KAAK,IAAI,CAAC,EAAE;gBAE7B,IAAI,OAAO,MAAM;YACnB;;QACA,SAAS;iDAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAiB;gBAAC;gBAC7D,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;iDAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;;IACF;AACF;IAzBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAyBb,SAAS,gBAAgB,UAAkB,EAAE,QAAiB;;IACnE,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAiB;YAAY;SAAS;QACjD,OAAO;wCAAE;oBAmDS,8BAAA;gBAlDhB,oBAAoB;gBACpB,MAAM,EAAE,MAAM,YAAY,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACjD,IAAI,CAAC,kBACL,MAAM,CAAE,4RAcR,EAAE,CAAC,eAAe,YAClB,EAAE,CAAC,aAAa,MAChB,MAAM;gBAET,IAAI,OAAO,MAAM,IAAI,MAAM;gBAE3B,6CAA6C;gBAC7C,IAAI,aAAa,aAAa,IAAI,UAAU;oBAC1C,MAAM,kBAAkB,MAAM,eAAe,UAAU,aAAa,aAAa;oBACjF,IAAI,CAAC,iBAAiB;wBACpB,MAAM,IAAI,MAAM;oBAClB;gBACF,OAAO,IAAI,aAAa,aAAa,IAAI,CAAC,UAAU;oBAClD,MAAM,IAAI,MAAM;gBAClB;gBAEA,+BAA+B;gBAC/B,IAAI,aAAa,UAAU,IAAI,IAAI,KAAK,aAAa,UAAU,IAAI,IAAI,QAAQ;oBAC7E,MAAM,IAAI,MAAM;gBAClB;gBAEA,8BAA8B;gBAC9B,oIAAA,CAAA,kBAAQ,CACL,IAAI,CAAC,kBACL,MAAM,CAAC;oBAAE,YAAY,aAAa,UAAU,GAAG;gBAAE,GACjD,EAAE,CAAC,MAAM,aAAa,EAAE,EACxB,IAAI;gDAAC;wBACJ,uBAAuB;wBACvB,WAAW;oBACb;;gBAEF,OAAO;oBACL,GAAG,YAAY;oBACf,cAAc,EAAA,uBAAA,aAAa,MAAM,cAAnB,4CAAA,+BAAA,qBAAqB,OAAO,cAA5B,mDAAA,6BAA8B,IAAI,KAAI;oBACpD,cAAc,iBAAiB,yCAAyC;gBAC1E;YACF;;QACA,SAAS,CAAC,CAAC;QACX,WAAW;QACX,OAAO;IACT;AACF;IA9DgB;;QACP,8KAAA,CAAA,WAAQ;;;AA+DjB,oBAAoB;AACpB,SAAS;IACP,wBAAwB;IACxB,MAAM,QAAQ,IAAI,WAAW;IAC7B,OAAO,eAAe,CAAC;IACvB,OAAO,MAAM,IAAI,CAAC,OAAO,CAAA,OAAQ,KAAK,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,MAAM,IAAI,CAAC;AAC5E;AAEA,eAAe,aAAa,QAAgB;IAC1C,+DAA+D;IAC/D,MAAM,UAAU,IAAI;IACpB,MAAM,OAAO,QAAQ,MAAM,CAAC;IAC5B,MAAM,OAAO,MAAM,OAAO,MAAM,CAAC,MAAM,CAAC,WAAW;IACnD,OAAO,MAAM,IAAI,CAAC,IAAI,WAAW,OAAO,CAAA,IAAK,EAAE,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,MAAM,IAAI,CAAC;AACrF;AAEA,eAAe,eAAe,QAAgB,EAAE,IAAY;IAC1D,MAAM,eAAe,MAAM,aAAa;IACxC,OAAO,iBAAiB;AAC1B;AAEA,SAAS,WAAW,UAAkB;IACpC,4BAA4B;IAC5B,MAAM,WAAW;QACf,aAAa;QACb,WAAW;QACX,mBAAmB,UAAU,SAAS;QACtC,UAAU,SAAS,QAAQ,IAAI;QAC/B,YAAY;QACZ,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,8CAA8C;IAC9C,MAAM,mCAAmC;QACvC,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,MAAM,KAAK,SAAS,CAAC;IACvB,GAAG,KAAK,CAAC,QAAQ,KAAK;AACxB;AAEA,SAAS;IACP,OAAO,AAAC,WAAwB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AACvE", "debugId": null}}, {"offset": {"line": 6707, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/share-prompt-button.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Share2, <PERSON><PERSON>, Eye, EyeOff, Calendar, Lock, Globe, Link2 } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { \n  Dialog, \n  DialogContent, \n  DialogDescription, \n  DialogHeader, \n  DialogTitle, \n  DialogTrigger \n} from '@/components/ui/dialog'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Switch } from '@/components/ui/switch'\nimport { Badge } from '@/components/ui/badge'\nimport { Separator } from '@/components/ui/separator'\nimport { useCreateSharedPrompt, useUserSharedPrompts } from '@/hooks/use-shared-prompts'\nimport { toast } from 'sonner'\n\ninterface SharePromptButtonProps {\n  promptId: string\n  promptTitle?: string\n  promptText: string\n  taskCode?: string\n  className?: string\n  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'\n  size?: 'default' | 'sm' | 'lg' | 'icon'\n}\n\nexport function SharePromptButton({\n  promptId,\n  promptTitle,\n  promptText,\n  taskCode,\n  className,\n  variant = 'ghost',\n  size = 'sm'\n}: SharePromptButtonProps) {\n  const [isOpen, setIsOpen] = useState(false)\n  const [shareTitle, setShareTitle] = useState(promptTitle || taskCode || '')\n  const [shareDescription, setShareDescription] = useState('')\n  const [isPublic, setIsPublic] = useState(true)\n  const [password, setPassword] = useState('')\n  const [expiresAt, setExpiresAt] = useState('')\n  const [showPassword, setShowPassword] = useState(false)\n\n  const createSharedPromptMutation = useCreateSharedPrompt()\n  const { data: userSharedPrompts } = useUserSharedPrompts()\n\n  // Bu prompt zaten paylaşılmış mı kontrol et\n  const existingShare = userSharedPrompts?.find(sp => sp.prompt_id === promptId)\n\n  const handleShare = async () => {\n    try {\n      await createSharedPromptMutation.mutateAsync({\n        prompt_id: promptId,\n        title: shareTitle.trim() || undefined,\n        description: shareDescription.trim() || undefined,\n        is_public: isPublic,\n        password: password.trim() || undefined,\n        expires_at: expiresAt || undefined\n      })\n      \n      setIsOpen(false)\n      resetForm()\n    } catch (error) {\n      console.error('Share error:', error)\n    }\n  }\n\n  const resetForm = () => {\n    setShareTitle(promptTitle || taskCode || '')\n    setShareDescription('')\n    setIsPublic(true)\n    setPassword('')\n    setExpiresAt('')\n    setShowPassword(false)\n  }\n\n  const copyExistingLink = async () => {\n    if (existingShare) {\n      const shareUrl = `${window.location.origin}/share/${existingShare.share_token}`\n      try {\n        await navigator.clipboard.writeText(shareUrl)\n        toast.success('Link panoya kopyalandı!')\n      } catch (error) {\n        toast.error('Link kopyalanamadı')\n      }\n    }\n  }\n\n  const formatPromptPreview = (text: string, maxLength: number = 100) => {\n    if (text.length <= maxLength) return text\n    return text.substring(0, maxLength) + '...'\n  }\n\n  return (\n    <Dialog open={isOpen} onOpenChange={setIsOpen}>\n      <DialogTrigger asChild>\n        <Button\n          variant={variant}\n          size={size}\n          className={className}\n          onClick={() => setIsOpen(true)}\n        >\n          <Share2 className=\"h-4 w-4\" />\n        </Button>\n      </DialogTrigger>\n\n      <DialogContent className=\"sm:max-w-md\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Share2 className=\"h-5 w-5\" />\n            Prompt Paylaş\n          </DialogTitle>\n          <DialogDescription>\n            Bu prompt'u başkalarıyla paylaşmak için bir link oluşturun\n          </DialogDescription>\n        </DialogHeader>\n\n        {existingShare ? (\n          // Zaten paylaşılmış prompt için mevcut link göster\n          <div className=\"space-y-4\">\n            <div className=\"p-4 bg-green-50 border border-green-200 rounded-lg\">\n              <div className=\"flex items-center gap-2 mb-2\">\n                <Link2 className=\"h-4 w-4 text-green-600\" />\n                <span className=\"text-sm font-medium text-green-800\">\n                  Bu prompt zaten paylaşılmış\n                </span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <Input\n                  value={`${window.location.origin}/share/${existingShare.share_token}`}\n                  readOnly\n                  className=\"text-xs bg-white\"\n                />\n                <Button\n                  size=\"sm\"\n                  onClick={copyExistingLink}\n                  className=\"shrink-0\"\n                >\n                  <Copy className=\"h-4 w-4\" />\n                </Button>\n              </div>\n              <div className=\"mt-2 flex items-center gap-4 text-xs text-green-700\">\n                <span className=\"flex items-center gap-1\">\n                  <Eye className=\"h-3 w-3\" />\n                  {existingShare.view_count} görüntülenme\n                </span>\n                <span>\n                  {new Date(existingShare.created_at).toLocaleDateString('tr-TR')}\n                </span>\n              </div>\n            </div>\n\n            <div className=\"p-3 bg-gray-50 rounded-lg\">\n              <h4 className=\"text-sm font-medium mb-2\">Prompt Önizleme</h4>\n              <p className=\"text-xs text-gray-600 font-mono\">\n                {formatPromptPreview(promptText)}\n              </p>\n            </div>\n          </div>\n        ) : (\n          // Yeni paylaşım oluşturma formu\n          <div className=\"space-y-4\">\n            {/* Prompt Önizleme */}\n            <div className=\"p-3 bg-gray-50 rounded-lg\">\n              <h4 className=\"text-sm font-medium mb-2\">Prompt Önizleme</h4>\n              <p className=\"text-xs text-gray-600 font-mono\">\n                {formatPromptPreview(promptText)}\n              </p>\n            </div>\n\n            <Separator />\n\n            {/* Paylaşım Ayarları */}\n            <div className=\"space-y-4\">\n              <div>\n                <Label htmlFor=\"share-title\">Başlık (Opsiyonel)</Label>\n                <Input\n                  id=\"share-title\"\n                  value={shareTitle}\n                  onChange={(e) => setShareTitle(e.target.value)}\n                  placeholder=\"Paylaşım için özel başlık\"\n                  className=\"mt-1\"\n                />\n              </div>\n\n              <div>\n                <Label htmlFor=\"share-description\">Açıklama (Opsiyonel)</Label>\n                <Textarea\n                  id=\"share-description\"\n                  value={shareDescription}\n                  onChange={(e) => setShareDescription(e.target.value)}\n                  placeholder=\"Bu prompt hakkında kısa açıklama\"\n                  className=\"mt-1 resize-none\"\n                  rows={2}\n                />\n              </div>\n\n              {/* Görünürlük Ayarları */}\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-2\">\n                  {isPublic ? (\n                    <Globe className=\"h-4 w-4 text-green-600\" />\n                  ) : (\n                    <Lock className=\"h-4 w-4 text-orange-600\" />\n                  )}\n                  <Label htmlFor=\"is-public\">Herkese Açık</Label>\n                </div>\n                <Switch\n                  id=\"is-public\"\n                  checked={isPublic}\n                  onCheckedChange={setIsPublic}\n                />\n              </div>\n\n              {/* Şifre Koruması */}\n              <div>\n                <Label htmlFor=\"password\">Şifre Koruması (Opsiyonel)</Label>\n                <div className=\"flex gap-2 mt-1\">\n                  <Input\n                    id=\"password\"\n                    type={showPassword ? 'text' : 'password'}\n                    value={password}\n                    onChange={(e) => setPassword(e.target.value)}\n                    placeholder=\"Şifre belirleyin\"\n                  />\n                  <Button\n                    type=\"button\"\n                    variant=\"outline\"\n                    size=\"icon\"\n                    onClick={() => setShowPassword(!showPassword)}\n                  >\n                    {showPassword ? (\n                      <EyeOff className=\"h-4 w-4\" />\n                    ) : (\n                      <Eye className=\"h-4 w-4\" />\n                    )}\n                  </Button>\n                </div>\n              </div>\n\n              {/* Son Kullanma Tarihi */}\n              <div>\n                <Label htmlFor=\"expires-at\">Son Kullanma Tarihi (Opsiyonel)</Label>\n                <Input\n                  id=\"expires-at\"\n                  type=\"datetime-local\"\n                  value={expiresAt}\n                  onChange={(e) => setExpiresAt(e.target.value)}\n                  className=\"mt-1\"\n                  min={new Date().toISOString().slice(0, 16)}\n                />\n              </div>\n            </div>\n\n            <Separator />\n\n            {/* Paylaş Butonu */}\n            <div className=\"flex gap-2\">\n              <Button\n                onClick={handleShare}\n                disabled={createSharedPromptMutation.isPending}\n                className=\"flex-1\"\n              >\n                {createSharedPromptMutation.isPending ? (\n                  'Oluşturuluyor...'\n                ) : (\n                  <>\n                    <Share2 className=\"h-4 w-4 mr-2\" />\n                    Paylaşım Linki Oluştur\n                  </>\n                )}\n              </Button>\n              <Button\n                variant=\"outline\"\n                onClick={() => setIsOpen(false)}\n              >\n                İptal\n              </Button>\n            </div>\n          </div>\n        )}\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAQA;AACA;AACA;AACA;AAEA;AACA;AACA;;;AApBA;;;;;;;;;;;;AAgCO,SAAS,kBAAkB,KAQT;QARS,EAChC,QAAQ,EACR,WAAW,EACX,UAAU,EACV,QAAQ,EACR,SAAS,EACT,UAAU,OAAO,EACjB,OAAO,IAAI,EACY,GARS;;IAShC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,YAAY;IACxE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,6BAA6B,CAAA,GAAA,2IAAA,CAAA,wBAAqB,AAAD;IACvD,MAAM,EAAE,MAAM,iBAAiB,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,uBAAoB,AAAD;IAEvD,4CAA4C;IAC5C,MAAM,gBAAgB,8BAAA,wCAAA,kBAAmB,IAAI,CAAC,CAAA,KAAM,GAAG,SAAS,KAAK;IAErE,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,2BAA2B,WAAW,CAAC;gBAC3C,WAAW;gBACX,OAAO,WAAW,IAAI,MAAM;gBAC5B,aAAa,iBAAiB,IAAI,MAAM;gBACxC,WAAW;gBACX,UAAU,SAAS,IAAI,MAAM;gBAC7B,YAAY,aAAa;YAC3B;YAEA,UAAU;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;QAChC;IACF;IAEA,MAAM,YAAY;QAChB,cAAc,eAAe,YAAY;QACzC,oBAAoB;QACpB,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,gBAAgB;IAClB;IAEA,MAAM,mBAAmB;QACvB,IAAI,eAAe;YACjB,MAAM,WAAW,AAAC,GAAkC,OAAhC,OAAO,QAAQ,CAAC,MAAM,EAAC,WAAmC,OAA1B,cAAc,WAAW;YAC7E,IAAI;gBACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;gBACpC,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAO;gBACd,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;IACF;IAEA,MAAM,sBAAsB,SAAC;YAAc,6EAAoB;QAC7D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;QACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;IACxC;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;;0BAClC,6LAAC,qIAAA,CAAA,gBAAa;gBAAC,OAAO;0BACpB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAS;oBACT,MAAM;oBACN,WAAW;oBACX,SAAS,IAAM,UAAU;8BAEzB,cAAA,6LAAC,6MAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAItB,6LAAC,qIAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACvB,6LAAC,qIAAA,CAAA,eAAY;;0CACX,6LAAC,qIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGhC,6LAAC,qIAAA,CAAA,oBAAiB;0CAAC;;;;;;;;;;;;oBAKpB,gBACC,mDAAmD;kCACnD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,2MAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDAAK,WAAU;0DAAqC;;;;;;;;;;;;kDAIvD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDACJ,OAAO,AAAC,GAAkC,OAAhC,OAAO,QAAQ,CAAC,MAAM,EAAC,WAAmC,OAA1B,cAAc,WAAW;gDACnE,QAAQ;gDACR,WAAU;;;;;;0DAEZ,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAS;gDACT,WAAU;0DAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;;kEACd,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDACd,cAAc,UAAU;oDAAC;;;;;;;0DAE5B,6LAAC;0DACE,IAAI,KAAK,cAAc,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;0CAK7D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2B;;;;;;kDACzC,6LAAC;wCAAE,WAAU;kDACV,oBAAoB;;;;;;;;;;;;;;;;;+BAK3B,gCAAgC;kCAChC,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2B;;;;;;kDACzC,6LAAC;wCAAE,WAAU;kDACV,oBAAoB;;;;;;;;;;;;0CAIzB,6LAAC,wIAAA,CAAA,YAAS;;;;;0CAGV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAc;;;;;;0DAC7B,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAId,6LAAC;;0DACC,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAoB;;;;;;0DACnC,6LAAC,uIAAA,CAAA,WAAQ;gDACP,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gDACnD,aAAY;gDACZ,WAAU;gDACV,MAAM;;;;;;;;;;;;kDAKV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;oDACZ,yBACC,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;6EAEjB,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAElB,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAY;;;;;;;;;;;;0DAE7B,6LAAC,qIAAA,CAAA,SAAM;gDACL,IAAG;gDACH,SAAS;gDACT,iBAAiB;;;;;;;;;;;;kDAKrB,6LAAC;;0DACC,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAW;;;;;;0DAC1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAM,eAAe,SAAS;wDAC9B,OAAO;wDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wDAC3C,aAAY;;;;;;kEAEd,6LAAC,qIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,gBAAgB,CAAC;kEAE/B,6BACC,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;iFAElB,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kDAOvB,6LAAC;;0DACC,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAa;;;;;;0DAC5B,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gDAC5C,WAAU;gDACV,KAAK,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,GAAG;;;;;;;;;;;;;;;;;;0CAK7C,6LAAC,wIAAA,CAAA,YAAS;;;;;0CAGV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,2BAA2B,SAAS;wCAC9C,WAAU;kDAET,2BAA2B,SAAS,GACnC,mCAEA;;8DACE,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;kDAKzC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS,IAAM,UAAU;kDAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAlQgB;;QAiBqB,2IAAA,CAAA,wBAAqB;QACpB,2IAAA,CAAA,uBAAoB;;;KAlB1C", "debugId": null}}, {"offset": {"line": 7308, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/prompt-workspace.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useRef, useEffect, useCallback, memo, Suspense, lazy } from \"react\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Card, CardContent, CardHeader } from \"@/components/ui/card\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport { Copy, Plus, CheckCircle, Circle, Edit2, Check, X, Square, CheckSquare, MousePointer, GripVertical, Download, Undo, Menu, Settings, Search, Hash, Folder, ChevronDown, ChevronUp, FileText } from \"lucide-react\";\r\nimport { useAppStore } from \"@/store/app-store\";\r\nimport { usePrompts, useCreatePrompt, useMarkPromptAsUsed, useUpdatePrompt, useBulkUpdatePrompts } from \"@/hooks/use-prompts\";\r\nimport { useProject } from \"@/hooks/use-projects\";\r\nimport { useAllHashtags, useAllCategories } from \"@/hooks/use-hashtags\";\r\nimport { useUserLimits } from \"@/hooks/use-plans\";\r\nimport { InlineLimitWarning } from \"@/components/limit-warning\";\r\nimport { toast } from \"sonner\";\r\nimport { HashtagInput } from \"@/components/hashtag-input\";\r\nimport { CategorySelector } from \"@/components/category-selector\";\r\nimport { PopularHashtagsSidebar } from \"@/components/popular-hashtags-sidebar\";\r\nimport { parsePromptCategoriesPreserveText, mergeHashtags } from \"@/lib/hashtag-utils\";\r\n\r\nimport { Context } from \"./context-gallery\";\r\nimport SmartAutocomplete from \"./smart-autocomplete\";\r\nimport { ContextGallerySkeleton } from \"./progressive-loader\";\r\nimport { SharePromptButton } from \"./share-prompt-button\";\r\n\r\n// Lazy load heavy modal\r\nconst EnhancedContextGalleryModal = lazy(() => import(\"./enhanced-context-gallery-modal\").then(m => ({ default: m.EnhancedContextGalleryModal })));\r\n\r\n// Console Log Fix prefix constant\r\nconst CONSOLE_LOG_FIX_PREFIX = \"Yapılan canlı testlerde Console log ekranında hatalar karşımıza çıkmıştır. Proje yapımıza uygun olarak düzenleme yap. Consol logları şu şekildedir:\";\r\n\r\n// Helper component for displaying console log formatted prompts\r\nconst ConsoleLogPromptDisplay = memo(function ConsoleLogPromptDisplay({\r\n  promptText,\r\n  className,\r\n  onDoubleClick,\r\n  title\r\n}: {\r\n  promptText: string;\r\n  className?: string;\r\n  onDoubleClick?: () => void;\r\n  title?: string;\r\n}) {\r\n  const [isExpanded, setIsExpanded] = useState(false);\r\n\r\n  // Check if this prompt has console log fix prefix\r\n  const hasConsoleLogFix = promptText.startsWith(CONSOLE_LOG_FIX_PREFIX);\r\n\r\n  if (!hasConsoleLogFix) {\r\n    // Regular prompt display\r\n    return (\r\n      <p\r\n        className={className}\r\n        onDoubleClick={onDoubleClick}\r\n        title={title}\r\n      >\r\n        {promptText}\r\n      </p>\r\n    );\r\n  }\r\n\r\n  // Extract the console log part (everything after the prefix)\r\n  const consoleLogContent = promptText.substring(CONSOLE_LOG_FIX_PREFIX.length).trim();\r\n\r\n  return (\r\n    <div className=\"space-y-2 w-full max-w-full overflow-hidden\">\r\n      {/* Main text */}\r\n      <p\r\n        className={className}\r\n        onDoubleClick={onDoubleClick}\r\n        title={title}\r\n      >\r\n        {CONSOLE_LOG_FIX_PREFIX}\r\n      </p>\r\n\r\n      {/* Console log content in red-bordered table - Fixed width constraints */}\r\n      <div className=\"border-2 border-red-300 rounded-md bg-red-50 p-3 w-full max-w-full overflow-hidden box-border\">\r\n        <div className=\"flex items-center justify-between mb-2\">\r\n          <span className=\"text-sm font-medium text-red-700 truncate\">Console Log İçeriği</span>\r\n          <button\r\n            onClick={() => setIsExpanded(!isExpanded)}\r\n            className=\"text-red-600 hover:text-red-800 text-sm font-medium flex items-center gap-1 flex-shrink-0\"\r\n          >\r\n            {isExpanded ? (\r\n              <>\r\n                <ChevronUp className=\"h-4 w-4\" />\r\n                Daralt\r\n              </>\r\n            ) : (\r\n              <>\r\n                <ChevronDown className=\"h-4 w-4\" />\r\n                Genişlet\r\n              </>\r\n            )}\r\n          </button>\r\n        </div>\r\n\r\n        {isExpanded ? (\r\n          <pre className=\"text-sm text-red-800 whitespace-pre-wrap bg-white p-2 rounded border border-red-200 max-h-40 overflow-y-auto w-full max-w-full overflow-x-hidden break-words\">\r\n            {consoleLogContent}\r\n          </pre>\r\n        ) : (\r\n          <p className=\"text-sm text-red-700 w-full max-w-full overflow-hidden break-words whitespace-pre-wrap\">\r\n            {consoleLogContent.split('\\n')[0].length > 100\r\n              ? consoleLogContent.split('\\n')[0].substring(0, 100) + '...'\r\n              : consoleLogContent.split('\\n')[0]\r\n            }\r\n          </p>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n});\r\n\r\nimport {\r\n  DndContext,\r\n  closestCenter,\r\n  KeyboardSensor,\r\n  PointerSensor,\r\n  useSensor,\r\n  useSensors,\r\n  DragEndEvent,\r\n} from '@dnd-kit/core';\r\nimport {\r\n  arrayMove,\r\n  SortableContext,\r\n  sortableKeyboardCoordinates,\r\n  verticalListSortingStrategy,\r\n} from '@dnd-kit/sortable';\r\nimport {\r\n  useSortable,\r\n} from '@dnd-kit/sortable';\r\nimport { CSS } from '@dnd-kit/utilities';\r\n\r\ntype ExtendedPrompt = {\r\n  id: string;\r\n  project_id: string;\r\n  user_id: string;\r\n  prompt_text: string;\r\n  title: string | null;\r\n  description: string | null;\r\n  category: string | null;\r\n  tags: string[];\r\n  order_index: number;\r\n  is_used: boolean;\r\n  is_favorite: boolean;\r\n  usage_count: number;\r\n  last_used_at: string | null;\r\n  task_code: string | null;\r\n  created_at: string;\r\n  updated_at: string;\r\n};\r\n\r\ninterface SortablePromptItemProps {\r\n  prompt: ExtendedPrompt;\r\n  isMultiSelectMode: boolean;\r\n  selectedPrompts: Set<string>;\r\n  editingPromptId: string | null;\r\n  editingText: string;\r\n  editTextareaRef: React.RefObject<HTMLTextAreaElement | null>;\r\n  onSelectPrompt: (id: string) => void;\r\n  onEditPrompt: (prompt: ExtendedPrompt) => void;\r\n  onSaveEdit: () => void;\r\n  onCancelEdit: () => void;\r\n  onCopyPrompt: (prompt: ExtendedPrompt) => void;\r\n  setEditingText: (text: string) => void;\r\n  onHashtagFilter: (hashtag: string) => void;\r\n}\r\n\r\nconst SortablePromptItem = memo(function SortablePromptItem({\r\n  prompt,\r\n  isMultiSelectMode,\r\n  selectedPrompts,\r\n  editingPromptId,\r\n  editingText,\r\n  editTextareaRef,\r\n  onSelectPrompt,\r\n  onEditPrompt,\r\n  onSaveEdit,\r\n  onCancelEdit,\r\n  onCopyPrompt,\r\n  setEditingText,\r\n  onHashtagFilter,\r\n}: SortablePromptItemProps) {\r\n  const {\r\n    attributes,\r\n    listeners,\r\n    setNodeRef,\r\n    transform,\r\n    transition,\r\n    isDragging,\r\n  } = useSortable({ id: prompt.id });\r\n\r\n  const style = {\r\n    transform: CSS.Transform.toString(transform),\r\n    transition,\r\n    opacity: isDragging ? 0.5 : 1,\r\n  };\r\n\r\n  return (\r\n    <Card\r\n      ref={setNodeRef}\r\n      style={style}\r\n      className={`transition-all duration-200 ${\r\n        prompt.is_used\r\n          ? 'bg-gray-50 border-gray-300'\r\n          : 'bg-white border-gray-200 hover:border-blue-300 hover:shadow-md'\r\n      } ${\r\n        selectedPrompts.has(prompt.id)\r\n          ? 'ring-2 ring-blue-500 border-blue-500'\r\n          : ''\r\n      }`}\r\n      onClick={() => isMultiSelectMode ? onSelectPrompt(prompt.id) : undefined}\r\n    >\r\n      <CardHeader className=\"pb-3\">\r\n        {/* Mobil Layout */}\r\n        <div className=\"block lg:hidden\">\r\n          <div className=\"flex items-center justify-between mb-3\">\r\n            <div className=\"flex items-center gap-2\">\r\n              {!isMultiSelectMode && (\r\n                <div\r\n                  className=\"flex items-center justify-center w-8 h-8 cursor-grab active:cursor-grabbing\"\r\n                  {...attributes}\r\n                  {...listeners}\r\n                >\r\n                  <GripVertical className=\"h-5 w-5 text-gray-400\" />\r\n                </div>\r\n              )}\r\n              {isMultiSelectMode && (\r\n                <div\r\n                  className=\"flex items-center justify-center w-8 h-8 cursor-pointer\"\r\n                  onClick={(e) => {\r\n                    e.stopPropagation();\r\n                    onSelectPrompt(prompt.id);\r\n                  }}\r\n                >\r\n                  {selectedPrompts.has(prompt.id) ? (\r\n                    <CheckSquare className=\"h-6 w-6 text-blue-600\" />\r\n                  ) : (\r\n                    <Square className=\"h-6 w-6 text-gray-400\" />\r\n                  )}\r\n                </div>\r\n              )}\r\n              <div className={`flex items-center justify-center w-10 h-10 rounded-full text-base font-medium ${\r\n                prompt.is_used\r\n                  ? 'bg-green-100 text-green-700'\r\n                  : 'bg-blue-100 text-blue-700'\r\n              }`}>\r\n                {prompt.order_index}\r\n              </div>\r\n              <div className=\"flex flex-col\">\r\n                <span className={`text-sm font-mono px-2 py-1 rounded ${\r\n                  prompt.is_used\r\n                    ? 'bg-gray-100 text-gray-500'\r\n                    : 'bg-blue-50 text-blue-600'\r\n                }`}>\r\n                  {prompt.task_code || `task-${prompt.order_index}`}\r\n                </span>\r\n              </div>\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              {prompt.is_used ? (\r\n                <CheckCircle className=\"h-5 w-5 text-green-600\" />\r\n              ) : (\r\n                <Circle className=\"h-5 w-5 text-gray-400\" />\r\n              )}\r\n            </div>\r\n          </div>\r\n          {!isMultiSelectMode && (\r\n            <div className=\"flex items-center gap-2 justify-end\">\r\n              {editingPromptId === prompt.id ? (\r\n                <>\r\n                  <Button\r\n                    variant=\"default\"\r\n                    size=\"sm\"\r\n                    onClick={onSaveEdit}\r\n                    className=\"bg-green-600 hover:bg-green-700 text-white border-green-600 hover:border-green-700 min-h-[44px] px-4\"\r\n                  >\r\n                    <Check className=\"h-4 w-4 mr-2\" />\r\n                    Kaydet\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    size=\"sm\"\r\n                    onClick={onCancelEdit}\r\n                    className=\"text-red-600 hover:text-red-700 min-h-[44px] px-4\"\r\n                  >\r\n                    <X className=\"h-4 w-4 mr-2\" />\r\n                    İptal\r\n                  </Button>\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    size=\"sm\"\r\n                    onClick={() => onEditPrompt(prompt)}\r\n                    className=\"text-gray-600 hover:text-gray-700 min-h-[44px] px-4\"\r\n                  >\r\n                    <Edit2 className=\"h-4 w-4 mr-2\" />\r\n                    Düzenle\r\n                  </Button>\r\n                  <SharePromptButton\r\n                    promptId={prompt.id}\r\n                    promptTitle={prompt.title || undefined}\r\n                    promptText={prompt.prompt_text}\r\n                    taskCode={prompt.task_code || undefined}\r\n                    variant=\"outline\"\r\n                    size=\"sm\"\r\n                    className=\"min-h-[44px] px-4\"\r\n                  />\r\n                  <Button\r\n                    variant={prompt.is_used ? \"secondary\" : \"default\"}\r\n                    size=\"sm\"\r\n                    onClick={() => onCopyPrompt(prompt)}\r\n                    className={`min-h-[44px] px-4 ${prompt.is_used ? \"opacity-60\" : \"\"}`}\r\n                  >\r\n                    <Copy className=\"h-4 w-4 mr-2\" />\r\n                    {prompt.is_used ? 'Kopyalandı' : 'Kopyala'}\r\n                  </Button>\r\n                </>\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Desktop Layout */}\r\n        <div className=\"hidden lg:flex items-center justify-between\">\r\n          <div className=\"flex items-center gap-3\">\r\n            {!isMultiSelectMode && (\r\n              <div\r\n                className=\"flex items-center justify-center w-6 h-6 cursor-grab active:cursor-grabbing\"\r\n                {...attributes}\r\n                {...listeners}\r\n              >\r\n                <GripVertical className=\"h-4 w-4 text-gray-400\" />\r\n              </div>\r\n            )}\r\n            {isMultiSelectMode && (\r\n              <div\r\n                className=\"flex items-center justify-center w-6 h-6 cursor-pointer\"\r\n                onClick={(e) => {\r\n                  e.stopPropagation();\r\n                  onSelectPrompt(prompt.id);\r\n                }}\r\n              >\r\n                {selectedPrompts.has(prompt.id) ? (\r\n                  <CheckSquare className=\"h-5 w-5 text-blue-600\" />\r\n                ) : (\r\n                  <Square className=\"h-5 w-5 text-gray-400\" />\r\n                )}\r\n              </div>\r\n            )}\r\n            <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${\r\n              prompt.is_used\r\n                ? 'bg-green-100 text-green-700'\r\n                : 'bg-blue-100 text-blue-700'\r\n            }`}>\r\n              {prompt.order_index}\r\n            </div>\r\n            <div className=\"flex flex-col\">\r\n              <span className={`text-xs font-mono px-2 py-1 rounded ${\r\n                prompt.is_used\r\n                  ? 'bg-gray-100 text-gray-500'\r\n                  : 'bg-blue-50 text-blue-600'\r\n              }`}>\r\n                {prompt.task_code || `task-${prompt.order_index}`}\r\n              </span>\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              {prompt.is_used ? (\r\n                <CheckCircle className=\"h-4 w-4 text-green-600\" />\r\n              ) : (\r\n                <Circle className=\"h-4 w-4 text-gray-400\" />\r\n              )}\r\n            </div>\r\n          </div>\r\n          {!isMultiSelectMode && (\r\n            <div className=\"flex items-center gap-2\">\r\n              {editingPromptId === prompt.id ? (\r\n                <>\r\n                  <Button\r\n                    variant=\"default\"\r\n                    size=\"sm\"\r\n                    onClick={onSaveEdit}\r\n                    className=\"bg-green-600 hover:bg-green-700 text-white border-green-600 hover:border-green-700\"\r\n                  >\r\n                    <Check className=\"h-4 w-4 mr-2\" />\r\n                    Kaydet\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    size=\"sm\"\r\n                    onClick={onCancelEdit}\r\n                    className=\"text-red-600 hover:text-red-700\"\r\n                  >\r\n                    <X className=\"h-4 w-4 mr-2\" />\r\n                    İptal\r\n                  </Button>\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    size=\"sm\"\r\n                    onClick={() => onEditPrompt(prompt)}\r\n                    className=\"text-gray-600 hover:text-gray-700\"\r\n                  >\r\n                    <Edit2 className=\"h-4 w-4 mr-2\" />\r\n                    Düzenle\r\n                  </Button>\r\n                  <SharePromptButton\r\n                    promptId={prompt.id}\r\n                    promptTitle={prompt.title || undefined}\r\n                    promptText={prompt.prompt_text}\r\n                    taskCode={prompt.task_code || undefined}\r\n                    variant=\"outline\"\r\n                    size=\"sm\"\r\n                  />\r\n                  <Button\r\n                    variant={prompt.is_used ? \"secondary\" : \"default\"}\r\n                    size=\"sm\"\r\n                    onClick={() => onCopyPrompt(prompt)}\r\n                    className={prompt.is_used ? \"opacity-60\" : \"\"}\r\n                  >\r\n                    <Copy className=\"h-4 w-4 mr-2\" />\r\n                    {prompt.is_used ? 'Kopyalandı' : 'Kopyala'}\r\n                  </Button>\r\n                </>\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </CardHeader>\r\n      <CardContent className=\"pt-0\">\r\n        {editingPromptId === prompt.id ? (\r\n          <Textarea\r\n            ref={editTextareaRef}\r\n            value={editingText}\r\n            onChange={(e) => setEditingText(e.target.value)}\r\n            className=\"min-h-[60px] max-h-[200px] resize-none\"\r\n            style={{ height: 'auto' }}\r\n            onKeyDown={(e) => {\r\n              if (e.key === 'Enter' && e.ctrlKey) {\r\n                onSaveEdit();\r\n              } else if (e.key === 'Escape') {\r\n                onCancelEdit();\r\n              }\r\n            }}\r\n            autoFocus\r\n          />\r\n        ) : (\r\n          <div className=\"space-y-2\">\r\n            <ConsoleLogPromptDisplay\r\n              promptText={prompt.prompt_text}\r\n              className={`text-sm leading-relaxed cursor-pointer whitespace-pre-wrap break-words ${\r\n                prompt.is_used ? 'text-gray-500' : 'text-gray-700'\r\n              }`}\r\n              onDoubleClick={() => onEditPrompt(prompt)}\r\n              title=\"Düzenlemek için çift tıklayın\"\r\n            />\r\n\r\n            {/* Hashtags and Category Display */}\r\n            {((prompt.tags && prompt.tags.length > 0) || prompt.category) && (\r\n              <div className=\"flex flex-wrap items-center gap-1 pt-2 sm:gap-1.5\">\r\n                {/* Category */}\r\n                {prompt.category && (\r\n                  <Badge\r\n                    variant=\"outline\"\r\n                    className=\"text-xs sm:text-xs bg-gray-50 text-gray-700 border-gray-300 hover:bg-gray-100 transition-colors cursor-pointer\"\r\n                    onClick={(e) => {\r\n                      e.preventDefault();\r\n                      e.stopPropagation();\r\n                      // TODO: Add category filter functionality\r\n                    }}\r\n                  >\r\n                    <Folder className=\"w-3 h-3 mr-1 text-gray-500\" />\r\n                    {prompt.category}\r\n                  </Badge>\r\n                )}\r\n\r\n                {/* Hashtags */}\r\n                {prompt.tags && Array.isArray(prompt.tags) && prompt.tags.map((tag: string, index: number) => {\r\n                  // Ensure tag is a valid string\r\n                  const tagStr = typeof tag === 'string' ? tag : String(tag);\r\n                  if (!tagStr || tagStr.trim() === '') return null;\r\n\r\n                  return (\r\n                    <Badge\r\n                      key={index}\r\n                      variant=\"secondary\"\r\n                      className=\"text-xs sm:text-xs bg-blue-50 text-blue-700 border-blue-200 cursor-pointer hover:bg-blue-100 transition-colors\"\r\n                      onClick={(e) => {\r\n                        e.preventDefault();\r\n                        e.stopPropagation();\r\n                        onHashtagFilter(tagStr);\r\n                      }}\r\n                    >\r\n                      <Hash className=\"w-3 h-3 mr-1 text-blue-500\" />\r\n                      {tagStr.replace('#', '')}\r\n                    </Badge>\r\n                  );\r\n                })}\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n});\r\n\r\ninterface PromptWorkspaceProps {\r\n  isContextGalleryOpen?: boolean;\r\n  onToggleContextGallery?: () => void;\r\n}\r\n\r\nexport function PromptWorkspace({\r\n  isContextGalleryOpen = false,\r\n  onToggleContextGallery\r\n}: PromptWorkspaceProps = {}) {\r\n  const [newPromptText, setNewPromptText] = useState(\"\");\r\n  const [editingPromptId, setEditingPromptId] = useState<string | null>(null);\r\n  const [editingText, setEditingText] = useState(\"\");\r\n  const [selectedPrompts, setSelectedPrompts] = useState<Set<string>>(new Set());\r\n  const [isMultiSelectMode, setIsMultiSelectMode] = useState(false);\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n\r\n  // New categorization state\r\n  const [newPromptHashtags, setNewPromptHashtags] = useState<string[]>([]);\r\n  const [newPromptCategory, setNewPromptCategory] = useState<string | null>(null);\r\n  const [newPromptTitle, setNewPromptTitle] = useState(\"\");\r\n  const [showAdvancedForm, setShowAdvancedForm] = useState(false);\r\n  const [showHashtagsSidebar, setShowHashtagsSidebar] = useState(true);\r\n  const [filterHashtags, setFilterHashtags] = useState<string[]>([]);\r\n  const [filterCategory, setFilterCategory] = useState<string | null>(null);\r\n\r\n  // Console Log Fix feature state\r\n  const [isConsoleLogFixEnabled, setIsConsoleLogFixEnabled] = useState(false);\r\n\r\n  // Pagination state\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const PROMPTS_PER_PAGE = 20;\r\n\r\n  // Reset page when search query changes\r\n  useEffect(() => {\r\n    setCurrentPage(1);\r\n  }, [searchQuery]);\r\n\r\n  const editTextareaRef = useRef<HTMLTextAreaElement>(null);\r\n  const { activeProjectId, isContextEnabled } = useAppStore();\r\n  const { data: limits } = useUserLimits();\r\n  \r\n  const sensors = useSensors(\r\n    useSensor(PointerSensor),\r\n    useSensor(KeyboardSensor, {\r\n      coordinateGetter: sortableKeyboardCoordinates,\r\n    })\r\n  );\r\n  \r\n  const { data: prompts = [] } = usePrompts(activeProjectId);\r\n  const { data: project } = useProject(activeProjectId);\r\n  const { data: allHashtags = [] } = useAllHashtags(activeProjectId);\r\n  const { data: allCategories = [] } = useAllCategories(activeProjectId);\r\n  const createPromptMutation = useCreatePrompt();\r\n  const updatePromptMutation = useUpdatePrompt();\r\n  const bulkUpdatePromptsMutation = useBulkUpdatePrompts();\r\n  const markAsUsedMutation = useMarkPromptAsUsed();\r\n\r\n  // Arama ve kategori filtrelemesi\r\n  const filteredPrompts = prompts.filter((prompt: ExtendedPrompt) => {\r\n    try {\r\n      // Text search filter\r\n      const matchesSearch = searchQuery.trim() === \"\" ||\r\n        prompt.prompt_text.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n        (prompt.title && prompt.title.toLowerCase().includes(searchQuery.toLowerCase()));\r\n\r\n      // Category filter\r\n      const matchesCategory = !filterCategory || prompt.category === filterCategory;\r\n\r\n      // Hashtag filter\r\n      const matchesHashtags = filterHashtags.length === 0 ||\r\n        (prompt.tags && Array.isArray(prompt.tags) &&\r\n         filterHashtags.some(hashtag =>\r\n           prompt.tags.some((tag: string) => {\r\n             const tagStr = typeof tag === 'string' ? tag : String(tag);\r\n             return tagStr.toLowerCase() === hashtag.toLowerCase();\r\n           })\r\n         ));\r\n\r\n      return matchesSearch && matchesCategory && matchesHashtags;\r\n    } catch (error) {\r\n      console.error('Error filtering prompt:', error, prompt);\r\n      return true; // Include prompt if filtering fails\r\n    }\r\n  });\r\n\r\n\r\n  // Otomatik boyutlandırma fonksiyonu (sadece düzenleme için)\r\n  const adjustTextareaHeight = (textarea: HTMLTextAreaElement) => {\r\n    textarea.style.height = 'auto';\r\n    textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px';\r\n  };\r\n\r\n  // Düzenleme textarea'sı için otomatik boyutlandırma\r\n  useEffect(() => {\r\n    if (editTextareaRef.current) {\r\n      adjustTextareaHeight(editTextareaRef.current);\r\n    }\r\n  }, [editingText]);\r\n\r\n  const handleAddPrompt = async () => {\r\n    if (newPromptText.trim() && activeProjectId) {\r\n      // Plan limiti kontrol et\r\n      if (limits && !limits.can_create_prompt) {\r\n        toast.error(`Prompt oluşturma limitinize ulaştınız (${limits.current_prompts}/${limits.max_prompts_per_project}). Planınızı yükseltin.`);\r\n        return;\r\n      }\r\n\r\n      // Apply Console Log Fix if enabled\r\n      let processedPromptText = newPromptText;\r\n      if (isConsoleLogFixEnabled) {\r\n        const consoleLogPrefix = `Yapılan canlı testlerde Console log ekranında hatalar karşımıza çıkmıştır. Proje yapımıza uygun olarak düzenleme yap. Consol logları şu şekildedir:\r\n\r\n`;\r\n        processedPromptText = consoleLogPrefix + newPromptText;\r\n      }\r\n\r\n      // Store form values before clearing\r\n      const formData = {\r\n        promptText: processedPromptText,\r\n        promptTitle: newPromptTitle,\r\n        promptHashtags: newPromptHashtags,\r\n        promptCategory: newPromptCategory,\r\n        showAdvanced: showAdvancedForm\r\n      };\r\n\r\n      // Reset form immediately for better UX (optimistic UI)\r\n      setNewPromptText(\"\");\r\n      setNewPromptTitle(\"\");\r\n      setNewPromptHashtags([]);\r\n      setNewPromptCategory(null);\r\n      setShowAdvancedForm(false);\r\n\r\n      try {\r\n        // Parse hashtags and folder paths from prompt text while preserving original text\r\n        const { hashtags: extractedHashtags, folderPaths, originalText } = parsePromptCategoriesPreserveText(formData.promptText);\r\n\r\n        // Merge extracted hashtags with manually added ones\r\n        const allHashtags = mergeHashtags(formData.promptHashtags || [], extractedHashtags || []);\r\n\r\n        // Use folder path as category if found, otherwise use manually selected category\r\n        const finalCategory = folderPaths && folderPaths.length > 0 ? folderPaths[0] : formData.promptCategory;\r\n\r\n        // Calculate proper order_index to avoid conflicts\r\n        const maxOrderIndex = prompts.length > 0\r\n          ? Math.max(...prompts.map(p => p.order_index || 0))\r\n          : 0\r\n        const nextOrderIndex = maxOrderIndex + 1\r\n\r\n        await createPromptMutation.mutateAsync({\r\n          project_id: activeProjectId,\r\n          prompt_text: originalText, // Keep original text with hashtags and folders\r\n          title: formData.promptTitle?.trim() || undefined,\r\n          category: finalCategory || undefined,\r\n          tags: allHashtags || [],\r\n          order_index: nextOrderIndex,\r\n          is_used: false,\r\n        });\r\n\r\n        // Show success feedback\r\n        toast.success('Prompt başarıyla eklendi!');\r\n      } catch (error) {\r\n        console.error('Prompt ekleme hatası:', error);\r\n\r\n        // Restore form data on error\r\n        setNewPromptText(formData.promptText);\r\n        setNewPromptTitle(formData.promptTitle);\r\n        setNewPromptHashtags(formData.promptHashtags);\r\n        setNewPromptCategory(formData.promptCategory);\r\n        setShowAdvancedForm(formData.showAdvanced);\r\n\r\n        // Show user-friendly error message\r\n        toast.error('Prompt eklenirken bir hata oluştu. Lütfen tekrar deneyin.');\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleSelectContextFromGallery = (context: Context) => {\r\n    setNewPromptText(context.content);\r\n    onToggleContextGallery?.();\r\n  };\r\n\r\n  // Hashtag sidebar handlers\r\n  const handleHashtagFilter = useCallback((hashtag: string) => {\r\n    try {\r\n      // Ensure hashtag is a string\r\n      const hashtagStr = typeof hashtag === 'string' ? hashtag : String(hashtag);\r\n\r\n      if (!hashtagStr || hashtagStr.trim() === '') {\r\n        console.warn('Invalid hashtag provided to handleHashtagFilter:', hashtag);\r\n        return;\r\n      }\r\n\r\n      if (filterHashtags.includes(hashtagStr)) {\r\n        setFilterHashtags(filterHashtags.filter(h => h !== hashtagStr));\r\n      } else {\r\n        setFilterHashtags([...filterHashtags, hashtagStr]);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error in handleHashtagFilter:', error, hashtag);\r\n    }\r\n  }, [filterHashtags]);\r\n\r\n  const handleCategoryFilter = useCallback((category: string) => {\r\n    setFilterCategory(filterCategory === category ? null : category);\r\n  }, [filterCategory]);\r\n\r\n\r\n\r\n\r\n\r\n  const handleCopyPrompt = useCallback(async (prompt: ExtendedPrompt) => {\r\n    try {\r\n      // Context text'i proje bilgilerinden al ve context enabled durumunu kontrol et\r\n      const contextText = isContextEnabled ? (project?.context_text || \"\") : \"\";\r\n      const taskCode = prompt.task_code || `task-${prompt.order_index}`;\r\n\r\n      let fullText = \"\";\r\n      if (contextText) {\r\n        fullText = `${contextText}\\n\\n${taskCode}\\n${prompt.prompt_text}`;\r\n      } else {\r\n        fullText = `${taskCode}\\n${prompt.prompt_text}`;\r\n      }\r\n\r\n      await navigator.clipboard.writeText(fullText);\r\n\r\n      // Prompt'u kullanıldı olarak işaretle\r\n      await markAsUsedMutation.mutateAsync(prompt.id);\r\n    } catch (error) {\r\n      console.error('Kopyalama hatası:', error);\r\n    }\r\n  }, [isContextEnabled, project?.context_text, markAsUsedMutation]);\r\n\r\n  const handleUndoLastCopy = async () => {\r\n    if (activeProjectId) {\r\n      try {\r\n        // En son kopyalanan (is_used=true) prompt'u bul\r\n        const lastCopiedPrompt = prompts\r\n          .filter(p => p.is_used)\r\n          .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())[0];\r\n        \r\n        if (lastCopiedPrompt) {\r\n          // En son kopyalanan prompt'u \"Bekliyor\" durumuna getir\r\n          await updatePromptMutation.mutateAsync({\r\n            id: lastCopiedPrompt.id,\r\n            is_used: false\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error('Geri alma hatası:', error);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleEditPrompt = useCallback((prompt: ExtendedPrompt) => {\r\n    setEditingPromptId(prompt.id);\r\n    setEditingText(prompt.prompt_text);\r\n  }, []);\r\n\r\n  const handleSaveEdit = async () => {\r\n    if (editingPromptId && editingText.trim()) {\r\n      try {\r\n        // Parse hashtags and folder paths from edited text while preserving original text\r\n        const { hashtags: extractedHashtags, folderPaths, originalText } = parsePromptCategoriesPreserveText(editingText);\r\n\r\n        // Get the current prompt to preserve existing tags and category\r\n        const currentPrompt = prompts.find(p => p.id === editingPromptId);\r\n        const existingTags = currentPrompt?.tags || [];\r\n        const existingCategory = currentPrompt?.category;\r\n\r\n        // Merge extracted hashtags with existing ones (avoid duplicates)\r\n        const allHashtags = mergeHashtags(existingTags, extractedHashtags || []);\r\n\r\n        // Use folder path as category if found, otherwise keep existing category\r\n        const finalCategory = folderPaths && folderPaths.length > 0 ? folderPaths[0] : existingCategory;\r\n\r\n        await updatePromptMutation.mutateAsync({\r\n          id: editingPromptId,\r\n          prompt_text: originalText, // Keep original text with hashtags and folders\r\n          tags: allHashtags,\r\n          category: finalCategory || undefined,\r\n        });\r\n\r\n        setEditingPromptId(null);\r\n        setEditingText(\"\");\r\n      } catch (error) {\r\n        console.error('Prompt güncelleme hatası:', error);\r\n        // Show user-friendly error message\r\n        alert('Prompt güncellenirken bir hata oluştu. Lütfen tekrar deneyin.');\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleCancelEdit = useCallback(() => {\r\n    setEditingPromptId(null);\r\n    setEditingText(\"\");\r\n  }, []);\r\n\r\n  const handleToggleMultiSelect = useCallback(() => {\r\n    setIsMultiSelectMode(!isMultiSelectMode);\r\n    setSelectedPrompts(new Set());\r\n  }, [isMultiSelectMode]);\r\n\r\n  const handleSelectPrompt = useCallback((promptId: string) => {\r\n    const newSelected = new Set(selectedPrompts);\r\n    if (newSelected.has(promptId)) {\r\n      newSelected.delete(promptId);\r\n    } else {\r\n      newSelected.add(promptId);\r\n    }\r\n    setSelectedPrompts(newSelected);\r\n  }, [selectedPrompts]);\r\n\r\n  const handleCopySelectedPrompts = async () => {\r\n    if (selectedPrompts.size === 0) return;\r\n\r\n    try {\r\n      const selectedPromptList = prompts\r\n        .filter(p => selectedPrompts.has(p.id))\r\n        .sort((a, b) => a.order_index - b.order_index);\r\n\r\n      const contextText = isContextEnabled ? (project?.context_text || \"\") : \"\";\r\n      \r\n      let fullText = contextText ? `${contextText}\\n\\n` : \"\";\r\n      \r\n      selectedPromptList.forEach((prompt, index) => {\r\n        const taskCode = prompt.task_code || `task-${prompt.order_index}`;\r\n        fullText += `${index + 1}. ${taskCode}\\n${prompt.prompt_text}`;\r\n        if (index < selectedPromptList.length - 1) {\r\n          fullText += \"\\n\\n\";\r\n        }\r\n      });\r\n\r\n      await navigator.clipboard.writeText(fullText);\r\n      \r\n      // Tüm seçili prompt'ları kullanıldı olarak işaretle\r\n      for (const promptId of selectedPrompts) {\r\n        await markAsUsedMutation.mutateAsync(promptId);\r\n      }\r\n      \r\n      // Seçimi temizle\r\n      setSelectedPrompts(new Set());\r\n      setIsMultiSelectMode(false);\r\n    } catch (error) {\r\n      console.error('Çoklu kopyalama hatası:', error);\r\n    }\r\n  };\r\n\r\n  const handleExportUnusedPrompts = () => {\r\n    const unusedPrompts = prompts\r\n      .filter(p => !p.is_used)\r\n      .sort((a, b) => a.order_index - b.order_index);\r\n\r\n    if (unusedPrompts.length === 0) {\r\n      alert('Kopyalanmamış prompt bulunamadı!');\r\n      return;\r\n    }\r\n\r\n    const contextText = isContextEnabled ? (project?.context_text || \"\") : \"\";\r\n    \r\n    let markdownContent = `# ${project?.name || 'Prompt Listesi'}\\n\\n`;\r\n    \r\n    if (contextText) {\r\n      markdownContent += `## Context\\n\\n${contextText}\\n\\n`;\r\n    }\r\n    \r\n    markdownContent += `## Kopyalanmamış Prompt'lar\\n\\n`;\r\n    \r\n    unusedPrompts.forEach((prompt, index) => {\r\n      const taskCode = prompt.task_code || `task-${prompt.order_index}`;\r\n      markdownContent += `### ${index + 1}. ${taskCode}\\n\\n`;\r\n      markdownContent += `${prompt.prompt_text}\\n\\n`;\r\n      markdownContent += `---\\n\\n`;\r\n    });\r\n\r\n    // Markdown dosyasını indir\r\n    const blob = new Blob([markdownContent], { type: 'text/markdown' });\r\n    const url = URL.createObjectURL(blob);\r\n    const a = document.createElement('a');\r\n    a.href = url;\r\n    a.download = `${project?.name || 'prompts'}-unused.md`;\r\n    document.body.appendChild(a);\r\n    a.click();\r\n    document.body.removeChild(a);\r\n    URL.revokeObjectURL(url);\r\n  };\r\n\r\n\r\n\r\n  const handleDragEnd = useCallback(async (event: DragEndEvent) => {\r\n    const { active, over } = event;\r\n\r\n    if (!over || active.id === over.id) {\r\n      return;\r\n    }\r\n\r\n    const sortedPrompts = prompts.sort((a, b) => {\r\n      // Önce kullanılmayan promptlar (is_used: false), sonra kullanılanlar (is_used: true)\r\n      if (a.is_used !== b.is_used) {\r\n        return a.is_used ? 1 : -1;\r\n      }\r\n\r\n      // Kullanılmayan promptları order_index'e göre sırala\r\n      if (!a.is_used && !b.is_used) {\r\n        return a.order_index - b.order_index;\r\n      }\r\n\r\n      // Kullanılan promptları last_used_at'e göre sırala (en son kullanılan önce)\r\n      if (a.is_used && b.is_used) {\r\n        const aLastUsed = a.last_used_at ? new Date(a.last_used_at).getTime() : 0;\r\n        const bLastUsed = b.last_used_at ? new Date(b.last_used_at).getTime() : 0;\r\n        return bLastUsed - aLastUsed; // Descending order (most recent first)\r\n      }\r\n\r\n      return 0;\r\n    });\r\n\r\n    const oldIndex = sortedPrompts.findIndex((prompt) => prompt.id === active.id);\r\n    const newIndex = sortedPrompts.findIndex((prompt) => prompt.id === over.id);\r\n\r\n    const newOrder = arrayMove(sortedPrompts, oldIndex, newIndex);\r\n\r\n    // Bulk update için değişiklikleri topla\r\n    const updates: Array<{ id: string; order_index: number; task_code: string }> = [];\r\n\r\n    for (let i = 0; i < newOrder.length; i++) {\r\n      const prompt = newOrder[i];\r\n      const newOrderIndex = i + 1;\r\n      const newTaskCode = `task-${newOrderIndex}`;\r\n\r\n      if (prompt.order_index !== newOrderIndex || prompt.task_code !== newTaskCode) {\r\n        updates.push({\r\n          id: prompt.id,\r\n          order_index: newOrderIndex,\r\n          task_code: newTaskCode,\r\n        });\r\n      }\r\n    }\r\n\r\n    // Bulk update ile tüm değişiklikleri tek seferde uygula\r\n    if (updates.length > 0) {\r\n      try {\r\n        await bulkUpdatePromptsMutation.mutateAsync(updates);\r\n      } catch (error) {\r\n        console.error('Sıralama güncelleme hatası:', error);\r\n      }\r\n    }\r\n  }, [prompts, bulkUpdatePromptsMutation]);\r\n\r\n  if (!activeProjectId) {\r\n    return (\r\n      <div className=\"flex flex-col h-full\">\r\n        {/* Header boş alan */}\r\n        <div className=\"h-16 sm:h-20 lg:h-24\"></div>\r\n\r\n        {/* Orta bölüm - Proje Seçin ve Context Alanı */}\r\n        <div className=\"flex-1 flex items-center justify-center px-4\">\r\n          <div className=\"max-w-2xl w-full\">\r\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12\">\r\n              {/* Proje Seçin */}\r\n              <div className=\"text-center\">\r\n                <Circle className=\"h-16 w-16 text-gray-400 mx-auto mb-6\" />\r\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Proje Seçin</h3>\r\n                <p className=\"text-gray-600 leading-relaxed\">\r\n                  Başlamak için sol panelden bir proje seçin veya yeni bir proje oluşturun\r\n                </p>\r\n              </div>\r\n\r\n              {/* Context Alanı */}\r\n              <div className=\"text-center\">\r\n                <FileText className=\"h-16 w-16 text-gray-400 mx-auto mb-6\" />\r\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Context Alanı</h3>\r\n                <p className=\"text-gray-600 leading-relaxed\">\r\n                  Proje seçtikten sonra sağ panelden context metninizi yazabilirsiniz\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Alt boş alan */}\r\n        <div className=\"h-32 sm:h-40 lg:h-48\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex h-full\">\r\n      {/* Main Content */}\r\n      <div className=\"flex flex-col flex-1\">\r\n        {/* Enhanced Responsive Header */}\r\n        <div className=\"p-3 sm:p-4 lg:p-6 border-b border-gray-200 bg-white\">\r\n        <div className=\"flex items-center justify-between mb-3 sm:mb-4\">\r\n          <div className=\"flex items-center gap-2 sm:gap-3\">\r\n            {/* Mobile Menu Buttons - Enhanced */}\r\n            <div className=\"flex md:hidden gap-1 sm:gap-2\">\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                className=\"h-8 w-8 sm:h-10 sm:w-10 p-0 touch-manipulation\"\r\n              >\r\n                <Menu className=\"h-4 w-4 sm:h-5 sm:w-5\" />\r\n              </Button>\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                className=\"h-8 w-8 sm:h-10 sm:w-10 p-0 touch-manipulation\"\r\n              >\r\n                <Settings className=\"h-4 w-4 sm:h-5 sm:w-5\" />\r\n              </Button>\r\n            </div>\r\n            <h2 className=\"text-base sm:text-lg lg:text-xl font-semibold text-gray-900 truncate\">\r\n              {project?.name || 'Prompt Listesi'}\r\n            </h2>\r\n          </div>\r\n          <div className=\"flex items-center gap-1 sm:gap-2 overflow-x-auto\">\r\n            {/* Enhanced Filter indicators for mobile */}\r\n            {(filterHashtags.length > 0 || filterCategory) && (\r\n              <div className=\"flex items-center gap-1 flex-shrink-0\">\r\n                {filterHashtags.slice(0, 2).map(hashtag => (\r\n                  <Badge key={hashtag} variant=\"outline\" className=\"text-xs whitespace-nowrap\">\r\n                    <Hash className=\"w-3 h-3 mr-1\" />\r\n                    <span className=\"hidden sm:inline\">{hashtag.replace('#', '')}</span>\r\n                    <span className=\"sm:hidden\">{hashtag.replace('#', '').slice(0, 3)}</span>\r\n                    <Button\r\n                      type=\"button\"\r\n                      variant=\"ghost\"\r\n                      size=\"sm\"\r\n                      className=\"h-auto p-0 w-3 h-3 ml-1 hover:bg-transparent touch-manipulation\"\r\n                      onClick={(e) => {\r\n                        e.preventDefault();\r\n                        e.stopPropagation();\r\n                        handleHashtagFilter(hashtag);\r\n                      }}\r\n                    >\r\n                      <X className=\"w-2 h-2\" />\r\n                    </Button>\r\n                  </Badge>\r\n                ))}\r\n                {filterHashtags.length > 2 && (\r\n                  <Badge variant=\"outline\" className=\"text-xs\">\r\n                    +{filterHashtags.length - 2}\r\n                  </Badge>\r\n                )}\r\n                {filterCategory && (\r\n                  <Badge variant=\"outline\" className=\"text-xs whitespace-nowrap\">\r\n                    <Folder className=\"w-3 h-3 mr-1\" />\r\n                    <span className=\"hidden sm:inline\">{filterCategory}</span>\r\n                    <span className=\"sm:hidden\">{filterCategory.slice(0, 3)}</span>\r\n                    <Button\r\n                      type=\"button\"\r\n                      variant=\"ghost\"\r\n                      size=\"sm\"\r\n                      className=\"h-auto p-0 w-3 h-3 ml-1 hover:bg-transparent\"\r\n                      onClick={() => setFilterCategory(null)}\r\n                    >\r\n                      <X className=\"w-2 h-2\" />\r\n                    </Button>\r\n                  </Badge>\r\n                )}\r\n              </div>\r\n            )}\r\n\r\n            {/* Hashtags sidebar toggle */}\r\n            <Button\r\n              type=\"button\"\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              onClick={() => setShowHashtagsSidebar(!showHashtagsSidebar)}\r\n              className=\"hidden lg:flex\"\r\n            >\r\n              <Hash className=\"w-4 h-4 mr-1\" />\r\n              AI Tag {showHashtagsSidebar ? 'Sakla' : 'Göster'}\r\n            </Button>\r\n\r\n            <Badge variant=\"secondary\" className=\"text-xs lg:text-sm\">\r\n              {searchQuery ? filteredPrompts.filter(p => !p.is_used).length : prompts.filter(p => !p.is_used).length} / {searchQuery ? filteredPrompts.length : prompts.length}\r\n            </Badge>\r\n          </div>\r\n        </div>\r\n        \r\n        {/* Enhanced Responsive Search Box */}\r\n        <div className=\"relative\">\r\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\r\n          <input\r\n            type=\"text\"\r\n            placeholder=\"Prompt'larda ara...\"\r\n            value={searchQuery}\r\n            onChange={(e) => setSearchQuery(e.target.value)}\r\n            className=\"w-full pl-10 pr-10 py-2.5 sm:py-2 border border-gray-300 rounded-lg text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50 hover:bg-white transition-colors touch-manipulation\"\r\n          />\r\n          {searchQuery && (\r\n            <button\r\n              onClick={() => setSearchQuery(\"\")}\r\n              className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 touch-manipulation p-1\"\r\n            >\r\n              <X className=\"h-4 w-4\" />\r\n            </button>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Prompts List */}\r\n      <ScrollArea className=\"flex-1 p-2 sm:p-3 lg:p-6 pb-32 sm:pb-28 lg:pb-24\">\r\n        <div className=\"space-y-2 sm:space-y-3\">\r\n          {prompts.length === 0 ? (\r\n            <div className=\"text-center py-8 sm:py-12\">\r\n              <Plus className=\"h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-3 sm:mb-4\" />\r\n              <h3 className=\"text-base sm:text-lg font-medium text-gray-900 mb-2\">Henüz Prompt Yok</h3>\r\n              <p className=\"text-sm sm:text-base text-gray-500 px-4\">Aşağıdaki alandan ilk promptunuzu ekleyin</p>\r\n            </div>\r\n          ) : filteredPrompts.length === 0 ? (\r\n            <div className=\"text-center py-8 sm:py-12\">\r\n              <Search className=\"h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-3 sm:mb-4\" />\r\n              <h3 className=\"text-base sm:text-lg font-medium text-gray-900 mb-2\">Arama Sonucu Bulunamadı</h3>\r\n              <p className=\"text-sm sm:text-base text-gray-500 px-4\">&quot;{searchQuery}&quot; için eşleşen prompt bulunamadı</p>\r\n              <Button\r\n                variant=\"outline\"\r\n                className=\"mt-3 sm:mt-4 touch-manipulation\"\r\n                onClick={() => setSearchQuery(\"\")}\r\n              >\r\n                Aramayı Temizle\r\n              </Button>\r\n            </div>\r\n          ) : (\r\n            <DndContext\r\n              sensors={sensors}\r\n              collisionDetection={closestCenter}\r\n              onDragEnd={handleDragEnd}\r\n            >\r\n              <SortableContext\r\n                items={filteredPrompts.map(p => p.id)}\r\n                strategy={verticalListSortingStrategy}\r\n              >\r\n{(() => {\r\n                  const sortedPrompts = filteredPrompts.sort((a, b) => {\r\n                    // Önce kullanılmayan promptlar (is_used: false), sonra kullanılanlar (is_used: true)\r\n                    if (a.is_used !== b.is_used) {\r\n                      return a.is_used ? 1 : -1;\r\n                    }\r\n\r\n                    // Kullanılmayan promptları order_index'e göre sırala\r\n                    if (!a.is_used && !b.is_used) {\r\n                      return a.order_index - b.order_index;\r\n                    }\r\n\r\n                    // Kullanılan promptları last_used_at'e göre sırala (en son kullanılan önce)\r\n                    if (a.is_used && b.is_used) {\r\n                      const aLastUsed = a.last_used_at ? new Date(a.last_used_at).getTime() : 0;\r\n                      const bLastUsed = b.last_used_at ? new Date(b.last_used_at).getTime() : 0;\r\n                      return bLastUsed - aLastUsed; // Descending order (most recent first)\r\n                    }\r\n\r\n                    return 0;\r\n                  });\r\n\r\n                  // Pagination logic - only apply when not searching\r\n                  const isSearching = searchQuery.trim().length > 0;\r\n                  let displayPrompts = sortedPrompts;\r\n\r\n                  if (!isSearching) {\r\n                    // Apply pagination only when not searching\r\n                    const startIndex = (currentPage - 1) * PROMPTS_PER_PAGE;\r\n                    const endIndex = startIndex + PROMPTS_PER_PAGE;\r\n                    displayPrompts = sortedPrompts.slice(startIndex, endIndex);\r\n                  }\r\n\r\n                  const unusedPrompts = displayPrompts.filter(p => !p.is_used);\r\n                  const usedPrompts = displayPrompts.filter(p => p.is_used);\r\n                  const hasUnusedPrompts = unusedPrompts.length > 0;\r\n                  const hasUsedPrompts = usedPrompts.length > 0;\r\n\r\n                  // Calculate pagination info\r\n                  const totalPrompts = sortedPrompts.length;\r\n                  const totalPages = Math.ceil(totalPrompts / PROMPTS_PER_PAGE);\r\n                  const showPagination = !isSearching && totalPages > 1;\r\n\r\n                  return (\r\n                    <>\r\n                      {/* Render unused prompts */}\r\n                      {unusedPrompts.map((prompt) => (\r\n                        <SortablePromptItem\r\n                          key={prompt.id}\r\n                          prompt={prompt}\r\n                          isMultiSelectMode={isMultiSelectMode}\r\n                          selectedPrompts={selectedPrompts}\r\n                          editingPromptId={editingPromptId}\r\n                          editingText={editingText}\r\n                          editTextareaRef={editTextareaRef}\r\n                          onSelectPrompt={handleSelectPrompt}\r\n                          onEditPrompt={handleEditPrompt}\r\n                          onSaveEdit={handleSaveEdit}\r\n                          onCancelEdit={handleCancelEdit}\r\n                          onCopyPrompt={handleCopyPrompt}\r\n                          setEditingText={setEditingText}\r\n                          onHashtagFilter={handleHashtagFilter}\r\n                        />\r\n                      ))}\r\n\r\n                      {/* Visual separator between unused and used prompts */}\r\n                      {hasUnusedPrompts && hasUsedPrompts && (\r\n                        <div className=\"relative my-8\">\r\n                          <div className=\"absolute inset-0 flex items-center\">\r\n                            <div className=\"w-full border-t-2 border-green-300 shadow-md\"></div>\r\n                          </div>\r\n                          <div className=\"relative flex justify-center text-sm\">\r\n                            <span className=\"bg-gradient-to-r from-green-50 to-green-100 px-6 py-2 text-green-700 font-semibold rounded-full border border-green-200 shadow-sm\">\r\n                              <CheckCircle className=\"h-4 w-4 inline mr-2\" />\r\n                              Kullanılan Promptlar\r\n                            </span>\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Render used prompts */}\r\n                      {usedPrompts.map((prompt) => (\r\n                        <SortablePromptItem\r\n                          key={prompt.id}\r\n                          prompt={prompt}\r\n                          isMultiSelectMode={isMultiSelectMode}\r\n                          selectedPrompts={selectedPrompts}\r\n                          editingPromptId={editingPromptId}\r\n                          editingText={editingText}\r\n                          editTextareaRef={editTextareaRef}\r\n                          onSelectPrompt={handleSelectPrompt}\r\n                          onEditPrompt={handleEditPrompt}\r\n                          onSaveEdit={handleSaveEdit}\r\n                          onCancelEdit={handleCancelEdit}\r\n                          onCopyPrompt={handleCopyPrompt}\r\n                          setEditingText={setEditingText}\r\n                          onHashtagFilter={handleHashtagFilter}\r\n                        />\r\n                      ))}\r\n                    </>\r\n                  );\r\n                })()}\r\n              </SortableContext>\r\n            </DndContext>\r\n          )}\r\n\r\n          {/* Pagination Controls */}\r\n          {(() => {\r\n            const sortedPrompts = filteredPrompts.sort((a, b) => {\r\n              if (a.is_used !== b.is_used) {\r\n                return a.is_used ? 1 : -1;\r\n              }\r\n              if (!a.is_used && !b.is_used) {\r\n                return a.order_index - b.order_index;\r\n              }\r\n              if (a.is_used && b.is_used) {\r\n                const aLastUsed = a.last_used_at ? new Date(a.last_used_at).getTime() : 0;\r\n                const bLastUsed = b.last_used_at ? new Date(b.last_used_at).getTime() : 0;\r\n                return bLastUsed - aLastUsed;\r\n              }\r\n              return 0;\r\n            });\r\n\r\n            const isSearching = searchQuery.trim().length > 0;\r\n            const totalPrompts = sortedPrompts.length;\r\n            const totalPages = Math.ceil(totalPrompts / PROMPTS_PER_PAGE);\r\n            const showPagination = !isSearching && totalPages > 1;\r\n\r\n            if (!showPagination) return null;\r\n\r\n            return (\r\n              <div className=\"flex items-center justify-between px-4 py-3 border-t border-gray-200 bg-gray-50\">\r\n                <div className=\"text-sm text-gray-600\">\r\n                  Toplam {totalPrompts} prompt, sayfa {currentPage} / {totalPages}\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    size=\"sm\"\r\n                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}\r\n                    disabled={currentPage === 1}\r\n                  >\r\n                    Önceki\r\n                  </Button>\r\n                  <div className=\"flex items-center gap-1\">\r\n                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\r\n                      let pageNum;\r\n                      if (totalPages <= 5) {\r\n                        pageNum = i + 1;\r\n                      } else if (currentPage <= 3) {\r\n                        pageNum = i + 1;\r\n                      } else if (currentPage >= totalPages - 2) {\r\n                        pageNum = totalPages - 4 + i;\r\n                      } else {\r\n                        pageNum = currentPage - 2 + i;\r\n                      }\r\n\r\n                      return (\r\n                        <Button\r\n                          key={pageNum}\r\n                          variant={currentPage === pageNum ? \"default\" : \"outline\"}\r\n                          size=\"sm\"\r\n                          onClick={() => setCurrentPage(pageNum)}\r\n                          className=\"w-8 h-8 p-0\"\r\n                        >\r\n                          {pageNum}\r\n                        </Button>\r\n                      );\r\n                    })}\r\n                  </div>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    size=\"sm\"\r\n                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}\r\n                    disabled={currentPage === totalPages}\r\n                  >\r\n                    Sonraki\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            );\r\n          })()}\r\n        </div>\r\n      </ScrollArea>\r\n\r\n      {/* Enhanced Context Gallery Modal */}\r\n      <Suspense fallback={\r\n        isContextGalleryOpen ? (\r\n          <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50\">\r\n            <div className=\"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden\">\r\n              <ContextGallerySkeleton />\r\n            </div>\r\n          </div>\r\n        ) : null\r\n      }>\r\n        <EnhancedContextGalleryModal\r\n          open={isContextGalleryOpen}\r\n          onOpenChange={onToggleContextGallery || (() => {})}\r\n          onSelectContext={handleSelectContextFromGallery}\r\n        />\r\n      </Suspense>\r\n\r\n      {/* Enhanced Sticky Add New Prompt - Redesigned Interface */}\r\n      <div className=\"fixed bottom-0 left-0 right-0 bg-gradient-to-t from-white via-white to-white/95 border-t border-gray-200 shadow-xl safe-area-bottom z-[60] backdrop-blur-sm\">\r\n        <div className=\"max-w-6xl mx-auto\">\r\n          {/* Mobile: Enhanced two-row layout */}\r\n          <div className=\"block lg:hidden\">\r\n            {/* Top row: Redesigned toolbar with better organization */}\r\n            <div className=\"p-3 border-b border-gray-100 bg-gray-50/50\">\r\n              <div className=\"flex justify-center mb-2\">\r\n                <Button\r\n                  variant={isMultiSelectMode ? \"default\" : \"outline\"}\r\n                  size=\"sm\"\r\n                  onClick={handleToggleMultiSelect}\r\n                  className={`min-h-[44px] px-3 transition-all duration-200 ${isMultiSelectMode ? \"text-white bg-blue-600 hover:bg-blue-700 shadow-md\" : \"text-blue-600 hover:text-blue-700 border-blue-200 hover:border-blue-300 hover:bg-blue-50\"}`}\r\n                >\r\n                  <MousePointer className=\"h-4 w-4 mr-1\" />\r\n                  <span className=\"text-xs font-medium\">{isMultiSelectMode ? 'Seçim İptal' : 'Çoklu Seç'}</span>\r\n                </Button>\r\n              </div>\r\n\r\n              <div className=\"grid grid-cols-2 gap-2\">\r\n                {isMultiSelectMode && selectedPrompts.size > 0 && (\r\n                  <Button\r\n                    variant=\"default\"\r\n                    size=\"sm\"\r\n                    onClick={handleCopySelectedPrompts}\r\n                    className=\"text-white bg-green-600 hover:bg-green-700 min-h-[40px] px-2 shadow-md transition-all duration-200\"\r\n                  >\r\n                    <Copy className=\"h-4 w-4 mr-1\" />\r\n                    <span className=\"text-xs font-medium\">Kopyala ({selectedPrompts.size})</span>\r\n                  </Button>\r\n                )}\r\n\r\n                <Button\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  onClick={handleExportUnusedPrompts}\r\n                  className=\"text-purple-600 hover:text-purple-700 border-purple-200 hover:border-purple-300 hover:bg-purple-50 min-h-[40px] px-2 transition-all duration-200\"\r\n                >\r\n                  <Download className=\"h-4 w-4 mr-1\" />\r\n                  <span className=\"text-xs font-medium\">Export</span>\r\n                </Button>\r\n\r\n                <Button\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  onClick={handleUndoLastCopy}\r\n                  className=\"text-orange-600 hover:text-orange-700 border-orange-200 hover:border-orange-300 hover:bg-orange-50 min-h-[40px] px-2 transition-all duration-200\"\r\n                >\r\n                  <Undo className=\"h-4 w-4 mr-1\" />\r\n                  <span className=\"text-xs font-medium\">Geri Al</span>\r\n                </Button>\r\n              </div>\r\n            </div>\r\n            \r\n            {/* Enhanced Mobile-First Input Section */}\r\n            <div className=\"p-2 sm:p-3 relative z-[60]\">\r\n              {/* Mobile: Stacked layout for small screens */}\r\n              <div className=\"block sm:hidden space-y-2\">\r\n                <div className=\"relative\">\r\n                  <SmartAutocomplete\r\n                    value={newPromptText}\r\n                    onChange={setNewPromptText}\r\n                    onKeyDown={(e) => {\r\n                      if (e.key === 'Enter' && !e.shiftKey) {\r\n                        e.preventDefault();\r\n                        handleAddPrompt();\r\n                      }\r\n                    }}\r\n                    placeholder=\"Prompt yazın... (/ klasör, # etiket)\"\r\n                    className=\"resize-none text-base border-2 focus:border-blue-500 transition-colors duration-200 pr-16 w-full rounded-lg p-3 touch-manipulation\"\r\n                    suggestions={{\r\n                      hashtags: allHashtags || [],\r\n                      folders: allCategories || []\r\n                    }}\r\n                    enableDynamicHeight={true}\r\n                    heightConfig={{\r\n                      minHeight: 48,\r\n                      maxHeightFraction: 0.25, // 1/4 for mobile\r\n                      baseHeight: 48\r\n                    }}\r\n                  />\r\n\r\n                </div>\r\n\r\n                {/* Mobile: Button row */}\r\n                <div className=\"flex gap-1\">\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    size=\"sm\"\r\n                    onClick={handleToggleMultiSelect}\r\n                    className={`flex-1 min-h-[44px] transition-all duration-200 touch-manipulation ${isMultiSelectMode ? \"text-white bg-blue-600 hover:bg-blue-700\" : \"text-blue-600 hover:text-blue-700 border-blue-200\"}`}\r\n                  >\r\n                    <MousePointer className=\"h-4 w-4 mr-1\" />\r\n                    <span className=\"text-xs\">Seç</span>\r\n                  </Button>\r\n\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    size=\"sm\"\r\n                    onClick={handleExportUnusedPrompts}\r\n                    className=\"flex-1 text-purple-600 hover:text-purple-700 border-purple-200 hover:border-purple-300 hover:bg-purple-50 min-h-[44px] touch-manipulation\"\r\n                  >\r\n                    <Download className=\"h-4 w-4 mr-1\" />\r\n                    <span className=\"text-xs\">Export</span>\r\n                  </Button>\r\n\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    size=\"sm\"\r\n                    onClick={handleUndoLastCopy}\r\n                    className=\"flex-1 text-orange-600 hover:text-orange-700 border-orange-200 hover:border-orange-300 hover:bg-orange-50 min-h-[44px] touch-manipulation\"\r\n                  >\r\n                    <Undo className=\"h-4 w-4 mr-1\" />\r\n                    <span className=\"text-xs\">Geri</span>\r\n                  </Button>\r\n\r\n                  <Button\r\n                    onClick={handleAddPrompt}\r\n                    disabled={!newPromptText.trim()}\r\n                    size=\"default\"\r\n                    className=\"flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white min-h-[44px] shadow-lg hover:shadow-xl transition-all duration-200 font-medium touch-manipulation\"\r\n                  >\r\n                    <Plus className=\"h-4 w-4 mr-1\" />\r\n                    <span className=\"text-xs\">Ekle</span>\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Tablet/Desktop: Side-by-side layout */}\r\n              <div className=\"hidden sm:flex gap-2 items-end relative z-[60]\">\r\n                <div className=\"flex-1 relative\">\r\n                  <SmartAutocomplete\r\n                    value={newPromptText}\r\n                    onChange={setNewPromptText}\r\n                    onKeyDown={(e) => {\r\n                      if (e.key === 'Enter' && !e.shiftKey) {\r\n                        e.preventDefault();\r\n                        handleAddPrompt();\r\n                      }\r\n                    }}\r\n                    placeholder=\"Prompt metninizi buraya yazın... (/ için klasör, # için etiket)\"\r\n                    className=\"resize-none text-base border-2 focus:border-blue-500 transition-colors duration-200 pr-16 w-full rounded-lg p-3\"\r\n                    suggestions={{\r\n                      hashtags: allHashtags || [],\r\n                      folders: allCategories || []\r\n                    }}\r\n                    enableDynamicHeight={true}\r\n                    heightConfig={{\r\n                      minHeight: 44,\r\n                      maxHeightFraction: 0.33, // 1/3 for tablet\r\n                      baseHeight: 44\r\n                    }}\r\n                  />\r\n\r\n                </div>\r\n\r\n                {/* Tablet/Desktop: Compact button row */}\r\n                <div className=\"flex gap-1\">\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    size=\"sm\"\r\n                    onClick={handleToggleMultiSelect}\r\n                    className={`min-h-[44px] px-2 transition-all duration-200 ${isMultiSelectMode ? \"text-white bg-blue-600 hover:bg-blue-700\" : \"text-blue-600 hover:text-blue-700 border-blue-200\"}`}\r\n                  >\r\n                    <MousePointer className=\"h-4 w-4\" />\r\n                  </Button>\r\n\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    size=\"sm\"\r\n                    onClick={handleExportUnusedPrompts}\r\n                    className=\"text-purple-600 hover:text-purple-700 border-purple-200 hover:border-purple-300 hover:bg-purple-50 min-h-[44px] px-2\"\r\n                  >\r\n                    <Download className=\"h-4 w-4\" />\r\n                  </Button>\r\n\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    size=\"sm\"\r\n                    onClick={handleUndoLastCopy}\r\n                    className=\"text-orange-600 hover:text-orange-700 border-orange-200 hover:border-orange-300 hover:bg-orange-50 min-h-[44px] px-2\"\r\n                  >\r\n                    <Undo className=\"h-4 w-4\" />\r\n                  </Button>\r\n\r\n                  <Button\r\n                    onClick={handleAddPrompt}\r\n                    disabled={!newPromptText.trim()}\r\n                    size=\"default\"\r\n                    className=\"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white px-4 min-h-[44px] shrink-0 shadow-lg hover:shadow-xl transition-all duration-200 font-medium\"\r\n                  >\r\n                    <Plus className=\"h-4 w-4 mr-1\" />\r\n                    Ekle\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Desktop: Optimized compact layout */}\r\n          <div className=\"hidden lg:block p-4 bg-gradient-to-r from-gray-50 to-white border-t border-gray-100 relative z-[60]\">\r\n            <div className=\"bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 p-4\">\r\n              <div className=\"flex gap-4 items-end\">\r\n                {/* Smart Autocomplete Textarea */}\r\n                <div className=\"flex-1 relative\">\r\n                  <SmartAutocomplete\r\n                    value={newPromptText}\r\n                    onChange={setNewPromptText}\r\n                    onKeyDown={(e) => {\r\n                      if (e.key === 'Enter' && !e.shiftKey) {\r\n                        e.preventDefault();\r\n                        handleAddPrompt();\r\n                      }\r\n                    }}\r\n                    placeholder=\"Prompt metninizi buraya yazın... (/ için klasör, # için etiket)\"\r\n                    className=\"resize-none border-0 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg bg-gray-50 hover:bg-white transition-all duration-200 text-base pr-20 w-full p-3\"\r\n                    suggestions={{\r\n                      hashtags: allHashtags || [],\r\n                      folders: allCategories || []\r\n                    }}\r\n                    enableDynamicHeight={true}\r\n                    heightConfig={{\r\n                      minHeight: 56,\r\n                      maxHeightFraction: 0.33, // 1/3 for desktop\r\n                      baseHeight: 56\r\n                    }}\r\n                  />\r\n\r\n\r\n                    {/* Modern Advanced Categorization Fields */}\r\n                    {showAdvancedForm && (\r\n                      <div className=\"space-y-4 pt-4 border-t border-gray-200 bg-gray-50 rounded-lg p-4 mt-3\">\r\n                        <div className=\"flex items-center gap-2 mb-2\">\r\n                          <div className=\"w-1 h-4 bg-purple-500 rounded-full\"></div>\r\n                          <span className=\"text-sm font-medium text-gray-700\">Kategorilendirme</span>\r\n                        </div>\r\n\r\n                        {/* Modern Title Field */}\r\n                        <div>\r\n                          <label className=\"text-sm font-medium text-gray-700 mb-2 block\">\r\n                            Başlık (İsteğe bağlı)\r\n                          </label>\r\n                          <Input\r\n                            type=\"text\"\r\n                            placeholder=\"Bu prompt için kısa bir başlık...\"\r\n                            value={newPromptTitle}\r\n                            onChange={(e) => setNewPromptTitle(e.target.value)}\r\n                            className=\"border-gray-300 focus:border-purple-500 focus:ring-purple-500\"\r\n                          />\r\n                        </div>\r\n\r\n                        {/* Modern Hashtags */}\r\n                        <div>\r\n                          <label className=\"text-sm font-medium text-gray-700 mb-2 block\">\r\n                            Etiketler\r\n                          </label>\r\n                          {activeProjectId && (\r\n                            <HashtagInput\r\n                              hashtags={newPromptHashtags}\r\n                              onHashtagsChange={setNewPromptHashtags}\r\n                              suggestions={allHashtags || []}\r\n                              placeholder=\"Etiket ekleyin... (örn: #frontend, #api)\"\r\n                              maxTags={5}\r\n                            />\r\n                          )}\r\n                        </div>\r\n\r\n                        {/* Modern Category */}\r\n                        <div>\r\n                          <label className=\"text-sm font-medium text-gray-700 mb-2 block\">\r\n                            Klasör/Kategori\r\n                          </label>\r\n                          {activeProjectId && (\r\n                            <CategorySelector\r\n                              category={newPromptCategory}\r\n                              onCategoryChange={setNewPromptCategory}\r\n                              suggestions={allCategories || []}\r\n                              placeholder=\"Klasör seçin... (örn: /frontend, /admin)\"\r\n                            />\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n\r\n                  {/* Console Log Fix Toggle */}\r\n                  <div className=\"flex items-center justify-center gap-3 py-3 border-t border-gray-200 mt-4 pt-4\">\r\n                    <label className=\"text-sm font-medium text-gray-700\">Console Log Fix</label>\r\n                    <Switch\r\n                      checked={isConsoleLogFixEnabled}\r\n                      onCheckedChange={setIsConsoleLogFixEnabled}\r\n                      className=\"scale-90\"\r\n                    />\r\n                  </div>\r\n\r\n                  {/* Prompt Limit Warning */}\r\n                  {limits && (\r\n                    <InlineLimitWarning\r\n                      type=\"prompt\"\r\n                      current={limits.current_prompts}\r\n                      max={limits.max_prompts_per_project}\r\n                      onUpgrade={() => {/* Plan upgrade modal açılacak */}}\r\n                    />\r\n                  )}\r\n\r\n                  {/* Add Prompt Button - Moved from bottom */}\r\n                  <Button\r\n                    onClick={handleAddPrompt}\r\n                    disabled={!newPromptText.trim() || (limits && !limits.can_create_prompt)}\r\n                    size=\"lg\"\r\n                    className=\"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:from-gray-300 disabled:to-gray-400 text-white px-8 py-3 min-h-[56px] shadow-lg hover:shadow-xl transition-all duration-200 font-semibold rounded-lg\"\r\n                  >\r\n                    <Plus className=\"h-5 w-5 mr-2\" />\r\n                    Prompt Ekle\r\n                  </Button>\r\n                </div>\r\n\r\n                {/* Action buttons row */}\r\n                <div className=\"space-y-3\">\r\n\r\n                  {/* Reorganized buttons */}\r\n                  <div className=\"flex gap-2\">\r\n                    <Button\r\n                      variant={isMultiSelectMode ? \"default\" : \"outline\"}\r\n                      size=\"sm\"\r\n                      onClick={handleToggleMultiSelect}\r\n                      className={`transition-all duration-200 rounded-lg font-medium ${\r\n                        isMultiSelectMode\r\n                          ? \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-md\"\r\n                          : \"border-blue-200 text-blue-600 hover:text-blue-700 hover:border-blue-300 hover:bg-blue-50\"\r\n                      }`}\r\n                    >\r\n                      <MousePointer className=\"h-4 w-4 mr-2\" />\r\n                      Çoklu Seç\r\n                    </Button>\r\n\r\n                    {isMultiSelectMode && selectedPrompts.size > 0 && (\r\n                      <Button\r\n                        variant=\"default\"\r\n                        size=\"sm\"\r\n                        onClick={handleCopySelectedPrompts}\r\n                        className=\"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white shadow-md transition-all duration-200 rounded-lg font-medium\"\r\n                      >\r\n                        <Copy className=\"h-4 w-4 mr-2\" />\r\n                        Kopyala ({selectedPrompts.size})\r\n                      </Button>\r\n                    )}\r\n\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={handleExportUnusedPrompts}\r\n                      className=\"border-purple-200 text-purple-600 hover:text-purple-700 hover:border-purple-300 hover:bg-purple-50 transition-all duration-200 rounded-lg font-medium\"\r\n                    >\r\n                      <Download className=\"h-4 w-4 mr-2\" />\r\n                      Export\r\n                    </Button>\r\n\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={handleUndoLastCopy}\r\n                      className=\"border-orange-200 text-orange-600 hover:text-orange-700 hover:border-orange-300 hover:bg-orange-50 transition-all duration-200 rounded-lg font-medium\"\r\n                    >\r\n                      <Undo className=\"h-4 w-4 mr-2\" />\r\n                      Geri Al\r\n                    </Button>\r\n\r\n                    {/* Advanced Form Toggle - Moved from top */}\r\n                    {!newPromptText.trim() && (\r\n                      <Button\r\n                        type=\"button\"\r\n                        variant=\"outline\"\r\n                        size=\"sm\"\r\n                        onClick={() => setShowAdvancedForm(!showAdvancedForm)}\r\n                        className=\"border-gray-300 text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition-all duration-200\"\r\n                      >\r\n                        {showAdvancedForm ? (\r\n                          <>\r\n                            <ChevronUp className=\"w-4 h-4 mr-2\" />\r\n                            Kategorileri Gizle\r\n                          </>\r\n                        ) : (\r\n                          <>\r\n                            <ChevronDown className=\"w-4 h-4 mr-2\" />\r\n                            Kategori Ekle\r\n                          </>\r\n                        )}\r\n                      </Button>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Hashtags Sidebar */}\r\n      {showHashtagsSidebar && activeProjectId && (\r\n        <PopularHashtagsSidebar\r\n          projectId={activeProjectId}\r\n          onHashtagClick={handleHashtagFilter}\r\n          onCategoryClick={handleCategoryFilter}\r\n          selectedHashtags={filterHashtags}\r\n          selectedCategory={filterCategory}\r\n          className=\"hidden lg:flex\"\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default PromptWorkspace;"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AA2FA;AASA;AASA;;;AAvIA;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,wBAAwB;AACxB,MAAM,4CAA8B,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,IAAM,wJAA2C,IAAI,CAAC,CAAA,IAAK,CAAC;YAAE,SAAS,EAAE,2BAA2B;QAAC,CAAC;KAAzI;AAEN,kCAAkC;AAClC,MAAM,yBAAyB;AAE/B,gEAAgE;AAChE,MAAM,wCAA0B,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,KAAE,SAAS,wBAAwB,KAUrE;QAVqE,EACpE,UAAU,EACV,SAAS,EACT,aAAa,EACb,KAAK,EAMN,GAVqE;;IAWpE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,kDAAkD;IAClD,MAAM,mBAAmB,WAAW,UAAU,CAAC;IAE/C,IAAI,CAAC,kBAAkB;QACrB,yBAAyB;QACzB,qBACE,6LAAC;YACC,WAAW;YACX,eAAe;YACf,OAAO;sBAEN;;;;;;IAGP;IAEA,6DAA6D;IAC7D,MAAM,oBAAoB,WAAW,SAAS,CAAC,uBAAuB,MAAM,EAAE,IAAI;IAElF,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBACC,WAAW;gBACX,eAAe;gBACf,OAAO;0BAEN;;;;;;0BAIH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAA4C;;;;;;0CAC5D,6LAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;0CAET,2BACC;;sDACE,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAY;;iEAInC;;sDACE,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;oBAO1C,2BACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;6CAGH,6LAAC;wBAAE,WAAU;kCACV,kBAAkB,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,MACvC,kBAAkB,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,OAAO,QACrD,kBAAkB,KAAK,CAAC,KAAK,CAAC,EAAE;;;;;;;;;;;;;;;;;;AAOhD;MAhFM;;;;;AAyIN,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,MAAE,SAAS,mBAAmB,KAclC;QAdkC,EAC1D,MAAM,EACN,iBAAiB,EACjB,eAAe,EACf,eAAe,EACf,WAAW,EACX,eAAe,EACf,cAAc,EACd,YAAY,EACZ,UAAU,EACV,YAAY,EACZ,YAAY,EACZ,cAAc,EACd,eAAe,EACS,GAdkC;;IAe1D,MAAM,EACJ,UAAU,EACV,SAAS,EACT,UAAU,EACV,SAAS,EACT,UAAU,EACV,UAAU,EACX,GAAG,CAAA,GAAA,sKAAA,CAAA,cAAW,AAAD,EAAE;QAAE,IAAI,OAAO,EAAE;IAAC;IAEhC,MAAM,QAAQ;QACZ,WAAW,wKAAA,CAAA,MAAG,CAAC,SAAS,CAAC,QAAQ,CAAC;QAClC;QACA,SAAS,aAAa,MAAM;IAC9B;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QACH,KAAK;QACL,OAAO;QACP,WAAW,AAAC,+BAKV,OAJA,OAAO,OAAO,GACV,+BACA,kEACL,KAIA,OAHC,gBAAgB,GAAG,CAAC,OAAO,EAAE,IACzB,yCACA;QAEN,SAAS,IAAM,oBAAoB,eAAe,OAAO,EAAE,IAAI;;0BAE/D,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;;kCAEpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CACZ,CAAC,mCACA,6LAAC;gDACC,WAAU;gDACT,GAAG,UAAU;gDACb,GAAG,SAAS;0DAEb,cAAA,6LAAC,yNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;4CAG3B,mCACC,6LAAC;gDACC,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,eAAe,OAAO,EAAE;gDAC1B;0DAEC,gBAAgB,GAAG,CAAC,OAAO,EAAE,kBAC5B,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;yEAEvB,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAIxB,6LAAC;gDAAI,WAAW,AAAC,iFAIhB,OAHC,OAAO,OAAO,GACV,gCACA;0DAEH,OAAO,WAAW;;;;;;0DAErB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAW,AAAC,uCAIjB,OAHC,OAAO,OAAO,GACV,8BACA;8DAEH,OAAO,SAAS,IAAI,AAAC,QAA0B,OAAnB,OAAO,WAAW;;;;;;;;;;;;;;;;;kDAIrD,6LAAC;wCAAI,WAAU;kDACZ,OAAO,OAAO,iBACb,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;iEAEvB,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;4BAIvB,CAAC,mCACA,6LAAC;gCAAI,WAAU;0CACZ,oBAAoB,OAAO,EAAE,iBAC5B;;sDACE,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGpC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;iEAKlC;;sDACE,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,aAAa;4CAC5B,WAAU;;8DAEV,6LAAC,qMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGpC,6LAAC,kJAAA,CAAA,oBAAiB;4CAChB,UAAU,OAAO,EAAE;4CACnB,aAAa,OAAO,KAAK,IAAI;4CAC7B,YAAY,OAAO,WAAW;4CAC9B,UAAU,OAAO,SAAS,IAAI;4CAC9B,SAAQ;4CACR,MAAK;4CACL,WAAU;;;;;;sDAEZ,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,OAAO,OAAO,GAAG,cAAc;4CACxC,MAAK;4CACL,SAAS,IAAM,aAAa;4CAC5B,WAAW,AAAC,qBAAuD,OAAnC,OAAO,OAAO,GAAG,eAAe;;8DAEhE,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDACf,OAAO,OAAO,GAAG,eAAe;;;;;;;;;;;;;;;;;;;;kCAS7C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCACZ,CAAC,mCACA,6LAAC;wCACC,WAAU;wCACT,GAAG,UAAU;wCACb,GAAG,SAAS;kDAEb,cAAA,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;oCAG3B,mCACC,6LAAC;wCACC,WAAU;wCACV,SAAS,CAAC;4CACR,EAAE,eAAe;4CACjB,eAAe,OAAO,EAAE;wCAC1B;kDAEC,gBAAgB,GAAG,CAAC,OAAO,EAAE,kBAC5B,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;iEAEvB,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAIxB,6LAAC;wCAAI,WAAW,AAAC,6EAIhB,OAHC,OAAO,OAAO,GACV,gCACA;kDAEH,OAAO,WAAW;;;;;;kDAErB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAW,AAAC,uCAIjB,OAHC,OAAO,OAAO,GACV,8BACA;sDAEH,OAAO,SAAS,IAAI,AAAC,QAA0B,OAAnB,OAAO,WAAW;;;;;;;;;;;kDAGnD,6LAAC;wCAAI,WAAU;kDACZ,OAAO,OAAO,iBACb,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;iEAEvB,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;4BAIvB,CAAC,mCACA,6LAAC;gCAAI,WAAU;0CACZ,oBAAoB,OAAO,EAAE,iBAC5B;;sDACE,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGpC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;iEAKlC;;sDACE,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,aAAa;4CAC5B,WAAU;;8DAEV,6LAAC,qMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGpC,6LAAC,kJAAA,CAAA,oBAAiB;4CAChB,UAAU,OAAO,EAAE;4CACnB,aAAa,OAAO,KAAK,IAAI;4CAC7B,YAAY,OAAO,WAAW;4CAC9B,UAAU,OAAO,SAAS,IAAI;4CAC9B,SAAQ;4CACR,MAAK;;;;;;sDAEP,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,OAAO,OAAO,GAAG,cAAc;4CACxC,MAAK;4CACL,SAAS,IAAM,aAAa;4CAC5B,WAAW,OAAO,OAAO,GAAG,eAAe;;8DAE3C,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDACf,OAAO,OAAO,GAAG,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/C,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACpB,oBAAoB,OAAO,EAAE,iBAC5B,6LAAC,uIAAA,CAAA,WAAQ;oBACP,KAAK;oBACL,OAAO;oBACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oBAC9C,WAAU;oBACV,OAAO;wBAAE,QAAQ;oBAAO;oBACxB,WAAW,CAAC;wBACV,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,OAAO,EAAE;4BAClC;wBACF,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;4BAC7B;wBACF;oBACF;oBACA,SAAS;;;;;yCAGX,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,YAAY,OAAO,WAAW;4BAC9B,WAAW,AAAC,0EAEX,OADC,OAAO,OAAO,GAAG,kBAAkB;4BAErC,eAAe,IAAM,aAAa;4BAClC,OAAM;;;;;;wBAIP,CAAC,AAAC,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,MAAM,GAAG,KAAM,OAAO,QAAQ,mBAC1D,6LAAC;4BAAI,WAAU;;gCAEZ,OAAO,QAAQ,kBACd,6LAAC,oIAAA,CAAA,QAAK;oCACJ,SAAQ;oCACR,WAAU;oCACV,SAAS,CAAC;wCACR,EAAE,cAAc;wCAChB,EAAE,eAAe;oCACjB,0CAA0C;oCAC5C;;sDAEA,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCACjB,OAAO,QAAQ;;;;;;;gCAKnB,OAAO,IAAI,IAAI,MAAM,OAAO,CAAC,OAAO,IAAI,KAAK,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,KAAa;oCAC1E,+BAA+B;oCAC/B,MAAM,SAAS,OAAO,QAAQ,WAAW,MAAM,OAAO;oCACtD,IAAI,CAAC,UAAU,OAAO,IAAI,OAAO,IAAI,OAAO;oCAE5C,qBACE,6LAAC,oIAAA,CAAA,QAAK;wCAEJ,SAAQ;wCACR,WAAU;wCACV,SAAS,CAAC;4CACR,EAAE,cAAc;4CAChB,EAAE,eAAe;4CACjB,gBAAgB;wCAClB;;0DAEA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACf,OAAO,OAAO,CAAC,KAAK;;uCAVhB;;;;;gCAaX;;;;;;;;;;;;;;;;;;;;;;;;AAQhB;;QA/TM,sKAAA,CAAA,cAAW;;;MAtBX;AA4VC,SAAS;QAAgB,EAC9B,uBAAuB,KAAK,EAC5B,sBAAsB,EACD,GAHS,iEAGN,CAAC;;IACzB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IACxE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,2BAA2B;IAC3B,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACvE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,gCAAgC;IAChC,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErE,mBAAmB;IACnB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,mBAAmB;IAEzB,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,eAAe;QACjB;oCAAG;QAAC;KAAY;IAEhB,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAuB;IACpD,MAAM,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD;IACxD,MAAM,EAAE,MAAM,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD;IAErC,MAAM,UAAU,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EACvB,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EAAE,8JAAA,CAAA,gBAAa,GACvB,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EAAE,8JAAA,CAAA,iBAAc,EAAE;QACxB,kBAAkB,sKAAA,CAAA,8BAA2B;IAC/C;IAGF,MAAM,EAAE,MAAM,UAAU,EAAE,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,aAAU,AAAD,EAAE;IAC1C,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,aAAU,AAAD,EAAE;IACrC,MAAM,EAAE,MAAM,cAAc,EAAE,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;IAClD,MAAM,EAAE,MAAM,gBAAgB,EAAE,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,mBAAgB,AAAD,EAAE;IACtD,MAAM,uBAAuB,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD;IAC3C,MAAM,uBAAuB,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD;IAC3C,MAAM,4BAA4B,CAAA,GAAA,iIAAA,CAAA,uBAAoB,AAAD;IACrD,MAAM,qBAAqB,CAAA,GAAA,iIAAA,CAAA,sBAAmB,AAAD;IAE7C,iCAAiC;IACjC,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAC;QACtC,IAAI;YACF,qBAAqB;YACrB,MAAM,gBAAgB,YAAY,IAAI,OAAO,MAC3C,OAAO,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAChE,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;YAE9E,kBAAkB;YAClB,MAAM,kBAAkB,CAAC,kBAAkB,OAAO,QAAQ,KAAK;YAE/D,iBAAiB;YACjB,MAAM,kBAAkB,eAAe,MAAM,KAAK,KAC/C,OAAO,IAAI,IAAI,MAAM,OAAO,CAAC,OAAO,IAAI,KACxC,eAAe,IAAI,CAAC,CAAA,UAClB,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC;oBAChB,MAAM,SAAS,OAAO,QAAQ,WAAW,MAAM,OAAO;oBACtD,OAAO,OAAO,WAAW,OAAO,QAAQ,WAAW;gBACrD;YAGL,OAAO,iBAAiB,mBAAmB;QAC7C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B,OAAO;YAChD,OAAO,MAAM,oCAAoC;QACnD;IACF;IAGA,4DAA4D;IAC5D,MAAM,uBAAuB,CAAC;QAC5B,SAAS,KAAK,CAAC,MAAM,GAAG;QACxB,SAAS,KAAK,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,SAAS,YAAY,EAAE,OAAO;IACjE;IAEA,oDAAoD;IACpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,gBAAgB,OAAO,EAAE;gBAC3B,qBAAqB,gBAAgB,OAAO;YAC9C;QACF;oCAAG;QAAC;KAAY;IAEhB,MAAM,kBAAkB;QACtB,IAAI,cAAc,IAAI,MAAM,iBAAiB;YAC3C,yBAAyB;YACzB,IAAI,UAAU,CAAC,OAAO,iBAAiB,EAAE;gBACvC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,AAAC,0CAAmE,OAA1B,OAAO,eAAe,EAAC,KAAkC,OAA/B,OAAO,uBAAuB,EAAC;gBAC/G;YACF;YAEA,mCAAmC;YACnC,IAAI,sBAAsB;YAC1B,IAAI,wBAAwB;gBAC1B,MAAM,mBAAoB;gBAG1B,sBAAsB,mBAAmB;YAC3C;YAEA,oCAAoC;YACpC,MAAM,WAAW;gBACf,YAAY;gBACZ,aAAa;gBACb,gBAAgB;gBAChB,gBAAgB;gBAChB,cAAc;YAChB;YAEA,uDAAuD;YACvD,iBAAiB;YACjB,kBAAkB;YAClB,qBAAqB,EAAE;YACvB,qBAAqB;YACrB,oBAAoB;YAEpB,IAAI;oBAmBO;gBAlBT,kFAAkF;gBAClF,MAAM,EAAE,UAAU,iBAAiB,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,oCAAiC,AAAD,EAAE,SAAS,UAAU;gBAExH,oDAAoD;gBACpD,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,cAAc,IAAI,EAAE,EAAE,qBAAqB,EAAE;gBAExF,iFAAiF;gBACjF,MAAM,gBAAgB,eAAe,YAAY,MAAM,GAAG,IAAI,WAAW,CAAC,EAAE,GAAG,SAAS,cAAc;gBAEtG,kDAAkD;gBAClD,MAAM,gBAAgB,QAAQ,MAAM,GAAG,IACnC,KAAK,GAAG,IAAI,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,WAAW,IAAI,MAC9C;gBACJ,MAAM,iBAAiB,gBAAgB;gBAEvC,MAAM,qBAAqB,WAAW,CAAC;oBACrC,YAAY;oBACZ,aAAa;oBACb,OAAO,EAAA,wBAAA,SAAS,WAAW,cAApB,4CAAA,sBAAsB,IAAI,OAAM;oBACvC,UAAU,iBAAiB;oBAC3B,MAAM,eAAe,EAAE;oBACvB,aAAa;oBACb,SAAS;gBACX;gBAEA,wBAAwB;gBACxB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yBAAyB;gBAEvC,6BAA6B;gBAC7B,iBAAiB,SAAS,UAAU;gBACpC,kBAAkB,SAAS,WAAW;gBACtC,qBAAqB,SAAS,cAAc;gBAC5C,qBAAqB,SAAS,cAAc;gBAC5C,oBAAoB,SAAS,YAAY;gBAEzC,mCAAmC;gBACnC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;IACF;IAEA,MAAM,iCAAiC,CAAC;QACtC,iBAAiB,QAAQ,OAAO;QAChC,mCAAA,6CAAA;IACF;IAEA,2BAA2B;IAC3B,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE,CAAC;YACvC,IAAI;gBACF,6BAA6B;gBAC7B,MAAM,aAAa,OAAO,YAAY,WAAW,UAAU,OAAO;gBAElE,IAAI,CAAC,cAAc,WAAW,IAAI,OAAO,IAAI;oBAC3C,QAAQ,IAAI,CAAC,oDAAoD;oBACjE;gBACF;gBAEA,IAAI,eAAe,QAAQ,CAAC,aAAa;oBACvC,kBAAkB,eAAe,MAAM;4EAAC,CAAA,IAAK,MAAM;;gBACrD,OAAO;oBACL,kBAAkB;2BAAI;wBAAgB;qBAAW;gBACnD;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC,OAAO;YACxD;QACF;2DAAG;QAAC;KAAe;IAEnB,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,CAAC;YACxC,kBAAkB,mBAAmB,WAAW,OAAO;QACzD;4DAAG;QAAC;KAAe;IAMnB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,OAAO;YAC1C,IAAI;gBACF,+EAA+E;gBAC/E,MAAM,cAAc,mBAAoB,CAAA,oBAAA,8BAAA,QAAS,YAAY,KAAI,KAAM;gBACvE,MAAM,WAAW,OAAO,SAAS,IAAI,AAAC,QAA0B,OAAnB,OAAO,WAAW;gBAE/D,IAAI,WAAW;gBACf,IAAI,aAAa;oBACf,WAAW,AAAC,GAAoB,OAAlB,aAAY,QAAmB,OAAb,UAAS,MAAuB,OAAnB,OAAO,WAAW;gBACjE,OAAO;oBACL,WAAW,AAAC,GAAe,OAAb,UAAS,MAAuB,OAAnB,OAAO,WAAW;gBAC/C;gBAEA,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;gBAEpC,sCAAsC;gBACtC,MAAM,mBAAmB,WAAW,CAAC,OAAO,EAAE;YAChD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qBAAqB;YACrC;QACF;wDAAG;QAAC;QAAkB,oBAAA,8BAAA,QAAS,YAAY;QAAE;KAAmB;IAEhE,MAAM,qBAAqB;QACzB,IAAI,iBAAiB;YACnB,IAAI;gBACF,gDAAgD;gBAChD,MAAM,mBAAmB,QACtB,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,EACrB,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,GAAG,CAAC,EAAE;gBAEzF,IAAI,kBAAkB;oBACpB,uDAAuD;oBACvD,MAAM,qBAAqB,WAAW,CAAC;wBACrC,IAAI,iBAAiB,EAAE;wBACvB,SAAS;oBACX;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qBAAqB;YACrC;QACF;IACF;IAEA,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC;YACpC,mBAAmB,OAAO,EAAE;YAC5B,eAAe,OAAO,WAAW;QACnC;wDAAG,EAAE;IAEL,MAAM,iBAAiB;QACrB,IAAI,mBAAmB,YAAY,IAAI,IAAI;YACzC,IAAI;gBACF,kFAAkF;gBAClF,MAAM,EAAE,UAAU,iBAAiB,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,oCAAiC,AAAD,EAAE;gBAErG,gEAAgE;gBAChE,MAAM,gBAAgB,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACjD,MAAM,eAAe,CAAA,0BAAA,oCAAA,cAAe,IAAI,KAAI,EAAE;gBAC9C,MAAM,mBAAmB,0BAAA,oCAAA,cAAe,QAAQ;gBAEhD,iEAAiE;gBACjE,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD,EAAE,cAAc,qBAAqB,EAAE;gBAEvE,yEAAyE;gBACzE,MAAM,gBAAgB,eAAe,YAAY,MAAM,GAAG,IAAI,WAAW,CAAC,EAAE,GAAG;gBAE/E,MAAM,qBAAqB,WAAW,CAAC;oBACrC,IAAI;oBACJ,aAAa;oBACb,MAAM;oBACN,UAAU,iBAAiB;gBAC7B;gBAEA,mBAAmB;gBACnB,eAAe;YACjB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,mCAAmC;gBACnC,MAAM;YACR;QACF;IACF;IAEA,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE;YACnC,mBAAmB;YACnB,eAAe;QACjB;wDAAG,EAAE;IAEL,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gEAAE;YAC1C,qBAAqB,CAAC;YACtB,mBAAmB,IAAI;QACzB;+DAAG;QAAC;KAAkB;IAEtB,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,CAAC;YACtC,MAAM,cAAc,IAAI,IAAI;YAC5B,IAAI,YAAY,GAAG,CAAC,WAAW;gBAC7B,YAAY,MAAM,CAAC;YACrB,OAAO;gBACL,YAAY,GAAG,CAAC;YAClB;YACA,mBAAmB;QACrB;0DAAG;QAAC;KAAgB;IAEpB,MAAM,4BAA4B;QAChC,IAAI,gBAAgB,IAAI,KAAK,GAAG;QAEhC,IAAI;YACF,MAAM,qBAAqB,QACxB,MAAM,CAAC,CAAA,IAAK,gBAAgB,GAAG,CAAC,EAAE,EAAE,GACpC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,WAAW,GAAG,EAAE,WAAW;YAE/C,MAAM,cAAc,mBAAoB,CAAA,oBAAA,8BAAA,QAAS,YAAY,KAAI,KAAM;YAEvE,IAAI,WAAW,cAAc,AAAC,GAAc,OAAZ,aAAY,UAAQ;YAEpD,mBAAmB,OAAO,CAAC,CAAC,QAAQ;gBAClC,MAAM,WAAW,OAAO,SAAS,IAAI,AAAC,QAA0B,OAAnB,OAAO,WAAW;gBAC/D,YAAY,AAAC,GAAgB,OAAd,QAAQ,GAAE,MAAiB,OAAb,UAAS,MAAuB,OAAnB,OAAO,WAAW;gBAC5D,IAAI,QAAQ,mBAAmB,MAAM,GAAG,GAAG;oBACzC,YAAY;gBACd;YACF;YAEA,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YAEpC,oDAAoD;YACpD,KAAK,MAAM,YAAY,gBAAiB;gBACtC,MAAM,mBAAmB,WAAW,CAAC;YACvC;YAEA,iBAAiB;YACjB,mBAAmB,IAAI;YACvB,qBAAqB;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,4BAA4B;QAChC,MAAM,gBAAgB,QACnB,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,OAAO,EACtB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,WAAW,GAAG,EAAE,WAAW;QAE/C,IAAI,cAAc,MAAM,KAAK,GAAG;YAC9B,MAAM;YACN;QACF;QAEA,MAAM,cAAc,mBAAoB,CAAA,oBAAA,8BAAA,QAAS,YAAY,KAAI,KAAM;QAEvE,IAAI,kBAAkB,AAAC,KAAsC,OAAlC,CAAA,oBAAA,8BAAA,QAAS,IAAI,KAAI,kBAAiB;QAE7D,IAAI,aAAa;YACf,mBAAmB,AAAC,iBAA4B,OAAZ,aAAY;QAClD;QAEA,mBAAoB;QAEpB,cAAc,OAAO,CAAC,CAAC,QAAQ;YAC7B,MAAM,WAAW,OAAO,SAAS,IAAI,AAAC,QAA0B,OAAnB,OAAO,WAAW;YAC/D,mBAAmB,AAAC,OAAoB,OAAd,QAAQ,GAAE,MAAa,OAAT,UAAS;YACjD,mBAAmB,AAAC,GAAqB,OAAnB,OAAO,WAAW,EAAC;YACzC,mBAAoB;QACtB;QAEA,2BAA2B;QAC3B,MAAM,OAAO,IAAI,KAAK;YAAC;SAAgB,EAAE;YAAE,MAAM;QAAgB;QACjE,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,AAAC,GAA6B,OAA3B,CAAA,oBAAA,8BAAA,QAAS,IAAI,KAAI,WAAU;QAC3C,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAIA,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,OAAO;YACvC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;YAEzB,IAAI,CAAC,QAAQ,OAAO,EAAE,KAAK,KAAK,EAAE,EAAE;gBAClC;YACF;YAEA,MAAM,gBAAgB,QAAQ,IAAI;4EAAC,CAAC,GAAG;oBACrC,qFAAqF;oBACrF,IAAI,EAAE,OAAO,KAAK,EAAE,OAAO,EAAE;wBAC3B,OAAO,EAAE,OAAO,GAAG,IAAI,CAAC;oBAC1B;oBAEA,qDAAqD;oBACrD,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE;wBAC5B,OAAO,EAAE,WAAW,GAAG,EAAE,WAAW;oBACtC;oBAEA,4EAA4E;oBAC5E,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,EAAE;wBAC1B,MAAM,YAAY,EAAE,YAAY,GAAG,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK;wBACxE,MAAM,YAAY,EAAE,YAAY,GAAG,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK;wBACxE,OAAO,YAAY,WAAW,uCAAuC;oBACvE;oBAEA,OAAO;gBACT;;YAEA,MAAM,WAAW,cAAc,SAAS;uEAAC,CAAC,SAAW,OAAO,EAAE,KAAK,OAAO,EAAE;;YAC5E,MAAM,WAAW,cAAc,SAAS;uEAAC,CAAC,SAAW,OAAO,EAAE,KAAK,KAAK,EAAE;;YAE1E,MAAM,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,eAAe,UAAU;YAEpD,wCAAwC;YACxC,MAAM,UAAyE,EAAE;YAEjF,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;gBACxC,MAAM,SAAS,QAAQ,CAAC,EAAE;gBAC1B,MAAM,gBAAgB,IAAI;gBAC1B,MAAM,cAAc,AAAC,QAAqB,OAAd;gBAE5B,IAAI,OAAO,WAAW,KAAK,iBAAiB,OAAO,SAAS,KAAK,aAAa;oBAC5E,QAAQ,IAAI,CAAC;wBACX,IAAI,OAAO,EAAE;wBACb,aAAa;wBACb,WAAW;oBACb;gBACF;YACF;YAEA,wDAAwD;YACxD,IAAI,QAAQ,MAAM,GAAG,GAAG;gBACtB,IAAI;oBACF,MAAM,0BAA0B,WAAW,CAAC;gBAC9C,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC/C;YACF;QACF;qDAAG;QAAC;QAAS;KAA0B;IAEvC,IAAI,CAAC,iBAAiB;QACpB,qBACE,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;;;;;8BAGf,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAM/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASrD,6LAAC;oBAAI,WAAU;;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;kEAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;kEAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAGxB,6LAAC;gDAAG,WAAU;0DACX,CAAA,oBAAA,8BAAA,QAAS,IAAI,KAAI;;;;;;;;;;;;kDAGtB,6LAAC;wCAAI,WAAU;;4CAEZ,CAAC,eAAe,MAAM,GAAG,KAAK,cAAc,mBAC3C,6LAAC;gDAAI,WAAU;;oDACZ,eAAe,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,wBAC9B,6LAAC,oIAAA,CAAA,QAAK;4DAAe,SAAQ;4DAAU,WAAU;;8EAC/C,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC;oEAAK,WAAU;8EAAoB,QAAQ,OAAO,CAAC,KAAK;;;;;;8EACzD,6LAAC;oEAAK,WAAU;8EAAa,QAAQ,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC,GAAG;;;;;;8EAC/D,6LAAC,qIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,MAAK;oEACL,WAAU;oEACV,SAAS,CAAC;wEACR,EAAE,cAAc;wEAChB,EAAE,eAAe;wEACjB,oBAAoB;oEACtB;8EAEA,cAAA,6LAAC,+LAAA,CAAA,IAAC;wEAAC,WAAU;;;;;;;;;;;;2DAfL;;;;;oDAmBb,eAAe,MAAM,GAAG,mBACvB,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;;4DAAU;4DACzC,eAAe,MAAM,GAAG;;;;;;;oDAG7B,gCACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;;0EACjC,6LAAC,yMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,6LAAC;gEAAK,WAAU;0EAAoB;;;;;;0EACpC,6LAAC;gEAAK,WAAU;0EAAa,eAAe,KAAK,CAAC,GAAG;;;;;;0EACrD,6LAAC,qIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,kBAAkB;0EAEjC,cAAA,6LAAC,+LAAA,CAAA,IAAC;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0DAQvB,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,uBAAuB,CAAC;gDACvC,WAAU;;kEAEV,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;oDACzB,sBAAsB,UAAU;;;;;;;0DAG1C,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;;oDAClC,cAAc,gBAAgB,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,OAAO,EAAE,MAAM,GAAG,QAAQ,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,OAAO,EAAE,MAAM;oDAAC;oDAAI,cAAc,gBAAgB,MAAM,GAAG,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;0CAMtK,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;oCAEX,6BACC,6LAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAOrB,6LAAC,6IAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,6LAAC;4BAAI,WAAU;;gCACZ,QAAQ,MAAM,KAAK,kBAClB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;4CAAG,WAAU;sDAAsD;;;;;;sDACpE,6LAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;2CAEvD,gBAAgB,MAAM,KAAK,kBAC7B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAG,WAAU;sDAAsD;;;;;;sDACpE,6LAAC;4CAAE,WAAU;;gDAA0C;gDAAO;gDAAY;;;;;;;sDAC1E,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,SAAS,IAAM,eAAe;sDAC/B;;;;;;;;;;;yDAKH,6LAAC,8JAAA,CAAA,aAAU;oCACT,SAAS;oCACT,oBAAoB,8JAAA,CAAA,gBAAa;oCACjC,WAAW;8CAEX,cAAA,6LAAC,sKAAA,CAAA,kBAAe;wCACd,OAAO,gBAAgB,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;wCACpC,UAAU,sKAAA,CAAA,8BAA2B;kDAEpD,CAAC;4CACgB,MAAM,gBAAgB,gBAAgB,IAAI,CAAC,CAAC,GAAG;gDAC7C,qFAAqF;gDACrF,IAAI,EAAE,OAAO,KAAK,EAAE,OAAO,EAAE;oDAC3B,OAAO,EAAE,OAAO,GAAG,IAAI,CAAC;gDAC1B;gDAEA,qDAAqD;gDACrD,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE;oDAC5B,OAAO,EAAE,WAAW,GAAG,EAAE,WAAW;gDACtC;gDAEA,4EAA4E;gDAC5E,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,EAAE;oDAC1B,MAAM,YAAY,EAAE,YAAY,GAAG,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK;oDACxE,MAAM,YAAY,EAAE,YAAY,GAAG,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK;oDACxE,OAAO,YAAY,WAAW,uCAAuC;gDACvE;gDAEA,OAAO;4CACT;4CAEA,mDAAmD;4CACnD,MAAM,cAAc,YAAY,IAAI,GAAG,MAAM,GAAG;4CAChD,IAAI,iBAAiB;4CAErB,IAAI,CAAC,aAAa;gDAChB,2CAA2C;gDAC3C,MAAM,aAAa,CAAC,cAAc,CAAC,IAAI;gDACvC,MAAM,WAAW,aAAa;gDAC9B,iBAAiB,cAAc,KAAK,CAAC,YAAY;4CACnD;4CAEA,MAAM,gBAAgB,eAAe,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,OAAO;4CAC3D,MAAM,cAAc,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO;4CACxD,MAAM,mBAAmB,cAAc,MAAM,GAAG;4CAChD,MAAM,iBAAiB,YAAY,MAAM,GAAG;4CAE5C,4BAA4B;4CAC5B,MAAM,eAAe,cAAc,MAAM;4CACzC,MAAM,aAAa,KAAK,IAAI,CAAC,eAAe;4CAC5C,MAAM,iBAAiB,CAAC,eAAe,aAAa;4CAEpD,qBACE;;oDAEG,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC;4DAEC,QAAQ;4DACR,mBAAmB;4DACnB,iBAAiB;4DACjB,iBAAiB;4DACjB,aAAa;4DACb,iBAAiB;4DACjB,gBAAgB;4DAChB,cAAc;4DACd,YAAY;4DACZ,cAAc;4DACd,cAAc;4DACd,gBAAgB;4DAChB,iBAAiB;2DAbZ,OAAO,EAAE;;;;;oDAkBjB,oBAAoB,gCACnB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;;;;;;;;;;;0EAEjB,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;;sFACd,6LAAC,8NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;wEAAwB;;;;;;;;;;;;;;;;;;oDAQtD,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC;4DAEC,QAAQ;4DACR,mBAAmB;4DACnB,iBAAiB;4DACjB,iBAAiB;4DACjB,aAAa;4DACb,iBAAiB;4DACjB,gBAAgB;4DAChB,cAAc;4DACd,YAAY;4DACZ,cAAc;4DACd,cAAc;4DACd,gBAAgB;4DAChB,iBAAiB;2DAbZ,OAAO,EAAE;;;;;;;wCAkBxB,CAAC;;;;;;;;;;;gCAMN,CAAC;oCACA,MAAM,gBAAgB,gBAAgB,IAAI,CAAC,CAAC,GAAG;wCAC7C,IAAI,EAAE,OAAO,KAAK,EAAE,OAAO,EAAE;4CAC3B,OAAO,EAAE,OAAO,GAAG,IAAI,CAAC;wCAC1B;wCACA,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE;4CAC5B,OAAO,EAAE,WAAW,GAAG,EAAE,WAAW;wCACtC;wCACA,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,EAAE;4CAC1B,MAAM,YAAY,EAAE,YAAY,GAAG,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK;4CACxE,MAAM,YAAY,EAAE,YAAY,GAAG,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK;4CACxE,OAAO,YAAY;wCACrB;wCACA,OAAO;oCACT;oCAEA,MAAM,cAAc,YAAY,IAAI,GAAG,MAAM,GAAG;oCAChD,MAAM,eAAe,cAAc,MAAM;oCACzC,MAAM,aAAa,KAAK,IAAI,CAAC,eAAe;oCAC5C,MAAM,iBAAiB,CAAC,eAAe,aAAa;oCAEpD,IAAI,CAAC,gBAAgB,OAAO;oCAE5B,qBACE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;oDAAwB;oDAC7B;oDAAa;oDAAgB;oDAAY;oDAAI;;;;;;;0DAEvD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,GAAG,cAAc;wDACxD,UAAU,gBAAgB;kEAC3B;;;;;;kEAGD,6LAAC;wDAAI,WAAU;kEACZ,MAAM,IAAI,CAAC;4DAAE,QAAQ,KAAK,GAAG,CAAC,GAAG;wDAAY,GAAG,CAAC,GAAG;4DACnD,IAAI;4DACJ,IAAI,cAAc,GAAG;gEACnB,UAAU,IAAI;4DAChB,OAAO,IAAI,eAAe,GAAG;gEAC3B,UAAU,IAAI;4DAChB,OAAO,IAAI,eAAe,aAAa,GAAG;gEACxC,UAAU,aAAa,IAAI;4DAC7B,OAAO;gEACL,UAAU,cAAc,IAAI;4DAC9B;4DAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;gEAEL,SAAS,gBAAgB,UAAU,YAAY;gEAC/C,MAAK;gEACL,SAAS,IAAM,eAAe;gEAC9B,WAAU;0EAET;+DANI;;;;;wDASX;;;;;;kEAEF,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,YAAY,cAAc;wDACjE,UAAU,gBAAgB;kEAC3B;;;;;;;;;;;;;;;;;;gCAMT,CAAC;;;;;;;;;;;;kCAKL,6LAAC,6JAAA,CAAA,WAAQ;wBAAC,UACR,qCACE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,8IAAA,CAAA,yBAAsB;;;;;;;;;;;;;;qCAGzB;kCAEJ,cAAA,6LAAC;4BACC,MAAM;4BACN,cAAc,0BAA0B,CAAC,KAAO,CAAC;4BACjD,iBAAiB;;;;;;;;;;;kCAKrB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS,oBAAoB,YAAY;wDACzC,MAAK;wDACL,SAAS;wDACT,WAAW,AAAC,iDAAsN,OAAtK,oBAAoB,uDAAuD;;0EAEvI,6LAAC,yNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;0EACxB,6LAAC;gEAAK,WAAU;0EAAuB,oBAAoB,gBAAgB;;;;;;;;;;;;;;;;;8DAI/E,6LAAC;oDAAI,WAAU;;wDACZ,qBAAqB,gBAAgB,IAAI,GAAG,mBAC3C,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS;4DACT,WAAU;;8EAEV,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC;oEAAK,WAAU;;wEAAsB;wEAAU,gBAAgB,IAAI;wEAAC;;;;;;;;;;;;;sEAIzE,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS;4DACT,WAAU;;8EAEV,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6LAAC;oEAAK,WAAU;8EAAsB;;;;;;;;;;;;sEAGxC,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS;4DACT,WAAU;;8EAEV,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC;oEAAK,WAAU;8EAAsB;;;;;;;;;;;;;;;;;;;;;;;;sDAM5C,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,8IAAA,CAAA,UAAiB;gEAChB,OAAO;gEACP,UAAU;gEACV,WAAW,CAAC;oEACV,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;wEACpC,EAAE,cAAc;wEAChB;oEACF;gEACF;gEACA,aAAY;gEACZ,WAAU;gEACV,aAAa;oEACX,UAAU,eAAe,EAAE;oEAC3B,SAAS,iBAAiB,EAAE;gEAC9B;gEACA,qBAAqB;gEACrB,cAAc;oEACZ,WAAW;oEACX,mBAAmB;oEACnB,YAAY;gEACd;;;;;;;;;;;sEAMJ,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS;oEACT,WAAW,AAAC,sEAA0L,OAArH,oBAAoB,6CAA6C;;sFAElJ,6LAAC,yNAAA,CAAA,eAAY;4EAAC,WAAU;;;;;;sFACxB,6LAAC;4EAAK,WAAU;sFAAU;;;;;;;;;;;;8EAG5B,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS;oEACT,WAAU;;sFAEV,6LAAC,6MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;sFACpB,6LAAC;4EAAK,WAAU;sFAAU;;;;;;;;;;;;8EAG5B,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS;oEACT,WAAU;;sFAEV,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,6LAAC;4EAAK,WAAU;sFAAU;;;;;;;;;;;;8EAG5B,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAS;oEACT,UAAU,CAAC,cAAc,IAAI;oEAC7B,MAAK;oEACL,WAAU;;sFAEV,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,6LAAC;4EAAK,WAAU;sFAAU;;;;;;;;;;;;;;;;;;;;;;;;8DAMhC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,8IAAA,CAAA,UAAiB;gEAChB,OAAO;gEACP,UAAU;gEACV,WAAW,CAAC;oEACV,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;wEACpC,EAAE,cAAc;wEAChB;oEACF;gEACF;gEACA,aAAY;gEACZ,WAAU;gEACV,aAAa;oEACX,UAAU,eAAe,EAAE;oEAC3B,SAAS,iBAAiB,EAAE;gEAC9B;gEACA,qBAAqB;gEACrB,cAAc;oEACZ,WAAW;oEACX,mBAAmB;oEACnB,YAAY;gEACd;;;;;;;;;;;sEAMJ,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS;oEACT,WAAW,AAAC,iDAAqK,OAArH,oBAAoB,6CAA6C;8EAE7H,cAAA,6LAAC,yNAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;;;;;;8EAG1B,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS;oEACT,WAAU;8EAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;8EAGtB,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS;oEACT,WAAU;8EAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAGlB,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAS;oEACT,UAAU,CAAC,cAAc,IAAI;oEAC7B,MAAK;oEACL,WAAU;;sFAEV,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAS3C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,8IAAA,CAAA,UAAiB;gEAChB,OAAO;gEACP,UAAU;gEACV,WAAW,CAAC;oEACV,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;wEACpC,EAAE,cAAc;wEAChB;oEACF;gEACF;gEACA,aAAY;gEACZ,WAAU;gEACV,aAAa;oEACX,UAAU,eAAe,EAAE;oEAC3B,SAAS,iBAAiB,EAAE;gEAC9B;gEACA,qBAAqB;gEACrB,cAAc;oEACZ,WAAW;oEACX,mBAAmB;oEACnB,YAAY;gEACd;;;;;;4DAKC,kCACC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;;;;;0FACf,6LAAC;gFAAK,WAAU;0FAAoC;;;;;;;;;;;;kFAItD,6LAAC;;0FACC,6LAAC;gFAAM,WAAU;0FAA+C;;;;;;0FAGhE,6LAAC,oIAAA,CAAA,QAAK;gFACJ,MAAK;gFACL,aAAY;gFACZ,OAAO;gFACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gFACjD,WAAU;;;;;;;;;;;;kFAKd,6LAAC;;0FACC,6LAAC;gFAAM,WAAU;0FAA+C;;;;;;4EAG/D,iCACC,6LAAC,yIAAA,CAAA,eAAY;gFACX,UAAU;gFACV,kBAAkB;gFAClB,aAAa,eAAe,EAAE;gFAC9B,aAAY;gFACZ,SAAS;;;;;;;;;;;;kFAMf,6LAAC;;0FACC,6LAAC;gFAAM,WAAU;0FAA+C;;;;;;4EAG/D,iCACC,6LAAC,6IAAA,CAAA,mBAAgB;gFACf,UAAU;gFACV,kBAAkB;gFAClB,aAAa,iBAAiB,EAAE;gFAChC,aAAY;;;;;;;;;;;;;;;;;;;;;;;;kEASxB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAS;gEACT,iBAAiB;gEACjB,WAAU;;;;;;;;;;;;oDAKb,wBACC,6LAAC,yIAAA,CAAA,qBAAkB;wDACjB,MAAK;wDACL,SAAS,OAAO,eAAe;wDAC/B,KAAK,OAAO,uBAAuB;wDACnC,WAAW,KAAwC;;;;;;kEAKvD,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS;wDACT,UAAU,CAAC,cAAc,IAAI,MAAO,UAAU,CAAC,OAAO,iBAAiB;wDACvE,MAAK;wDACL,WAAU;;0EAEV,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;0DAMrC,6LAAC;gDAAI,WAAU;0DAGb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAS,oBAAoB,YAAY;4DACzC,MAAK;4DACL,SAAS;4DACT,WAAW,AAAC,sDAIX,OAHC,oBACI,0GACA;;8EAGN,6LAAC,yNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;wDAI1C,qBAAqB,gBAAgB,IAAI,GAAG,mBAC3C,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS;4DACT,WAAU;;8EAEV,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;gEACvB,gBAAgB,IAAI;gEAAC;;;;;;;sEAInC,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS;4DACT,WAAU;;8EAEV,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAIvC,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS;4DACT,WAAU;;8EAEV,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;wDAKlC,CAAC,cAAc,IAAI,oBAClB,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,oBAAoB,CAAC;4DACpC,WAAU;sEAET,iCACC;;kFACE,6LAAC,mNAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;oEAAiB;;6FAIxC;;kFACE,6LAAC,uNAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAgB7D,uBAAuB,iCACtB,6LAAC,uJAAA,CAAA,yBAAsB;gBACrB,WAAW;gBACX,gBAAgB;gBAChB,iBAAiB;gBACjB,kBAAkB;gBAClB,kBAAkB;gBAClB,WAAU;;;;;;;;;;;;AAKpB;IA1tCgB;;QAiCgC,+HAAA,CAAA,cAAW;QAChC,+HAAA,CAAA,gBAAa;QAEtB,8JAAA,CAAA,aAAU;QAOK,iIAAA,CAAA,aAAU;QACf,kIAAA,CAAA,aAAU;QACD,kIAAA,CAAA,iBAAc;QACZ,kIAAA,CAAA,mBAAgB;QACxB,iIAAA,CAAA,kBAAe;QACf,iIAAA,CAAA,kBAAe;QACV,iIAAA,CAAA,uBAAoB;QAC3B,iIAAA,CAAA,sBAAmB;;;MAlDhC;uCA4tCD", "debugId": null}}]}
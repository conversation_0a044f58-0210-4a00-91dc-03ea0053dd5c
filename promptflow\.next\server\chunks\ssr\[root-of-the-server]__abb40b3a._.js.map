{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 99, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/supabase-browser.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Single Supabase client instance to prevent multiple GoTrueClient warnings\nexport const supabaseBrowser = createBrowserClient(supabaseUrl, supabaseAnonKey, {\n  auth: {\n    persistSession: true,\n    autoRefreshToken: true,\n    detectSessionInUrl: true,\n    flowType: 'pkce',\n    debug: process.env.NODE_ENV === 'development',\n    storageKey: 'sb-iqehopwgrczylqliajww-auth-token',\n    storage: typeof window !== 'undefined' ? window.localStorage : undefined,\n  },\n  db: {\n    schema: 'public',\n  },\n  realtime: {\n    params: {\n      eventsPerSecond: 10, // Rate limit realtime events\n    },\n  },\n  global: {\n    headers: {\n      'X-Client-Info': 'promptflow-web',\n      'X-Client-Version': '1.0.0',\n    }\n  }\n})\n\n// Session debug için\nif (typeof window !== 'undefined') {\n  supabaseBrowser.auth.onAuthStateChange((event, session) => {\n    console.log(`🔐 [SUPABASE_BROWSER] Auth state change: ${event}`, {\n      hasSession: !!session,\n      userId: session?.user?.id,\n      email: session?.user?.email,\n      expiresAt: session?.expires_at ? new Date(session.expires_at * 1000).toISOString() : null\n    })\n    \n    // Manually set cookies for server-side access\n    if (session) {\n      const maxAge = Math.round((session.expires_at! * 1000 - Date.now()) / 1000)\n      document.cookie = `sb-iqehopwgrczylqliajww-auth-token=${JSON.stringify(session)}; path=/; max-age=${maxAge}; SameSite=Lax; secure=${location.protocol === 'https:'}`\n      console.log(`🍪 [SUPABASE_BROWSER] Set auth cookie with maxAge: ${maxAge}s`)\n    } else {\n      document.cookie = 'sb-iqehopwgrczylqliajww-auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'\n      console.log(`🍪 [SUPABASE_BROWSER] Cleared auth cookie`)\n    }\n  })\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,kBAAkB,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,iBAAiB;IAC/E,MAAM;QACJ,gBAAgB;QAChB,kBAAkB;QAClB,oBAAoB;QACpB,UAAU;QACV,OAAO,oDAAyB;QAChC,YAAY;QACZ,SAAS,sCAAgC,0BAAsB;IACjE;IACA,IAAI;QACF,QAAQ;IACV;IACA,UAAU;QACR,QAAQ;YACN,iBAAiB;QACnB;IACF;IACA,QAAQ;QACN,SAAS;YACP,iBAAiB;YACjB,oBAAoB;QACtB;IACF;AACF;AAEA,qBAAqB;AACrB", "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/hooks/use-auth.ts"], "sourcesContent": ["'use client'\n\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\nimport { User } from '@supabase/supabase-js'\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { toast } from 'sonner'\n\n// Auth error handling\nfunction handleAuthError(error: Error | unknown, queryClient: ReturnType<typeof useQueryClient>) {\n  const errorMessage = error instanceof Error ? error.message : String(error)\n  \n  if (errorMessage.includes('Invalid Refresh Token') || \n      errorMessage.includes('Refresh Token Not Found') ||\n      errorMessage.includes('refresh_token_not_found')) {\n    // Refresh token hatası durumunda oturumu temizle\n    console.warn('Refresh token hatası, oturum temizleniyor:', errorMessage)\n    supabase.auth.signOut({ scope: 'local' })\n    queryClient.clear()\n    return true\n  }\n  return false\n}\n\n// Auth state listener with enhanced logout handling\nexport function useAuthStateListener() {\n  const queryClient = useQueryClient()\n  const router = useRouter()\n\n  useEffect(() => {\n    console.log(`🎧 [AUTH_LISTENER] Setting up auth state listener`)\n\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      (event, session) => {\n        console.log(`🔄 [AUTH_LISTENER] Auth state change: ${event}`, {\n          userId: session?.user?.id,\n          email: session?.user?.email,\n          hasSession: !!session\n        })\n\n        if (event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED') {\n          console.log(`🔄 [AUTH_LISTENER] Invalidating queries for event: ${event}`)\n          queryClient.invalidateQueries({ queryKey: ['user'] })\n          queryClient.invalidateQueries({ queryKey: ['session'] })\n        }\n\n        if (event === 'SIGNED_OUT') {\n          console.log(`🚪 [AUTH_LISTENER] User signed out - clearing cache and redirecting`)\n          // Clear all cache and redirect to login\n          queryClient.clear()\n\n          // Clear any persisted state\n          localStorage.removeItem('promptflow-app-store')\n\n          // Force redirect to auth page\n          setTimeout(() => {\n            console.log(`🚪 [AUTH_LISTENER] Redirecting to /auth`)\n            router.push('/auth')\n            router.refresh()\n          }, 100)\n        }\n\n        if (event === 'SIGNED_IN') {\n          console.log(`🔑 [AUTH_LISTENER] User signed in - refreshing queries and redirecting`)\n          // Refresh queries when user signs in\n          queryClient.invalidateQueries({ queryKey: ['user'] })\n          queryClient.invalidateQueries({ queryKey: ['session'] })\n\n          // Redirect to dashboard after successful login\n          setTimeout(() => {\n            const currentPath = window.location.pathname\n            if (currentPath === '/auth' || currentPath === '/') {\n              console.log(`🚀 [AUTH_LISTENER] Redirecting from ${currentPath} to /dashboard`)\n              router.push('/dashboard')\n            }\n          }, 100)\n        }\n      }\n    )\n\n    return () => {\n      console.log(`🎧 [AUTH_LISTENER] Cleaning up auth state listener`)\n      subscription.unsubscribe()\n    }\n  }, [queryClient, router])\n}\n\n// Kullanıcı bilgilerini getir\nexport function useUser() {\n  const queryClient = useQueryClient()\n\n  return useQuery({\n    queryKey: ['user'],\n    queryFn: async (): Promise<User | null> => {\n      console.log(`🔐 [USE_USER] Getting user...`)\n      try {\n        // Önce session'ı kontrol et\n        const { data: { session }, error: sessionError } = await supabase.auth.getSession()\n\n        if (sessionError) {\n          console.error(`❌ [USE_USER] Session error:`, sessionError)\n          return null\n        }\n\n        if (!session) {\n          console.log(`🔐 [USE_USER] No session found`)\n          return null\n        }\n\n        console.log(`🔐 [USE_USER] Session found, getting user...`)\n        const { data: { user }, error } = await supabase.auth.getUser()\n\n        if (error) {\n          console.error(`❌ [USE_USER] Error getting user:`, error)\n          // Auth session missing error'ını handle et\n          if (error.message.includes('Auth session missing')) {\n            console.log(`🔄 [USE_USER] Auth session missing, returning null`)\n            return null\n          }\n          // Refresh token hatası kontrolü\n          if (handleAuthError(error, queryClient)) {\n            console.log(`🔄 [USE_USER] Handled auth error, returning null`)\n            return null\n          }\n          throw new Error(error.message)\n        }\n\n        console.log(`✅ [USE_USER] User retrieved:`, user?.email || 'null')\n        return user\n      } catch (error: unknown) {\n        console.error(`💥 [USE_USER] Exception:`, error)\n        const errorMessage = error instanceof Error ? error.message : String(error)\n\n        // Auth session missing error'ını handle et\n        if (errorMessage.includes('Auth session missing')) {\n          console.log(`🔄 [USE_USER] Auth session missing exception, returning null`)\n          return null\n        }\n\n        // Refresh token hatası kontrolü\n        if (handleAuthError(error, queryClient)) {\n          console.log(`🔄 [USE_USER] Handled auth error exception, returning null`)\n          return null\n        }\n        throw error\n      }\n    },\n    staleTime: 5 * 60 * 1000, // 5 dakika\n    retry: (failureCount, error: unknown) => {\n      // Refresh token hatalarında ve session missing'de retry yapma\n      const errorMessage = error instanceof Error ? error.message : String(error)\n      console.log(`🔄 [USE_USER] Retry attempt ${failureCount}, error: ${errorMessage}`)\n      if (errorMessage.includes('Invalid Refresh Token') ||\n          errorMessage.includes('Refresh Token Not Found') ||\n          errorMessage.includes('Auth session missing')) {\n        console.log(`🚫 [USE_USER] Not retrying auth error: ${errorMessage}`)\n        return false\n      }\n      return failureCount < 3\n    },\n  })\n}\n\n// Oturum durumunu kontrol et\nexport function useSession() {\n  const queryClient = useQueryClient()\n  \n  return useQuery({\n    queryKey: ['session'],\n    queryFn: async () => {\n      try {\n        const { data: { session }, error } = await supabase.auth.getSession()\n        \n        if (error) {\n          // Refresh token hatası kontrolü\n          if (handleAuthError(error, queryClient)) {\n            return null\n          }\n          throw new Error(error.message)\n        }\n\n        return session\n      } catch (error: unknown) {\n        // Refresh token hatası kontrolü\n        if (handleAuthError(error, queryClient)) {\n          return null\n        }\n        throw error\n      }\n    },\n    staleTime: 5 * 60 * 1000, // 5 dakika\n    retry: (failureCount, error: unknown) => {\n      // Refresh token hatalarında retry yapma\n      const errorMessage = error instanceof Error ? error.message : String(error)\n      if (errorMessage.includes('Invalid Refresh Token') || \n          errorMessage.includes('Refresh Token Not Found')) {\n        return false\n      }\n      return failureCount < 3\n    },\n  })\n}\n\n// Email ile giriş yap\nexport function useSignInWithEmail() {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async ({ email, password }: { email: string; password: string }) => {\n      console.log(`🔑 [SIGN_IN] Attempting login for: ${email}`)\n\n      const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      })\n\n      if (error) {\n        console.error(`❌ [SIGN_IN] Login failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [SIGN_IN] Login successful:`, {\n        userId: data.user?.id,\n        email: data.user?.email,\n        hasSession: !!data.session\n      })\n\n      return data\n    },\n    onSuccess: () => {\n      console.log(`🎉 [SIGN_IN] onSuccess triggered, invalidating queries`)\n      queryClient.invalidateQueries({ queryKey: ['user'] })\n      queryClient.invalidateQueries({ queryKey: ['session'] })\n    },\n    onError: (error) => {\n      console.error(`💥 [SIGN_IN] onError triggered:`, error)\n    }\n  })\n}\n\n// Email ile kayıt ol\nexport function useSignUpWithEmail() {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async ({ email, password }: { email: string; password: string }) => {\n      const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n      })\n\n      if (error) {\n        throw new Error(error.message)\n      }\n\n      return data\n    },\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: ['user'] })\n      queryClient.invalidateQueries({ queryKey: ['session'] })\n    },\n  })\n}\n\n// Enhanced logout with proper redirect\nexport function useSignOut() {\n  const queryClient = useQueryClient()\n  const router = useRouter()\n\n  return useMutation({\n    mutationFn: async () => {\n      console.log('Starting logout process...')\n\n      // Sign out from Supabase\n      const { error } = await supabase.auth.signOut({ scope: 'global' })\n\n      if (error) {\n        console.error('Logout error:', error)\n        throw new Error(error.message)\n      }\n\n      console.log('Logout successful')\n    },\n    onSuccess: () => {\n      console.log('Logout onSuccess triggered')\n\n      // Show success toast\n      toast.success('Başarıyla çıkış yapıldı', {\n        description: 'Giriş sayfasına yönlendiriliyorsunuz...'\n      })\n\n      // Clear all cache\n      queryClient.clear()\n\n      // Clear persisted state\n      localStorage.removeItem('promptflow-app-store')\n      sessionStorage.clear()\n\n      // Force redirect to auth page\n      setTimeout(() => {\n        console.log('Redirecting to auth page...')\n        router.push('/auth')\n        router.refresh()\n        window.location.href = '/auth' // Fallback for complete page reload\n      }, 1000) // Increased delay to show toast\n    },\n    onError: (error) => {\n      console.error('Logout failed:', error)\n\n      // Show error toast\n      toast.error('Çıkış yapılırken hata oluştu', {\n        description: 'Yine de oturum temizleniyor...'\n      })\n\n      // Even if logout fails, clear local state and redirect\n      queryClient.clear()\n      localStorage.removeItem('promptflow-app-store')\n      sessionStorage.clear()\n\n      setTimeout(() => {\n        router.push('/auth')\n        router.refresh()\n      }, 1000)\n    }\n  })\n}"], "names": [], "mappings": ";;;;;;;;AAEA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAPA;;;;;;AASA,sBAAsB;AACtB,SAAS,gBAAgB,KAAsB,EAAE,WAA8C;IAC7F,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;IAErE,IAAI,aAAa,QAAQ,CAAC,4BACtB,aAAa,QAAQ,CAAC,8BACtB,aAAa,QAAQ,CAAC,4BAA4B;QACpD,iDAAiD;QACjD,QAAQ,IAAI,CAAC,8CAA8C;QAC3D,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO;QAAQ;QACvC,YAAY,KAAK;QACjB,OAAO;IACT;IACA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,CAAC,iDAAiD,CAAC;QAE/D,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,iBAAiB,CAChE,CAAC,OAAO;YACN,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,OAAO,EAAE;gBAC5D,QAAQ,SAAS,MAAM;gBACvB,OAAO,SAAS,MAAM;gBACtB,YAAY,CAAC,CAAC;YAChB;YAEA,IAAI,UAAU,gBAAgB,UAAU,mBAAmB;gBACzD,QAAQ,GAAG,CAAC,CAAC,mDAAmD,EAAE,OAAO;gBACzE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAO;gBAAC;gBACnD,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAU;gBAAC;YACxD;YAEA,IAAI,UAAU,cAAc;gBAC1B,QAAQ,GAAG,CAAC,CAAC,mEAAmE,CAAC;gBACjF,wCAAwC;gBACxC,YAAY,KAAK;gBAEjB,4BAA4B;gBAC5B,aAAa,UAAU,CAAC;gBAExB,8BAA8B;gBAC9B,WAAW;oBACT,QAAQ,GAAG,CAAC,CAAC,uCAAuC,CAAC;oBACrD,OAAO,IAAI,CAAC;oBACZ,OAAO,OAAO;gBAChB,GAAG;YACL;YAEA,IAAI,UAAU,aAAa;gBACzB,QAAQ,GAAG,CAAC,CAAC,sEAAsE,CAAC;gBACpF,qCAAqC;gBACrC,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAO;gBAAC;gBACnD,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAU;gBAAC;gBAEtD,+CAA+C;gBAC/C,WAAW;oBACT,MAAM,cAAc,OAAO,QAAQ,CAAC,QAAQ;oBAC5C,IAAI,gBAAgB,WAAW,gBAAgB,KAAK;wBAClD,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,YAAY,cAAc,CAAC;wBAC9E,OAAO,IAAI,CAAC;oBACd;gBACF,GAAG;YACL;QACF;QAGF,OAAO;YACL,QAAQ,GAAG,CAAC,CAAC,kDAAkD,CAAC;YAChE,aAAa,WAAW;QAC1B;IACF,GAAG;QAAC;QAAa;KAAO;AAC1B;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAO;QAClB,SAAS;YACP,QAAQ,GAAG,CAAC,CAAC,6BAA6B,CAAC;YAC3C,IAAI;gBACF,4BAA4B;gBAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,UAAU;gBAEjF,IAAI,cAAc;oBAChB,QAAQ,KAAK,CAAC,CAAC,2BAA2B,CAAC,EAAE;oBAC7C,OAAO;gBACT;gBAEA,IAAI,CAAC,SAAS;oBACZ,QAAQ,GAAG,CAAC,CAAC,8BAA8B,CAAC;oBAC5C,OAAO;gBACT;gBAEA,QAAQ,GAAG,CAAC,CAAC,4CAA4C,CAAC;gBAC1D,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;gBAE7D,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,CAAC,gCAAgC,CAAC,EAAE;oBAClD,2CAA2C;oBAC3C,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,yBAAyB;wBAClD,QAAQ,GAAG,CAAC,CAAC,kDAAkD,CAAC;wBAChE,OAAO;oBACT;oBACA,gCAAgC;oBAChC,IAAI,gBAAgB,OAAO,cAAc;wBACvC,QAAQ,GAAG,CAAC,CAAC,gDAAgD,CAAC;wBAC9D,OAAO;oBACT;oBACA,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,QAAQ,GAAG,CAAC,CAAC,4BAA4B,CAAC,EAAE,MAAM,SAAS;gBAC3D,OAAO;YACT,EAAE,OAAO,OAAgB;gBACvB,QAAQ,KAAK,CAAC,CAAC,wBAAwB,CAAC,EAAE;gBAC1C,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;gBAErE,2CAA2C;gBAC3C,IAAI,aAAa,QAAQ,CAAC,yBAAyB;oBACjD,QAAQ,GAAG,CAAC,CAAC,4DAA4D,CAAC;oBAC1E,OAAO;gBACT;gBAEA,gCAAgC;gBAChC,IAAI,gBAAgB,OAAO,cAAc;oBACvC,QAAQ,GAAG,CAAC,CAAC,0DAA0D,CAAC;oBACxE,OAAO;gBACT;gBACA,MAAM;YACR;QACF;QACA,WAAW,IAAI,KAAK;QACpB,OAAO,CAAC,cAAc;YACpB,8DAA8D;YAC9D,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;YACrE,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,aAAa,SAAS,EAAE,cAAc;YACjF,IAAI,aAAa,QAAQ,CAAC,4BACtB,aAAa,QAAQ,CAAC,8BACtB,aAAa,QAAQ,CAAC,yBAAyB;gBACjD,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,cAAc;gBACpE,OAAO;YACT;YACA,OAAO,eAAe;QACxB;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAU;QACrB,SAAS;YACP,IAAI;gBACF,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,UAAU;gBAEnE,IAAI,OAAO;oBACT,gCAAgC;oBAChC,IAAI,gBAAgB,OAAO,cAAc;wBACvC,OAAO;oBACT;oBACA,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,OAAO;YACT,EAAE,OAAO,OAAgB;gBACvB,gCAAgC;gBAChC,IAAI,gBAAgB,OAAO,cAAc;oBACvC,OAAO;gBACT;gBACA,MAAM;YACR;QACF;QACA,WAAW,IAAI,KAAK;QACpB,OAAO,CAAC,cAAc;YACpB,wCAAwC;YACxC,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;YACrE,IAAI,aAAa,QAAQ,CAAC,4BACtB,aAAa,QAAQ,CAAC,4BAA4B;gBACpD,OAAO;YACT;YACA,OAAO,eAAe;QACxB;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAuC;YACzE,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,OAAO;YAEzD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;gBAC7D;gBACA;YACF;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,CAAC,yBAAyB,CAAC,EAAE;gBAC3C,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,QAAQ,GAAG,CAAC,CAAC,6BAA6B,CAAC,EAAE;gBAC3C,QAAQ,KAAK,IAAI,EAAE;gBACnB,OAAO,KAAK,IAAI,EAAE;gBAClB,YAAY,CAAC,CAAC,KAAK,OAAO;YAC5B;YAEA,OAAO;QACT;QACA,WAAW;YACT,QAAQ,GAAG,CAAC,CAAC,sDAAsD,CAAC;YACpE,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAO;YAAC;YACnD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAU;YAAC;QACxD;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,CAAC,+BAA+B,CAAC,EAAE;QACnD;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAuC;YACzE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;gBACjD;gBACA;YACF;YAEA,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,OAAO;QACT;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAO;YAAC;YACnD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAU;YAAC;QACxD;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY;YACV,QAAQ,GAAG,CAAC;YAEZ,yBAAyB;YACzB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;gBAAE,OAAO;YAAS;YAEhE,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,iBAAiB;gBAC/B,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,QAAQ,GAAG,CAAC;QACd;QACA,WAAW;YACT,QAAQ,GAAG,CAAC;YAEZ,qBAAqB;YACrB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,2BAA2B;gBACvC,aAAa;YACf;YAEA,kBAAkB;YAClB,YAAY,KAAK;YAEjB,wBAAwB;YACxB,aAAa,UAAU,CAAC;YACxB,eAAe,KAAK;YAEpB,8BAA8B;YAC9B,WAAW;gBACT,QAAQ,GAAG,CAAC;gBACZ,OAAO,IAAI,CAAC;gBACZ,OAAO,OAAO;gBACd,OAAO,QAAQ,CAAC,IAAI,GAAG,SAAQ,oCAAoC;YACrE,GAAG,OAAM,gCAAgC;QAC3C;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,kBAAkB;YAEhC,mBAAmB;YACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gCAAgC;gBAC1C,aAAa;YACf;YAEA,uDAAuD;YACvD,YAAY,KAAK;YACjB,aAAa,UAAU,CAAC;YACxB,eAAe,KAAK;YAEpB,WAAW;gBACT,OAAO,IAAI,CAAC;gBACZ,OAAO,OAAO;YAChB,GAAG;QACL;IACF;AACF", "debugId": null}}, {"offset": {"line": 606, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/auth-guard.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter, usePathname } from 'next/navigation'\nimport { useUser, useAuthStateListener } from '@/hooks/use-auth'\n\ninterface AuthGuardProps {\n  children: React.ReactNode\n}\n\nexport function AuthGuard({ children }: AuthGuardProps) {\n  const { data: user, isLoading, error } = useUser()\n  const router = useRouter()\n  const pathname = usePathname()\n\n  console.log(`🛡️ [AUTH_GUARD] Status: loading=${isLoading}, user=${user?.email || 'null'}, error=${error ? 'YES' : 'NO'}, pathname=${pathname}`)\n\n  // Auth state değişikliklerini dinle\n  useAuthStateListener()\n\n  // Authenticated user'ları dashboard'a yönlendir\n  useEffect(() => {\n    if (!isLoading && user && pathname === '/auth') {\n      console.log(`🚀 [AUTH_GUARD] Authenticated user on /auth, redirecting to dashboard`)\n      router.replace('/dashboard')\n    }\n  }, [user, isLoading, pathname, router])\n\n  // Loading durumu - middleware handles redirects, so we just show loading\n  if (isLoading) {\n    console.log(`⏳ [AUTH_GUARD] Showing loading state`)\n    return (\n      <div className=\"flex items-center justify-center min-h-screen bg-gray-50\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">PromptFlow yükleniyor...</p>\n        </div>\n      </div>\n    )\n  }\n\n  // Auth error durumu (refresh token hataları vs.)\n  if (error) {\n    const errorMessage = error instanceof Error ? error.message : String(error)\n    console.error(`❌ [AUTH_GUARD] Auth error:`, errorMessage)\n\n    if (errorMessage.includes('Invalid Refresh Token') ||\n        errorMessage.includes('Refresh Token Not Found')) {\n      console.log(`🔄 [AUTH_GUARD] Refresh token error - showing loading`)\n      return (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">Oturum yenileniyor...</p>\n          </div>\n        </div>\n      )\n    }\n  }\n\n  // If no user, let middleware handle redirect - just show loading\n  if (!user) {\n    console.log(`🚫 [AUTH_GUARD] No user - showing loading (middleware will handle redirect)`)\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Kimlik doğrulanıyor...</p>\n        </div>\n      </div>\n    )\n  }\n\n  // Kullanıcı giriş yapmışsa ana uygulamayı göster\n  console.log(`✅ [AUTH_GUARD] User authenticated - showing app`)\n  return <>{children}</>\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUO,SAAS,UAAU,EAAE,QAAQ,EAAkB;IACpD,MAAM,EAAE,MAAM,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAC/C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,UAAU,OAAO,EAAE,MAAM,SAAS,OAAO,QAAQ,EAAE,QAAQ,QAAQ,KAAK,WAAW,EAAE,UAAU;IAE/I,oCAAoC;IACpC,CAAA,GAAA,2HAAA,CAAA,uBAAoB,AAAD;IAEnB,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,QAAQ,aAAa,SAAS;YAC9C,QAAQ,GAAG,CAAC,CAAC,qEAAqE,CAAC;YACnF,OAAO,OAAO,CAAC;QACjB;IACF,GAAG;QAAC;QAAM;QAAW;QAAU;KAAO;IAEtC,yEAAyE;IACzE,IAAI,WAAW;QACb,QAAQ,GAAG,CAAC,CAAC,oCAAoC,CAAC;QAClD,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,iDAAiD;IACjD,IAAI,OAAO;QACT,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QACrE,QAAQ,KAAK,CAAC,CAAC,0BAA0B,CAAC,EAAE;QAE5C,IAAI,aAAa,QAAQ,CAAC,4BACtB,aAAa,QAAQ,CAAC,4BAA4B;YACpD,QAAQ,GAAG,CAAC,CAAC,qDAAqD,CAAC;YACnE,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;QAIrC;IACF;IAEA,iEAAiE;IACjE,IAAI,CAAC,MAAM;QACT,QAAQ,GAAG,CAAC,CAAC,2EAA2E,CAAC;QACzF,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,iDAAiD;IACjD,QAAQ,GAAG,CAAC,CAAC,+CAA+C,CAAC;IAC7D,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 756, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/plan-limits.ts"], "sourcesContent": ["// Plan Limitleri Kontrol Middleware ve Utility Fonksiyonları\n\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\n\nexport interface PlanLimits {\n  current_projects: number\n  current_prompts: number\n  max_projects: number\n  max_prompts_per_project: number\n  can_create_project: boolean\n  can_create_prompt: boolean\n}\n\nexport interface UserPlan {\n  plan_id: string\n  plan_name: string\n  display_name: string\n  max_projects: number\n  max_prompts_per_project: number\n  features: Record<string, boolean | string | number>\n  status: string\n  expires_at: string | null\n}\n\n// Plan limitlerini kontrol et\nexport async function checkUserLimits(): Promise<PlanLimits | null> {\n  try {\n    const { data: user } = await supabase.auth.getUser()\n    if (!user.user) {\n      throw new Error('Kullanıcı oturumu bulunamadı')\n    }\n\n    const { data, error } = await supabase.rpc('check_user_limits', {\n      user_uuid: user.user.id\n    })\n\n    if (error) {\n      console.error('Plan limitleri kontrol edilemedi:', error)\n      throw new Error(error.message)\n    }\n\n    return data?.[0] || null\n  } catch (error) {\n    console.error('Plan limitleri kontrol hatası:', error)\n    return null\n  }\n}\n\n// Kullanıcının aktif planını getir\nexport async function getUserActivePlan(): Promise<UserPlan | null> {\n  try {\n    const { data: user } = await supabase.auth.getUser()\n    if (!user.user) {\n      throw new Error('Kullanıcı oturumu bulunamadı')\n    }\n\n    const { data, error } = await supabase.rpc('get_user_active_plan', {\n      user_uuid: user.user.id\n    })\n\n    if (error) {\n      console.error('Aktif plan getirilemedi:', error)\n      throw new Error(error.message)\n    }\n\n    return data?.[0] || null\n  } catch (error) {\n    console.error('Aktif plan getirme hatası:', error)\n    return null\n  }\n}\n\n// Proje oluşturma limiti kontrol et\nexport async function canCreateProject(): Promise<{ allowed: boolean; reason?: string }> {\n  const limits = await checkUserLimits()\n  \n  if (!limits) {\n    return { allowed: false, reason: 'Plan bilgileri alınamadı' }\n  }\n\n  if (!limits.can_create_project) {\n    return { \n      allowed: false, \n      reason: `Proje limiti aşıldı (${limits.current_projects}/${limits.max_projects})` \n    }\n  }\n\n  return { allowed: true }\n}\n\n// Prompt oluşturma limiti kontrol et\nexport async function canCreatePrompt(projectId?: string): Promise<{ allowed: boolean; reason?: string }> {\n  const limits = await checkUserLimits()\n  \n  if (!limits) {\n    return { allowed: false, reason: 'Plan bilgileri alınamadı' }\n  }\n\n  if (!limits.can_create_prompt) {\n    return { \n      allowed: false, \n      reason: `Prompt limiti aşıldı (${limits.current_prompts}/${limits.max_prompts_per_project})` \n    }\n  }\n\n  // Eğer belirli bir proje için kontrol ediliyorsa, o projenin prompt sayısını kontrol et\n  if (projectId) {\n    try {\n      const { count, error } = await supabase\n        .from('prompts')\n        .select('*', { count: 'exact', head: true })\n        .eq('project_id', projectId)\n\n      if (error) {\n        console.error('Proje prompt sayısı kontrol edilemedi:', error)\n        return { allowed: false, reason: 'Proje bilgileri alınamadı' }\n      }\n\n      const currentPrompts = count || 0\n      if (limits.max_prompts_per_project !== -1 && currentPrompts >= limits.max_prompts_per_project) {\n        return { \n          allowed: false, \n          reason: `Bu proje için prompt limiti aşıldı (${currentPrompts}/${limits.max_prompts_per_project})` \n        }\n      }\n    } catch (error) {\n      console.error('Proje prompt sayısı kontrol hatası:', error)\n      return { allowed: false, reason: 'Proje bilgileri kontrol edilemedi' }\n    }\n  }\n\n  return { allowed: true }\n}\n\n// Plan özelliği kontrol et\nexport async function hasPlanFeature(featureName: string): Promise<boolean> {\n  const plan = await getUserActivePlan()\n  return plan?.features?.[featureName] === true\n}\n\n// Kullanım istatistiklerini güncelle\nexport async function updateUsageStats(): Promise<void> {\n  try {\n    const { data: user } = await supabase.auth.getUser()\n    if (!user.user) {\n      throw new Error('Kullanıcı oturumu bulunamadı')\n    }\n\n    const { error } = await supabase.rpc('update_usage_stats', {\n      user_uuid: user.user.id\n    })\n\n    if (error) {\n      console.error('Kullanım istatistikleri güncellenemedi:', error)\n      throw new Error(error.message)\n    }\n  } catch (error) {\n    console.error('Kullanım istatistikleri güncelleme hatası:', error)\n    throw error\n  }\n}\n\n// Plan upgrade önerisi\nexport function getPlanUpgradeSuggestion(limits: PlanLimits): {\n  shouldUpgrade: boolean\n  reason: string\n  suggestedPlan: string\n} {\n  // Proje limiti %80'e ulaştıysa\n  if (limits.max_projects !== -1 && limits.current_projects / limits.max_projects >= 0.8) {\n    return {\n      shouldUpgrade: true,\n      reason: 'Proje limitinizin %80\\'ine ulaştınız',\n      suggestedPlan: 'professional'\n    }\n  }\n\n  // Prompt limiti %80'e ulaştıysa\n  if (limits.max_prompts_per_project !== -1 && limits.current_prompts / limits.max_prompts_per_project >= 0.8) {\n    return {\n      shouldUpgrade: true,\n      reason: 'Prompt limitinizin %80\\'ine ulaştınız',\n      suggestedPlan: 'professional'\n    }\n  }\n\n  return {\n    shouldUpgrade: false,\n    reason: '',\n    suggestedPlan: ''\n  }\n}\n\n// Plan limiti hata mesajları\nexport const PLAN_LIMIT_MESSAGES = {\n  PROJECT_LIMIT_REACHED: 'Proje oluşturma limitinize ulaştınız. Daha fazla proje oluşturmak için planınızı yükseltin.',\n  PROMPT_LIMIT_REACHED: 'Prompt oluşturma limitinize ulaştınız. Daha fazla prompt oluşturmak için planınızı yükseltin.',\n  FEATURE_NOT_AVAILABLE: 'Bu özellik mevcut planınızda bulunmamaktadır. Planınızı yükseltin.',\n  PLAN_EXPIRED: 'Planınızın süresi dolmuştur. Lütfen planınızı yenileyin.',\n  PLAN_SUSPENDED: 'Hesabınız askıya alınmıştır. Lütfen destek ekibi ile iletişime geçin.'\n}\n\n// Plan durumu kontrol et\nexport async function checkPlanStatus(): Promise<{\n  isActive: boolean\n  status: string\n  message?: string\n}> {\n  const plan = await getUserActivePlan()\n  \n  if (!plan) {\n    return {\n      isActive: false,\n      status: 'no_plan',\n      message: 'Aktif plan bulunamadı'\n    }\n  }\n\n  if (plan.status !== 'active') {\n    return {\n      isActive: false,\n      status: plan.status,\n      message: PLAN_LIMIT_MESSAGES.PLAN_SUSPENDED\n    }\n  }\n\n  if (plan.expires_at && new Date(plan.expires_at) < new Date()) {\n    return {\n      isActive: false,\n      status: 'expired',\n      message: PLAN_LIMIT_MESSAGES.PLAN_EXPIRED\n    }\n  }\n\n  return {\n    isActive: true,\n    status: 'active'\n  }\n}\n"], "names": [], "mappings": "AAAA,6DAA6D;;;;;;;;;;;;AAE7D;;AAuBO,eAAe;IACpB,IAAI;QACF,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;QAClD,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,qBAAqB;YAC9D,WAAW,KAAK,IAAI,CAAC,EAAE;QACzB;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;QAEA,OAAO,MAAM,CAAC,EAAE,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;IACT;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;QAClD,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,wBAAwB;YACjE,WAAW,KAAK,IAAI,CAAC,EAAE;QACzB;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;QAEA,OAAO,MAAM,CAAC,EAAE,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF;AAGO,eAAe;IACpB,MAAM,SAAS,MAAM;IAErB,IAAI,CAAC,QAAQ;QACX,OAAO;YAAE,SAAS;YAAO,QAAQ;QAA2B;IAC9D;IAEA,IAAI,CAAC,OAAO,kBAAkB,EAAE;QAC9B,OAAO;YACL,SAAS;YACT,QAAQ,CAAC,qBAAqB,EAAE,OAAO,gBAAgB,CAAC,CAAC,EAAE,OAAO,YAAY,CAAC,CAAC,CAAC;QACnF;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,eAAe,gBAAgB,SAAkB;IACtD,MAAM,SAAS,MAAM;IAErB,IAAI,CAAC,QAAQ;QACX,OAAO;YAAE,SAAS;YAAO,QAAQ;QAA2B;IAC9D;IAEA,IAAI,CAAC,OAAO,iBAAiB,EAAE;QAC7B,OAAO;YACL,SAAS;YACT,QAAQ,CAAC,sBAAsB,EAAE,OAAO,eAAe,CAAC,CAAC,EAAE,OAAO,uBAAuB,CAAC,CAAC,CAAC;QAC9F;IACF;IAEA,wFAAwF;IACxF,IAAI,WAAW;QACb,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACpC,IAAI,CAAC,WACL,MAAM,CAAC,KAAK;gBAAE,OAAO;gBAAS,MAAM;YAAK,GACzC,EAAE,CAAC,cAAc;YAEpB,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,0CAA0C;gBACxD,OAAO;oBAAE,SAAS;oBAAO,QAAQ;gBAA4B;YAC/D;YAEA,MAAM,iBAAiB,SAAS;YAChC,IAAI,OAAO,uBAAuB,KAAK,CAAC,KAAK,kBAAkB,OAAO,uBAAuB,EAAE;gBAC7F,OAAO;oBACL,SAAS;oBACT,QAAQ,CAAC,oCAAoC,EAAE,eAAe,CAAC,EAAE,OAAO,uBAAuB,CAAC,CAAC,CAAC;gBACpG;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,OAAO;gBAAE,SAAS;gBAAO,QAAQ;YAAoC;QACvE;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,eAAe,eAAe,WAAmB;IACtD,MAAM,OAAO,MAAM;IACnB,OAAO,MAAM,UAAU,CAAC,YAAY,KAAK;AAC3C;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;QAClD,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,sBAAsB;YACzD,WAAW,KAAK,IAAI,CAAC,EAAE;QACzB;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2CAA2C;YACzD,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,MAAM;IACR;AACF;AAGO,SAAS,yBAAyB,MAAkB;IAKzD,+BAA+B;IAC/B,IAAI,OAAO,YAAY,KAAK,CAAC,KAAK,OAAO,gBAAgB,GAAG,OAAO,YAAY,IAAI,KAAK;QACtF,OAAO;YACL,eAAe;YACf,QAAQ;YACR,eAAe;QACjB;IACF;IAEA,gCAAgC;IAChC,IAAI,OAAO,uBAAuB,KAAK,CAAC,KAAK,OAAO,eAAe,GAAG,OAAO,uBAAuB,IAAI,KAAK;QAC3G,OAAO;YACL,eAAe;YACf,QAAQ;YACR,eAAe;QACjB;IACF;IAEA,OAAO;QACL,eAAe;QACf,QAAQ;QACR,eAAe;IACjB;AACF;AAGO,MAAM,sBAAsB;IACjC,uBAAuB;IACvB,sBAAsB;IACtB,uBAAuB;IACvB,cAAc;IACd,gBAAgB;AAClB;AAGO,eAAe;IAKpB,MAAM,OAAO,MAAM;IAEnB,IAAI,CAAC,MAAM;QACT,OAAO;YACL,UAAU;YACV,QAAQ;YACR,SAAS;QACX;IACF;IAEA,IAAI,KAAK,MAAM,KAAK,UAAU;QAC5B,OAAO;YACL,UAAU;YACV,QAAQ,KAAK,MAAM;YACnB,SAAS,oBAAoB,cAAc;QAC7C;IACF;IAEA,IAAI,KAAK,UAAU,IAAI,IAAI,KAAK,KAAK,UAAU,IAAI,IAAI,QAAQ;QAC7D,OAAO;YACL,UAAU;YACV,QAAQ;YACR,SAAS,oBAAoB,YAAY;QAC3C;IACF;IAEA,OAAO;QACL,UAAU;QACV,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 957, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/project-validation.ts"], "sourcesContent": ["/**\n * Proje adı validation ve güvenlik utilities\n * Güvenlik odaklı input validation ve sanitization\n */\n\n// Proje adı limitleri ve kuralları\nexport const PROJECT_NAME_RULES = {\n  minLength: 3,\n  maxLength: 50,\n  allowedCharsRegex: /^[a-zA-Z0-9\\s\\-_.]+$/,\n  debounceMs: 300,\n  rateLimit: {\n    maxRequests: 10,\n    windowMinutes: 1\n  }\n} as const;\n\n// Validation sonuç tipi\nexport interface ValidationResult {\n  isValid: boolean;\n  error?: string;\n  sanitizedValue?: string;\n}\n\n// Rate limiting için local storage key\nconst RATE_LIMIT_KEY = 'project_name_update_rate_limit';\n\n/**\n * Proje adını sanitize eder\n */\nexport function sanitizeProjectName(name: string): string {\n  if (!name) return '';\n  \n  // Trim ve normalize\n  let sanitized = name.trim();\n  \n  // Çoklu boşlukları tek boşluğa çevir\n  sanitized = sanitized.replace(/\\s+/g, ' ');\n  \n  // Başlangıç ve bitiş boşluklarını kaldır\n  sanitized = sanitized.trim();\n  \n  return sanitized;\n}\n\n/**\n * Proje adını validate eder\n */\nexport function validateProjectName(name: string): ValidationResult {\n  const sanitized = sanitizeProjectName(name);\n  \n  // Boş kontrol\n  if (!sanitized) {\n    return {\n      isValid: false,\n      error: 'Proje adı boş olamaz'\n    };\n  }\n  \n  // Uzunluk kontrol\n  if (sanitized.length < PROJECT_NAME_RULES.minLength) {\n    return {\n      isValid: false,\n      error: `Proje adı en az ${PROJECT_NAME_RULES.minLength} karakter olmalıdır`\n    };\n  }\n  \n  if (sanitized.length > PROJECT_NAME_RULES.maxLength) {\n    return {\n      isValid: false,\n      error: `Proje adı en fazla ${PROJECT_NAME_RULES.maxLength} karakter olabilir`\n    };\n  }\n  \n  // Karakter kontrol\n  if (!PROJECT_NAME_RULES.allowedCharsRegex.test(sanitized)) {\n    return {\n      isValid: false,\n      error: 'Proje adı sadece harf, rakam, boşluk ve -_. karakterlerini içerebilir'\n    };\n  }\n  \n  // Sadece boşluk kontrolü\n  if (sanitized.replace(/\\s/g, '').length === 0) {\n    return {\n      isValid: false,\n      error: 'Proje adı sadece boşluk karakterlerinden oluşamaz'\n    };\n  }\n  \n  return {\n    isValid: true,\n    sanitizedValue: sanitized\n  };\n}\n\n/**\n * XSS koruması için HTML encode\n */\nexport function escapeHtml(text: string): string {\n  const div = document.createElement('div');\n  div.textContent = text;\n  return div.innerHTML;\n}\n\n/**\n * Client-side rate limiting kontrolü\n */\nexport function checkClientRateLimit(): boolean {\n  try {\n    const stored = localStorage.getItem(RATE_LIMIT_KEY);\n    const now = Date.now();\n    \n    if (!stored) {\n      // İlk istek\n      localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify({\n        count: 1,\n        windowStart: now\n      }));\n      return true;\n    }\n    \n    const data = JSON.parse(stored);\n    const windowDuration = PROJECT_NAME_RULES.rateLimit.windowMinutes * 60 * 1000; // ms\n    \n    // Pencere süresi dolmuş mu?\n    if (now - data.windowStart > windowDuration) {\n      // Yeni pencere başlat\n      localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify({\n        count: 1,\n        windowStart: now\n      }));\n      return true;\n    }\n    \n    // Limit kontrolü\n    if (data.count >= PROJECT_NAME_RULES.rateLimit.maxRequests) {\n      return false;\n    }\n    \n    // Sayacı artır\n    localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify({\n      count: data.count + 1,\n      windowStart: data.windowStart\n    }));\n    \n    return true;\n  } catch (error) {\n    console.warn('Rate limit check failed:', error);\n    return true; // Hata durumunda izin ver\n  }\n}\n\n/**\n * Rate limit reset\n */\nexport function resetClientRateLimit(): void {\n  try {\n    localStorage.removeItem(RATE_LIMIT_KEY);\n  } catch (error) {\n    console.warn('Rate limit reset failed:', error);\n  }\n}\n\n/**\n * Debounced validation hook için utility\n */\nexport function createDebouncedValidator(\n  validator: (value: string) => ValidationResult,\n  delay: number = PROJECT_NAME_RULES.debounceMs\n) {\n  let timeoutId: NodeJS.Timeout;\n  \n  return (value: string, callback: (result: ValidationResult) => void) => {\n    clearTimeout(timeoutId);\n    timeoutId = setTimeout(() => {\n      const result = validator(value);\n      callback(result);\n    }, delay);\n  };\n}\n\n/**\n * Güvenli proje adı karşılaştırması\n */\nexport function isSameProjectName(name1: string, name2: string): boolean {\n  const sanitized1 = sanitizeProjectName(name1).toLowerCase();\n  const sanitized2 = sanitizeProjectName(name2).toLowerCase();\n  return sanitized1 === sanitized2;\n}\n\n/**\n * Duplicate name kontrolü için async validation\n */\nexport async function validateProjectNameUnique(\n  name: string,\n  currentProjectId: string,\n  projects: Array<{ id: string; name: string }>\n): Promise<ValidationResult> {\n  const sanitized = sanitizeProjectName(name);\n\n  // Önce temel validation\n  const basicValidation = validateProjectName(name);\n  if (!basicValidation.isValid) {\n    return basicValidation;\n  }\n\n  // Duplicate kontrolü (case-insensitive)\n  const isDuplicate = projects.some(project =>\n    project.id !== currentProjectId &&\n    isSameProjectName(project.name, sanitized)\n  );\n\n  if (isDuplicate) {\n    return {\n      isValid: false,\n      error: 'Bu isimde bir proje zaten mevcut'\n    };\n  }\n\n  return {\n    isValid: true,\n    sanitizedValue: sanitized\n  };\n}\n\n/**\n * Error mesajlarını kullanıcı dostu hale getir\n */\nexport function formatValidationError(error: string): string {\n  // Teknik hataları kullanıcı dostu mesajlara çevir\n  const errorMap: Record<string, string> = {\n    'unique_violation': 'Bu isimde bir proje zaten mevcut',\n    'check_violation': 'Proje adı geçersiz karakterler içeriyor',\n    'not_null_violation': 'Proje adı boş olamaz',\n    'foreign_key_violation': 'Geçersiz proje referansı',\n    'RateLimitExceeded': 'Çok fazla güncelleme isteği. Lütfen bekleyin.',\n    'InvalidInput': 'Geçersiz proje adı',\n    'DuplicateName': 'Bu isimde bir proje zaten mevcut',\n    'NotFound': 'Proje bulunamadı',\n    'Unauthorized': 'Bu işlem için yetkiniz yok'\n  };\n\n  return errorMap[error] || error;\n}\n\n/**\n * Advanced debounced validator with duplicate check\n */\nexport function createAdvancedDebouncedValidator(\n  projects: Array<{ id: string; name: string }>,\n  currentProjectId: string,\n  delay: number = PROJECT_NAME_RULES.debounceMs\n) {\n  let timeoutId: NodeJS.Timeout;\n\n  return (value: string, callback: (result: ValidationResult) => void) => {\n    clearTimeout(timeoutId);\n    timeoutId = setTimeout(async () => {\n      const result = await validateProjectNameUnique(value, currentProjectId, projects);\n      callback(result);\n    }, delay);\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,mCAAmC;;;;;;;;;;;;;;AAC5B,MAAM,qBAAqB;IAChC,WAAW;IACX,WAAW;IACX,mBAAmB;IACnB,YAAY;IACZ,WAAW;QACT,aAAa;QACb,eAAe;IACjB;AACF;AASA,uCAAuC;AACvC,MAAM,iBAAiB;AAKhB,SAAS,oBAAoB,IAAY;IAC9C,IAAI,CAAC,MAAM,OAAO;IAElB,oBAAoB;IACpB,IAAI,YAAY,KAAK,IAAI;IAEzB,qCAAqC;IACrC,YAAY,UAAU,OAAO,CAAC,QAAQ;IAEtC,yCAAyC;IACzC,YAAY,UAAU,IAAI;IAE1B,OAAO;AACT;AAKO,SAAS,oBAAoB,IAAY;IAC9C,MAAM,YAAY,oBAAoB;IAEtC,cAAc;IACd,IAAI,CAAC,WAAW;QACd,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,kBAAkB;IAClB,IAAI,UAAU,MAAM,GAAG,mBAAmB,SAAS,EAAE;QACnD,OAAO;YACL,SAAS;YACT,OAAO,CAAC,gBAAgB,EAAE,mBAAmB,SAAS,CAAC,mBAAmB,CAAC;QAC7E;IACF;IAEA,IAAI,UAAU,MAAM,GAAG,mBAAmB,SAAS,EAAE;QACnD,OAAO;YACL,SAAS;YACT,OAAO,CAAC,mBAAmB,EAAE,mBAAmB,SAAS,CAAC,kBAAkB,CAAC;QAC/E;IACF;IAEA,mBAAmB;IACnB,IAAI,CAAC,mBAAmB,iBAAiB,CAAC,IAAI,CAAC,YAAY;QACzD,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,yBAAyB;IACzB,IAAI,UAAU,OAAO,CAAC,OAAO,IAAI,MAAM,KAAK,GAAG;QAC7C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,OAAO;QACL,SAAS;QACT,gBAAgB;IAClB;AACF;AAKO,SAAS,WAAW,IAAY;IACrC,MAAM,MAAM,SAAS,aAAa,CAAC;IACnC,IAAI,WAAW,GAAG;IAClB,OAAO,IAAI,SAAS;AACtB;AAKO,SAAS;IACd,IAAI;QACF,MAAM,SAAS,aAAa,OAAO,CAAC;QACpC,MAAM,MAAM,KAAK,GAAG;QAEpB,IAAI,CAAC,QAAQ;YACX,YAAY;YACZ,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;gBAClD,OAAO;gBACP,aAAa;YACf;YACA,OAAO;QACT;QAEA,MAAM,OAAO,KAAK,KAAK,CAAC;QACxB,MAAM,iBAAiB,mBAAmB,SAAS,CAAC,aAAa,GAAG,KAAK,MAAM,KAAK;QAEpF,4BAA4B;QAC5B,IAAI,MAAM,KAAK,WAAW,GAAG,gBAAgB;YAC3C,sBAAsB;YACtB,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;gBAClD,OAAO;gBACP,aAAa;YACf;YACA,OAAO;QACT;QAEA,iBAAiB;QACjB,IAAI,KAAK,KAAK,IAAI,mBAAmB,SAAS,CAAC,WAAW,EAAE;YAC1D,OAAO;QACT;QAEA,eAAe;QACf,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;YAClD,OAAO,KAAK,KAAK,GAAG;YACpB,aAAa,KAAK,WAAW;QAC/B;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,4BAA4B;QACzC,OAAO,MAAM,0BAA0B;IACzC;AACF;AAKO,SAAS;IACd,IAAI;QACF,aAAa,UAAU,CAAC;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,4BAA4B;IAC3C;AACF;AAKO,SAAS,yBACd,SAA8C,EAC9C,QAAgB,mBAAmB,UAAU;IAE7C,IAAI;IAEJ,OAAO,CAAC,OAAe;QACrB,aAAa;QACb,YAAY,WAAW;YACrB,MAAM,SAAS,UAAU;YACzB,SAAS;QACX,GAAG;IACL;AACF;AAKO,SAAS,kBAAkB,KAAa,EAAE,KAAa;IAC5D,MAAM,aAAa,oBAAoB,OAAO,WAAW;IACzD,MAAM,aAAa,oBAAoB,OAAO,WAAW;IACzD,OAAO,eAAe;AACxB;AAKO,eAAe,0BACpB,IAAY,EACZ,gBAAwB,EACxB,QAA6C;IAE7C,MAAM,YAAY,oBAAoB;IAEtC,wBAAwB;IACxB,MAAM,kBAAkB,oBAAoB;IAC5C,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAC5B,OAAO;IACT;IAEA,wCAAwC;IACxC,MAAM,cAAc,SAAS,IAAI,CAAC,CAAA,UAChC,QAAQ,EAAE,KAAK,oBACf,kBAAkB,QAAQ,IAAI,EAAE;IAGlC,IAAI,aAAa;QACf,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,OAAO;QACL,SAAS;QACT,gBAAgB;IAClB;AACF;AAKO,SAAS,sBAAsB,KAAa;IACjD,kDAAkD;IAClD,MAAM,WAAmC;QACvC,oBAAoB;QACpB,mBAAmB;QACnB,sBAAsB;QACtB,yBAAyB;QACzB,qBAAqB;QACrB,gBAAgB;QAChB,iBAAiB;QACjB,YAAY;QACZ,gBAAgB;IAClB;IAEA,OAAO,QAAQ,CAAC,MAAM,IAAI;AAC5B;AAKO,SAAS,iCACd,QAA6C,EAC7C,gBAAwB,EACxB,QAAgB,mBAAmB,UAAU;IAE7C,IAAI;IAEJ,OAAO,CAAC,OAAe;QACrB,aAAa;QACb,YAAY,WAAW;YACrB,MAAM,SAAS,MAAM,0BAA0B,OAAO,kBAAkB;YACxE,SAAS;QACX,GAAG;IACL;AACF", "debugId": null}}, {"offset": {"line": 1151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/rate-limiter.ts"], "sourcesContent": ["'use client'\n\n/**\n * Advanced Rate Limiting System for PromptFlow\n * Provides both client-side and server-side rate limiting\n */\n\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\n\n// Rate limiting configurations for different actions\nexport const RATE_LIMITS = {\n  // Authentication actions\n  login: { maxRequests: 5, windowMinutes: 15 },\n  signup: { maxRequests: 3, windowMinutes: 60 },\n  password_reset: { maxRequests: 3, windowMinutes: 60 },\n  \n  // Project actions\n  project_create: { maxRequests: 10, windowMinutes: 60 },\n  project_update: { maxRequests: 20, windowMinutes: 10 },\n  project_delete: { maxRequests: 5, windowMinutes: 60 },\n  \n  // Prompt actions\n  prompt_create: { maxRequests: 50, windowMinutes: 10 },\n  prompt_update: { maxRequests: 100, windowMinutes: 10 },\n  prompt_delete: { maxRequests: 20, windowMinutes: 10 },\n  \n  // Context actions\n  context_create: { maxRequests: 20, windowMinutes: 10 },\n  context_batch_add: { maxRequests: 5, windowMinutes: 10 },\n  \n  // Plan actions\n  plan_change: { maxRequests: 3, windowMinutes: 60 },\n  \n  // General API\n  api_general: { maxRequests: 200, windowMinutes: 10 },\n} as const\n\nexport type RateLimitAction = keyof typeof RATE_LIMITS\n\ninterface RateLimitData {\n  count: number\n  windowStart: number\n  lastRequest: number\n}\n\n/**\n * Client-side rate limiting with localStorage\n */\nexport class ClientRateLimiter {\n  private getStorageKey(action: RateLimitAction, userId?: string): string {\n    const userSuffix = userId ? `_${userId}` : ''\n    return `rate_limit_${action}${userSuffix}`\n  }\n\n  /**\n   * Check if action is allowed based on rate limits\n   */\n  async checkLimit(action: RateLimitAction, userId?: string): Promise<{\n    allowed: boolean\n    remainingRequests: number\n    resetTime: number\n    retryAfter?: number\n  }> {\n    try {\n      const config = RATE_LIMITS[action]\n      const storageKey = this.getStorageKey(action, userId)\n      const now = Date.now()\n      const windowMs = config.windowMinutes * 60 * 1000\n\n      // Get current data\n      const stored = localStorage.getItem(storageKey)\n      let data: RateLimitData\n\n      if (!stored) {\n        // First request\n        data = {\n          count: 1,\n          windowStart: now,\n          lastRequest: now\n        }\n        localStorage.setItem(storageKey, JSON.stringify(data))\n        \n        return {\n          allowed: true,\n          remainingRequests: config.maxRequests - 1,\n          resetTime: now + windowMs\n        }\n      }\n\n      data = JSON.parse(stored)\n\n      // Check if window has expired\n      if (now - data.windowStart > windowMs) {\n        // Reset window\n        data = {\n          count: 1,\n          windowStart: now,\n          lastRequest: now\n        }\n        localStorage.setItem(storageKey, JSON.stringify(data))\n        \n        return {\n          allowed: true,\n          remainingRequests: config.maxRequests - 1,\n          resetTime: now + windowMs\n        }\n      }\n\n      // Check if limit exceeded\n      if (data.count >= config.maxRequests) {\n        const resetTime = data.windowStart + windowMs\n        const retryAfter = Math.ceil((resetTime - now) / 1000)\n        \n        return {\n          allowed: false,\n          remainingRequests: 0,\n          resetTime,\n          retryAfter\n        }\n      }\n\n      // Increment counter\n      data.count++\n      data.lastRequest = now\n      localStorage.setItem(storageKey, JSON.stringify(data))\n\n      return {\n        allowed: true,\n        remainingRequests: config.maxRequests - data.count,\n        resetTime: data.windowStart + windowMs\n      }\n    } catch (error) {\n      console.warn(`Rate limit check failed for ${action}:`, error)\n      // On error, allow the request but log it\n      return {\n        allowed: true,\n        remainingRequests: 0,\n        resetTime: Date.now() + 60000 // 1 minute fallback\n      }\n    }\n  }\n\n  /**\n   * Reset rate limit for specific action\n   */\n  resetLimit(action: RateLimitAction, userId?: string): void {\n    try {\n      const storageKey = this.getStorageKey(action, userId)\n      localStorage.removeItem(storageKey)\n    } catch (error) {\n      console.warn(`Rate limit reset failed for ${action}:`, error)\n    }\n  }\n\n  /**\n   * Get current rate limit status\n   */\n  getStatus(action: RateLimitAction, userId?: string): {\n    count: number\n    remaining: number\n    resetTime: number\n  } | null {\n    try {\n      const config = RATE_LIMITS[action]\n      const storageKey = this.getStorageKey(action, userId)\n      const stored = localStorage.getItem(storageKey)\n      \n      if (!stored) {\n        return {\n          count: 0,\n          remaining: config.maxRequests,\n          resetTime: Date.now() + config.windowMinutes * 60 * 1000\n        }\n      }\n\n      const data: RateLimitData = JSON.parse(stored)\n      const windowMs = config.windowMinutes * 60 * 1000\n      const now = Date.now()\n\n      // Check if window expired\n      if (now - data.windowStart > windowMs) {\n        return {\n          count: 0,\n          remaining: config.maxRequests,\n          resetTime: now + windowMs\n        }\n      }\n\n      return {\n        count: data.count,\n        remaining: Math.max(0, config.maxRequests - data.count),\n        resetTime: data.windowStart + windowMs\n      }\n    } catch (error) {\n      console.warn(`Rate limit status check failed for ${action}:`, error)\n      return null\n    }\n  }\n}\n\n/**\n * Server-side rate limiting with Supabase\n */\nexport class ServerRateLimiter {\n  /**\n   * Check server-side rate limit using Supabase function\n   */\n  async checkLimit(\n    action: RateLimitAction,\n    userId?: string\n  ): Promise<{\n    allowed: boolean\n    remainingRequests: number\n    resetTime: number\n    retryAfter?: number\n  }> {\n    try {\n      const config = RATE_LIMITS[action]\n      \n      // Get current user if not provided\n      let targetUserId = userId\n      if (!targetUserId) {\n        const { data: { user } } = await supabase.auth.getUser()\n        if (!user) {\n          throw new Error('User not authenticated')\n        }\n        targetUserId = user.id\n      }\n\n      // Call Supabase rate limiting function\n      const { data, error } = await supabase.rpc('check_rate_limit', {\n        p_user_id: targetUserId,\n        p_action_type: action,\n        p_max_requests: config.maxRequests,\n        p_window_minutes: config.windowMinutes\n      })\n\n      if (error) {\n        console.error(`Server rate limit check failed for ${action}:`, error)\n        // On error, allow but log\n        return {\n          allowed: true,\n          remainingRequests: 0,\n          resetTime: Date.now() + config.windowMinutes * 60 * 1000\n        }\n      }\n\n      const allowed = data === true\n      const windowMs = config.windowMinutes * 60 * 1000\n      const resetTime = Date.now() + windowMs\n\n      if (!allowed) {\n        return {\n          allowed: false,\n          remainingRequests: 0,\n          resetTime,\n          retryAfter: Math.ceil(windowMs / 1000)\n        }\n      }\n\n      return {\n        allowed: true,\n        remainingRequests: config.maxRequests - 1, // Approximate\n        resetTime\n      }\n    } catch (error) {\n      console.error(`Server rate limit error for ${action}:`, error)\n      // On error, allow but log\n      return {\n        allowed: true,\n        remainingRequests: 0,\n        resetTime: Date.now() + 60000\n      }\n    }\n  }\n}\n\n// Global instances\nexport const clientRateLimiter = new ClientRateLimiter()\nexport const serverRateLimiter = new ServerRateLimiter()\n\n/**\n * Combined rate limiting check (client + server)\n */\nexport async function checkRateLimit(\n  action: RateLimitAction,\n  options: {\n    clientOnly?: boolean\n    serverOnly?: boolean\n    userId?: string\n  } = {}\n): Promise<{\n  allowed: boolean\n  remainingRequests: number\n  resetTime: number\n  retryAfter?: number\n  source: 'client' | 'server' | 'both'\n}> {\n  const { clientOnly = false, serverOnly = false, userId } = options\n\n  try {\n    // Client-side check\n    if (!serverOnly) {\n      const clientResult = await clientRateLimiter.checkLimit(action, userId)\n      if (!clientResult.allowed) {\n        return {\n          ...clientResult,\n          source: 'client'\n        }\n      }\n      \n      if (clientOnly) {\n        return {\n          ...clientResult,\n          source: 'client'\n        }\n      }\n    }\n\n    // Server-side check\n    if (!clientOnly) {\n      const serverResult = await serverRateLimiter.checkLimit(action, userId)\n      return {\n        ...serverResult,\n        source: serverOnly ? 'server' : 'both'\n      }\n    }\n\n    // Fallback (shouldn't reach here)\n    return {\n      allowed: true,\n      remainingRequests: 0,\n      resetTime: Date.now() + 60000,\n      source: 'client'\n    }\n  } catch (error) {\n    console.error(`Rate limit check failed for ${action}:`, error)\n    return {\n      allowed: true,\n      remainingRequests: 0,\n      resetTime: Date.now() + 60000,\n      source: 'client'\n    }\n  }\n}\n\n/**\n * Rate limit error class\n */\nexport class RateLimitError extends Error {\n  constructor(\n    public action: RateLimitAction,\n    public retryAfter: number,\n    public resetTime: number\n  ) {\n    super(`Rate limit exceeded for ${action}. Try again in ${retryAfter} seconds.`)\n    this.name = 'RateLimitError'\n  }\n}\n\n/**\n * Rate limiting hook for React components\n */\nexport function useRateLimit(action: RateLimitAction) {\n  return {\n    checkLimit: (options?: { clientOnly?: boolean; serverOnly?: boolean }) =>\n      checkRateLimit(action, options),\n    resetLimit: () => clientRateLimiter.resetLimit(action),\n    getStatus: () => clientRateLimiter.getStatus(action)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;CAGC,GAED;AAPA;;AAUO,MAAM,cAAc;IACzB,yBAAyB;IACzB,OAAO;QAAE,aAAa;QAAG,eAAe;IAAG;IAC3C,QAAQ;QAAE,aAAa;QAAG,eAAe;IAAG;IAC5C,gBAAgB;QAAE,aAAa;QAAG,eAAe;IAAG;IAEpD,kBAAkB;IAClB,gBAAgB;QAAE,aAAa;QAAI,eAAe;IAAG;IACrD,gBAAgB;QAAE,aAAa;QAAI,eAAe;IAAG;IACrD,gBAAgB;QAAE,aAAa;QAAG,eAAe;IAAG;IAEpD,iBAAiB;IACjB,eAAe;QAAE,aAAa;QAAI,eAAe;IAAG;IACpD,eAAe;QAAE,aAAa;QAAK,eAAe;IAAG;IACrD,eAAe;QAAE,aAAa;QAAI,eAAe;IAAG;IAEpD,kBAAkB;IAClB,gBAAgB;QAAE,aAAa;QAAI,eAAe;IAAG;IACrD,mBAAmB;QAAE,aAAa;QAAG,eAAe;IAAG;IAEvD,eAAe;IACf,aAAa;QAAE,aAAa;QAAG,eAAe;IAAG;IAEjD,cAAc;IACd,aAAa;QAAE,aAAa;QAAK,eAAe;IAAG;AACrD;AAaO,MAAM;IACH,cAAc,MAAuB,EAAE,MAAe,EAAU;QACtE,MAAM,aAAa,SAAS,CAAC,CAAC,EAAE,QAAQ,GAAG;QAC3C,OAAO,CAAC,WAAW,EAAE,SAAS,YAAY;IAC5C;IAEA;;GAEC,GACD,MAAM,WAAW,MAAuB,EAAE,MAAe,EAKtD;QACD,IAAI;YACF,MAAM,SAAS,WAAW,CAAC,OAAO;YAClC,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC,QAAQ;YAC9C,MAAM,MAAM,KAAK,GAAG;YACpB,MAAM,WAAW,OAAO,aAAa,GAAG,KAAK;YAE7C,mBAAmB;YACnB,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,IAAI;YAEJ,IAAI,CAAC,QAAQ;gBACX,gBAAgB;gBAChB,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,aAAa;gBACf;gBACA,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;gBAEhD,OAAO;oBACL,SAAS;oBACT,mBAAmB,OAAO,WAAW,GAAG;oBACxC,WAAW,MAAM;gBACnB;YACF;YAEA,OAAO,KAAK,KAAK,CAAC;YAElB,8BAA8B;YAC9B,IAAI,MAAM,KAAK,WAAW,GAAG,UAAU;gBACrC,eAAe;gBACf,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,aAAa;gBACf;gBACA,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;gBAEhD,OAAO;oBACL,SAAS;oBACT,mBAAmB,OAAO,WAAW,GAAG;oBACxC,WAAW,MAAM;gBACnB;YACF;YAEA,0BAA0B;YAC1B,IAAI,KAAK,KAAK,IAAI,OAAO,WAAW,EAAE;gBACpC,MAAM,YAAY,KAAK,WAAW,GAAG;gBACrC,MAAM,aAAa,KAAK,IAAI,CAAC,CAAC,YAAY,GAAG,IAAI;gBAEjD,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB;oBACA;gBACF;YACF;YAEA,oBAAoB;YACpB,KAAK,KAAK;YACV,KAAK,WAAW,GAAG;YACnB,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;YAEhD,OAAO;gBACL,SAAS;gBACT,mBAAmB,OAAO,WAAW,GAAG,KAAK,KAAK;gBAClD,WAAW,KAAK,WAAW,GAAG;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC,EAAE;YACvD,yCAAyC;YACzC,OAAO;gBACL,SAAS;gBACT,mBAAmB;gBACnB,WAAW,KAAK,GAAG,KAAK,MAAM,oBAAoB;YACpD;QACF;IACF;IAEA;;GAEC,GACD,WAAW,MAAuB,EAAE,MAAe,EAAQ;QACzD,IAAI;YACF,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC,QAAQ;YAC9C,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC,EAAE;QACzD;IACF;IAEA;;GAEC,GACD,UAAU,MAAuB,EAAE,MAAe,EAIzC;QACP,IAAI;YACF,MAAM,SAAS,WAAW,CAAC,OAAO;YAClC,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC,QAAQ;YAC9C,MAAM,SAAS,aAAa,OAAO,CAAC;YAEpC,IAAI,CAAC,QAAQ;gBACX,OAAO;oBACL,OAAO;oBACP,WAAW,OAAO,WAAW;oBAC7B,WAAW,KAAK,GAAG,KAAK,OAAO,aAAa,GAAG,KAAK;gBACtD;YACF;YAEA,MAAM,OAAsB,KAAK,KAAK,CAAC;YACvC,MAAM,WAAW,OAAO,aAAa,GAAG,KAAK;YAC7C,MAAM,MAAM,KAAK,GAAG;YAEpB,0BAA0B;YAC1B,IAAI,MAAM,KAAK,WAAW,GAAG,UAAU;gBACrC,OAAO;oBACL,OAAO;oBACP,WAAW,OAAO,WAAW;oBAC7B,WAAW,MAAM;gBACnB;YACF;YAEA,OAAO;gBACL,OAAO,KAAK,KAAK;gBACjB,WAAW,KAAK,GAAG,CAAC,GAAG,OAAO,WAAW,GAAG,KAAK,KAAK;gBACtD,WAAW,KAAK,WAAW,GAAG;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC,EAAE;YAC9D,OAAO;QACT;IACF;AACF;AAKO,MAAM;IACX;;GAEC,GACD,MAAM,WACJ,MAAuB,EACvB,MAAe,EAMd;QACD,IAAI;YACF,MAAM,SAAS,WAAW,CAAC,OAAO;YAElC,mCAAmC;YACnC,IAAI,eAAe;YACnB,IAAI,CAAC,cAAc;gBACjB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;gBACtD,IAAI,CAAC,MAAM;oBACT,MAAM,IAAI,MAAM;gBAClB;gBACA,eAAe,KAAK,EAAE;YACxB;YAEA,uCAAuC;YACvC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,oBAAoB;gBAC7D,WAAW;gBACX,eAAe;gBACf,gBAAgB,OAAO,WAAW;gBAClC,kBAAkB,OAAO,aAAa;YACxC;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC,EAAE;gBAC/D,0BAA0B;gBAC1B,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB,WAAW,KAAK,GAAG,KAAK,OAAO,aAAa,GAAG,KAAK;gBACtD;YACF;YAEA,MAAM,UAAU,SAAS;YACzB,MAAM,WAAW,OAAO,aAAa,GAAG,KAAK;YAC7C,MAAM,YAAY,KAAK,GAAG,KAAK;YAE/B,IAAI,CAAC,SAAS;gBACZ,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB;oBACA,YAAY,KAAK,IAAI,CAAC,WAAW;gBACnC;YACF;YAEA,OAAO;gBACL,SAAS;gBACT,mBAAmB,OAAO,WAAW,GAAG;gBACxC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC,EAAE;YACxD,0BAA0B;YAC1B,OAAO;gBACL,SAAS;gBACT,mBAAmB;gBACnB,WAAW,KAAK,GAAG,KAAK;YAC1B;QACF;IACF;AACF;AAGO,MAAM,oBAAoB,IAAI;AAC9B,MAAM,oBAAoB,IAAI;AAK9B,eAAe,eACpB,MAAuB,EACvB,UAII,CAAC,CAAC;IAQN,MAAM,EAAE,aAAa,KAAK,EAAE,aAAa,KAAK,EAAE,MAAM,EAAE,GAAG;IAE3D,IAAI;QACF,oBAAoB;QACpB,IAAI,CAAC,YAAY;YACf,MAAM,eAAe,MAAM,kBAAkB,UAAU,CAAC,QAAQ;YAChE,IAAI,CAAC,aAAa,OAAO,EAAE;gBACzB,OAAO;oBACL,GAAG,YAAY;oBACf,QAAQ;gBACV;YACF;YAEA,IAAI,YAAY;gBACd,OAAO;oBACL,GAAG,YAAY;oBACf,QAAQ;gBACV;YACF;QACF;QAEA,oBAAoB;QACpB,IAAI,CAAC,YAAY;YACf,MAAM,eAAe,MAAM,kBAAkB,UAAU,CAAC,QAAQ;YAChE,OAAO;gBACL,GAAG,YAAY;gBACf,QAAQ,aAAa,WAAW;YAClC;QACF;QAEA,kCAAkC;QAClC,OAAO;YACL,SAAS;YACT,mBAAmB;YACnB,WAAW,KAAK,GAAG,KAAK;YACxB,QAAQ;QACV;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC,EAAE;QACxD,OAAO;YACL,SAAS;YACT,mBAAmB;YACnB,WAAW,KAAK,GAAG,KAAK;YACxB,QAAQ;QACV;IACF;AACF;AAKO,MAAM,uBAAuB;;;;IAClC,YACE,AAAO,MAAuB,EAC9B,AAAO,UAAkB,EACzB,AAAO,SAAiB,CACxB;QACA,KAAK,CAAC,CAAC,wBAAwB,EAAE,OAAO,eAAe,EAAE,WAAW,SAAS,CAAC,QAJvE,SAAA,aACA,aAAA,iBACA,YAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAKO,SAAS,aAAa,MAAuB;IAClD,OAAO;QACL,YAAY,CAAC,UACX,eAAe,QAAQ;QACzB,YAAY,IAAM,kBAAkB,UAAU,CAAC;QAC/C,WAAW,IAAM,kBAAkB,SAAS,CAAC;IAC/C;AACF", "debugId": null}}, {"offset": {"line": 1473, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/hooks/use-projects.ts"], "sourcesContent": ["'use client'\r\n\r\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\r\nimport { Database } from '@/lib/supabase'\r\nimport { canCreateProject, updateUsageStats, PLAN_LIMIT_MESSAGES } from '@/lib/plan-limits'\r\nimport {\r\n  validateProjectName,\r\n  formatValidationError\r\n} from '@/lib/project-validation'\r\nimport { checkRateLimit, RateLimitError } from '@/lib/rate-limiter'\r\nimport { OptimizedQueries, QueryPerformanceMonitor } from '@/lib/database-optimization'\r\nimport { CACHE_CONFIGS } from '@/lib/cache-strategies'\r\n\r\ntype Project = Database['public']['Tables']['projects']['Row']\r\ntype ProjectInsert = Database['public']['Tables']['projects']['Insert']\r\ntype ProjectUpdate = Database['public']['Tables']['projects']['Update']\r\n\r\n// Projeleri getir\r\nexport function useProjects() {\r\n  return useQuery({\r\n    queryKey: ['projects'],\r\n    queryFn: async (): Promise<Project[]> => {\r\n      console.log(`📁 [USE_PROJECTS] Fetching projects...`)\r\n\r\n      try {\r\n        // Check if we have a session first\r\n        const { data: { session }, error: sessionError } = await supabase.auth.getSession()\r\n        console.log(`📁 [USE_PROJECTS] Session check:`, {\r\n          hasSession: !!session,\r\n          sessionError: sessionError?.message,\r\n          userId: session?.user?.id\r\n        })\r\n\r\n        const { data, error } = await supabase\r\n          .from('projects')\r\n          .select('*')\r\n          .order('created_at', { ascending: false })\r\n\r\n        if (error) {\r\n          console.error(`❌ [USE_PROJECTS] Error fetching projects:`, error)\r\n          throw new Error(error.message)\r\n        }\r\n\r\n        console.log(`✅ [USE_PROJECTS] Projects fetched:`, data?.length || 0, 'projects')\r\n        return data || []\r\n      } catch (err) {\r\n        console.error(`💥 [USE_PROJECTS] Exception:`, err)\r\n        throw err\r\n      }\r\n    },\r\n  })\r\n}\r\n\r\n// Tek proje getir\r\nexport function useProject(projectId: string | null) {\r\n  return useQuery({\r\n    queryKey: ['project', projectId],\r\n    queryFn: async (): Promise<Project | null> => {\r\n      if (!projectId) return null\r\n\r\n      const { data, error } = await supabase\r\n        .from('projects')\r\n        .select('*')\r\n        .eq('id', projectId)\r\n        .single()\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n\r\n      return data\r\n    },\r\n    enabled: !!projectId,\r\n  })\r\n}\r\n\r\n// Proje oluştur\r\nexport function useCreateProject() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async (project: Omit<ProjectInsert, 'user_id'>): Promise<Project> => {\r\n      // Plan limiti kontrol et\r\n      const limitCheck = await canCreateProject()\r\n      if (!limitCheck.allowed) {\r\n        throw new Error(limitCheck.reason || PLAN_LIMIT_MESSAGES.PROJECT_LIMIT_REACHED)\r\n      }\r\n\r\n      const { data: { user }, error: userError } = await supabase.auth.getUser()\r\n\r\n      if (userError || !user) {\r\n        throw new Error('Kullanıcı oturumu bulunamadı')\r\n      }\r\n\r\n      const { data, error } = await supabase\r\n        .from('projects')\r\n        .insert({\r\n          ...project,\r\n          user_id: user.id,\r\n        })\r\n        .select()\r\n        .single()\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n\r\n      return data\r\n    },\r\n    onSuccess: async () => {\r\n      queryClient.invalidateQueries({ queryKey: ['projects'] })\r\n      // Kullanım istatistiklerini güncelle\r\n      try {\r\n        await updateUsageStats()\r\n        queryClient.invalidateQueries({ queryKey: ['user-limits'] })\r\n        queryClient.invalidateQueries({ queryKey: ['usage-stats'] })\r\n      } catch (error) {\r\n        console.warn('Kullanım istatistikleri güncellenemedi:', error)\r\n      }\r\n    },\r\n  })\r\n}\r\n\r\n// Proje güncelle\r\nexport function useUpdateProject() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async ({ id, ...updates }: ProjectUpdate & { id: string }): Promise<Project> => {\r\n      console.log('🔄 [UPDATE_PROJECT] Starting update:', { id, updates })\r\n\r\n      // Önce mevcut kullanıcıyı kontrol et\r\n      const { data: user } = await supabase.auth.getUser()\r\n      if (!user.user) {\r\n        throw new Error('Kullanıcı girişi gerekli')\r\n      }\r\n\r\n      // Güncelleme işlemini yap - RLS politikaları otomatik olarak kontrol edecek\r\n      const { data, error } = await supabase\r\n        .from('projects')\r\n        .update({\r\n          ...updates,\r\n          updated_at: new Date().toISOString()\r\n        })\r\n        .eq('id', id)\r\n        .eq('user_id', user.user.id) // Ekstra güvenlik için user_id kontrolü\r\n        .select()\r\n        .single()\r\n\r\n      if (error) {\r\n        console.error('❌ [UPDATE_PROJECT] Database error:', error)\r\n        throw new Error(`Proje güncellenirken hata oluştu: ${error.message}`)\r\n      }\r\n\r\n      if (!data) {\r\n        throw new Error('Proje bulunamadı veya güncelleme yetkisi yok')\r\n      }\r\n\r\n      console.log('✅ [UPDATE_PROJECT] Success:', data)\r\n      return data\r\n    },\r\n    onSuccess: (data) => {\r\n      queryClient.invalidateQueries({ queryKey: ['projects'] })\r\n      queryClient.invalidateQueries({ queryKey: ['project', data.id] })\r\n    },\r\n    onError: (error) => {\r\n      console.error('❌ [UPDATE_PROJECT] Mutation error:', error)\r\n    }\r\n  })\r\n}\r\n\r\n// Güvenli proje adı güncelleme\r\nexport function useUpdateProjectNameSecure() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async ({\r\n      projectId,\r\n      newName\r\n    }: {\r\n      projectId: string;\r\n      newName: string\r\n    }): Promise<Project> => {\r\n      console.log(`🔒 [UPDATE_PROJECT_NAME] Starting secure update:`, { projectId, newName })\r\n\r\n      // Advanced rate limiting check\r\n      const rateLimitResult = await checkRateLimit('project_update')\r\n      if (!rateLimitResult.allowed) {\r\n        throw new RateLimitError(\r\n          'project_update',\r\n          rateLimitResult.retryAfter || 60,\r\n          rateLimitResult.resetTime\r\n        )\r\n      }\r\n\r\n      // Client-side validation\r\n      const validation = validateProjectName(newName)\r\n      if (!validation.isValid) {\r\n        throw new Error(validation.error || 'Geçersiz proje adı')\r\n      }\r\n\r\n      const sanitizedName = validation.sanitizedValue!\r\n\r\n      try {\r\n        // Güvenli database function'ını çağır\r\n        const { data, error } = await supabase.rpc('update_project_name_secure', {\r\n          p_project_id: projectId,\r\n          p_new_name: sanitizedName\r\n        })\r\n\r\n        if (error) {\r\n          console.error(`❌ [UPDATE_PROJECT_NAME] Database error:`, error)\r\n          throw new Error(formatValidationError(error.message))\r\n        }\r\n\r\n        // Function response kontrolü\r\n        if (!data?.success) {\r\n          console.error(`❌ [UPDATE_PROJECT_NAME] Function error:`, data)\r\n          throw new Error(data?.message || 'Proje adı güncellenemedi')\r\n        }\r\n\r\n        console.log(`✅ [UPDATE_PROJECT_NAME] Success:`, data.data)\r\n        return data.data as Project\r\n\r\n      } catch (err) {\r\n        console.error(`💥 [UPDATE_PROJECT_NAME] Exception:`, err)\r\n\r\n        // Error mesajını kullanıcı dostu hale getir\r\n        let errorMessage = err instanceof Error ? err.message : 'Bilinmeyen hata'\r\n\r\n        if (errorMessage.includes('unique_violation') || errorMessage.includes('DuplicateName')) {\r\n          errorMessage = 'Bu isimde bir proje zaten mevcut'\r\n        } else if (errorMessage.includes('check_violation') || errorMessage.includes('InvalidInput')) {\r\n          errorMessage = 'Proje adı geçersiz karakterler içeriyor'\r\n        } else if (errorMessage.includes('RateLimitExceeded')) {\r\n          errorMessage = 'Çok fazla güncelleme isteği. Lütfen bekleyin.'\r\n        }\r\n\r\n        throw new Error(errorMessage)\r\n      }\r\n    },\r\n    onMutate: async ({ projectId, newName }) => {\r\n      console.log(`🚀 [UPDATE_PROJECT_NAME] Starting optimistic update:`, { projectId, newName })\r\n\r\n      // Cancel outgoing refetches\r\n      await queryClient.cancelQueries({ queryKey: ['projects'] })\r\n      await queryClient.cancelQueries({ queryKey: ['project', projectId] })\r\n\r\n      // Snapshot previous values\r\n      const previousProjects = queryClient.getQueryData<Project[]>(['projects'])\r\n      const previousProject = queryClient.getQueryData<Project>(['project', projectId])\r\n\r\n      // Optimistically update projects list\r\n      if (previousProjects) {\r\n        queryClient.setQueryData<Project[]>(['projects'], (old) => {\r\n          if (!old) return old\r\n          return old.map(project =>\r\n            project.id === projectId\r\n              ? { ...project, name: newName.trim(), updated_at: new Date().toISOString() }\r\n              : project\r\n          )\r\n        })\r\n      }\r\n\r\n      // Optimistically update single project\r\n      if (previousProject) {\r\n        queryClient.setQueryData<Project>(['project', projectId], {\r\n          ...previousProject,\r\n          name: newName.trim(),\r\n          updated_at: new Date().toISOString()\r\n        })\r\n      }\r\n\r\n      return { previousProjects, previousProject }\r\n    },\r\n    onSuccess: (data) => {\r\n      console.log(`✅ [UPDATE_PROJECT_NAME] Success, updating cache with server data:`, data.id)\r\n\r\n      // Update with actual server data\r\n      queryClient.setQueryData<Project[]>(['projects'], (old) => {\r\n        if (!old) return old\r\n        return old.map(project =>\r\n          project.id === data.id ? { ...project, ...data } : project\r\n        )\r\n      })\r\n\r\n      queryClient.setQueryData<Project>(['project', data.id], data)\r\n    },\r\n    onError: (error, variables, context) => {\r\n      console.error(`💥 [UPDATE_PROJECT_NAME] Error, rolling back:`, error)\r\n\r\n      // Rollback optimistic updates\r\n      if (context?.previousProjects) {\r\n        queryClient.setQueryData(['projects'], context.previousProjects)\r\n      }\r\n      if (context?.previousProject) {\r\n        queryClient.setQueryData(['project', variables.projectId], context.previousProject)\r\n      }\r\n    },\r\n    onSettled: () => {\r\n      // Always refetch to ensure consistency\r\n      queryClient.invalidateQueries({ queryKey: ['projects'] })\r\n    }\r\n  })\r\n}\r\n\r\n// Proje sil\r\nexport function useDeleteProject() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async (projectId: string): Promise<void> => {\r\n      const { error } = await supabase\r\n        .from('projects')\r\n        .delete()\r\n        .eq('id', projectId)\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n    },\r\n    onSuccess: async () => {\r\n      queryClient.invalidateQueries({ queryKey: ['projects'] })\r\n      // Kullanım istatistiklerini güncelle\r\n      try {\r\n        await updateUsageStats()\r\n        queryClient.invalidateQueries({ queryKey: ['user-limits'] })\r\n        queryClient.invalidateQueries({ queryKey: ['usage-stats'] })\r\n      } catch (error) {\r\n        console.warn('Kullanım istatistikleri güncellenemedi:', error)\r\n      }\r\n    },\r\n  })\r\n} "], "names": [], "mappings": ";;;;;;;;AAEA;AAAA;AAAA;AACA;AAEA;AACA;AAIA;AAVA;;;;;;AAmBO,SAAS;IACd,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAW;QACtB,SAAS;YACP,QAAQ,GAAG,CAAC,CAAC,sCAAsC,CAAC;YAEpD,IAAI;gBACF,mCAAmC;gBACnC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,UAAU;gBACjF,QAAQ,GAAG,CAAC,CAAC,gCAAgC,CAAC,EAAE;oBAC9C,YAAY,CAAC,CAAC;oBACd,cAAc,cAAc;oBAC5B,QAAQ,SAAS,MAAM;gBACzB;gBAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAM;gBAE1C,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,CAAC,yCAAyC,CAAC,EAAE;oBAC3D,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,QAAQ,GAAG,CAAC,CAAC,kCAAkC,CAAC,EAAE,MAAM,UAAU,GAAG;gBACrE,OAAO,QAAQ,EAAE;YACnB,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,CAAC,4BAA4B,CAAC,EAAE;gBAC9C,MAAM;YACR;QACF;IACF;AACF;AAGO,SAAS,WAAW,SAAwB;IACjD,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;SAAU;QAChC,SAAS;YACP,IAAI,CAAC,WAAW,OAAO;YAEvB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,WACT,MAAM;YAET,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,OAAO;QACT;QACA,SAAS,CAAC,CAAC;IACb;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO;YACjB,yBAAyB;YACzB,MAAM,aAAa,MAAM,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD;YACxC,IAAI,CAAC,WAAW,OAAO,EAAE;gBACvB,MAAM,IAAI,MAAM,WAAW,MAAM,IAAI,4HAAA,CAAA,sBAAmB,CAAC,qBAAqB;YAChF;YAEA,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;YAExE,IAAI,aAAa,CAAC,MAAM;gBACtB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC;gBACN,GAAG,OAAO;gBACV,SAAS,KAAK,EAAE;YAClB,GACC,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,OAAO;QACT;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACvD,qCAAqC;YACrC,IAAI;gBACF,MAAM,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD;gBACrB,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAc;gBAAC;gBAC1D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAc;gBAAC;YAC5D,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,2CAA2C;YAC1D;QACF;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EAAE,EAAE,EAAE,GAAG,SAAyC;YACnE,QAAQ,GAAG,CAAC,wCAAwC;gBAAE;gBAAI;YAAQ;YAElE,qCAAqC;YACrC,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;YAClD,IAAI,CAAC,KAAK,IAAI,EAAE;gBACd,MAAM,IAAI,MAAM;YAClB;YAEA,4EAA4E;YAC5E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC;gBACN,GAAG,OAAO;gBACV,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,EAAE,CAAC,MAAM,IACT,EAAE,CAAC,WAAW,KAAK,IAAI,CAAC,EAAE,EAAE,wCAAwC;aACpE,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,sCAAsC;gBACpD,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,MAAM,OAAO,EAAE;YACtE;YAEA,IAAI,CAAC,MAAM;gBACT,MAAM,IAAI,MAAM;YAClB;YAEA,QAAQ,GAAG,CAAC,+BAA+B;YAC3C,OAAO;QACT;QACA,WAAW,CAAC;YACV,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACvD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAW,KAAK,EAAE;iBAAC;YAAC;QACjE;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,sCAAsC;QACtD;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EACjB,SAAS,EACT,OAAO,EAIR;YACC,QAAQ,GAAG,CAAC,CAAC,gDAAgD,CAAC,EAAE;gBAAE;gBAAW;YAAQ;YAErF,+BAA+B;YAC/B,MAAM,kBAAkB,MAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;YAC7C,IAAI,CAAC,gBAAgB,OAAO,EAAE;gBAC5B,MAAM,IAAI,6HAAA,CAAA,iBAAc,CACtB,kBACA,gBAAgB,UAAU,IAAI,IAC9B,gBAAgB,SAAS;YAE7B;YAEA,yBAAyB;YACzB,MAAM,aAAa,CAAA,GAAA,mIAAA,CAAA,sBAAmB,AAAD,EAAE;YACvC,IAAI,CAAC,WAAW,OAAO,EAAE;gBACvB,MAAM,IAAI,MAAM,WAAW,KAAK,IAAI;YACtC;YAEA,MAAM,gBAAgB,WAAW,cAAc;YAE/C,IAAI;gBACF,sCAAsC;gBACtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,8BAA8B;oBACvE,cAAc;oBACd,YAAY;gBACd;gBAEA,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,CAAC,uCAAuC,CAAC,EAAE;oBACzD,MAAM,IAAI,MAAM,CAAA,GAAA,mIAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM,OAAO;gBACrD;gBAEA,6BAA6B;gBAC7B,IAAI,CAAC,MAAM,SAAS;oBAClB,QAAQ,KAAK,CAAC,CAAC,uCAAuC,CAAC,EAAE;oBACzD,MAAM,IAAI,MAAM,MAAM,WAAW;gBACnC;gBAEA,QAAQ,GAAG,CAAC,CAAC,gCAAgC,CAAC,EAAE,KAAK,IAAI;gBACzD,OAAO,KAAK,IAAI;YAElB,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,CAAC,mCAAmC,CAAC,EAAE;gBAErD,4CAA4C;gBAC5C,IAAI,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAExD,IAAI,aAAa,QAAQ,CAAC,uBAAuB,aAAa,QAAQ,CAAC,kBAAkB;oBACvF,eAAe;gBACjB,OAAO,IAAI,aAAa,QAAQ,CAAC,sBAAsB,aAAa,QAAQ,CAAC,iBAAiB;oBAC5F,eAAe;gBACjB,OAAO,IAAI,aAAa,QAAQ,CAAC,sBAAsB;oBACrD,eAAe;gBACjB;gBAEA,MAAM,IAAI,MAAM;YAClB;QACF;QACA,UAAU,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE;YACrC,QAAQ,GAAG,CAAC,CAAC,oDAAoD,CAAC,EAAE;gBAAE;gBAAW;YAAQ;YAEzF,4BAA4B;YAC5B,MAAM,YAAY,aAAa,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACzD,MAAM,YAAY,aAAa,CAAC;gBAAE,UAAU;oBAAC;oBAAW;iBAAU;YAAC;YAEnE,2BAA2B;YAC3B,MAAM,mBAAmB,YAAY,YAAY,CAAY;gBAAC;aAAW;YACzE,MAAM,kBAAkB,YAAY,YAAY,CAAU;gBAAC;gBAAW;aAAU;YAEhF,sCAAsC;YACtC,IAAI,kBAAkB;gBACpB,YAAY,YAAY,CAAY;oBAAC;iBAAW,EAAE,CAAC;oBACjD,IAAI,CAAC,KAAK,OAAO;oBACjB,OAAO,IAAI,GAAG,CAAC,CAAA,UACb,QAAQ,EAAE,KAAK,YACX;4BAAE,GAAG,OAAO;4BAAE,MAAM,QAAQ,IAAI;4BAAI,YAAY,IAAI,OAAO,WAAW;wBAAG,IACzE;gBAER;YACF;YAEA,uCAAuC;YACvC,IAAI,iBAAiB;gBACnB,YAAY,YAAY,CAAU;oBAAC;oBAAW;iBAAU,EAAE;oBACxD,GAAG,eAAe;oBAClB,MAAM,QAAQ,IAAI;oBAClB,YAAY,IAAI,OAAO,WAAW;gBACpC;YACF;YAEA,OAAO;gBAAE;gBAAkB;YAAgB;QAC7C;QACA,WAAW,CAAC;YACV,QAAQ,GAAG,CAAC,CAAC,iEAAiE,CAAC,EAAE,KAAK,EAAE;YAExF,iCAAiC;YACjC,YAAY,YAAY,CAAY;gBAAC;aAAW,EAAE,CAAC;gBACjD,IAAI,CAAC,KAAK,OAAO;gBACjB,OAAO,IAAI,GAAG,CAAC,CAAA,UACb,QAAQ,EAAE,KAAK,KAAK,EAAE,GAAG;wBAAE,GAAG,OAAO;wBAAE,GAAG,IAAI;oBAAC,IAAI;YAEvD;YAEA,YAAY,YAAY,CAAU;gBAAC;gBAAW,KAAK,EAAE;aAAC,EAAE;QAC1D;QACA,SAAS,CAAC,OAAO,WAAW;YAC1B,QAAQ,KAAK,CAAC,CAAC,6CAA6C,CAAC,EAAE;YAE/D,8BAA8B;YAC9B,IAAI,SAAS,kBAAkB;gBAC7B,YAAY,YAAY,CAAC;oBAAC;iBAAW,EAAE,QAAQ,gBAAgB;YACjE;YACA,IAAI,SAAS,iBAAiB;gBAC5B,YAAY,YAAY,CAAC;oBAAC;oBAAW,UAAU,SAAS;iBAAC,EAAE,QAAQ,eAAe;YACpF;QACF;QACA,WAAW;YACT,uCAAuC;YACvC,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;QACzD;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO;YACjB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAC7B,IAAI,CAAC,YACL,MAAM,GACN,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;QACF;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACvD,qCAAqC;YACrC,IAAI;gBACF,MAAM,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD;gBACrB,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAc;gBAAC;gBAC1D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAc;gBAAC;YAC5D,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,2CAA2C;YAC1D;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 1820, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/hooks/use-plans.ts"], "sourcesContent": ["'use client'\n\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\nimport { Database } from '@/lib/supabase'\n\ntype PlanType = Database['public']['Tables']['plan_types']['Row']\ntype UserPlan = Database['public']['Tables']['user_plans']['Row']\ntype UsageStats = Database['public']['Tables']['usage_stats']['Row']\n\n// Plan türlerini getir\nexport function usePlanTypes() {\n  return useQuery({\n    queryKey: ['plan-types'],\n    queryFn: async (): Promise<PlanType[]> => {\n      console.log(`📋 [USE_PLANS] Fetching plan types...`)\n      \n      const { data, error } = await supabase\n        .from('plan_types')\n        .select('*')\n        .eq('is_active', true)\n        .order('sort_order')\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] Plan types fetch failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] Plan types fetched:`, data?.length)\n      return data || []\n    },\n    staleTime: 5 * 60 * 1000, // 5 dakika\n  })\n}\n\n// Kullanıcının aktif planını getir\nexport function useUserActivePlan() {\n  return useQuery({\n    queryKey: ['user-active-plan'],\n    queryFn: async () => {\n      console.log(`👤 [USE_PLANS] Fetching user active plan...`)\n      \n      const { data, error } = await supabase.rpc('get_user_active_plan', {\n        user_uuid: (await supabase.auth.getUser()).data.user?.id\n      })\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] User active plan fetch failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] User active plan fetched:`, data?.[0])\n      return data?.[0] || null\n    },\n    staleTime: 2 * 60 * 1000, // 2 dakika\n  })\n}\n\n// Kullanıcının limitlerini kontrol et\nexport function useUserLimits() {\n  return useQuery({\n    queryKey: ['user-limits'],\n    queryFn: async () => {\n      console.log(`🔍 [USE_PLANS] Checking user limits...`)\n      \n      const { data, error } = await supabase.rpc('check_user_limits', {\n        user_uuid: (await supabase.auth.getUser()).data.user?.id\n      })\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] User limits check failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] User limits checked:`, data?.[0])\n      return data?.[0] || null\n    },\n    staleTime: 30 * 1000, // 30 saniye\n  })\n}\n\n// Kullanıcının plan geçmişini getir\nexport function useUserPlanHistory() {\n  return useQuery({\n    queryKey: ['user-plan-history'],\n    queryFn: async (): Promise<UserPlan[]> => {\n      console.log(`📜 [USE_PLANS] Fetching user plan history...`)\n      \n      const { data, error } = await supabase\n        .from('user_plans')\n        .select(`\n          *,\n          plan_types (\n            name,\n            display_name,\n            price_monthly,\n            price_yearly\n          )\n        `)\n        .order('created_at', { ascending: false })\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] User plan history fetch failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] User plan history fetched:`, data?.length)\n      return data || []\n    },\n    staleTime: 5 * 60 * 1000, // 5 dakika\n  })\n}\n\n// Kullanıcının kullanım istatistiklerini getir\nexport function useUsageStats(days: number = 30) {\n  return useQuery({\n    queryKey: ['usage-stats', days],\n    queryFn: async (): Promise<UsageStats[]> => {\n      console.log(`📊 [USE_PLANS] Fetching usage stats for ${days} days...`)\n      \n      const startDate = new Date()\n      startDate.setDate(startDate.getDate() - days)\n      \n      const { data, error } = await supabase\n        .from('usage_stats')\n        .select('*')\n        .gte('stat_date', startDate.toISOString().split('T')[0])\n        .order('stat_date', { ascending: false })\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] Usage stats fetch failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] Usage stats fetched:`, data?.length)\n      return data || []\n    },\n    staleTime: 5 * 60 * 1000, // 5 dakika\n  })\n}\n\n// Plan değiştir\nexport function useChangePlan() {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async ({ \n      planName, \n      billingCycle = 'monthly',\n      paymentReference \n    }: { \n      planName: string\n      billingCycle?: string\n      paymentReference?: string \n    }) => {\n      console.log(`🔄 [USE_PLANS] Changing plan to: ${planName}`)\n      \n      const { data, error } = await supabase.rpc('change_user_plan', {\n        user_uuid: (await supabase.auth.getUser()).data.user?.id,\n        new_plan_name: planName,\n        billing_cycle_param: billingCycle,\n        payment_reference_param: paymentReference\n      })\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] Plan change failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] Plan changed successfully:`, data)\n      return data\n    },\n    onSuccess: () => {\n      console.log(`🎉 [USE_PLANS] Plan change successful, invalidating queries`)\n      queryClient.invalidateQueries({ queryKey: ['user-active-plan'] })\n      queryClient.invalidateQueries({ queryKey: ['user-limits'] })\n      queryClient.invalidateQueries({ queryKey: ['user-plan-history'] })\n      queryClient.invalidateQueries({ queryKey: ['usage-stats'] })\n    },\n    onError: (error) => {\n      console.error(`💥 [USE_PLANS] Plan change error:`, error)\n    }\n  })\n}\n\n// Kullanım istatistiklerini güncelle\nexport function useUpdateUsageStats() {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async () => {\n      console.log(`📈 [USE_PLANS] Updating usage stats...`)\n      \n      const { error } = await supabase.rpc('update_usage_stats', {\n        user_uuid: (await supabase.auth.getUser()).data.user?.id\n      })\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] Usage stats update failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] Usage stats updated successfully`)\n    },\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: ['usage-stats'] })\n      queryClient.invalidateQueries({ queryKey: ['user-limits'] })\n    }\n  })\n}\n\n// Plan limiti kontrol fonksiyonu\nexport async function checkPlanLimit(action: 'create_project' | 'create_prompt'): Promise<boolean> {\n  try {\n    const { data, error } = await supabase.rpc('check_user_limits', {\n      user_uuid: (await supabase.auth.getUser()).data.user?.id\n    })\n\n    if (error) {\n      console.error(`❌ [USE_PLANS] Limit check failed:`, error)\n      return false\n    }\n\n    const limits = data?.[0]\n    if (!limits) return false\n\n    switch (action) {\n      case 'create_project':\n        return limits.can_create_project\n      case 'create_prompt':\n        return limits.can_create_prompt\n      default:\n        return false\n    }\n  } catch (error) {\n    console.error(`❌ [USE_PLANS] Limit check error:`, error)\n    return false\n  }\n}\n\n// Plan özelliği kontrol fonksiyonu\nexport function usePlanFeature(featureName: string) {\n  const { data: activePlan } = useUserActivePlan()\n\n  return {\n    hasFeature: activePlan?.features?.[featureName] === true,\n    featureValue: activePlan?.features?.[featureName],\n    planName: activePlan?.plan_name,\n    displayName: activePlan?.display_name\n  }\n}\n\n// Kullanıcının deneme süresi bilgilerini getir\nexport function useUserTrialInfo() {\n  return useQuery({\n    queryKey: ['user-trial-info'],\n    queryFn: async () => {\n      console.log(`🔍 [USE_PLANS] Fetching user trial info...`)\n\n      const { data, error } = await supabase.rpc('get_user_trial_info', {\n        user_uuid: (await supabase.auth.getUser()).data.user?.id\n      })\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] User trial info fetch failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] User trial info fetched:`, data?.[0])\n      return data?.[0] || null\n    },\n    staleTime: 30 * 1000, // 30 saniye\n  })\n}\n\n// İptal uygunluğunu kontrol et\nexport function useCancellationEligibility() {\n  return useQuery({\n    queryKey: ['cancellation-eligibility'],\n    queryFn: async () => {\n      console.log(`🔍 [USE_PLANS] Checking cancellation eligibility...`)\n\n      const { data, error } = await supabase.rpc('get_cancellation_eligibility', {\n        user_uuid: (await supabase.auth.getUser()).data.user?.id\n      })\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] Cancellation eligibility check failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] Cancellation eligibility checked:`, data?.[0])\n      return data?.[0] || null\n    },\n    staleTime: 30 * 1000, // 30 saniye\n  })\n}\n\n// Planı iptal et\nexport function useCancelPlan() {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async ({\n      cancellationReason,\n      requestRefund = false\n    }: {\n      cancellationReason?: string\n      requestRefund?: boolean\n    }) => {\n      console.log(`🚫 [USE_PLANS] Cancelling plan with reason: ${cancellationReason}`)\n\n      const { data, error } = await supabase.rpc('cancel_user_plan', {\n        user_uuid: (await supabase.auth.getUser()).data.user?.id,\n        cancellation_reason_param: cancellationReason,\n        request_refund: requestRefund\n      })\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] Plan cancellation failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] Plan cancelled successfully:`, data?.[0])\n      return data?.[0]\n    },\n    onSuccess: (data) => {\n      console.log(`🎉 [USE_PLANS] Plan cancellation successful, invalidating queries`)\n      queryClient.invalidateQueries({ queryKey: ['user-active-plan'] })\n      queryClient.invalidateQueries({ queryKey: ['user-limits'] })\n      queryClient.invalidateQueries({ queryKey: ['user-plan-history'] })\n      queryClient.invalidateQueries({ queryKey: ['user-trial-info'] })\n      queryClient.invalidateQueries({ queryKey: ['cancellation-eligibility'] })\n      queryClient.invalidateQueries({ queryKey: ['usage-stats'] })\n    },\n    onError: (error) => {\n      console.error(`💥 [USE_PLANS] Plan cancellation error:`, error)\n    }\n  })\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA;AAAA;AAAA;AACA;AAHA;;;AAWO,SAAS;IACd,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAa;QACxB,SAAS;YACP,QAAQ,GAAG,CAAC,CAAC,qCAAqC,CAAC;YAEnD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,MAChB,KAAK,CAAC;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,CAAC,sCAAsC,CAAC,EAAE;gBACxD,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,QAAQ,GAAG,CAAC,CAAC,iCAAiC,CAAC,EAAE,MAAM;YACvD,OAAO,QAAQ,EAAE;QACnB;QACA,WAAW,IAAI,KAAK;IACtB;AACF;AAGO,SAAS;IACd,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAmB;QAC9B,SAAS;YACP,QAAQ,GAAG,CAAC,CAAC,2CAA2C,CAAC;YAEzD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,wBAAwB;gBACjE,WAAW,CAAC,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE;YACxD;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,CAAC,4CAA4C,CAAC,EAAE;gBAC9D,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,QAAQ,GAAG,CAAC,CAAC,uCAAuC,CAAC,EAAE,MAAM,CAAC,EAAE;YAChE,OAAO,MAAM,CAAC,EAAE,IAAI;QACtB;QACA,WAAW,IAAI,KAAK;IACtB;AACF;AAGO,SAAS;IACd,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAc;QACzB,SAAS;YACP,QAAQ,GAAG,CAAC,CAAC,sCAAsC,CAAC;YAEpD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,qBAAqB;gBAC9D,WAAW,CAAC,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE;YACxD;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,CAAC,uCAAuC,CAAC,EAAE;gBACzD,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,QAAQ,GAAG,CAAC,CAAC,kCAAkC,CAAC,EAAE,MAAM,CAAC,EAAE;YAC3D,OAAO,MAAM,CAAC,EAAE,IAAI;QACtB;QACA,WAAW,KAAK;IAClB;AACF;AAGO,SAAS;IACd,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAoB;QAC/B,SAAS;YACP,QAAQ,GAAG,CAAC,CAAC,4CAA4C,CAAC;YAE1D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,CAAC;;;;;;;;QAQT,CAAC,EACA,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,CAAC,6CAA6C,CAAC,EAAE;gBAC/D,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,QAAQ,GAAG,CAAC,CAAC,wCAAwC,CAAC,EAAE,MAAM;YAC9D,OAAO,QAAQ,EAAE;QACnB;QACA,WAAW,IAAI,KAAK;IACtB;AACF;AAGO,SAAS,cAAc,OAAe,EAAE;IAC7C,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAe;SAAK;QAC/B,SAAS;YACP,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,KAAK,QAAQ,CAAC;YAErE,MAAM,YAAY,IAAI;YACtB,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;YAExC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAC,KACP,GAAG,CAAC,aAAa,UAAU,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EACtD,KAAK,CAAC,aAAa;gBAAE,WAAW;YAAM;YAEzC,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,CAAC,uCAAuC,CAAC,EAAE;gBACzD,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,QAAQ,GAAG,CAAC,CAAC,kCAAkC,CAAC,EAAE,MAAM;YACxD,OAAO,QAAQ,EAAE;QACnB;QACA,WAAW,IAAI,KAAK;IACtB;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EACjB,QAAQ,EACR,eAAe,SAAS,EACxB,gBAAgB,EAKjB;YACC,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,UAAU;YAE1D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,oBAAoB;gBAC7D,WAAW,CAAC,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE;gBACtD,eAAe;gBACf,qBAAqB;gBACrB,yBAAyB;YAC3B;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,CAAC,iCAAiC,CAAC,EAAE;gBACnD,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,QAAQ,GAAG,CAAC,CAAC,wCAAwC,CAAC,EAAE;YACxD,OAAO;QACT;QACA,WAAW;YACT,QAAQ,GAAG,CAAC,CAAC,2DAA2D,CAAC;YACzE,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAmB;YAAC;YAC/D,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAc;YAAC;YAC1D,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAoB;YAAC;YAChE,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAc;YAAC;QAC5D;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,CAAC,iCAAiC,CAAC,EAAE;QACrD;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY;YACV,QAAQ,GAAG,CAAC,CAAC,sCAAsC,CAAC;YAEpD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,sBAAsB;gBACzD,WAAW,CAAC,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE;YACxD;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,CAAC,wCAAwC,CAAC,EAAE;gBAC1D,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,QAAQ,GAAG,CAAC,CAAC,8CAA8C,CAAC;QAC9D;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAc;YAAC;YAC1D,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAc;YAAC;QAC5D;IACF;AACF;AAGO,eAAe,eAAe,MAA0C;IAC7E,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,qBAAqB;YAC9D,WAAW,CAAC,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE;QACxD;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,iCAAiC,CAAC,EAAE;YACnD,OAAO;QACT;QAEA,MAAM,SAAS,MAAM,CAAC,EAAE;QACxB,IAAI,CAAC,QAAQ,OAAO;QAEpB,OAAQ;YACN,KAAK;gBACH,OAAO,OAAO,kBAAkB;YAClC,KAAK;gBACH,OAAO,OAAO,iBAAiB;YACjC;gBACE,OAAO;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,CAAC,EAAE;QAClD,OAAO;IACT;AACF;AAGO,SAAS,eAAe,WAAmB;IAChD,MAAM,EAAE,MAAM,UAAU,EAAE,GAAG;IAE7B,OAAO;QACL,YAAY,YAAY,UAAU,CAAC,YAAY,KAAK;QACpD,cAAc,YAAY,UAAU,CAAC,YAAY;QACjD,UAAU,YAAY;QACtB,aAAa,YAAY;IAC3B;AACF;AAGO,SAAS;IACd,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAkB;QAC7B,SAAS;YACP,QAAQ,GAAG,CAAC,CAAC,0CAA0C,CAAC;YAExD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,uBAAuB;gBAChE,WAAW,CAAC,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE;YACxD;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,CAAC,2CAA2C,CAAC,EAAE;gBAC7D,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,QAAQ,GAAG,CAAC,CAAC,sCAAsC,CAAC,EAAE,MAAM,CAAC,EAAE;YAC/D,OAAO,MAAM,CAAC,EAAE,IAAI;QACtB;QACA,WAAW,KAAK;IAClB;AACF;AAGO,SAAS;IACd,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAA2B;QACtC,SAAS;YACP,QAAQ,GAAG,CAAC,CAAC,mDAAmD,CAAC;YAEjE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,gCAAgC;gBACzE,WAAW,CAAC,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE;YACxD;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,CAAC,oDAAoD,CAAC,EAAE;gBACtE,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,QAAQ,GAAG,CAAC,CAAC,+CAA+C,CAAC,EAAE,MAAM,CAAC,EAAE;YACxE,OAAO,MAAM,CAAC,EAAE,IAAI;QACtB;QACA,WAAW,KAAK;IAClB;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EACjB,kBAAkB,EAClB,gBAAgB,KAAK,EAItB;YACC,QAAQ,GAAG,CAAC,CAAC,4CAA4C,EAAE,oBAAoB;YAE/E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,oBAAoB;gBAC7D,WAAW,CAAC,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE;gBACtD,2BAA2B;gBAC3B,gBAAgB;YAClB;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,CAAC,uCAAuC,CAAC,EAAE;gBACzD,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,QAAQ,GAAG,CAAC,CAAC,0CAA0C,CAAC,EAAE,MAAM,CAAC,EAAE;YACnE,OAAO,MAAM,CAAC,EAAE;QAClB;QACA,WAAW,CAAC;YACV,QAAQ,GAAG,CAAC,CAAC,iEAAiE,CAAC;YAC/E,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAmB;YAAC;YAC/D,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAc;YAAC;YAC1D,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAoB;YAAC;YAChE,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAkB;YAAC;YAC9D,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAA2B;YAAC;YACvE,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAc;YAAC;QAC5D;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,CAAC,uCAAuC,CAAC,EAAE;QAC3D;IACF;AACF", "debugId": null}}, {"offset": {"line": 2156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/supabase.ts"], "sourcesContent": ["// Import and re-export the browser client as the main client to avoid multiple instances\nimport { supabaseBrowser } from './supabase-browser'\nexport { supabaseBrowser as supabase } from './supabase-browser'\n\n// Session debug için\nif (typeof window !== 'undefined') {\n  supabaseBrowser.auth.onAuthStateChange((event, session) => {\n    console.log(`🔐 [SUPABASE_CLIENT] Auth state change: ${event}`, {\n      hasSession: !!session,\n      userId: session?.user?.id,\n      email: session?.user?.email,\n      expiresAt: session?.expires_at ? new Date(session.expires_at * 1000).toISOString() : null\n    })\n  })\n}\n\n// Global auth error handler\nif (typeof window !== 'undefined') {\n  supabaseBrowser.auth.onAuthStateChange((event) => {\n    if (event === 'SIGNED_OUT') {\n      // Oturum sonlandığında localStorage'ı temizle\n      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\n      if (supabaseUrl) {\n        localStorage.removeItem('sb-' + supabaseUrl.split('//')[1].split('.')[0] + '-auth-token')\n      }\n    }\n  })\n}\n\n// Database types\nexport interface Database {\n  public: {\n    Tables: {\n      projects: {\n        Row: {\n          id: string\n          user_id: string\n          name: string\n          context_text: string\n          description?: string\n          status?: string\n          tags?: string[]\n          is_public?: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          name: string\n          context_text?: string\n          description?: string\n          status?: string\n          tags?: string[]\n          is_public?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          name?: string\n          context_text?: string\n          description?: string\n          status?: string\n          tags?: string[]\n          is_public?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      prompts: {\n        Row: {\n          id: string\n          project_id: string\n          user_id: string\n          prompt_text: string\n          title: string | null\n          description: string | null\n          category: string | null\n          tags: string[]\n          order_index: number\n          is_used: boolean\n          is_favorite: boolean\n          usage_count: number\n          last_used_at: string | null\n          task_code: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          project_id: string\n          user_id: string\n          prompt_text: string\n          title?: string\n          description?: string\n          category?: string\n          tags?: string[]\n          order_index: number\n          is_used?: boolean\n          is_favorite?: boolean\n          usage_count?: number\n          last_used_at?: string\n          task_code?: string\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          project_id?: string\n          user_id?: string\n          prompt_text?: string\n          title?: string\n          description?: string\n          category?: string\n          tags?: string[]\n          order_index?: number\n          is_used?: boolean\n          is_favorite?: boolean\n          usage_count?: number\n          last_used_at?: string\n          task_code?: string\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      plan_types: {\n        Row: {\n          id: string\n          name: string\n          display_name: string\n          description: string | null\n          price_monthly: number\n          price_yearly: number\n          max_projects: number\n          max_prompts_per_project: number\n          features: Record<string, boolean | string | number>\n          is_active: boolean\n          sort_order: number\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          name: string\n          display_name: string\n          description?: string\n          price_monthly?: number\n          price_yearly?: number\n          max_projects?: number\n          max_prompts_per_project?: number\n          features?: Record<string, boolean | string | number>\n          is_active?: boolean\n          sort_order?: number\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          name?: string\n          display_name?: string\n          description?: string\n          price_monthly?: number\n          price_yearly?: number\n          max_projects?: number\n          max_prompts_per_project?: number\n          features?: Record<string, boolean | string | number>\n          is_active?: boolean\n          sort_order?: number\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      user_plans: {\n        Row: {\n          id: string\n          user_id: string\n          plan_type_id: string\n          status: string\n          billing_cycle: string\n          started_at: string\n          expires_at: string | null\n          cancelled_at: string | null\n          trial_ends_at: string | null\n          cancellation_reason: string | null\n          refund_status: string\n          refund_amount: number\n          auto_renew: boolean\n          payment_method: string | null\n          subscription_id: string | null\n          metadata: Record<string, boolean | string | number>\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          plan_type_id: string\n          status?: string\n          billing_cycle?: string\n          started_at?: string\n          expires_at?: string | null\n          cancelled_at?: string | null\n          trial_ends_at?: string | null\n          cancellation_reason?: string | null\n          refund_status?: string\n          refund_amount?: number\n          auto_renew?: boolean\n          payment_method?: string | null\n          subscription_id?: string | null\n          metadata?: Record<string, boolean | string | number>\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          plan_type_id?: string\n          status?: string\n          billing_cycle?: string\n          started_at?: string\n          expires_at?: string | null\n          cancelled_at?: string | null\n          trial_ends_at?: string | null\n          cancellation_reason?: string | null\n          refund_status?: string\n          refund_amount?: number\n          auto_renew?: boolean\n          payment_method?: string | null\n          subscription_id?: string | null\n          metadata?: Record<string, boolean | string | number>\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      usage_stats: {\n        Row: {\n          id: string\n          user_id: string\n          stat_date: string\n          projects_count: number\n          prompts_count: number\n          api_calls_count: number\n          storage_used_mb: number\n          last_activity_at: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          stat_date?: string\n          projects_count?: number\n          prompts_count?: number\n          api_calls_count?: number\n          storage_used_mb?: number\n          last_activity_at?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          stat_date?: string\n          projects_count?: number\n          prompts_count?: number\n          api_calls_count?: number\n          storage_used_mb?: number\n          last_activity_at?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      plan_transactions: {\n        Row: {\n          id: string\n          user_id: string\n          from_plan_id: string | null\n          to_plan_id: string\n          transaction_type: string\n          amount: number\n          currency: string\n          payment_status: string\n          payment_provider: string | null\n          payment_reference: string | null\n          notes: string | null\n          processed_at: string | null\n          created_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          from_plan_id?: string | null\n          to_plan_id: string\n          transaction_type: string\n          amount?: number\n          currency?: string\n          payment_status?: string\n          payment_provider?: string | null\n          payment_reference?: string | null\n          notes?: string | null\n          processed_at?: string | null\n          created_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          from_plan_id?: string | null\n          to_plan_id?: string\n          transaction_type?: string\n          amount?: number\n          currency?: string\n          payment_status?: string\n          payment_provider?: string | null\n          payment_reference?: string | null\n          notes?: string | null\n          processed_at?: string | null\n          created_at?: string\n        }\n      }\n    }\n    Functions: {\n      get_user_active_plan: {\n        Args: { user_uuid: string }\n        Returns: {\n          plan_id: string\n          plan_name: string\n          display_name: string\n          max_projects: number\n          max_prompts_per_project: number\n          features: Record<string, boolean | string | number>\n          status: string\n          expires_at: string | null\n        }[]\n      }\n      check_user_limits: {\n        Args: { user_uuid: string }\n        Returns: {\n          current_projects: number\n          current_prompts: number\n          max_projects: number\n          max_prompts_per_project: number\n          can_create_project: boolean\n          can_create_prompt: boolean\n        }[]\n      }\n      change_user_plan: {\n        Args: {\n          user_uuid: string\n          new_plan_name: string\n          billing_cycle_param?: string\n          payment_reference_param?: string\n        }\n        Returns: string\n      }\n      shared_prompts: {\n        Row: {\n          id: string\n          prompt_id: string\n          user_id: string\n          share_token: string\n          title: string | null\n          description: string | null\n          is_public: boolean\n          password_hash: string | null\n          expires_at: string | null\n          view_count: number\n          copy_count: number\n          is_active: boolean\n          metadata: any\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          prompt_id: string\n          user_id: string\n          share_token: string\n          title?: string | null\n          description?: string | null\n          is_public?: boolean\n          password_hash?: string | null\n          expires_at?: string | null\n          view_count?: number\n          copy_count?: number\n          is_active?: boolean\n          metadata?: any\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          prompt_id?: string\n          user_id?: string\n          share_token?: string\n          title?: string | null\n          description?: string | null\n          is_public?: boolean\n          password_hash?: string | null\n          expires_at?: string | null\n          view_count?: number\n          copy_count?: number\n          is_active?: boolean\n          metadata?: any\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      shared_prompt_views: {\n        Row: {\n          id: string\n          shared_prompt_id: string\n          viewer_ip: string | null\n          viewer_user_agent: string | null\n          referrer: string | null\n          viewed_at: string\n          session_id: string | null\n        }\n        Insert: {\n          id?: string\n          shared_prompt_id: string\n          viewer_ip?: string | null\n          viewer_user_agent?: string | null\n          referrer?: string | null\n          viewed_at?: string\n          session_id?: string | null\n        }\n        Update: {\n          id?: string\n          shared_prompt_id?: string\n          viewer_ip?: string | null\n          viewer_user_agent?: string | null\n          referrer?: string | null\n          viewed_at?: string\n          session_id?: string | null\n        }\n      }\n    }\n  }\n}"], "names": [], "mappings": "AAAA,yFAAyF;;AACzF;;;AAGA,qBAAqB;AACrB;;AAWA,4BAA4B;AAC5B", "debugId": null}}, {"offset": {"line": 2186, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Dialog = DialogPrimitive.Root\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger\r\n\r\nconst DialogPortal = DialogPrimitive.Portal\r\n\r\nconst DialogClose = DialogPrimitive.Close\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Kapat</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n))\r\nDialogContent.displayName = DialogPrimitive.Content.displayName\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogHeader.displayName = \"DialogHeader\"\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogFooter.displayName = \"DialogFooter\"\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogClose,\r\n  DialogTrigger,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n} "], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,kKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,kKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,qMAAA,CAAA,aAAgB,CAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,qMAAA,CAAA,aAAgB,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,qMAAA,CAAA,aAAgB,CAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,kKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2316, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2339, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,8OAAC,wMAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B", "debugId": null}}, {"offset": {"line": 2382, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-card text-card-foreground\",\n        destructive:\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Alert({\n  className,\n  variant,\n  ...props\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\n  return (\n    <div\n      data-slot=\"alert\"\n      role=\"alert\"\n      className={cn(alertVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-title\"\n      className={cn(\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AlertDescription({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-description\"\n      className={cn(\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2445, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/plan-cancellation-modal.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON>Footer, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Label } from '@/components/ui/label'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertTriangle, CheckCircle, XCircle } from 'lucide-react'\nimport { useCancelPlan, useCancellationEligibility, useUserTrialInfo } from '@/hooks/use-plans'\nimport { toast } from 'sonner'\n\ninterface PlanCancellationModalProps {\n  isOpen: boolean\n  onClose: () => void\n  onSuccess?: () => void\n}\n\nexport function PlanCancellationModal({ isOpen, onClose, onSuccess }: PlanCancellationModalProps) {\n  const [cancellationReason, setCancellationReason] = useState('')\n  const [requestRefund, setRequestRefund] = useState(false)\n  const [confirmCancellation, setConfirmCancellation] = useState(false)\n\n  const { data: eligibility, isLoading: eligibilityLoading } = useCancellationEligibility()\n  const { data: trialInfo, isLoading: trialLoading } = useUserTrialInfo()\n  const cancelPlanMutation = useCancelPlan()\n\n  const isLoading = eligibilityLoading || trialLoading || cancelPlanMutation.isPending\n\n  const handleCancel = async () => {\n    if (!confirmCancellation) {\n      toast.error('Lütfen iptal işlemini onaylayın')\n      return\n    }\n\n    if (!cancellationReason.trim()) {\n      toast.error('Lütfen iptal nedeninizi belirtin')\n      return\n    }\n\n    try {\n      const result = await cancelPlanMutation.mutateAsync({\n        cancellationReason: cancellationReason.trim(),\n        requestRefund: requestRefund && trialInfo?.is_in_trial\n      })\n\n      if (result?.success) {\n        toast.success(result.message || 'Plan başarıyla iptal edildi')\n        onSuccess?.()\n        onClose()\n        \n        // Reset form\n        setCancellationReason('')\n        setRequestRefund(false)\n        setConfirmCancellation(false)\n      } else {\n        toast.error(result?.message || 'Plan iptal edilemedi')\n      }\n    } catch (error) {\n      console.error('Plan cancellation error:', error)\n      toast.error('Plan iptal edilirken bir hata oluştu')\n    }\n  }\n\n  const handleClose = () => {\n    if (cancelPlanMutation.isPending) return\n    \n    setCancellationReason('')\n    setRequestRefund(false)\n    setConfirmCancellation(false)\n    onClose()\n  }\n\n  if (!eligibility?.can_cancel) {\n    return (\n      <Dialog open={isOpen} onOpenChange={handleClose}>\n        <DialogContent className=\"sm:max-w-md\">\n          <DialogHeader>\n            <DialogTitle className=\"flex items-center gap-2\">\n              <XCircle className=\"h-5 w-5 text-red-500\" />\n              Plan İptal Edilemiyor\n            </DialogTitle>\n            <DialogDescription>\n              {eligibility?.reason || 'Bu plan iptal edilemez.'}\n            </DialogDescription>\n          </DialogHeader>\n          <DialogFooter>\n            <Button onClick={handleClose} variant=\"outline\">\n              Kapat\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n    )\n  }\n\n  return (\n    <Dialog open={isOpen} onOpenChange={handleClose}>\n      <DialogContent className=\"sm:max-w-lg\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <AlertTriangle className=\"h-5 w-5 text-orange-500\" />\n            Plan İptal Et\n          </DialogTitle>\n          <DialogDescription>\n            {eligibility?.plan_display_name} planınızı iptal etmek üzeresiniz. Bu işlem geri alınamaz.\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          {/* Trial Information */}\n          {trialInfo?.is_in_trial && (\n            <Alert>\n              <CheckCircle className=\"h-4 w-4\" />\n              <AlertDescription>\n                Deneme süreniz devam ediyor ({trialInfo.days_remaining} gün kaldı). \n                İptal ederseniz tam iade alabilirsiniz.\n              </AlertDescription>\n            </Alert>\n          )}\n\n          {/* Refund Information */}\n          {eligibility?.refund_eligible && (\n            <Alert>\n              <CheckCircle className=\"h-4 w-4\" />\n              <AlertDescription>\n                Deneme süresi içinde olduğunuz için {eligibility.estimated_refund_amount} TL tam iade alabilirsiniz.\n              </AlertDescription>\n            </Alert>\n          )}\n\n          {/* Cancellation Reason */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"cancellation-reason\">\n              İptal Nedeni <span className=\"text-red-500\">*</span>\n            </Label>\n            <Textarea\n              id=\"cancellation-reason\"\n              placeholder=\"Planınızı neden iptal etmek istiyorsunuz? Geri bildiriminiz bizim için değerli.\"\n              value={cancellationReason}\n              onChange={(e) => setCancellationReason(e.target.value)}\n              rows={3}\n              maxLength={500}\n              disabled={isLoading}\n            />\n            <p className=\"text-xs text-muted-foreground\">\n              {cancellationReason.length}/500 karakter\n            </p>\n          </div>\n\n          {/* Refund Request */}\n          {eligibility?.refund_eligible && (\n            <div className=\"flex items-center space-x-2\">\n              <Checkbox\n                id=\"request-refund\"\n                checked={requestRefund}\n                onCheckedChange={(checked) => setRequestRefund(checked as boolean)}\n                disabled={isLoading}\n              />\n              <Label htmlFor=\"request-refund\" className=\"text-sm\">\n                İade talep ediyorum ({eligibility.estimated_refund_amount} TL)\n              </Label>\n            </div>\n          )}\n\n          {/* Confirmation */}\n          <div className=\"flex items-center space-x-2\">\n            <Checkbox\n              id=\"confirm-cancellation\"\n              checked={confirmCancellation}\n              onCheckedChange={(checked) => setConfirmCancellation(checked as boolean)}\n              disabled={isLoading}\n            />\n            <Label htmlFor=\"confirm-cancellation\" className=\"text-sm\">\n              Plan iptal işlemini onaylıyorum ve bu işlemin geri alınamayacağını anlıyorum\n            </Label>\n          </div>\n\n          {/* Warning */}\n          <Alert>\n            <AlertTriangle className=\"h-4 w-4\" />\n            <AlertDescription>\n              Plan iptal edildikten sonra ücretsiz plana geçeceksiniz. \n              Mevcut projeleriniz ve promptlarınız korunacak ancak plan limitleri geçerli olacak.\n            </AlertDescription>\n          </Alert>\n        </div>\n\n        <DialogFooter className=\"gap-2\">\n          <Button \n            onClick={handleClose} \n            variant=\"outline\"\n            disabled={isLoading}\n          >\n            Vazgeç\n          </Button>\n          <Button \n            onClick={handleCancel}\n            variant=\"destructive\"\n            disabled={isLoading || !confirmCancellation || !cancellationReason.trim()}\n          >\n            {isLoading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n            Planı İptal Et\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAXA;;;;;;;;;;;;AAmBO,SAAS,sBAAsB,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAA8B;IAC9F,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,MAAM,EAAE,MAAM,WAAW,EAAE,WAAW,kBAAkB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,6BAA0B,AAAD;IACtF,MAAM,EAAE,MAAM,SAAS,EAAE,WAAW,YAAY,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD;IACpE,MAAM,qBAAqB,CAAA,GAAA,4HAAA,CAAA,gBAAa,AAAD;IAEvC,MAAM,YAAY,sBAAsB,gBAAgB,mBAAmB,SAAS;IAEpF,MAAM,eAAe;QACnB,IAAI,CAAC,qBAAqB;YACxB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,mBAAmB,IAAI,IAAI;YAC9B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,mBAAmB,WAAW,CAAC;gBAClD,oBAAoB,mBAAmB,IAAI;gBAC3C,eAAe,iBAAiB,WAAW;YAC7C;YAEA,IAAI,QAAQ,SAAS;gBACnB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO,IAAI;gBAChC;gBACA;gBAEA,aAAa;gBACb,sBAAsB;gBACtB,iBAAiB;gBACjB,uBAAuB;YACzB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,QAAQ,WAAW;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,mBAAmB,SAAS,EAAE;QAElC,sBAAsB;QACtB,iBAAiB;QACjB,uBAAuB;QACvB;IACF;IAEA,IAAI,CAAC,aAAa,YAAY;QAC5B,qBACE,8OAAC,kIAAA,CAAA,SAAM;YAAC,MAAM;YAAQ,cAAc;sBAClC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACvB,8OAAC,kIAAA,CAAA,eAAY;;0CACX,8OAAC,kIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,4MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAyB;;;;;;;0CAG9C,8OAAC,kIAAA,CAAA,oBAAiB;0CACf,aAAa,UAAU;;;;;;;;;;;;kCAG5B,8OAAC,kIAAA,CAAA,eAAY;kCACX,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAa,SAAQ;sCAAU;;;;;;;;;;;;;;;;;;;;;;IAO1D;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAA4B;;;;;;;sCAGvD,8OAAC,kIAAA,CAAA,oBAAiB;;gCACf,aAAa;gCAAkB;;;;;;;;;;;;;8BAIpC,8OAAC;oBAAI,WAAU;;wBAEZ,WAAW,6BACV,8OAAC,iIAAA,CAAA,QAAK;;8CACJ,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;;wCAAC;wCACc,UAAU,cAAc;wCAAC;;;;;;;;;;;;;wBAO5D,aAAa,iCACZ,8OAAC,iIAAA,CAAA,QAAK;;8CACJ,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;;wCAAC;wCACqB,YAAY,uBAAuB;wCAAC;;;;;;;;;;;;;sCAM/E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;;wCAAsB;sDACtB,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAE9C,8OAAC,oIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,sBAAsB,EAAE,MAAM,CAAC,KAAK;oCACrD,MAAM;oCACN,WAAW;oCACX,UAAU;;;;;;8CAEZ,8OAAC;oCAAE,WAAU;;wCACV,mBAAmB,MAAM;wCAAC;;;;;;;;;;;;;wBAK9B,aAAa,iCACZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,SAAS;oCACT,iBAAiB,CAAC,UAAY,iBAAiB;oCAC/C,UAAU;;;;;;8CAEZ,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAiB,WAAU;;wCAAU;wCAC5B,YAAY,uBAAuB;wCAAC;;;;;;;;;;;;;sCAMhE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,SAAS;oCACT,iBAAiB,CAAC,UAAY,uBAAuB;oCACrD,UAAU;;;;;;8CAEZ,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAuB,WAAU;8CAAU;;;;;;;;;;;;sCAM5D,8OAAC,iIAAA,CAAA,QAAK;;8CACJ,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAC;;;;;;;;;;;;;;;;;;8BAOtB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,SAAQ;4BACR,UAAU;sCACX;;;;;;sCAGD,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,SAAQ;4BACR,UAAU,aAAa,CAAC,uBAAuB,CAAC,mBAAmB,IAAI;;gCAEtE,2BAAa,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAA+B;;;;;;;;;;;;;;;;;;;;;;;;AAO5E", "debugId": null}}, {"offset": {"line": 2874, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/app/profile/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Badge } from '@/components/ui/badge'\nimport { Separator } from '@/components/ui/separator'\nimport { AuthGuard } from '@/components/auth-guard'\nimport { useUser, useSignOut } from '@/hooks/use-auth'\nimport { useProjects } from '@/hooks/use-projects'\nimport { useUserActivePlan, usePlanTypes, useChangePlan, useCancellationEligibility, useUserTrialInfo } from '@/hooks/use-plans'\nimport { supabase } from '@/lib/supabase'\nimport { ArrowLeft, User, Mail, Calendar, Shield, Key, LogOut, AlertCircle, CheckCircle, Crown, Star, XCircle } from 'lucide-react'\nimport Link from 'next/link'\nimport { PlanCancellationModal } from '@/components/plan-cancellation-modal'\n\nexport default function ProfilePage() {\n  const [newPassword, setNewPassword] = useState('')\n  const [confirmPassword, setConfirmPassword] = useState('')\n  const [isChangingPassword, setIsChangingPassword] = useState(false)\n  const [passwordMessage, setPasswordMessage] = useState('')\n  const [messageType, setMessageType] = useState<'success' | 'error' | null>(null)\n  const [showCancellationModal, setShowCancellationModal] = useState(false)\n\n  const { data: user } = useUser()\n  const { data: projects = [] } = useProjects()\n  const { data: activePlan } = useUserActivePlan()\n  const { data: planTypes = [] } = usePlanTypes()\n  const { data: cancellationEligibility } = useCancellationEligibility()\n  const { data: trialInfo } = useUserTrialInfo()\n  const changePlanMutation = useChangePlan()\n  const signOutMutation = useSignOut()\n\n  const handlePasswordChange = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setPasswordMessage('')\n    setMessageType(null)\n\n    if (newPassword !== confirmPassword) {\n      setPasswordMessage('Yeni şifreler eşleşmiyor!')\n      setMessageType('error')\n      return\n    }\n\n    if (newPassword.length < 6) {\n      setPasswordMessage('Yeni şifre en az 6 karakter olmalı!')\n      setMessageType('error')\n      return\n    }\n\n    setIsChangingPassword(true)\n\n    try {\n      const { error } = await supabase.auth.updateUser({\n        password: newPassword\n      })\n\n      if (error) {\n        throw error\n      }\n\n      setPasswordMessage('Şifre başarıyla güncellendi!')\n      setMessageType('success')\n      setNewPassword('')\n      setConfirmPassword('')\n    } catch (error) {\n      setPasswordMessage(error instanceof Error ? error.message : 'Şifre güncellenirken bir hata oluştu')\n      setMessageType('error')\n    } finally {\n      setIsChangingPassword(false)\n    }\n  }\n\n  const handleSignOut = () => {\n    signOutMutation.mutate()\n  }\n\n  const handlePlanChange = async (planName: string) => {\n    try {\n      // Validation checks before plan change\n      const targetPlan = planTypes.find(p => p.name === planName)\n      if (!targetPlan) {\n        throw new Error('Geçersiz plan seçimi')\n      }\n\n      // Check if downgrading and validate current usage\n      if (activePlan && targetPlan.max_projects < activePlan.max_projects) {\n        if (projects.length > targetPlan.max_projects) {\n          throw new Error(\n            `Bu plana geçmek için önce proje sayınızı ${targetPlan.max_projects}'e düşürmeniz gerekiyor. ` +\n            `Şu anda ${projects.length} projeniz var.`\n          )\n        }\n      }\n\n      // Confirm downgrade if applicable\n      if (activePlan && targetPlan.price_monthly < activePlan.price_monthly) {\n        const confirmed = window.confirm(\n          `${targetPlan.display_name} planına geçmek istediğinizden emin misiniz? ` +\n          'Bu işlem bazı özelliklerinizi kısıtlayabilir.'\n        )\n        if (!confirmed) return\n      }\n\n      await changePlanMutation.mutateAsync({\n        planName,\n        billingCycle: 'monthly'\n      })\n\n      setPasswordMessage('Plan başarıyla değiştirildi!')\n      setMessageType('success')\n\n      // Clear message after 5 seconds\n      setTimeout(() => {\n        setPasswordMessage('')\n        setMessageType(null)\n      }, 5000)\n\n    } catch (error) {\n      setPasswordMessage(error instanceof Error ? error.message : 'Plan değiştirilemedi')\n      setMessageType('error')\n\n      // Clear error message after 8 seconds\n      setTimeout(() => {\n        setPasswordMessage('')\n        setMessageType(null)\n      }, 8000)\n    }\n  }\n\n  const getPlanIcon = (planName: string) => {\n    switch (planName) {\n      case 'free': return <User className=\"h-5 w-5\" />\n      case 'professional': return <Crown className=\"h-5 w-5\" />\n      case 'enterprise': return <Star className=\"h-5 w-5\" />\n      default: return <Shield className=\"h-5 w-5\" />\n    }\n  }\n\n  const getPlanColor = (planName: string) => {\n    switch (planName) {\n      case 'free': return 'bg-gray-100 text-gray-800'\n      case 'professional': return 'bg-blue-100 text-blue-800'\n      case 'enterprise': return 'bg-purple-100 text-purple-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  if (!user) {\n    return null\n  }\n\n  return (\n    <AuthGuard>\n      <div className=\"min-h-screen bg-gray-50\">\n        <div className=\"max-w-4xl mx-auto p-6\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between mb-8\">\n            <div className=\"flex items-center gap-4\">\n              <Link href=\"/\">\n                <Button variant=\"outline\" size=\"sm\">\n                  <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                  Ana Sayfaya Dön\n                </Button>\n              </Link>\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900\">Profil Ayarları</h1>\n                <p className=\"text-gray-600\">Hesap bilgilerinizi yönetin</p>\n              </div>\n            </div>\n            <Button\n              variant=\"outline\"\n              onClick={handleSignOut}\n              className=\"text-red-600 hover:text-red-700\"\n            >\n              <LogOut className=\"h-4 w-4 mr-2\" />\n              Çıkış Yap\n            </Button>\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n            {/* Profil Bilgileri */}\n            <div className=\"lg:col-span-2 space-y-6\">\n              {/* Temel Bilgiler */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2\">\n                    <User className=\"h-5 w-5\" />\n                    Temel Bilgiler\n                  </CardTitle>\n                  <CardDescription>\n                    Hesap bilgilerinizi görüntüleyin\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <div>\n                      <Label htmlFor=\"email\">E-posta Adresi</Label>\n                      <div className=\"flex items-center gap-2 mt-1\">\n                        <Mail className=\"h-4 w-4 text-gray-400\" />\n                        <span className=\"text-gray-900\">{user.email}</span>\n                      </div>\n                    </div>\n                    <div>\n                      <Label>Kayıt Tarihi</Label>\n                      <div className=\"flex items-center gap-2 mt-1\">\n                        <Calendar className=\"h-4 w-4 text-gray-400\" />\n                        <span className=\"text-gray-900\">\n                          {new Date(user.created_at).toLocaleDateString('tr-TR')}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                  <div>\n                    <Label>Hesap Durumu</Label>\n                    <div className=\"flex items-center gap-2 mt-1\">\n                      <Shield className=\"h-4 w-4 text-green-500\" />\n                      <Badge variant=\"secondary\" className=\"bg-green-100 text-green-800\">\n                        Aktif\n                      </Badge>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Şifre Değiştirme */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2\">\n                    <Key className=\"h-5 w-5\" />\n                    Şifre Değiştir\n                  </CardTitle>\n                  <CardDescription>\n                    Güvenliğiniz için düzenli olarak şifrenizi değiştirin\n                  </CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <form onSubmit={handlePasswordChange} className=\"space-y-4\">\n                    <div>\n                      <Label htmlFor=\"newPassword\">Yeni Şifre</Label>\n                      <Input\n                        id=\"newPassword\"\n                        type=\"password\"\n                        placeholder=\"Yeni şifrenizi girin\"\n                        value={newPassword}\n                        onChange={(e) => setNewPassword(e.target.value)}\n                        disabled={isChangingPassword}\n                        autoComplete=\"new-password\"\n                        required\n                      />\n                    </div>\n                    <div>\n                      <Label htmlFor=\"confirmPassword\">Yeni Şifre (Tekrar)</Label>\n                      <Input\n                        id=\"confirmPassword\"\n                        type=\"password\"\n                        placeholder=\"Yeni şifrenizi tekrar girin\"\n                        value={confirmPassword}\n                        onChange={(e) => setConfirmPassword(e.target.value)}\n                        disabled={isChangingPassword}\n                        autoComplete=\"new-password\"\n                        required\n                      />\n                    </div>\n\n                    {passwordMessage && (\n                      <div className={`flex items-center gap-2 text-sm p-3 rounded-md ${\n                        messageType === 'success' \n                          ? 'bg-green-50 text-green-700' \n                          : 'bg-red-50 text-red-700'\n                      }`}>\n                        {messageType === 'success' ? (\n                          <CheckCircle className=\"h-4 w-4\" />\n                        ) : (\n                          <AlertCircle className=\"h-4 w-4\" />\n                        )}\n                        {passwordMessage}\n                      </div>\n                    )}\n\n                    <Button\n                      type=\"submit\"\n                      disabled={isChangingPassword || !newPassword || !confirmPassword}\n                      className=\"w-full\"\n                    >\n                      {isChangingPassword ? 'Güncelleniyor...' : 'Şifreyi Güncelle'}\n                    </Button>\n                  </form>\n                </CardContent>\n              </Card>\n            </div>\n\n            {/* Yan Panel - İstatistikler */}\n            <div className=\"space-y-6\">\n              {/* Hesap İstatistikleri */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-lg\">Hesap İstatistikleri</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-gray-600\">Toplam Proje</span>\n                    <Badge variant=\"outline\">{projects.length}</Badge>\n                  </div>\n                  <Separator />\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-gray-600\">Hesap Tipi</span>\n                    <Badge variant=\"secondary\" className={getPlanColor(activePlan?.plan_name || 'free')}>\n                      {activePlan?.display_name || 'Ücretsiz'}\n                    </Badge>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-gray-600\">Son Giriş</span>\n                    <span className=\"text-sm text-gray-900\">\n                      {new Date(user.last_sign_in_at || user.created_at).toLocaleDateString('tr-TR')}\n                    </span>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Plan Yönetimi */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2\">\n                    <Crown className=\"h-5 w-5 text-blue-600\" />\n                    Plan Yönetimi\n                  </CardTitle>\n                  <CardDescription>\n                    Mevcut planınızı görüntüleyin ve değiştirin\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  {/* Mevcut Plan */}\n                  <div className=\"p-4 bg-blue-50 rounded-lg border border-blue-200\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <div className=\"flex items-center gap-2\">\n                        {getPlanIcon(activePlan?.plan_name || 'free')}\n                        <span className=\"font-medium text-blue-900\">\n                          {activePlan?.display_name || 'Ücretsiz Plan'}\n                        </span>\n                      </div>\n                      <Badge className={getPlanColor(activePlan?.plan_name || 'free')}>\n                        Aktif\n                      </Badge>\n                    </div>\n                    <div className=\"text-sm text-blue-700 space-y-1\">\n                      <div>• {activePlan?.max_projects || 5} proje limiti</div>\n                      <div>• {activePlan?.max_prompts_per_project || 100} prompt/proje limiti</div>\n                      {activePlan?.expires_at && (\n                        <div>• Bitiş: {new Date(activePlan.expires_at).toLocaleDateString('tr-TR')}</div>\n                      )}\n                    </div>\n                  </div>\n\n                  {/* Plan Seçenekleri */}\n                  <div className=\"space-y-3\">\n                    <Label className=\"text-sm font-medium\">Mevcut Planlar</Label>\n                    {planTypes.map((plan) => (\n                      <div\n                        key={plan.id}\n                        className={`p-3 border rounded-lg transition-all ${\n                          plan.name === activePlan?.plan_name\n                            ? 'border-blue-500 bg-blue-50'\n                            : 'border-gray-200 hover:border-gray-300'\n                        }`}\n                      >\n                        <div className=\"flex items-center justify-between\">\n                          <div className=\"flex items-center gap-3\">\n                            {getPlanIcon(plan.name)}\n                            <div>\n                              <div className=\"font-medium text-sm\">{plan.display_name}</div>\n                              <div className=\"text-xs text-gray-600\">\n                                {plan.max_projects === -1 ? 'Sınırsız' : plan.max_projects} proje, {' '}\n                                {plan.max_prompts_per_project === -1 ? 'Sınırsız' : plan.max_prompts_per_project} prompt/proje\n                              </div>\n                            </div>\n                          </div>\n                          <div className=\"flex items-center gap-2\">\n                            <span className=\"text-sm font-medium\">\n                              {plan.price_monthly === 0 ? 'Ücretsiz' : `₺${plan.price_monthly}/ay`}\n                            </span>\n                            {plan.name !== activePlan?.plan_name && (\n                              <Button\n                                size=\"sm\"\n                                variant={plan.name === 'free' ? 'outline' : 'default'}\n                                onClick={() => handlePlanChange(plan.name)}\n                                disabled={changePlanMutation.isPending}\n                                className=\"ml-2\"\n                              >\n                                {changePlanMutation.isPending ? 'Değiştiriliyor...' : 'Seç'}\n                              </Button>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n\n                  {/* Trial Information */}\n                  {trialInfo?.is_in_trial && (\n                    <div className=\"p-3 bg-green-50 border border-green-200 rounded-lg\">\n                      <div className=\"flex items-center gap-2 mb-1\">\n                        <CheckCircle className=\"h-4 w-4 text-green-600\" />\n                        <span className=\"text-sm font-medium text-green-800\">Deneme Süresi Aktif</span>\n                      </div>\n                      <p className=\"text-xs text-green-700\">\n                        {trialInfo.days_remaining} gün deneme süreniz kaldı.\n                        Bu süre içinde planınızı iptal ederseniz tam iade alabilirsiniz.\n                      </p>\n                    </div>\n                  )}\n\n                  {/* Plan Cancellation */}\n                  {cancellationEligibility?.can_cancel && activePlan?.plan_name !== 'free' && (\n                    <div className=\"pt-4 border-t border-gray-200\">\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <h4 className=\"text-sm font-medium text-gray-900\">Plan İptal</h4>\n                          <p className=\"text-xs text-gray-600\">\n                            {trialInfo?.is_in_trial\n                              ? 'Deneme süresi içinde tam iade alabilirsiniz'\n                              : 'Planınızı istediğiniz zaman iptal edebilirsiniz'\n                            }\n                          </p>\n                        </div>\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => setShowCancellationModal(true)}\n                          className=\"text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300\"\n                        >\n                          <XCircle className=\"h-4 w-4 mr-1\" />\n                          Planı İptal Et\n                        </Button>\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Plan Değişiklik Mesajı */}\n                  {passwordMessage && messageType && (\n                    <div className={`p-3 rounded-lg flex items-center gap-2 ${\n                      messageType === 'success'\n                        ? 'bg-green-50 text-green-800 border border-green-200'\n                        : 'bg-red-50 text-red-800 border border-red-200'\n                    }`}>\n                      {messageType === 'success' ? (\n                        <CheckCircle className=\"h-4 w-4\" />\n                      ) : (\n                        <AlertCircle className=\"h-4 w-4\" />\n                      )}\n                      <span className=\"text-sm\">{passwordMessage}</span>\n                    </div>\n                  )}\n                </CardContent>\n              </Card>\n\n              {/* Güvenlik Önerileri */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-lg\">Güvenlik Önerileri</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-3\">\n                  <div className=\"flex items-start gap-3\">\n                    <Shield className=\"h-4 w-4 text-blue-500 mt-0.5\" />\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-900\">Güçlü Şifre</p>\n                      <p className=\"text-xs text-gray-600\">En az 8 karakter, büyük/küçük harf ve rakam kullanın</p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-start gap-3\">\n                    <Shield className=\"h-4 w-4 text-blue-500 mt-0.5\" />\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-900\">Düzenli Güncelleme</p>\n                      <p className=\"text-xs text-gray-600\">Şifrenizi 3-6 ayda bir değiştirin</p>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Plan Cancellation Modal */}\n      <PlanCancellationModal\n        isOpen={showCancellationModal}\n        onClose={() => setShowCancellationModal(false)}\n        onSuccess={() => {\n          setPasswordMessage('Plan başarıyla iptal edildi. Ücretsiz plana geçtiniz.')\n          setMessageType('success')\n        }}\n      />\n    </AuthGuard>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAhBA;;;;;;;;;;;;;;;;;AAkBe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B;IAC3E,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAC7B,MAAM,EAAE,MAAM,WAAW,EAAE,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD;IAC1C,MAAM,EAAE,MAAM,UAAU,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD;IAC7C,MAAM,EAAE,MAAM,YAAY,EAAE,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAC5C,MAAM,EAAE,MAAM,uBAAuB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,6BAA0B,AAAD;IACnE,MAAM,EAAE,MAAM,SAAS,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD;IAC3C,MAAM,qBAAqB,CAAA,GAAA,4HAAA,CAAA,gBAAa,AAAD;IACvC,MAAM,kBAAkB,CAAA,GAAA,2HAAA,CAAA,aAAU,AAAD;IAEjC,MAAM,uBAAuB,OAAO;QAClC,EAAE,cAAc;QAChB,mBAAmB;QACnB,eAAe;QAEf,IAAI,gBAAgB,iBAAiB;YACnC,mBAAmB;YACnB,eAAe;YACf;QACF;QAEA,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,mBAAmB;YACnB,eAAe;YACf;QACF;QAEA,sBAAsB;QAEtB,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,gLAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU,CAAC;gBAC/C,UAAU;YACZ;YAEA,IAAI,OAAO;gBACT,MAAM;YACR;YAEA,mBAAmB;YACnB,eAAe;YACf,eAAe;YACf,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,mBAAmB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC5D,eAAe;QACjB,SAAU;YACR,sBAAsB;QACxB;IACF;IAEA,MAAM,gBAAgB;QACpB,gBAAgB,MAAM;IACxB;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,uCAAuC;YACvC,MAAM,aAAa,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;YAClD,IAAI,CAAC,YAAY;gBACf,MAAM,IAAI,MAAM;YAClB;YAEA,kDAAkD;YAClD,IAAI,cAAc,WAAW,YAAY,GAAG,WAAW,YAAY,EAAE;gBACnE,IAAI,SAAS,MAAM,GAAG,WAAW,YAAY,EAAE;oBAC7C,MAAM,IAAI,MACR,CAAC,yCAAyC,EAAE,WAAW,YAAY,CAAC,yBAAyB,CAAC,GAC9F,CAAC,QAAQ,EAAE,SAAS,MAAM,CAAC,cAAc,CAAC;gBAE9C;YACF;YAEA,kCAAkC;YAClC,IAAI,cAAc,WAAW,aAAa,GAAG,WAAW,aAAa,EAAE;gBACrE,MAAM,YAAY,OAAO,OAAO,CAC9B,GAAG,WAAW,YAAY,CAAC,6CAA6C,CAAC,GACzE;gBAEF,IAAI,CAAC,WAAW;YAClB;YAEA,MAAM,mBAAmB,WAAW,CAAC;gBACnC;gBACA,cAAc;YAChB;YAEA,mBAAmB;YACnB,eAAe;YAEf,gCAAgC;YAChC,WAAW;gBACT,mBAAmB;gBACnB,eAAe;YACjB,GAAG;QAEL,EAAE,OAAO,OAAO;YACd,mBAAmB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC5D,eAAe;YAEf,sCAAsC;YACtC,WAAW;gBACT,mBAAmB;gBACnB,eAAe;YACjB,GAAG;QACL;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBAAQ,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACpC,KAAK;gBAAgB,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC7C,KAAK;gBAAc,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YAC1C;gBAAS,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;QACpC;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAc,OAAO;YAC1B;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC,mIAAA,CAAA,YAAS;;0BACR,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;;kEAC7B,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;sDAI1C,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;8CAGjC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAKvC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;;sEACT,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAY;;;;;;;sEAG9B,8OAAC,gIAAA,CAAA,kBAAe;sEAAC;;;;;;;;;;;;8DAInB,8OAAC,gIAAA,CAAA,cAAW;oDAAC,WAAU;;sEACrB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;sFAAQ;;;;;;sFACvB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,kMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;8FAChB,8OAAC;oFAAK,WAAU;8FAAiB,KAAK,KAAK;;;;;;;;;;;;;;;;;;8EAG/C,8OAAC;;sFACC,8OAAC,iIAAA,CAAA,QAAK;sFAAC;;;;;;sFACP,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0MAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;8FACpB,8OAAC;oFAAK,WAAU;8FACb,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;sEAKtD,8OAAC;;8EACC,8OAAC,iIAAA,CAAA,QAAK;8EAAC;;;;;;8EACP,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,sMAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;sFAClB,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAY,WAAU;sFAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAS3E,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;;sEACT,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEAAY;;;;;;;sEAG7B,8OAAC,gIAAA,CAAA,kBAAe;sEAAC;;;;;;;;;;;;8DAInB,8OAAC,gIAAA,CAAA,cAAW;8DACV,cAAA,8OAAC;wDAAK,UAAU;wDAAsB,WAAU;;0EAC9C,8OAAC;;kFACC,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAc;;;;;;kFAC7B,8OAAC,iIAAA,CAAA,QAAK;wEACJ,IAAG;wEACH,MAAK;wEACL,aAAY;wEACZ,OAAO;wEACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wEAC9C,UAAU;wEACV,cAAa;wEACb,QAAQ;;;;;;;;;;;;0EAGZ,8OAAC;;kFACC,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAkB;;;;;;kFACjC,8OAAC,iIAAA,CAAA,QAAK;wEACJ,IAAG;wEACH,MAAK;wEACL,aAAY;wEACZ,OAAO;wEACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wEAClD,UAAU;wEACV,cAAa;wEACb,QAAQ;;;;;;;;;;;;4DAIX,iCACC,8OAAC;gEAAI,WAAW,CAAC,+CAA+C,EAC9D,gBAAgB,YACZ,+BACA,0BACJ;;oEACC,gBAAgB,0BACf,8OAAC,2NAAA,CAAA,cAAW;wEAAC,WAAU;;;;;6FAEvB,8OAAC,oNAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;oEAExB;;;;;;;0EAIL,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,UAAU,sBAAsB,CAAC,eAAe,CAAC;gEACjD,WAAU;0EAET,qBAAqB,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQrD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;8DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAU;;;;;;;;;;;8DAEjC,8OAAC,gIAAA,CAAA,cAAW;oDAAC,WAAU;;sEACrB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAW,SAAS,MAAM;;;;;;;;;;;;sEAE3C,8OAAC,qIAAA,CAAA,YAAS;;;;;sEACV,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAY,WAAW,aAAa,YAAY,aAAa;8EACzE,YAAY,gBAAgB;;;;;;;;;;;;sEAGjC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,8OAAC;oEAAK,WAAU;8EACb,IAAI,KAAK,KAAK,eAAe,IAAI,KAAK,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;sDAO9E,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;;sEACT,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAA0B;;;;;;;sEAG7C,8OAAC,gIAAA,CAAA,kBAAe;sEAAC;;;;;;;;;;;;8DAInB,8OAAC,gIAAA,CAAA,cAAW;oDAAC,WAAU;;sEAErB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;gFACZ,YAAY,YAAY,aAAa;8FACtC,8OAAC;oFAAK,WAAU;8FACb,YAAY,gBAAgB;;;;;;;;;;;;sFAGjC,8OAAC,iIAAA,CAAA,QAAK;4EAAC,WAAW,aAAa,YAAY,aAAa;sFAAS;;;;;;;;;;;;8EAInE,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;;gFAAI;gFAAG,YAAY,gBAAgB;gFAAE;;;;;;;sFACtC,8OAAC;;gFAAI;gFAAG,YAAY,2BAA2B;gFAAI;;;;;;;wEAClD,YAAY,4BACX,8OAAC;;gFAAI;gFAAU,IAAI,KAAK,WAAW,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;sEAMxE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEAAC,WAAU;8EAAsB;;;;;;gEACtC,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC;wEAEC,WAAW,CAAC,qCAAqC,EAC/C,KAAK,IAAI,KAAK,YAAY,YACtB,+BACA,yCACJ;kFAEF,cAAA,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;wFACZ,YAAY,KAAK,IAAI;sGACtB,8OAAC;;8GACC,8OAAC;oGAAI,WAAU;8GAAuB,KAAK,YAAY;;;;;;8GACvD,8OAAC;oGAAI,WAAU;;wGACZ,KAAK,YAAY,KAAK,CAAC,IAAI,aAAa,KAAK,YAAY;wGAAC;wGAAS;wGACnE,KAAK,uBAAuB,KAAK,CAAC,IAAI,aAAa,KAAK,uBAAuB;wGAAC;;;;;;;;;;;;;;;;;;;8FAIvF,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAK,WAAU;sGACb,KAAK,aAAa,KAAK,IAAI,aAAa,CAAC,CAAC,EAAE,KAAK,aAAa,CAAC,GAAG,CAAC;;;;;;wFAErE,KAAK,IAAI,KAAK,YAAY,2BACzB,8OAAC,kIAAA,CAAA,SAAM;4FACL,MAAK;4FACL,SAAS,KAAK,IAAI,KAAK,SAAS,YAAY;4FAC5C,SAAS,IAAM,iBAAiB,KAAK,IAAI;4FACzC,UAAU,mBAAmB,SAAS;4FACtC,WAAU;sGAET,mBAAmB,SAAS,GAAG,sBAAsB;;;;;;;;;;;;;;;;;;uEA9BzD,KAAK,EAAE;;;;;;;;;;;wDAwCjB,WAAW,6BACV,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;sFACvB,8OAAC;4EAAK,WAAU;sFAAqC;;;;;;;;;;;;8EAEvD,8OAAC;oEAAE,WAAU;;wEACV,UAAU,cAAc;wEAAC;;;;;;;;;;;;;wDAO/B,yBAAyB,cAAc,YAAY,cAAc,wBAChE,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAoC;;;;;;0FAClD,8OAAC;gFAAE,WAAU;0FACV,WAAW,cACR,gDACA;;;;;;;;;;;;kFAIR,8OAAC,kIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,SAAS,IAAM,yBAAyB;wEACxC,WAAU;;0FAEV,8OAAC,4MAAA,CAAA,UAAO;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;;;;;;wDAQ3C,mBAAmB,6BAClB,8OAAC;4DAAI,WAAW,CAAC,uCAAuC,EACtD,gBAAgB,YACZ,uDACA,gDACJ;;gEACC,gBAAgB,0BACf,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;yFAEvB,8OAAC,oNAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;8EAEzB,8OAAC;oEAAK,WAAU;8EAAW;;;;;;;;;;;;;;;;;;;;;;;;sDAOnC,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;8DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAU;;;;;;;;;;;8DAEjC,8OAAC,gIAAA,CAAA,cAAW;oDAAC,WAAU;;sEACrB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,8OAAC;;sFACC,8OAAC;4EAAE,WAAU;sFAAoC;;;;;;sFACjD,8OAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;sEAGzC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,8OAAC;;sFACC,8OAAC;4EAAE,WAAU;sFAAoC;;;;;;sFACjD,8OAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWrD,8OAAC,mJAAA,CAAA,wBAAqB;gBACpB,QAAQ;gBACR,SAAS,IAAM,yBAAyB;gBACxC,WAAW;oBACT,mBAAmB;oBACnB,eAAe;gBACjB;;;;;;;;;;;;AAIR", "debugId": null}}]}
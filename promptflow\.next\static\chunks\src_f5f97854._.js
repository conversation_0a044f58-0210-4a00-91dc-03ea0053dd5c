(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/cache-strategies.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Advanced Caching Strategies
 * Multi-layer caching for optimal performance
 */ __turbopack_context__.s({
    "BrowserCache": ()=>BrowserCache,
    "CACHE_CONFIGS": ()=>CACHE_CONFIGS,
    "CacheInvalidation": ()=>CacheInvalidation,
    "CachePerformance": ()=>CachePerformance,
    "OptimisticUpdates": ()=>OptimisticUpdates,
    "ServiceWorkerCache": ()=>ServiceWorkerCache,
    "createOptimizedQueryClient": ()=>createOptimizedQueryClient
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-core/build/modern/queryClient.js [app-client] (ecmascript)");
;
;
const CACHE_CONFIGS = {
    // User data - rarely changes
    user: {
        staleTime: 5 * 60 * 1000,
        gcTime: 30 * 60 * 1000
    },
    // Projects - moderate changes
    projects: {
        staleTime: 2 * 60 * 1000,
        gcTime: 15 * 60 * 1000
    },
    // Prompts - frequent changes
    prompts: {
        staleTime: 30 * 1000,
        gcTime: 5 * 60 * 1000
    },
    // Context templates - rarely changes
    templates: {
        staleTime: 10 * 60 * 1000,
        gcTime: 60 * 60 * 1000
    },
    // User plans - rarely changes
    plans: {
        staleTime: 15 * 60 * 1000,
        gcTime: 60 * 60 * 1000
    },
    // Real-time data - very fresh
    realtime: {
        staleTime: 0,
        gcTime: 1 * 60 * 1000
    }
};
const createOptimizedQueryClient = ()=>{
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QueryClient"]({
        defaultOptions: {
            queries: {
                // Default cache settings
                staleTime: 1 * 60 * 1000,
                gcTime: 5 * 60 * 1000,
                // Retry configuration
                retry: (failureCount, error)=>{
                    // Don't retry on 4xx errors
                    if ((error === null || error === void 0 ? void 0 : error.status) >= 400 && (error === null || error === void 0 ? void 0 : error.status) < 500) {
                        return false;
                    }
                    // Retry up to 3 times for other errors
                    return failureCount < 3;
                },
                // Retry delay with exponential backoff
                retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),
                // Background refetch settings
                refetchOnWindowFocus: false,
                refetchOnReconnect: true,
                refetchOnMount: true
            },
            mutations: {
                // Retry failed mutations
                retry: 1,
                retryDelay: 1000
            }
        }
    });
};
class BrowserCache {
    // Local Storage cache (persistent)
    static setLocal(key, data, ttl) {
        try {
            const item = {
                data,
                timestamp: Date.now(),
                ttl: ttl || 24 * 60 * 60 * 1000 // 24 hours default
            };
            localStorage.setItem(this.PREFIX + key, JSON.stringify(item));
        } catch (error) {
            console.warn('Failed to set localStorage cache:', error);
        }
    }
    static getLocal(key) {
        try {
            const item = localStorage.getItem(this.PREFIX + key);
            if (!item) return null;
            const parsed = JSON.parse(item);
            const now = Date.now();
            // Check if expired
            if (now - parsed.timestamp > parsed.ttl) {
                localStorage.removeItem(this.PREFIX + key);
                return null;
            }
            return parsed.data;
        } catch (error) {
            console.warn('Failed to get localStorage cache:', error);
            return null;
        }
    }
    // Session Storage cache (session-only)
    static setSession(key, data) {
        try {
            const item = {
                data,
                timestamp: Date.now()
            };
            sessionStorage.setItem(this.PREFIX + key, JSON.stringify(item));
        } catch (error) {
            console.warn('Failed to set sessionStorage cache:', error);
        }
    }
    static getSession(key) {
        try {
            const item = sessionStorage.getItem(this.PREFIX + key);
            if (!item) return null;
            const parsed = JSON.parse(item);
            return parsed.data;
        } catch (error) {
            console.warn('Failed to get sessionStorage cache:', error);
            return null;
        }
    }
    static setMemory(key, data) {
        let ttl = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 5 * 60 * 1000;
        this.memoryCache.set(key, {
            data,
            timestamp: Date.now(),
            ttl
        });
    }
    static getMemory(key) {
        const item = this.memoryCache.get(key);
        if (!item) return null;
        const now = Date.now();
        if (now - item.timestamp > item.ttl) {
            this.memoryCache.delete(key);
            return null;
        }
        return item.data;
    }
    // Clear all caches
    static clearAll() {
        // Clear localStorage
        Object.keys(localStorage).forEach((key)=>{
            if (key.startsWith(this.PREFIX)) {
                localStorage.removeItem(key);
            }
        });
        // Clear sessionStorage
        Object.keys(sessionStorage).forEach((key)=>{
            if (key.startsWith(this.PREFIX)) {
                sessionStorage.removeItem(key);
            }
        });
        // Clear memory cache
        this.memoryCache.clear();
    }
    // Cache size monitoring
    static getCacheInfo() {
        const localStorageSize = Object.keys(localStorage).filter((key)=>key.startsWith(this.PREFIX)).reduce((size, key)=>size + localStorage.getItem(key).length, 0);
        const sessionStorageSize = Object.keys(sessionStorage).filter((key)=>key.startsWith(this.PREFIX)).reduce((size, key)=>size + sessionStorage.getItem(key).length, 0);
        return {
            localStorage: {
                size: localStorageSize,
                items: Object.keys(localStorage).filter((key)=>key.startsWith(this.PREFIX)).length
            },
            sessionStorage: {
                size: sessionStorageSize,
                items: Object.keys(sessionStorage).filter((key)=>key.startsWith(this.PREFIX)).length
            },
            memory: {
                items: this.memoryCache.size
            }
        };
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(BrowserCache, "PREFIX", 'promptflow_');
// Memory cache (in-memory, fastest)
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(BrowserCache, "memoryCache", new Map());
class ServiceWorkerCache {
    static async cacheResources(urls) {
        if (!('serviceWorker' in navigator)) return;
        try {
            const cache = await caches.open(this.CACHE_NAME);
            await cache.addAll(urls);
            console.log('✅ [SW_CACHE] Resources cached:', urls.length);
        } catch (error) {
            console.warn('Failed to cache resources:', error);
        }
    }
    static async getCachedResponse(url) {
        if (!('caches' in window)) return null;
        try {
            const cache = await caches.open(this.CACHE_NAME);
            const response = await cache.match(url);
            return response || null;
        } catch (error) {
            console.warn('Failed to get cached response:', error);
            return null;
        }
    }
    static async clearCache() {
        if (!('caches' in window)) return;
        try {
            await caches.delete(this.CACHE_NAME);
            console.log('✅ [SW_CACHE] Cache cleared');
        } catch (error) {
            console.warn('Failed to clear cache:', error);
        }
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(ServiceWorkerCache, "CACHE_NAME", 'promptflow-v1');
class OptimisticUpdates {
    static updateQueryData(queryClient, queryKey, updater) {
        const previousData = queryClient.getQueryData(queryKey);
        queryClient.setQueryData(queryKey, updater);
        return previousData;
    }
    static rollbackQueryData(queryClient, queryKey, previousData) {
        queryClient.setQueryData(queryKey, previousData);
    }
}
class CacheInvalidation {
    static invalidateRelatedQueries(queryClient, entityType, entityId) {
        const patterns = {
            project: [
                'projects',
                'prompts',
                'user-stats'
            ],
            prompt: [
                'prompts',
                'project-prompts'
            ],
            user: [
                'user',
                'user-plans',
                'user-stats'
            ],
            template: [
                'templates',
                'context-templates'
            ]
        };
        const relatedPatterns = patterns[entityType] || [];
        relatedPatterns.forEach((pattern)=>{
            queryClient.invalidateQueries({
                queryKey: entityId ? [
                    pattern,
                    entityId
                ] : [
                    pattern
                ]
            });
        });
    }
    static prefetchRelatedData(queryClient, entityType, entityId) {
        // Prefetch related data based on entity type
        switch(entityType){
            case 'project':
                queryClient.prefetchQuery({
                    queryKey: [
                        'prompts',
                        entityId
                    ],
                    staleTime: CACHE_CONFIGS.prompts.staleTime
                });
                break;
            case 'user':
                queryClient.prefetchQuery({
                    queryKey: [
                        'user-plans',
                        entityId
                    ],
                    staleTime: CACHE_CONFIGS.plans.staleTime
                });
                break;
        }
    }
}
class CachePerformance {
    static recordCacheHit(key, responseTime) {
        const metric = this.metrics.get(key) || {
            hits: 0,
            misses: 0,
            totalTime: 0
        };
        metric.hits++;
        metric.totalTime += responseTime;
        this.metrics.set(key, metric);
    }
    static recordCacheMiss(key, responseTime) {
        const metric = this.metrics.get(key) || {
            hits: 0,
            misses: 0,
            totalTime: 0
        };
        metric.misses++;
        metric.totalTime += responseTime;
        this.metrics.set(key, metric);
    }
    static getCacheMetrics() {
        const results = Array.from(this.metrics.entries()).map((param)=>{
            let [key, metric] = param;
            return {
                key,
                hitRate: metric.hits / (metric.hits + metric.misses),
                avgResponseTime: metric.totalTime / (metric.hits + metric.misses),
                totalRequests: metric.hits + metric.misses
            };
        });
        return results.sort((a, b)=>b.totalRequests - a.totalRequests);
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(CachePerformance, "metrics", new Map());
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/providers/query-provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "QueryProvider": ()=>QueryProvider
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2d$devtools$2f$build$2f$modern$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query-devtools/build/modern/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$strategies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/cache-strategies.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
function QueryProvider(param) {
    let { children } = param;
    _s();
    const [queryClient] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "QueryProvider.useState": ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$strategies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createOptimizedQueryClient"])()
    }["QueryProvider.useState"]);
    // Initialize cache monitoring
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "QueryProvider.useEffect": ()=>{
            // Clear old cache on app start
            const lastClearTime = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$strategies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BrowserCache"].getLocal('cache-clear-time');
            const now = Date.now();
            const oneDay = 24 * 60 * 60 * 1000;
            if (!lastClearTime || now - lastClearTime > oneDay) {
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$strategies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BrowserCache"].clearAll();
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$strategies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BrowserCache"].setLocal('cache-clear-time', now);
                console.log('🧹 [CACHE] Daily cache cleanup completed');
            }
            // Performance monitoring in development
            if ("TURBOPACK compile-time truthy", 1) {
                const interval = setInterval({
                    "QueryProvider.useEffect.interval": ()=>{
                        const metrics = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$strategies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CachePerformance"].getCacheMetrics();
                        const cacheInfo = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$strategies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BrowserCache"].getCacheInfo();
                        console.log('📊 [CACHE_METRICS]', {
                            performance: metrics.slice(0, 5),
                            storage: cacheInfo
                        });
                    }
                }["QueryProvider.useEffect.interval"], 30000) // Every 30 seconds
                ;
                return ({
                    "QueryProvider.useEffect": ()=>clearInterval(interval)
                })["QueryProvider.useEffect"];
            }
        }
    }["QueryProvider.useEffect"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QueryClientProvider"], {
        client: queryClient,
        children: [
            children,
            ("TURBOPACK compile-time value", "development") === 'development' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2d$devtools$2f$build$2f$modern$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ReactQueryDevtools"], {
                initialIsOpen: false,
                position: "bottom-right"
            }, void 0, false, {
                fileName: "[project]/src/providers/query-provider.tsx",
                lineNumber: 44,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/providers/query-provider.tsx",
        lineNumber: 41,
        columnNumber: 5
    }, this);
}
_s(QueryProvider, "JR1hvDQCAfCeUMap/WKFY7wcrOM=");
_c = QueryProvider;
var _c;
__turbopack_context__.k.register(_c, "QueryProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/sonner.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Toaster": ()=>Toaster
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-themes/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
const Toaster = (param)=>{
    let { ...props } = param;
    _s();
    const { theme = "system" } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Toaster"], {
        theme: theme,
        className: "toaster group",
        style: {
            "--normal-bg": "var(--popover)",
            "--normal-text": "var(--popover-foreground)",
            "--normal-border": "var(--border)"
        },
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/sonner.tsx",
        lineNumber: 10,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(Toaster, "EriOrahfenYKDCErPq+L6926Dw4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"]
    ];
});
_c = Toaster;
;
var _c;
__turbopack_context__.k.register(_c, "Toaster");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/service-worker-registration.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ServiceWorkerRegistration": ()=>ServiceWorkerRegistration,
    "useServiceWorker": ()=>useServiceWorker
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
function ServiceWorkerRegistration() {
    _s();
    const [swState, setSwState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        isSupported: false,
        isRegistered: false,
        isUpdateAvailable: false,
        registration: null
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ServiceWorkerRegistration.useEffect": ()=>{
            // Check if service workers are supported
            if ("object" !== 'undefined' && 'serviceWorker' in navigator) {
                setSwState({
                    "ServiceWorkerRegistration.useEffect": (prev)=>({
                            ...prev,
                            isSupported: true
                        })
                }["ServiceWorkerRegistration.useEffect"]);
                registerServiceWorker();
            }
        }
    }["ServiceWorkerRegistration.useEffect"], []);
    const registerServiceWorker = async ()=>{
        try {
            const registration = await navigator.serviceWorker.register('/sw.js', {
                scope: '/',
                updateViaCache: 'none' // Always check for updates
            });
            console.log('Service Worker registered successfully:', registration);
            setSwState((prev)=>({
                    ...prev,
                    isRegistered: true,
                    registration
                }));
            // Check for updates
            registration.addEventListener('updatefound', ()=>{
                const newWorker = registration.installing;
                if (newWorker) {
                    newWorker.addEventListener('statechange', ()=>{
                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                            // New version available
                            setSwState((prev)=>({
                                    ...prev,
                                    isUpdateAvailable: true
                                }));
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"])('Yeni güncelleme mevcut!', {
                                description: 'Uygulamayı yenilemek için tıklayın',
                                action: {
                                    label: 'Yenile',
                                    onClick: ()=>updateServiceWorker(newWorker)
                                },
                                duration: 10000
                            });
                        }
                    });
                }
            });
            // Listen for controlling service worker changes
            navigator.serviceWorker.addEventListener('controllerchange', ()=>{
                window.location.reload();
            });
            // Check for waiting service worker
            if (registration.waiting) {
                setSwState((prev)=>({
                        ...prev,
                        isUpdateAvailable: true
                    }));
            }
            // Periodic update check (every 30 minutes)
            setInterval(()=>{
                registration.update();
            }, 30 * 60 * 1000);
        } catch (error) {
            console.error('Service Worker registration failed:', error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Çevrimdışı özellikler kullanılamıyor', {
                description: 'Service Worker kaydedilemedi'
            });
        }
    };
    const updateServiceWorker = (newWorker)=>{
        newWorker.postMessage({
            type: 'SKIP_WAITING'
        });
    };
    const forceUpdate = ()=>{
        var _swState_registration;
        if ((_swState_registration = swState.registration) === null || _swState_registration === void 0 ? void 0 : _swState_registration.waiting) {
            updateServiceWorker(swState.registration.waiting);
        }
    };
    // Cache management functions
    const clearCache = async ()=>{
        try {
            const cacheNames = await caches.keys();
            await Promise.all(cacheNames.map((cacheName)=>caches.delete(cacheName)));
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Önbellek temizlendi', {
                description: 'Uygulama yeniden yüklenecek'
            });
            setTimeout(()=>window.location.reload(), 1000);
        } catch (error) {
            console.error('Cache clear failed:', error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Önbellek temizlenemedi');
        }
    };
    const getCacheSize = async ()=>{
        try {
            if ('storage' in navigator && 'estimate' in navigator.storage) {
                const estimate = await navigator.storage.estimate();
                return {
                    used: estimate.usage || 0,
                    quota: estimate.quota || 0,
                    usedMB: Math.round((estimate.usage || 0) / 1024 / 1024 * 100) / 100,
                    quotaMB: Math.round((estimate.quota || 0) / 1024 / 1024 * 100) / 100
                };
            }
        } catch (error) {
            console.error('Storage estimate failed:', error);
        }
        return null;
    };
    // Preload critical resources
    const preloadResources = (urls)=>{
        if (swState.registration) {
            var _swState_registration_active;
            (_swState_registration_active = swState.registration.active) === null || _swState_registration_active === void 0 ? void 0 : _swState_registration_active.postMessage({
                type: 'CACHE_URLS',
                urls
            });
        }
    };
    // Background sync registration
    const registerBackgroundSync = async (tag)=>{
        try {
            if (swState.registration && 'sync' in swState.registration) {
                await swState.registration.sync.register(tag);
                console.log('Background sync registered:', tag);
            }
        } catch (error) {
            console.error('Background sync registration failed:', error);
        }
    };
    // Push notification subscription
    const subscribeToPushNotifications = async ()=>{
        try {
            if (!swState.registration) return null;
            const subscription = await swState.registration.pushManager.subscribe({
                userVisibleOnly: true,
                applicationServerKey: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_VAPID_PUBLIC_KEY
            });
            console.log('Push subscription:', subscription);
            return subscription;
        } catch (error) {
            console.error('Push subscription failed:', error);
            return null;
        }
    };
    // Expose utilities globally for debugging
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ServiceWorkerRegistration.useEffect": ()=>{
            if ("TURBOPACK compile-time truthy", 1) {
                window.swUtils = {
                    state: swState,
                    forceUpdate,
                    clearCache,
                    getCacheSize,
                    preloadResources,
                    registerBackgroundSync,
                    subscribeToPushNotifications
                };
            }
        }
    }["ServiceWorkerRegistration.useEffect"], [
        swState
    ]);
    // Don't render anything - this is just for registration
    return null;
}
_s(ServiceWorkerRegistration, "dxDCguP7gsYYONunW4/EbRe5gqk=");
_c = ServiceWorkerRegistration;
function useServiceWorker() {
    _s1();
    const [isOnline, setIsOnline] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [isInstallable, setIsInstallable] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [deferredPrompt, setDeferredPrompt] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useServiceWorker.useEffect": ()=>{
            // Online/offline detection
            const handleOnline = {
                "useServiceWorker.useEffect.handleOnline": ()=>setIsOnline(true)
            }["useServiceWorker.useEffect.handleOnline"];
            const handleOffline = {
                "useServiceWorker.useEffect.handleOffline": ()=>setIsOnline(false)
            }["useServiceWorker.useEffect.handleOffline"];
            window.addEventListener('online', handleOnline);
            window.addEventListener('offline', handleOffline);
            // PWA install prompt
            const handleBeforeInstallPrompt = {
                "useServiceWorker.useEffect.handleBeforeInstallPrompt": (e)=>{
                    e.preventDefault();
                    setDeferredPrompt(e);
                    setIsInstallable(true);
                }
            }["useServiceWorker.useEffect.handleBeforeInstallPrompt"];
            window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
            return ({
                "useServiceWorker.useEffect": ()=>{
                    window.removeEventListener('online', handleOnline);
                    window.removeEventListener('offline', handleOffline);
                    window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
                }
            })["useServiceWorker.useEffect"];
        }
    }["useServiceWorker.useEffect"], []);
    const installPWA = async ()=>{
        if (deferredPrompt) {
            deferredPrompt.prompt();
            const { outcome } = await deferredPrompt.userChoice;
            if (outcome === 'accepted') {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Uygulama yüklendi!');
            }
            setDeferredPrompt(null);
            setIsInstallable(false);
        }
    };
    const addToBackgroundSync = async (tag, data)=>{
        try {
            // Store data in IndexedDB for background sync
            // This would integrate with a proper IndexedDB wrapper
            console.log('Adding to background sync:', tag, data);
            if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
                const registration = await navigator.serviceWorker.ready;
                if ('sync' in registration && registration.sync) {
                    await registration.sync.register(tag);
                }
            }
        } catch (error) {
            console.error('Background sync failed:', error);
        }
    };
    return {
        isOnline,
        isInstallable,
        installPWA,
        addToBackgroundSync
    };
}
_s1(useServiceWorker, "hngaPQkRT8QSSX10xabCFV0VqYk=");
var _c;
__turbopack_context__.k.register(_c, "ServiceWorkerRegistration");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "cn": ()=>cn
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-client] (ecmascript)");
;
;
function cn() {
    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){
        inputs[_key] = arguments[_key];
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/button.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Button": ()=>Button,
    "buttonVariants": ()=>buttonVariants
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-slot/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/class-variance-authority/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
;
;
;
;
const buttonVariants = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cva"])("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive", {
    variants: {
        variant: {
            default: "bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",
            destructive: "bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
            outline: "border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",
            secondary: "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",
            ghost: "hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",
            link: "text-primary underline-offset-4 hover:underline"
        },
        size: {
            default: "h-9 px-4 py-2 has-[>svg]:px-3",
            sm: "h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",
            lg: "h-10 rounded-md px-6 has-[>svg]:px-4",
            icon: "size-9"
        }
    },
    defaultVariants: {
        variant: "default",
        size: "default"
    }
});
function Button(param) {
    let { className, variant, size, asChild = false, ...props } = param;
    const Comp = asChild ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Slot"] : "button";
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Comp, {
        "data-slot": "button",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])(buttonVariants({
            variant,
            size,
            className
        })),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/button.tsx",
        lineNumber: 51,
        columnNumber: 5
    }, this);
}
_c = Button;
;
var _c;
__turbopack_context__.k.register(_c, "Button");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/card.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Card": ()=>Card,
    "CardAction": ()=>CardAction,
    "CardContent": ()=>CardContent,
    "CardDescription": ()=>CardDescription,
    "CardFooter": ()=>CardFooter,
    "CardHeader": ()=>CardHeader,
    "CardTitle": ()=>CardTitle
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
;
;
function Card(param) {
    let { className, ...props } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 7,
        columnNumber: 5
    }, this);
}
_c = Card;
function CardHeader(param) {
    let { className, ...props } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-header",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 20,
        columnNumber: 5
    }, this);
}
_c1 = CardHeader;
function CardTitle(param) {
    let { className, ...props } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-title",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("leading-none font-semibold", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 33,
        columnNumber: 5
    }, this);
}
_c2 = CardTitle;
function CardDescription(param) {
    let { className, ...props } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-description",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("text-muted-foreground text-sm", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 43,
        columnNumber: 5
    }, this);
}
_c3 = CardDescription;
function CardAction(param) {
    let { className, ...props } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-action",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("col-start-2 row-span-2 row-start-1 self-start justify-self-end", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 53,
        columnNumber: 5
    }, this);
}
_c4 = CardAction;
function CardContent(param) {
    let { className, ...props } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-content",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("px-6", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 66,
        columnNumber: 5
    }, this);
}
_c5 = CardContent;
function CardFooter(param) {
    let { className, ...props } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-footer",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex items-center px-6 [.border-t]:pt-6", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 76,
        columnNumber: 5
    }, this);
}
_c6 = CardFooter;
;
var _c, _c1, _c2, _c3, _c4, _c5, _c6;
__turbopack_context__.k.register(_c, "Card");
__turbopack_context__.k.register(_c1, "CardHeader");
__turbopack_context__.k.register(_c2, "CardTitle");
__turbopack_context__.k.register(_c3, "CardDescription");
__turbopack_context__.k.register(_c4, "CardAction");
__turbopack_context__.k.register(_c5, "CardContent");
__turbopack_context__.k.register(_c6, "CardFooter");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/error-boundary.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Error Boundary Component
 * Comprehensive error handling with fallback UI and error reporting
 */ __turbopack_context__.s({
    "ErrorBoundary": ()=>ErrorBoundary,
    "useErrorHandler": ()=>useErrorHandler,
    "withErrorBoundary": ()=>withErrorBoundary
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/triangle-alert.js [app-client] (ecmascript) <export default as AlertTriangle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/refresh-cw.js [app-client] (ecmascript) <export default as RefreshCw>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$house$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Home$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/house.js [app-client] (ecmascript) <export default as Home>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bug$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bug$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bug.js [app-client] (ecmascript) <export default as Bug>");
'use client';
;
;
;
;
;
;
class ErrorBoundary extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Component"] {
    static getDerivedStateFromError(error) {
        // Update state so the next render will show the fallback UI
        return {
            hasError: true,
            error,
            errorId: "error-".concat(Date.now(), "-").concat(Math.random().toString(36).substr(2, 9))
        };
    }
    componentDidCatch(error, errorInfo) {
        var _errorInfo_componentStack;
        // Filter out hydration errors in development
        const isHydrationError = error.message.includes('Hydration') || error.message.includes('hydrating') || error.message.includes('Text content did not match') || ((_errorInfo_componentStack = errorInfo.componentStack) === null || _errorInfo_componentStack === void 0 ? void 0 : _errorInfo_componentStack.includes('hydration'));
        if (isHydrationError && ("TURBOPACK compile-time value", "development") === 'development') {
            // Log hydration errors with less severity
            console.warn('⚠️ [ERROR_BOUNDARY] Hydration mismatch detected:', {
                error: error.message,
                level: 'hydration',
                timestamp: new Date().toISOString()
            });
            return; // Don't show error UI for hydration mismatches
        }
        // Log error details for real errors
        console.error('🚨 [ERROR_BOUNDARY] Error caught:', {
            error: error.message,
            stack: error.stack,
            componentStack: errorInfo.componentStack,
            errorId: this.state.errorId,
            level: this.props.level || 'component',
            timestamp: new Date().toISOString()
        });
        // Update state with error info
        this.setState({
            errorInfo
        });
        // Call custom error handler
        if (this.props.onError) {
            this.props.onError(error, errorInfo);
        }
        // Report error to monitoring service
        this.reportError(error, errorInfo);
    }
    render() {
        if (this.state.hasError) {
            var _error_message;
            // Custom fallback UI
            if (this.props.fallback) {
                return this.props.fallback;
            }
            const errorLevel = this.getErrorLevel();
            const canRetry = this.retryCount < this.maxRetries;
            const { error } = this.state;
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "min-h-[400px] flex items-center justify-center p-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                    className: "w-full max-w-lg",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                            className: "text-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mx-auto mb-4",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__["AlertTriangle"], {
                                        className: "h-12 w-12 ".concat(errorLevel === 'high' ? 'text-red-500' : errorLevel === 'medium' ? 'text-yellow-500' : 'text-orange-500')
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/error-boundary.tsx",
                                        lineNumber: 211,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/error-boundary.tsx",
                                    lineNumber: 210,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                                    className: "text-xl",
                                    children: errorLevel === 'high' ? 'Kritik Hata' : errorLevel === 'medium' ? 'Bir Sorun Oluştu' : 'Beklenmeyen Hata'
                                }, void 0, false, {
                                    fileName: "[project]/src/components/error-boundary.tsx",
                                    lineNumber: 219,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardDescription"], {
                                    children: (error === null || error === void 0 ? void 0 : error.name) === 'ChunkLoadError' ? 'Uygulama güncellenmiş olabilir. Sayfayı yenilemeyi deneyin.' : (error === null || error === void 0 ? void 0 : (_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('Network')) ? 'Ağ bağlantısı sorunu. İnternet bağlantınızı kontrol edin.' : 'Bir hata oluştu. Lütfen tekrar deneyin veya sayfayı yenileyin.'
                                }, void 0, false, {
                                    fileName: "[project]/src/components/error-boundary.tsx",
                                    lineNumber: 224,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/error-boundary.tsx",
                            lineNumber: 209,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                            className: "space-y-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex flex-col sm:flex-row gap-2",
                                    children: [
                                        canRetry && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                            onClick: this.handleRetry,
                                            className: "flex-1",
                                            variant: "default",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__["RefreshCw"], {
                                                    className: "h-4 w-4 mr-2"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/error-boundary.tsx",
                                                    lineNumber: 242,
                                                    columnNumber: 21
                                                }, this),
                                                "Tekrar Dene (",
                                                this.maxRetries - this.retryCount,
                                                " kalan)"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/error-boundary.tsx",
                                            lineNumber: 237,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                            onClick: this.handleReload,
                                            variant: "outline",
                                            className: "flex-1",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__["RefreshCw"], {
                                                    className: "h-4 w-4 mr-2"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/error-boundary.tsx",
                                                    lineNumber: 252,
                                                    columnNumber: 19
                                                }, this),
                                                "Sayfayı Yenile"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/error-boundary.tsx",
                                            lineNumber: 247,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                            onClick: this.handleGoHome,
                                            variant: "outline",
                                            className: "flex-1",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$house$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Home$3e$__["Home"], {
                                                    className: "h-4 w-4 mr-2"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/error-boundary.tsx",
                                                    lineNumber: 261,
                                                    columnNumber: 19
                                                }, this),
                                                "Ana Sayfa"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/error-boundary.tsx",
                                            lineNumber: 256,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/error-boundary.tsx",
                                    lineNumber: 235,
                                    columnNumber: 15
                                }, this),
                                ("TURBOPACK compile-time value", "development") === 'development' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    onClick: ()=>console.log('Error details:', this.state),
                                    variant: "ghost",
                                    size: "sm",
                                    className: "w-full",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bug$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bug$3e$__["Bug"], {
                                            className: "h-4 w-4 mr-2"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/error-boundary.tsx",
                                            lineNumber: 273,
                                            columnNumber: 19
                                        }, this),
                                        "Console'da Detayları Gör"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/error-boundary.tsx",
                                    lineNumber: 267,
                                    columnNumber: 17
                                }, this),
                                this.renderErrorDetails()
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/error-boundary.tsx",
                            lineNumber: 234,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/error-boundary.tsx",
                    lineNumber: 208,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/error-boundary.tsx",
                lineNumber: 207,
                columnNumber: 9
            }, this);
        }
        return this.props.children;
    }
    constructor(props){
        super(props), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "retryCount", 0), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "maxRetries", 3), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "reportError", (error, errorInfo)=>{
            // In production, send to error monitoring service
            if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
            ;
        }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "handleRetry", ()=>{
            if (this.retryCount < this.maxRetries) {
                this.retryCount++;
                console.log("🔄 [ERROR_BOUNDARY] Retry attempt ".concat(this.retryCount, "/").concat(this.maxRetries));
                this.setState({
                    hasError: false,
                    error: null,
                    errorInfo: null,
                    errorId: null
                });
            } else {
                console.warn('⚠️ [ERROR_BOUNDARY] Max retries exceeded');
            }
        }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "handleReload", ()=>{
            window.location.reload();
        }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "handleGoHome", ()=>{
            window.location.href = '/';
        }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "getErrorLevel", ()=>{
            var _error_message, _error_message1;
            const { level } = this.props;
            const { error } = this.state;
            if (level === 'critical') return 'high';
            if (level === 'page') return 'medium';
            // Analyze error type
            if ((error === null || error === void 0 ? void 0 : error.name) === 'ChunkLoadError') return 'medium';
            if (error === null || error === void 0 ? void 0 : (_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('Network')) return 'medium';
            if (error === null || error === void 0 ? void 0 : (_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('TypeError')) return 'low';
            return 'low';
        }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "renderErrorDetails", ()=>{
            const { error, errorInfo, errorId } = this.state;
            const { showDetails = ("TURBOPACK compile-time value", "development") === 'development' } = this.props;
            if (!showDetails || !error) return null;
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("details", {
                className: "mt-4 p-4 bg-gray-50 rounded-lg",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("summary", {
                        className: "cursor-pointer font-medium text-gray-700 mb-2",
                        children: "Teknik Detaylar"
                    }, void 0, false, {
                        fileName: "[project]/src/components/error-boundary.tsx",
                        lineNumber: 161,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-2 text-sm font-mono",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                        children: "Error ID:"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/error-boundary.tsx",
                                        lineNumber: 166,
                                        columnNumber: 13
                                    }, this),
                                    " ",
                                    errorId
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/error-boundary.tsx",
                                lineNumber: 165,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                        children: "Error:"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/error-boundary.tsx",
                                        lineNumber: 169,
                                        columnNumber: 13
                                    }, this),
                                    " ",
                                    error.name
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/error-boundary.tsx",
                                lineNumber: 168,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                        children: "Message:"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/error-boundary.tsx",
                                        lineNumber: 172,
                                        columnNumber: 13
                                    }, this),
                                    " ",
                                    error.message
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/error-boundary.tsx",
                                lineNumber: 171,
                                columnNumber: 11
                            }, this),
                            error.stack && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                        children: "Stack Trace:"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/error-boundary.tsx",
                                        lineNumber: 176,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("pre", {
                                        className: "mt-1 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-32",
                                        children: error.stack
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/error-boundary.tsx",
                                        lineNumber: 177,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/error-boundary.tsx",
                                lineNumber: 175,
                                columnNumber: 13
                            }, this),
                            (errorInfo === null || errorInfo === void 0 ? void 0 : errorInfo.componentStack) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                        children: "Component Stack:"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/error-boundary.tsx",
                                        lineNumber: 184,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("pre", {
                                        className: "mt-1 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-32",
                                        children: errorInfo.componentStack
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/error-boundary.tsx",
                                        lineNumber: 185,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/error-boundary.tsx",
                                lineNumber: 183,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/error-boundary.tsx",
                        lineNumber: 164,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/error-boundary.tsx",
                lineNumber: 160,
                columnNumber: 7
            }, this);
        });
        this.state = {
            hasError: false,
            error: null,
            errorInfo: null,
            errorId: null
        };
    }
}
function withErrorBoundary(Component, errorBoundaryProps) {
    const WrappedComponent = (props)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ErrorBoundary, {
            ...errorBoundaryProps,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Component, {
                ...props
            }, void 0, false, {
                fileName: "[project]/src/components/error-boundary.tsx",
                lineNumber: 296,
                columnNumber: 7
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/error-boundary.tsx",
            lineNumber: 295,
            columnNumber: 5
        }, this);
    WrappedComponent.displayName = "withErrorBoundary(".concat(Component.displayName || Component.name, ")");
    return WrappedComponent;
}
function useErrorHandler() {
    const reportError = (error, context)=>{
        console.error("🚨 [ERROR_HANDLER] ".concat(context || 'Unhandled error', ":"), error);
        // Report to monitoring service
        if (("TURBOPACK compile-time value", "development") === 'production') {
        // window.Sentry?.captureException(error, { tags: { context } })
        }
    };
    return {
        reportError
    };
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/accessibility-utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Accessibility Utilities
 * WCAG 2.1 AA compliance tools and helpers
 */ // ARIA live region manager
__turbopack_context__.s({
    "AccessibilityTester": ()=>AccessibilityTester,
    "AriaLiveRegion": ()=>AriaLiveRegion,
    "ColorContrast": ()=>ColorContrast,
    "FormAccessibility": ()=>FormAccessibility,
    "KeyboardNavigation": ()=>KeyboardNavigation,
    "ScreenReader": ()=>ScreenReader
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
;
class AriaLiveRegion {
    static announce(message) {
        let priority = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'polite';
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
        const regionId = "aria-live-".concat(priority);
        let region = this.regions.get(regionId);
        if (!region) {
            region = document.createElement('div');
            region.id = regionId;
            region.setAttribute('aria-live', priority);
            region.setAttribute('aria-atomic', 'true');
            region.className = 'sr-only';
            region.style.cssText = "\n        position: absolute !important;\n        width: 1px !important;\n        height: 1px !important;\n        padding: 0 !important;\n        margin: -1px !important;\n        overflow: hidden !important;\n        clip: rect(0, 0, 0, 0) !important;\n        white-space: nowrap !important;\n        border: 0 !important;\n      ";
            document.body.appendChild(region);
            this.regions.set(regionId, region);
        }
        // Clear previous message and set new one
        region.textContent = '';
        setTimeout(()=>{
            region.textContent = message;
        }, 100);
    }
    static clear() {
        let priority = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'polite';
        const regionId = "aria-live-".concat(priority);
        const region = this.regions.get(regionId);
        if (region) {
            region.textContent = '';
        }
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(AriaLiveRegion, "regions", new Map());
class ColorContrast {
    // Convert hex to RGB
    static hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }
    // Calculate relative luminance
    static getLuminance(r, g, b) {
        const [rs, gs, bs] = [
            r,
            g,
            b
        ].map((c)=>{
            c = c / 255;
            return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
        });
        return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
    }
    // Calculate contrast ratio
    static getContrastRatio(color1, color2) {
        const rgb1 = this.hexToRgb(color1);
        const rgb2 = this.hexToRgb(color2);
        if (!rgb1 || !rgb2) return 0;
        const lum1 = this.getLuminance(rgb1.r, rgb1.g, rgb1.b);
        const lum2 = this.getLuminance(rgb2.r, rgb2.g, rgb2.b);
        const brightest = Math.max(lum1, lum2);
        const darkest = Math.min(lum1, lum2);
        return (brightest + 0.05) / (darkest + 0.05);
    }
    // Check WCAG compliance
    static checkWCAGCompliance(foreground, background) {
        const ratio = this.getContrastRatio(foreground, background);
        return {
            ratio,
            aa: ratio >= 4.5,
            aaa: ratio >= 7,
            aaLarge: ratio >= 3,
            aaaLarge: ratio >= 4.5
        };
    }
}
class KeyboardNavigation {
    // Focus trap for modals and dialogs
    static trapFocus(element) {
        this.trapStack.push(element);
        const focusableElements = this.getFocusableElements(element);
        if (focusableElements.length === 0) return;
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];
        const handleKeyDown = (e)=>{
            if (e.key !== 'Tab') return;
            if (e.shiftKey) {
                if (document.activeElement === firstElement) {
                    e.preventDefault();
                    lastElement.focus();
                }
            } else {
                if (document.activeElement === lastElement) {
                    e.preventDefault();
                    firstElement.focus();
                }
            }
        };
        element.addEventListener('keydown', handleKeyDown);
        firstElement.focus();
        return ()=>{
            element.removeEventListener('keydown', handleKeyDown);
            this.trapStack.pop();
        };
    }
    // Get all focusable elements
    static getFocusableElements(container) {
        const focusableSelectors = [
            'a[href]',
            'button:not([disabled])',
            'input:not([disabled])',
            'select:not([disabled])',
            'textarea:not([disabled])',
            '[tabindex]:not([tabindex="-1"])',
            '[contenteditable="true"]'
        ].join(', ');
        return Array.from(container.querySelectorAll(focusableSelectors)).filter((el)=>this.isVisible(el));
    }
    // Check if element is visible
    static isVisible(element) {
        const style = window.getComputedStyle(element);
        return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
    }
    // Restore focus to previous element
    static restoreFocus() {
        const lastTrap = this.trapStack[this.trapStack.length - 1];
        if (lastTrap) {
            const focusableElements = this.getFocusableElements(lastTrap);
            if (focusableElements.length > 0) {
                focusableElements[0].focus();
            }
        }
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(KeyboardNavigation, "trapStack", []);
class ScreenReader {
    // Generate unique IDs for ARIA relationships
    static generateId() {
        let prefix = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'aria';
        return "".concat(prefix, "-").concat(Math.random().toString(36).substr(2, 9));
    }
    // Create describedby relationship
    static createDescribedBy(element, description) {
        const descId = this.generateId('desc');
        // Create description element if it doesn't exist
        let descElement = document.getElementById(descId);
        if (!descElement) {
            descElement = document.createElement('div');
            descElement.id = descId;
            descElement.className = 'sr-only';
            descElement.textContent = description;
            document.body.appendChild(descElement);
        }
        // Add to aria-describedby
        const existingDescribedBy = element.getAttribute('aria-describedby');
        const newDescribedBy = existingDescribedBy ? "".concat(existingDescribedBy, " ").concat(descId) : descId;
        element.setAttribute('aria-describedby', newDescribedBy);
        return descId;
    }
    // Create labelledby relationship
    static createLabelledBy(element, labelText) {
        const labelId = this.generateId('label');
        // Create label element if it doesn't exist
        let labelElement = document.getElementById(labelId);
        if (!labelElement) {
            labelElement = document.createElement('div');
            labelElement.id = labelId;
            labelElement.className = 'sr-only';
            labelElement.textContent = labelText;
            document.body.appendChild(labelElement);
        }
        element.setAttribute('aria-labelledby', labelId);
        return labelId;
    }
    // Announce status changes
    static announceStatus(message) {
        let priority = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'polite';
        AriaLiveRegion.announce(message, priority);
    }
}
class FormAccessibility {
    // Add error announcement to form field
    static addFieldError(field, errorMessage) {
        var // Insert after field
        _field_parentNode;
        const errorId = ScreenReader.generateId('error');
        // Create error element
        const errorElement = document.createElement('div');
        errorElement.id = errorId;
        errorElement.className = 'text-red-600 text-sm mt-1';
        errorElement.setAttribute('role', 'alert');
        errorElement.textContent = errorMessage;
        (_field_parentNode = field.parentNode) === null || _field_parentNode === void 0 ? void 0 : _field_parentNode.insertBefore(errorElement, field.nextSibling);
        // Update field attributes
        field.setAttribute('aria-invalid', 'true');
        field.setAttribute('aria-describedby', errorId);
        // Announce error
        ScreenReader.announceStatus("Hata: ".concat(errorMessage), 'assertive');
        return errorId;
    }
    // Remove field error
    static removeFieldError(field, errorId) {
        const errorElement = document.getElementById(errorId);
        if (errorElement) {
            errorElement.remove();
        }
        field.removeAttribute('aria-invalid');
        field.removeAttribute('aria-describedby');
    }
    // Add success feedback
    static addFieldSuccess(field, successMessage) {
        var // Insert after field
        _field_parentNode;
        const successId = ScreenReader.generateId('success');
        // Create success element
        const successElement = document.createElement('div');
        successElement.id = successId;
        successElement.className = 'text-green-600 text-sm mt-1';
        successElement.setAttribute('role', 'status');
        successElement.textContent = successMessage;
        (_field_parentNode = field.parentNode) === null || _field_parentNode === void 0 ? void 0 : _field_parentNode.insertBefore(successElement, field.nextSibling);
        // Update field attributes
        field.setAttribute('aria-describedby', successId);
        return successId;
    }
}
class AccessibilityTester {
    // Test color contrast on page
    static testColorContrast() {
        const results = [];
        // Get all text elements
        const textElements = document.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6, a, button, label');
        textElements.forEach((element)=>{
            const styles = window.getComputedStyle(element);
            const foreground = styles.color;
            const background = styles.backgroundColor;
            if (foreground && background && background !== 'rgba(0, 0, 0, 0)') {
                // Convert to hex for testing (simplified)
                const ratio = ColorContrast.getContrastRatio(foreground, background);
                const passes = ratio >= 4.5;
                results.push({
                    element: element,
                    foreground,
                    background,
                    ratio,
                    passes
                });
            }
        });
        return results;
    }
    // Test keyboard navigation
    static testKeyboardNavigation() {
        const focusableElements = KeyboardNavigation.getFocusableElements(document.body);
        const issues = [];
        // Check for missing focus indicators
        focusableElements.forEach((element)=>{
            const styles = window.getComputedStyle(element);
            if (!styles.outline && !styles.boxShadow) {
                issues.push("Element missing focus indicator: ".concat(element.tagName));
            }
        });
        return {
            focusableElements: focusableElements.length,
            tabOrder: focusableElements,
            issues
        };
    }
    // Generate accessibility report
    static generateReport() {
        const colorContrast = this.testColorContrast();
        const keyboardNavigation = this.testKeyboardNavigation();
        // Count ARIA labels
        const ariaLabels = document.querySelectorAll('[aria-label], [aria-labelledby]').length;
        // Check heading structure
        const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'));
        const headingStructure = headings.map((heading)=>({
                level: parseInt(heading.tagName.charAt(1)),
                text: heading.textContent || ''
            }));
        return {
            colorContrast,
            keyboardNavigation,
            ariaLabels,
            headingStructure
        };
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/use-accessibility.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Accessibility Hooks
 * React hooks for accessibility features and WCAG compliance
 */ __turbopack_context__.s({
    "useAccessibility": ()=>useAccessibility,
    "useAccessibilityTesting": ()=>useAccessibilityTesting,
    "useAriaLive": ()=>useAriaLive,
    "useColorScheme": ()=>useColorScheme,
    "useFocusManagement": ()=>useFocusManagement,
    "useFocusTrap": ()=>useFocusTrap,
    "useFormAccessibility": ()=>useFormAccessibility,
    "useHighContrast": ()=>useHighContrast,
    "useKeyboardNavigation": ()=>useKeyboardNavigation,
    "useReducedMotion": ()=>useReducedMotion,
    "useScreenReader": ()=>useScreenReader
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$accessibility$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/accessibility-utils.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature(), _s5 = __turbopack_context__.k.signature(), _s6 = __turbopack_context__.k.signature(), _s7 = __turbopack_context__.k.signature(), _s8 = __turbopack_context__.k.signature(), _s9 = __turbopack_context__.k.signature(), _s10 = __turbopack_context__.k.signature();
'use client';
;
;
function useAriaLive() {
    _s();
    const announce = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAriaLive.useCallback[announce]": function(message) {
            let priority = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'polite';
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$accessibility$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AriaLiveRegion"].announce(message, priority);
        }
    }["useAriaLive.useCallback[announce]"], []);
    const clear = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAriaLive.useCallback[clear]": function() {
            let priority = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'polite';
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$accessibility$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AriaLiveRegion"].clear(priority);
        }
    }["useAriaLive.useCallback[clear]"], []);
    return {
        announce,
        clear
    };
}
_s(useAriaLive, "D2XnjMiL/SlcJIogXhoV9I/o9EI=");
function useFocusManagement() {
    _s1();
    const [focusedElement, setFocusedElement] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const saveFocus = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useFocusManagement.useCallback[saveFocus]": ()=>{
            setFocusedElement(document.activeElement);
        }
    }["useFocusManagement.useCallback[saveFocus]"], []);
    const restoreFocus = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useFocusManagement.useCallback[restoreFocus]": ()=>{
            if (focusedElement && focusedElement.focus) {
                focusedElement.focus();
            }
        }
    }["useFocusManagement.useCallback[restoreFocus]"], [
        focusedElement
    ]);
    const focusFirst = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useFocusManagement.useCallback[focusFirst]": (container)=>{
            const focusableElements = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$accessibility$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KeyboardNavigation"].getFocusableElements(container);
            if (focusableElements.length > 0) {
                focusableElements[0].focus();
            }
        }
    }["useFocusManagement.useCallback[focusFirst]"], []);
    return {
        saveFocus,
        restoreFocus,
        focusFirst,
        focusedElement
    };
}
_s1(useFocusManagement, "KE1l+EarMX7eO4JDu4k8TeJL3Ok=");
function useFocusTrap() {
    let isActive = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;
    _s2();
    const containerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const cleanupRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useFocusTrap.useEffect": ()=>{
            if (isActive && containerRef.current) {
                const cleanup = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$accessibility$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KeyboardNavigation"].trapFocus(containerRef.current);
                cleanupRef.current = cleanup || null;
            }
            return ({
                "useFocusTrap.useEffect": ()=>{
                    if (cleanupRef.current) {
                        cleanupRef.current();
                        cleanupRef.current = null;
                    }
                }
            })["useFocusTrap.useEffect"];
        }
    }["useFocusTrap.useEffect"], [
        isActive
    ]);
    return containerRef;
}
_s2(useFocusTrap, "7HConDru4M77YuPdINsRopO3o60=");
function useKeyboardNavigation() {
    let handlers = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    _s3();
    const elementRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useKeyboardNavigation.useEffect": ()=>{
            const element = elementRef.current;
            if (!element) return;
            const handleKeyDown = {
                "useKeyboardNavigation.useEffect.handleKeyDown": (e)=>{
                    switch(e.key){
                        case 'Escape':
                            var _handlers_onEscape;
                            (_handlers_onEscape = handlers.onEscape) === null || _handlers_onEscape === void 0 ? void 0 : _handlers_onEscape.call(handlers);
                            break;
                        case 'Enter':
                            var _handlers_onEnter;
                            (_handlers_onEnter = handlers.onEnter) === null || _handlers_onEnter === void 0 ? void 0 : _handlers_onEnter.call(handlers);
                            break;
                        case 'ArrowUp':
                            var _handlers_onArrowUp;
                            e.preventDefault();
                            (_handlers_onArrowUp = handlers.onArrowUp) === null || _handlers_onArrowUp === void 0 ? void 0 : _handlers_onArrowUp.call(handlers);
                            break;
                        case 'ArrowDown':
                            var _handlers_onArrowDown;
                            e.preventDefault();
                            (_handlers_onArrowDown = handlers.onArrowDown) === null || _handlers_onArrowDown === void 0 ? void 0 : _handlers_onArrowDown.call(handlers);
                            break;
                        case 'ArrowLeft':
                            var _handlers_onArrowLeft;
                            (_handlers_onArrowLeft = handlers.onArrowLeft) === null || _handlers_onArrowLeft === void 0 ? void 0 : _handlers_onArrowLeft.call(handlers);
                            break;
                        case 'ArrowRight':
                            var _handlers_onArrowRight;
                            (_handlers_onArrowRight = handlers.onArrowRight) === null || _handlers_onArrowRight === void 0 ? void 0 : _handlers_onArrowRight.call(handlers);
                            break;
                        case 'Tab':
                            var _handlers_onTab;
                            (_handlers_onTab = handlers.onTab) === null || _handlers_onTab === void 0 ? void 0 : _handlers_onTab.call(handlers, e);
                            break;
                    }
                }
            }["useKeyboardNavigation.useEffect.handleKeyDown"];
            element.addEventListener('keydown', handleKeyDown);
            return ({
                "useKeyboardNavigation.useEffect": ()=>element.removeEventListener('keydown', handleKeyDown)
            })["useKeyboardNavigation.useEffect"];
        }
    }["useKeyboardNavigation.useEffect"], [
        handlers
    ]);
    return elementRef;
}
_s3(useKeyboardNavigation, "CKe/C9l/PZDcdMrjDfTWxieUegY=");
function useFormAccessibility() {
    _s4();
    const addError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useFormAccessibility.useCallback[addError]": (field, message)=>{
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$accessibility$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormAccessibility"].addFieldError(field, message);
        }
    }["useFormAccessibility.useCallback[addError]"], []);
    const removeError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useFormAccessibility.useCallback[removeError]": (field, errorId)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$accessibility$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormAccessibility"].removeFieldError(field, errorId);
        }
    }["useFormAccessibility.useCallback[removeError]"], []);
    const addSuccess = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useFormAccessibility.useCallback[addSuccess]": (field, message)=>{
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$accessibility$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormAccessibility"].addFieldSuccess(field, message);
        }
    }["useFormAccessibility.useCallback[addSuccess]"], []);
    return {
        addError,
        removeError,
        addSuccess
    };
}
_s4(useFormAccessibility, "s5UXUnNk7E3iol7j/R3gh9kFwPs=");
function useScreenReader() {
    _s5();
    const announce = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useScreenReader.useCallback[announce]": function(message) {
            let priority = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'polite';
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$accessibility$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ScreenReader"].announceStatus(message, priority);
        }
    }["useScreenReader.useCallback[announce]"], []);
    const createDescribedBy = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useScreenReader.useCallback[createDescribedBy]": (element, description)=>{
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$accessibility$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ScreenReader"].createDescribedBy(element, description);
        }
    }["useScreenReader.useCallback[createDescribedBy]"], []);
    const createLabelledBy = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useScreenReader.useCallback[createLabelledBy]": (element, labelText)=>{
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$accessibility$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ScreenReader"].createLabelledBy(element, labelText);
        }
    }["useScreenReader.useCallback[createLabelledBy]"], []);
    return {
        announce,
        createDescribedBy,
        createLabelledBy
    };
}
_s5(useScreenReader, "3Q2TLUOEInjBrbyT4kJd80l3fXc=");
function useAccessibilityTesting() {
    _s6();
    const [report, setReport] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const runTests = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAccessibilityTesting.useCallback[runTests]": async ()=>{
            setIsLoading(true);
            // Wait for DOM to be ready
            await new Promise({
                "useAccessibilityTesting.useCallback[runTests]": (resolve)=>setTimeout(resolve, 100)
            }["useAccessibilityTesting.useCallback[runTests]"]);
            try {
                const testReport = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$accessibility$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccessibilityTester"].generateReport();
                setReport(testReport);
            } catch (error) {
                console.error('Accessibility testing failed:', error);
            } finally{
                setIsLoading(false);
            }
        }
    }["useAccessibilityTesting.useCallback[runTests]"], []);
    const getScore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAccessibilityTesting.useCallback[getScore]": ()=>{
            if (!report) return null;
            const colorContrastPassed = report.colorContrast.filter({
                "useAccessibilityTesting.useCallback[getScore]": (test)=>test.passes
            }["useAccessibilityTesting.useCallback[getScore]"]).length;
            const colorContrastTotal = report.colorContrast.length;
            const colorContrastScore = colorContrastTotal > 0 ? colorContrastPassed / colorContrastTotal * 100 : 100;
            const keyboardIssues = report.keyboardNavigation.issues.length;
            const keyboardScore = Math.max(0, 100 - keyboardIssues * 10);
            const ariaScore = report.ariaLabels > 0 ? 100 : 50;
            // Check heading structure
            let headingScore = 100;
            for(let i = 1; i < report.headingStructure.length; i++){
                const current = report.headingStructure[i];
                const previous = report.headingStructure[i - 1];
                if (current.level > previous.level + 1) {
                    headingScore -= 20; // Penalty for skipping heading levels
                }
            }
            const overallScore = (colorContrastScore + keyboardScore + ariaScore + headingScore) / 4;
            return {
                overall: Math.round(overallScore),
                colorContrast: Math.round(colorContrastScore),
                keyboard: Math.round(keyboardScore),
                aria: Math.round(ariaScore),
                headings: Math.round(headingScore)
            };
        }
    }["useAccessibilityTesting.useCallback[getScore]"], [
        report
    ]);
    return {
        report,
        isLoading,
        runTests,
        getScore
    };
}
_s6(useAccessibilityTesting, "3WTPSm0Z4cytXwQtOZ75rH04RcU=");
function useReducedMotion() {
    _s7();
    const [prefersReducedMotion, setPrefersReducedMotion] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useReducedMotion.useEffect": ()=>{
            if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
            ;
            const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
            setPrefersReducedMotion(mediaQuery.matches);
            const handleChange = {
                "useReducedMotion.useEffect.handleChange": (e)=>{
                    setPrefersReducedMotion(e.matches);
                }
            }["useReducedMotion.useEffect.handleChange"];
            mediaQuery.addEventListener('change', handleChange);
            return ({
                "useReducedMotion.useEffect": ()=>mediaQuery.removeEventListener('change', handleChange)
            })["useReducedMotion.useEffect"];
        }
    }["useReducedMotion.useEffect"], []);
    return prefersReducedMotion;
}
_s7(useReducedMotion, "c2o+PeDo1dLruq/wbnW+Z6a6rIY=");
function useHighContrast() {
    _s8();
    const [prefersHighContrast, setPrefersHighContrast] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useHighContrast.useEffect": ()=>{
            if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
            ;
            const mediaQuery = window.matchMedia('(prefers-contrast: high)');
            setPrefersHighContrast(mediaQuery.matches);
            const handleChange = {
                "useHighContrast.useEffect.handleChange": (e)=>{
                    setPrefersHighContrast(e.matches);
                }
            }["useHighContrast.useEffect.handleChange"];
            mediaQuery.addEventListener('change', handleChange);
            return ({
                "useHighContrast.useEffect": ()=>mediaQuery.removeEventListener('change', handleChange)
            })["useHighContrast.useEffect"];
        }
    }["useHighContrast.useEffect"], []);
    return prefersHighContrast;
}
_s8(useHighContrast, "5lg4err4w/R33O8pH8TDXog9KIs=");
function useColorScheme() {
    _s9();
    const [colorScheme, setColorScheme] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('light');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useColorScheme.useEffect": ()=>{
            if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
            ;
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            setColorScheme(mediaQuery.matches ? 'dark' : 'light');
            const handleChange = {
                "useColorScheme.useEffect.handleChange": (e)=>{
                    setColorScheme(e.matches ? 'dark' : 'light');
                }
            }["useColorScheme.useEffect.handleChange"];
            mediaQuery.addEventListener('change', handleChange);
            return ({
                "useColorScheme.useEffect": ()=>mediaQuery.removeEventListener('change', handleChange)
            })["useColorScheme.useEffect"];
        }
    }["useColorScheme.useEffect"], []);
    return colorScheme;
}
_s9(useColorScheme, "4osHCQ3SpzO+SLmgFKud4auGVmM=");
function useAccessibility() {
    _s10();
    const { announce, clear } = useAriaLive();
    const { saveFocus, restoreFocus, focusFirst } = useFocusManagement();
    const { announce: screenReaderAnnounce } = useScreenReader();
    const prefersReducedMotion = useReducedMotion();
    const prefersHighContrast = useHighContrast();
    const colorScheme = useColorScheme();
    return {
        // Announcements
        announce,
        clear,
        screenReaderAnnounce,
        // Focus management
        saveFocus,
        restoreFocus,
        focusFirst,
        // User preferences
        prefersReducedMotion,
        prefersHighContrast,
        colorScheme,
        // Utilities
        generateId: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$accessibility$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ScreenReader"].generateId
    };
}
_s10(useAccessibility, "SAJggKyKIMZBGkh3XSYNsn7hCOg=", false, function() {
    return [
        useAriaLive,
        useFocusManagement,
        useScreenReader,
        useReducedMotion,
        useHighContrast,
        useColorScheme
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/form-feedback.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Form Feedback Components
 * Real-time form validation, error handling, and user guidance system
 */ __turbopack_context__.s({
    "FeedbackMessage": ()=>FeedbackMessage,
    "FieldFeedback": ()=>FieldFeedback,
    "FormFeedbackProvider": ()=>FormFeedbackProvider,
    "FormProgress": ()=>FormProgress,
    "GlobalFeedback": ()=>GlobalFeedback,
    "useFormFeedback": ()=>useFormFeedback,
    "useFormValidation": ()=>useFormValidation
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-check-big.js [app-client] (ecmascript) <export default as CheckCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-alert.js [app-client] (ecmascript) <export default as AlertCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$info$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Info$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/info.js [app-client] (ecmascript) <export default as Info>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$accessibility$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-accessibility.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature();
'use client';
;
;
;
;
const FormFeedbackContext = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createContext(null);
function FormFeedbackProvider(param) {
    let { children } = param;
    _s();
    const [messages, setMessages] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const { announce } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$accessibility$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAccessibility"])();
    const addMessage = (message)=>{
        const id = "feedback-".concat(Date.now(), "-").concat(Math.random().toString(36).substr(2, 9));
        const newMessage = {
            ...message,
            id,
            timestamp: Date.now(),
            duration: message.duration || (message.type === 'error' ? 0 : 5000) // Errors persist, others auto-dismiss
        };
        setMessages((prev)=>[
                ...prev,
                newMessage
            ]);
        // Announce to screen readers
        const priority = message.type === 'error' ? 'assertive' : 'polite';
        announce(message.message, priority);
        // Auto-remove non-persistent messages
        if (newMessage.duration && newMessage.duration > 0) {
            setTimeout(()=>{
                setMessages((prev)=>prev.filter((m)=>m.id !== id));
            }, newMessage.duration);
        }
    };
    const removeMessage = (id)=>{
        setMessages((prev)=>prev.filter((m)=>m.id !== id));
    };
    const clearMessages = (field)=>{
        if (field) {
            setMessages((prev)=>prev.filter((m)=>m.field !== field));
        } else {
            setMessages([]);
        }
    };
    const clearAllMessages = ()=>{
        setMessages([]);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(FormFeedbackContext.Provider, {
        value: {
            messages,
            addMessage,
            removeMessage,
            clearMessages,
            clearAllMessages
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/ui/form-feedback.tsx",
        lineNumber: 82,
        columnNumber: 5
    }, this);
}
_s(FormFeedbackProvider, "FQtMz74NgKTiFYPRFBErhAepnlg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$accessibility$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAccessibility"]
    ];
});
_c = FormFeedbackProvider;
function useFormFeedback() {
    _s1();
    const context = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useContext(FormFeedbackContext);
    if (!context) {
        throw new Error('useFormFeedback must be used within FormFeedbackProvider');
    }
    return context;
}
_s1(useFormFeedback, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function FeedbackMessage(param) {
    let { message, onDismiss, className } = param;
    const getIcon = ()=>{
        switch(message.type){
            case 'success':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"], {
                    className: "h-4 w-4 text-green-600"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/form-feedback.tsx",
                    lineNumber: 116,
                    columnNumber: 16
                }, this);
            case 'error':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__["AlertCircle"], {
                    className: "h-4 w-4 text-red-600"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/form-feedback.tsx",
                    lineNumber: 118,
                    columnNumber: 16
                }, this);
            case 'warning':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__["AlertCircle"], {
                    className: "h-4 w-4 text-yellow-600"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/form-feedback.tsx",
                    lineNumber: 120,
                    columnNumber: 16
                }, this);
            case 'info':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$info$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Info$3e$__["Info"], {
                    className: "h-4 w-4 text-blue-600"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/form-feedback.tsx",
                    lineNumber: 122,
                    columnNumber: 16
                }, this);
            case 'loading':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                    className: "h-4 w-4 text-gray-600 animate-spin"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/form-feedback.tsx",
                    lineNumber: 124,
                    columnNumber: 16
                }, this);
            default:
                return null;
        }
    };
    const getStyles = ()=>{
        switch(message.type){
            case 'success':
                return 'bg-green-50 border-green-200 text-green-800';
            case 'error':
                return 'bg-red-50 border-red-200 text-red-800';
            case 'warning':
                return 'bg-yellow-50 border-yellow-200 text-yellow-800';
            case 'info':
                return 'bg-blue-50 border-blue-200 text-blue-800';
            case 'loading':
                return 'bg-gray-50 border-gray-200 text-gray-800';
            default:
                return 'bg-gray-50 border-gray-200 text-gray-800';
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('flex items-start gap-2 p-3 rounded-md border text-sm', getStyles(), className),
        role: "alert",
        "aria-live": message.type === 'error' ? 'assertive' : 'polite',
        children: [
            getIcon(),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex-1 min-w-0",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "break-words",
                    children: message.message
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/form-feedback.tsx",
                    lineNumber: 159,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/form-feedback.tsx",
                lineNumber: 158,
                columnNumber: 7
            }, this),
            onDismiss && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                onClick: onDismiss,
                className: "flex-shrink-0 p-1 hover:bg-black/5 rounded",
                "aria-label": "Mesajı kapat",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                    className: "h-3 w-3"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/form-feedback.tsx",
                    lineNumber: 167,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/form-feedback.tsx",
                lineNumber: 162,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/form-feedback.tsx",
        lineNumber: 148,
        columnNumber: 5
    }, this);
}
_c1 = FeedbackMessage;
function FieldFeedback(param) {
    let { field, className } = param;
    _s2();
    const { messages, removeMessage } = useFormFeedback();
    const fieldMessages = messages.filter((m)=>m.field === field);
    if (fieldMessages.length === 0) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('space-y-2 mt-1', className),
        children: fieldMessages.map((message)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(FeedbackMessage, {
                message: message,
                onDismiss: ()=>removeMessage(message.id)
            }, message.id, false, {
                fileName: "[project]/src/components/ui/form-feedback.tsx",
                lineNumber: 190,
                columnNumber: 9
            }, this))
    }, void 0, false, {
        fileName: "[project]/src/components/ui/form-feedback.tsx",
        lineNumber: 188,
        columnNumber: 5
    }, this);
}
_s2(FieldFeedback, "V6hrtIIDGjBAd2FR4RBr5u41Lvo=", false, function() {
    return [
        useFormFeedback
    ];
});
_c2 = FieldFeedback;
function GlobalFeedback(param) {
    let { className, position = 'top-right' } = param;
    _s3();
    const { messages, removeMessage } = useFormFeedback();
    const globalMessages = messages.filter((m)=>!m.field);
    const getPositionStyles = ()=>{
        switch(position){
            case 'top-right':
                return 'fixed top-4 right-4 z-50';
            case 'top-left':
                return 'fixed top-4 left-4 z-50';
            case 'bottom-right':
                return 'fixed bottom-4 right-4 z-50';
            case 'bottom-left':
                return 'fixed bottom-4 left-4 z-50';
            case 'top-center':
                return 'fixed top-4 left-1/2 transform -translate-x-1/2 z-50';
            default:
                return 'fixed top-4 right-4 z-50';
        }
    };
    if (globalMessages.length === 0) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])(getPositionStyles(), 'w-96 max-w-[calc(100vw-2rem)]', className),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "space-y-2",
            children: globalMessages.map((message)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(FeedbackMessage, {
                    message: message,
                    onDismiss: ()=>removeMessage(message.id),
                    className: "shadow-lg"
                }, message.id, false, {
                    fileName: "[project]/src/components/ui/form-feedback.tsx",
                    lineNumber: 234,
                    columnNumber: 11
                }, this))
        }, void 0, false, {
            fileName: "[project]/src/components/ui/form-feedback.tsx",
            lineNumber: 232,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/form-feedback.tsx",
        lineNumber: 231,
        columnNumber: 5
    }, this);
}
_s3(GlobalFeedback, "V6hrtIIDGjBAd2FR4RBr5u41Lvo=", false, function() {
    return [
        useFormFeedback
    ];
});
_c3 = GlobalFeedback;
function useFormValidation(initialValues, validationRules) {
    _s4();
    const [values, setValues] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(initialValues);
    const [errors, setErrors] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [touched, setTouched] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [isValidating, setIsValidating] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const { addMessage, clearMessages } = useFormFeedback();
    const validateField = (field, value)=>{
        const rule = validationRules[field];
        if (!rule) return null;
        const error = rule(value);
        return error;
    };
    const validateAllFields = ()=>{
        const newErrors = {};
        let hasErrors = false;
        Object.keys(values).forEach((key)=>{
            const field = key;
            const error = validateField(field, values[field]);
            if (error) {
                newErrors[field] = error;
                hasErrors = true;
            }
        });
        setErrors(newErrors);
        return !hasErrors;
    };
    const setValue = (field, value)=>{
        setValues((prev)=>({
                ...prev,
                [field]: value
            }));
        // Clear previous field messages
        clearMessages(field);
        // Validate if field has been touched
        if (touched[field]) {
            const error = validateField(field, value);
            if (error) {
                setErrors((prev)=>({
                        ...prev,
                        [field]: error
                    }));
                addMessage({
                    type: 'error',
                    message: error,
                    field: field
                });
            } else {
                setErrors((prev)=>{
                    const newErrors = {
                        ...prev
                    };
                    delete newErrors[field];
                    return newErrors;
                });
                addMessage({
                    type: 'success',
                    message: 'Geçerli',
                    field: field,
                    duration: 2000
                });
            }
        }
    };
    const markFieldTouched = (field)=>{
        setTouched((prev)=>({
                ...prev,
                [field]: true
            }));
    };
    const handleSubmit = async (onSubmit)=>{
        setIsValidating(true);
        // Mark all fields as touched
        const allTouched = Object.keys(values).reduce((acc, key)=>{
            acc[key] = true;
            return acc;
        }, {});
        setTouched(allTouched);
        // Validate all fields
        const isValid = validateAllFields();
        if (!isValid) {
            addMessage({
                type: 'error',
                message: 'Lütfen form hatalarını düzeltin',
                duration: 5000
            });
            setIsValidating(false);
            return;
        }
        try {
            await onSubmit(values);
            addMessage({
                type: 'success',
                message: 'Form başarıyla gönderildi',
                duration: 3000
            });
        } catch (error) {
            addMessage({
                type: 'error',
                message: error instanceof Error ? error.message : 'Bir hata oluştu',
                duration: 0 // Persist error messages
            });
        } finally{
            setIsValidating(false);
        }
    };
    const reset = ()=>{
        setValues(initialValues);
        setErrors({});
        setTouched({});
        clearMessages();
    };
    return {
        values,
        errors,
        touched,
        isValidating,
        setValue,
        setTouched: markFieldTouched,
        validateField,
        validateAllFields,
        handleSubmit,
        reset,
        isValid: Object.keys(errors).length === 0
    };
}
_s4(useFormValidation, "o3F2mU3Fe7ppdgvmqkj3/nXHyjo=", false, function() {
    return [
        useFormFeedback
    ];
});
function FormProgress(param) {
    let { currentStep, totalSteps, stepLabels, className } = param;
    const progress = currentStep / totalSteps * 100;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('w-full', className),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-between text-sm text-gray-600 mb-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: [
                            "Adım ",
                            currentStep,
                            " / ",
                            totalSteps
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/form-feedback.tsx",
                        lineNumber: 398,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: [
                            Math.round(progress),
                            "% tamamlandı"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/form-feedback.tsx",
                        lineNumber: 399,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/form-feedback.tsx",
                lineNumber: 397,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-full bg-gray-200 rounded-full h-2 mb-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-blue-600 h-2 rounded-full transition-all duration-300",
                    style: {
                        width: "".concat(progress, "%")
                    },
                    role: "progressbar",
                    "aria-valuenow": currentStep,
                    "aria-valuemin": 1,
                    "aria-valuemax": totalSteps,
                    "aria-label": "Form ilerlemesi: ".concat(currentStep, " / ").concat(totalSteps)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/form-feedback.tsx",
                    lineNumber: 403,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/form-feedback.tsx",
                lineNumber: 402,
                columnNumber: 7
            }, this),
            stepLabels && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-between",
                children: stepLabels.map((label, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-xs text-center flex-1', index + 1 <= currentStep ? 'text-blue-600 font-medium' : 'text-gray-400'),
                        children: label
                    }, index, false, {
                        fileName: "[project]/src/components/ui/form-feedback.tsx",
                        lineNumber: 417,
                        columnNumber: 13
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/components/ui/form-feedback.tsx",
                lineNumber: 415,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/form-feedback.tsx",
        lineNumber: 396,
        columnNumber: 5
    }, this);
}
_c4 = FormProgress;
var _c, _c1, _c2, _c3, _c4;
__turbopack_context__.k.register(_c, "FormFeedbackProvider");
__turbopack_context__.k.register(_c1, "FeedbackMessage");
__turbopack_context__.k.register(_c2, "FieldFeedback");
__turbopack_context__.k.register(_c3, "GlobalFeedback");
__turbopack_context__.k.register(_c4, "FormProgress");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/performance-monitor-wrapper.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "PerformanceMonitorWrapper": ()=>PerformanceMonitorWrapper,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
function PerformanceMonitorWrapper() {
    _s();
    const [Component, setComponent] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PerformanceMonitorWrapper.useEffect": ()=>{
            // Performance monitor'ü sadece client-side'da yükle
            let isMounted = true;
            const loadPerformanceMonitor = {
                "PerformanceMonitorWrapper.useEffect.loadPerformanceMonitor": async ()=>{
                    try {
                        // Development ortamında performance monitoring aktif
                        if ("TURBOPACK compile-time truthy", 1) {
                            const module = await __turbopack_context__.r("[project]/src/components/ui/performance-monitor.tsx [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
                            if (isMounted) {
                                setComponent({
                                    "PerformanceMonitorWrapper.useEffect.loadPerformanceMonitor": ()=>module.default
                                }["PerformanceMonitorWrapper.useEffect.loadPerformanceMonitor"]);
                            }
                        }
                    } catch (error) {
                        // Performance monitor yüklenemezse sessizce devam et
                        console.warn('Performance monitor could not be loaded:', error);
                        if (isMounted) {
                            setComponent({
                                "PerformanceMonitorWrapper.useEffect.loadPerformanceMonitor": ()=>null
                            }["PerformanceMonitorWrapper.useEffect.loadPerformanceMonitor"]);
                        }
                    } finally{
                        if (isMounted) {
                            setIsLoading(false);
                        }
                    }
                }
            }["PerformanceMonitorWrapper.useEffect.loadPerformanceMonitor"];
            loadPerformanceMonitor();
            // Cleanup function
            return ({
                "PerformanceMonitorWrapper.useEffect": ()=>{
                    isMounted = false;
                }
            })["PerformanceMonitorWrapper.useEffect"];
        }
    }["PerformanceMonitorWrapper.useEffect"], []);
    // Loading state'inde hiçbir şey render etme
    if (isLoading) {
        return null;
    }
    // Component yüklendiyse render et, yoksa null döndür
    return Component ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Component, {}, void 0, false, {
        fileName: "[project]/src/components/performance-monitor-wrapper.tsx",
        lineNumber: 62,
        columnNumber: 22
    }, this) : null;
}
_s(PerformanceMonitorWrapper, "oKG7sSiWt51wrWO/ZC3VyEOuwF4=");
_c = PerformanceMonitorWrapper;
const __TURBOPACK__default__export__ = PerformanceMonitorWrapper;
var _c;
__turbopack_context__.k.register(_c, "PerformanceMonitorWrapper");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/hydration-safe.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ClientOnly": ()=>ClientOnly,
    "HydrationSafe": ()=>HydrationSafe,
    "suppressHydrationConsoleErrors": ()=>suppressHydrationConsoleErrors,
    "suppressHydrationWarning": ()=>suppressHydrationWarning,
    "useHydrationSafe": ()=>useHydrationSafe,
    "useLocalStorage": ()=>useLocalStorage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature();
'use client';
;
function useHydrationSafe() {
    _s();
    const [isHydrated, setIsHydrated] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useHydrationSafe.useEffect": ()=>{
            setIsHydrated(true);
        }
    }["useHydrationSafe.useEffect"], []);
    return isHydrated;
}
_s(useHydrationSafe, "I77IOq3pAPHaLortJPfCkmuM/a0=");
function HydrationSafe(param) {
    let { children, fallback = null } = param;
    _s1();
    const isHydrated = useHydrationSafe();
    if (!isHydrated) {
        return fallback;
    }
    return children;
}
_s1(HydrationSafe, "tcOtWN6Sy9ZHFyLNCu4vOu0hQl0=", false, function() {
    return [
        useHydrationSafe
    ];
});
_c = HydrationSafe;
function suppressHydrationWarning(element) {
    // TypeScript doesn't recognize suppressHydrationWarning, so we use any
    const props = element.props || {};
    return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].cloneElement(element, {
        ...props,
        suppressHydrationWarning: true
    });
}
function useLocalStorage(key, defaultValue) {
    _s2();
    const [value, setValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(defaultValue);
    const [isLoaded, setIsLoaded] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useLocalStorage.useEffect": ()=>{
            try {
                const item = localStorage.getItem(key);
                if (item !== null) {
                    setValue(JSON.parse(item));
                }
            } catch (error) {
                console.warn('Error reading localStorage key "'.concat(key, '":'), error);
            } finally{
                setIsLoaded(true);
            }
        }
    }["useLocalStorage.useEffect"], [
        key
    ]);
    const setStoredValue = (newValue)=>{
        try {
            setValue(newValue);
            localStorage.setItem(key, JSON.stringify(newValue));
        } catch (error) {
            console.warn('Error setting localStorage key "'.concat(key, '":'), error);
        }
    };
    return [
        value,
        setStoredValue,
        isLoaded
    ];
}
_s2(useLocalStorage, "uxAfnW4k19SoVu0dOMLe+pz+bvw=");
function suppressHydrationConsoleErrors() {
    if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
    ;
    const originalError = console.error;
    const originalWarn = console.warn;
    console.error = function() {
        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
            args[_key] = arguments[_key];
        }
        const message = args[0];
        // Suppress known hydration warnings
        if (typeof message === 'string') {
            const hydrationWarnings = [
                'Warning: Text content did not match',
                'Warning: Expected server HTML to contain',
                'Warning: Did not expect server HTML to contain',
                'Hydration failed because the initial UI does not match',
                'There was an error while hydrating',
                'emitPendingHydrationWarnings'
            ];
            if (hydrationWarnings.some((warning)=>message.includes(warning))) {
                return; // Suppress hydration warnings
            }
        }
        // Allow all other errors through
        originalError.apply(console, args);
    };
    console.warn = function() {
        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
            args[_key] = arguments[_key];
        }
        const message = args[0];
        // Suppress known hydration warnings
        if (typeof message === 'string') {
            const hydrationWarnings = [
                'Warning: Text content did not match',
                'Warning: Expected server HTML to contain',
                'Warning: Did not expect server HTML to contain'
            ];
            if (hydrationWarnings.some((warning)=>message.includes(warning))) {
                return; // Suppress hydration warnings
            }
        }
        // Allow all other warnings through
        originalWarn.apply(console, args);
    };
    // Restore original console methods on page unload
    window.addEventListener('beforeunload', ()=>{
        console.error = originalError;
        console.warn = originalWarn;
    });
}
function ClientOnly(param) {
    let { children, fallback = null } = param;
    _s3();
    const [hasMounted, setHasMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ClientOnly.useEffect": ()=>{
            setHasMounted(true);
        }
    }["ClientOnly.useEffect"], []);
    if (!hasMounted) {
        return fallback;
    }
    return children;
}
_s3(ClientOnly, "aiSd/DQPOnbbLLZZL0Xv/KtPBDg=");
_c1 = ClientOnly;
var _c, _c1;
__turbopack_context__.k.register(_c, "HydrationSafe");
__turbopack_context__.k.register(_c1, "ClientOnly");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_f5f97854._.js.map
{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/supabase.ts"], "sourcesContent": ["// Import and re-export the browser client as the main client to avoid multiple instances\nimport { supabaseBrowser } from './supabase-browser'\nexport { supabaseBrowser as supabase } from './supabase-browser'\n\n// Session debug için\nif (typeof window !== 'undefined') {\n  supabaseBrowser.auth.onAuthStateChange((event, session) => {\n    console.log(`🔐 [SUPABASE_CLIENT] Auth state change: ${event}`, {\n      hasSession: !!session,\n      userId: session?.user?.id,\n      email: session?.user?.email,\n      expiresAt: session?.expires_at ? new Date(session.expires_at * 1000).toISOString() : null\n    })\n  })\n}\n\n// Global auth error handler\nif (typeof window !== 'undefined') {\n  supabaseBrowser.auth.onAuthStateChange((event) => {\n    if (event === 'SIGNED_OUT') {\n      // Oturum sonlandığında localStorage'ı temizle\n      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\n      if (supabaseUrl) {\n        localStorage.removeItem('sb-' + supabaseUrl.split('//')[1].split('.')[0] + '-auth-token')\n      }\n    }\n  })\n}\n\n// Database types\nexport interface Database {\n  public: {\n    Tables: {\n      projects: {\n        Row: {\n          id: string\n          user_id: string\n          name: string\n          context_text: string\n          description?: string\n          status?: string\n          tags?: string[]\n          is_public?: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          name: string\n          context_text?: string\n          description?: string\n          status?: string\n          tags?: string[]\n          is_public?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          name?: string\n          context_text?: string\n          description?: string\n          status?: string\n          tags?: string[]\n          is_public?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      prompts: {\n        Row: {\n          id: string\n          project_id: string\n          user_id: string\n          prompt_text: string\n          title: string | null\n          description: string | null\n          category: string | null\n          tags: string[]\n          order_index: number\n          is_used: boolean\n          is_favorite: boolean\n          usage_count: number\n          last_used_at: string | null\n          task_code: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          project_id: string\n          user_id: string\n          prompt_text: string\n          title?: string\n          description?: string\n          category?: string\n          tags?: string[]\n          order_index: number\n          is_used?: boolean\n          is_favorite?: boolean\n          usage_count?: number\n          last_used_at?: string\n          task_code?: string\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          project_id?: string\n          user_id?: string\n          prompt_text?: string\n          title?: string\n          description?: string\n          category?: string\n          tags?: string[]\n          order_index?: number\n          is_used?: boolean\n          is_favorite?: boolean\n          usage_count?: number\n          last_used_at?: string\n          task_code?: string\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      plan_types: {\n        Row: {\n          id: string\n          name: string\n          display_name: string\n          description: string | null\n          price_monthly: number\n          price_yearly: number\n          max_projects: number\n          max_prompts_per_project: number\n          features: Record<string, boolean | string | number>\n          is_active: boolean\n          sort_order: number\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          name: string\n          display_name: string\n          description?: string\n          price_monthly?: number\n          price_yearly?: number\n          max_projects?: number\n          max_prompts_per_project?: number\n          features?: Record<string, boolean | string | number>\n          is_active?: boolean\n          sort_order?: number\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          name?: string\n          display_name?: string\n          description?: string\n          price_monthly?: number\n          price_yearly?: number\n          max_projects?: number\n          max_prompts_per_project?: number\n          features?: Record<string, boolean | string | number>\n          is_active?: boolean\n          sort_order?: number\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      user_plans: {\n        Row: {\n          id: string\n          user_id: string\n          plan_type_id: string\n          status: string\n          billing_cycle: string\n          started_at: string\n          expires_at: string | null\n          cancelled_at: string | null\n          trial_ends_at: string | null\n          cancellation_reason: string | null\n          refund_status: string\n          refund_amount: number\n          auto_renew: boolean\n          payment_method: string | null\n          subscription_id: string | null\n          metadata: Record<string, boolean | string | number>\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          plan_type_id: string\n          status?: string\n          billing_cycle?: string\n          started_at?: string\n          expires_at?: string | null\n          cancelled_at?: string | null\n          trial_ends_at?: string | null\n          cancellation_reason?: string | null\n          refund_status?: string\n          refund_amount?: number\n          auto_renew?: boolean\n          payment_method?: string | null\n          subscription_id?: string | null\n          metadata?: Record<string, boolean | string | number>\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          plan_type_id?: string\n          status?: string\n          billing_cycle?: string\n          started_at?: string\n          expires_at?: string | null\n          cancelled_at?: string | null\n          trial_ends_at?: string | null\n          cancellation_reason?: string | null\n          refund_status?: string\n          refund_amount?: number\n          auto_renew?: boolean\n          payment_method?: string | null\n          subscription_id?: string | null\n          metadata?: Record<string, boolean | string | number>\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      usage_stats: {\n        Row: {\n          id: string\n          user_id: string\n          stat_date: string\n          projects_count: number\n          prompts_count: number\n          api_calls_count: number\n          storage_used_mb: number\n          last_activity_at: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          stat_date?: string\n          projects_count?: number\n          prompts_count?: number\n          api_calls_count?: number\n          storage_used_mb?: number\n          last_activity_at?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          stat_date?: string\n          projects_count?: number\n          prompts_count?: number\n          api_calls_count?: number\n          storage_used_mb?: number\n          last_activity_at?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      plan_transactions: {\n        Row: {\n          id: string\n          user_id: string\n          from_plan_id: string | null\n          to_plan_id: string\n          transaction_type: string\n          amount: number\n          currency: string\n          payment_status: string\n          payment_provider: string | null\n          payment_reference: string | null\n          notes: string | null\n          processed_at: string | null\n          created_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          from_plan_id?: string | null\n          to_plan_id: string\n          transaction_type: string\n          amount?: number\n          currency?: string\n          payment_status?: string\n          payment_provider?: string | null\n          payment_reference?: string | null\n          notes?: string | null\n          processed_at?: string | null\n          created_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          from_plan_id?: string | null\n          to_plan_id?: string\n          transaction_type?: string\n          amount?: number\n          currency?: string\n          payment_status?: string\n          payment_provider?: string | null\n          payment_reference?: string | null\n          notes?: string | null\n          processed_at?: string | null\n          created_at?: string\n        }\n      }\n    }\n    Functions: {\n      get_user_active_plan: {\n        Args: { user_uuid: string }\n        Returns: {\n          plan_id: string\n          plan_name: string\n          display_name: string\n          max_projects: number\n          max_prompts_per_project: number\n          features: Record<string, boolean | string | number>\n          status: string\n          expires_at: string | null\n        }[]\n      }\n      check_user_limits: {\n        Args: { user_uuid: string }\n        Returns: {\n          current_projects: number\n          current_prompts: number\n          max_projects: number\n          max_prompts_per_project: number\n          can_create_project: boolean\n          can_create_prompt: boolean\n        }[]\n      }\n      change_user_plan: {\n        Args: {\n          user_uuid: string\n          new_plan_name: string\n          billing_cycle_param?: string\n          payment_reference_param?: string\n        }\n        Returns: string\n      }\n      shared_prompts: {\n        Row: {\n          id: string\n          prompt_id: string\n          user_id: string\n          share_token: string\n          title: string | null\n          description: string | null\n          is_public: boolean\n          password_hash: string | null\n          expires_at: string | null\n          view_count: number\n          copy_count: number\n          is_active: boolean\n          metadata: any\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          prompt_id: string\n          user_id: string\n          share_token: string\n          title?: string | null\n          description?: string | null\n          is_public?: boolean\n          password_hash?: string | null\n          expires_at?: string | null\n          view_count?: number\n          copy_count?: number\n          is_active?: boolean\n          metadata?: any\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          prompt_id?: string\n          user_id?: string\n          share_token?: string\n          title?: string | null\n          description?: string | null\n          is_public?: boolean\n          password_hash?: string | null\n          expires_at?: string | null\n          view_count?: number\n          copy_count?: number\n          is_active?: boolean\n          metadata?: any\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      shared_prompt_views: {\n        Row: {\n          id: string\n          shared_prompt_id: string\n          viewer_ip: string | null\n          viewer_user_agent: string | null\n          referrer: string | null\n          viewed_at: string\n          session_id: string | null\n        }\n        Insert: {\n          id?: string\n          shared_prompt_id: string\n          viewer_ip?: string | null\n          viewer_user_agent?: string | null\n          referrer?: string | null\n          viewed_at?: string\n          session_id?: string | null\n        }\n        Update: {\n          id?: string\n          shared_prompt_id?: string\n          viewer_ip?: string | null\n          viewer_user_agent?: string | null\n          referrer?: string | null\n          viewed_at?: string\n          session_id?: string | null\n        }\n      }\n    }\n  }\n}"], "names": [], "mappings": "AAAA,yFAAyF;;AACzF;;;AAGA,qBAAqB;AACrB;;AAWA,4BAA4B;AAC5B", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,8OAAC,wMAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B", "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-card text-card-foreground\",\n        destructive:\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Alert({\n  className,\n  variant,\n  ...props\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\n  return (\n    <div\n      data-slot=\"alert\"\n      role=\"alert\"\n      className={cn(alertVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-title\"\n      className={cn(\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AlertDescription({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-description\"\n      className={cn(\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/plan-cancellation-modal.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON>Footer, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Label } from '@/components/ui/label'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertTriangle, CheckCircle, XCircle } from 'lucide-react'\nimport { useCancelPlan, useCancellationEligibility, useUserTrialInfo } from '@/hooks/use-plans'\nimport { toast } from 'sonner'\n\ninterface PlanCancellationModalProps {\n  isOpen: boolean\n  onClose: () => void\n  onSuccess?: () => void\n}\n\nexport function PlanCancellationModal({ isOpen, onClose, onSuccess }: PlanCancellationModalProps) {\n  const [cancellationReason, setCancellationReason] = useState('')\n  const [requestRefund, setRequestRefund] = useState(false)\n  const [confirmCancellation, setConfirmCancellation] = useState(false)\n\n  const { data: eligibility, isLoading: eligibilityLoading } = useCancellationEligibility()\n  const { data: trialInfo, isLoading: trialLoading } = useUserTrialInfo()\n  const cancelPlanMutation = useCancelPlan()\n\n  const isLoading = eligibilityLoading || trialLoading || cancelPlanMutation.isPending\n\n  const handleCancel = async () => {\n    if (!confirmCancellation) {\n      toast.error('Lütfen iptal işlemini onaylayın')\n      return\n    }\n\n    if (!cancellationReason.trim()) {\n      toast.error('Lütfen iptal nedeninizi belirtin')\n      return\n    }\n\n    try {\n      const result = await cancelPlanMutation.mutateAsync({\n        cancellationReason: cancellationReason.trim(),\n        requestRefund: requestRefund && trialInfo?.is_in_trial\n      })\n\n      if (result?.success) {\n        toast.success(result.message || 'Plan başarıyla iptal edildi')\n        onSuccess?.()\n        onClose()\n        \n        // Reset form\n        setCancellationReason('')\n        setRequestRefund(false)\n        setConfirmCancellation(false)\n      } else {\n        toast.error(result?.message || 'Plan iptal edilemedi')\n      }\n    } catch (error) {\n      console.error('Plan cancellation error:', error)\n      toast.error('Plan iptal edilirken bir hata oluştu')\n    }\n  }\n\n  const handleClose = () => {\n    if (cancelPlanMutation.isPending) return\n    \n    setCancellationReason('')\n    setRequestRefund(false)\n    setConfirmCancellation(false)\n    onClose()\n  }\n\n  if (!eligibility?.can_cancel) {\n    return (\n      <Dialog open={isOpen} onOpenChange={handleClose}>\n        <DialogContent className=\"sm:max-w-md\">\n          <DialogHeader>\n            <DialogTitle className=\"flex items-center gap-2\">\n              <XCircle className=\"h-5 w-5 text-red-500\" />\n              Plan İptal Edilemiyor\n            </DialogTitle>\n            <DialogDescription>\n              {eligibility?.reason || 'Bu plan iptal edilemez.'}\n            </DialogDescription>\n          </DialogHeader>\n          <DialogFooter>\n            <Button onClick={handleClose} variant=\"outline\">\n              Kapat\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n    )\n  }\n\n  return (\n    <Dialog open={isOpen} onOpenChange={handleClose}>\n      <DialogContent className=\"sm:max-w-lg\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <AlertTriangle className=\"h-5 w-5 text-orange-500\" />\n            Plan İptal Et\n          </DialogTitle>\n          <DialogDescription>\n            {eligibility?.plan_display_name} planınızı iptal etmek üzeresiniz. Bu işlem geri alınamaz.\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          {/* Trial Information */}\n          {trialInfo?.is_in_trial && (\n            <Alert>\n              <CheckCircle className=\"h-4 w-4\" />\n              <AlertDescription>\n                Deneme süreniz devam ediyor ({trialInfo.days_remaining} gün kaldı). \n                İptal ederseniz tam iade alabilirsiniz.\n              </AlertDescription>\n            </Alert>\n          )}\n\n          {/* Refund Information */}\n          {eligibility?.refund_eligible && (\n            <Alert>\n              <CheckCircle className=\"h-4 w-4\" />\n              <AlertDescription>\n                Deneme süresi içinde olduğunuz için {eligibility.estimated_refund_amount} TL tam iade alabilirsiniz.\n              </AlertDescription>\n            </Alert>\n          )}\n\n          {/* Cancellation Reason */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"cancellation-reason\">\n              İptal Nedeni <span className=\"text-red-500\">*</span>\n            </Label>\n            <Textarea\n              id=\"cancellation-reason\"\n              placeholder=\"Planınızı neden iptal etmek istiyorsunuz? Geri bildiriminiz bizim için değerli.\"\n              value={cancellationReason}\n              onChange={(e) => setCancellationReason(e.target.value)}\n              rows={3}\n              maxLength={500}\n              disabled={isLoading}\n            />\n            <p className=\"text-xs text-muted-foreground\">\n              {cancellationReason.length}/500 karakter\n            </p>\n          </div>\n\n          {/* Refund Request */}\n          {eligibility?.refund_eligible && (\n            <div className=\"flex items-center space-x-2\">\n              <Checkbox\n                id=\"request-refund\"\n                checked={requestRefund}\n                onCheckedChange={(checked) => setRequestRefund(checked as boolean)}\n                disabled={isLoading}\n              />\n              <Label htmlFor=\"request-refund\" className=\"text-sm\">\n                İade talep ediyorum ({eligibility.estimated_refund_amount} TL)\n              </Label>\n            </div>\n          )}\n\n          {/* Confirmation */}\n          <div className=\"flex items-center space-x-2\">\n            <Checkbox\n              id=\"confirm-cancellation\"\n              checked={confirmCancellation}\n              onCheckedChange={(checked) => setConfirmCancellation(checked as boolean)}\n              disabled={isLoading}\n            />\n            <Label htmlFor=\"confirm-cancellation\" className=\"text-sm\">\n              Plan iptal işlemini onaylıyorum ve bu işlemin geri alınamayacağını anlıyorum\n            </Label>\n          </div>\n\n          {/* Warning */}\n          <Alert>\n            <AlertTriangle className=\"h-4 w-4\" />\n            <AlertDescription>\n              Plan iptal edildikten sonra ücretsiz plana geçeceksiniz. \n              Mevcut projeleriniz ve promptlarınız korunacak ancak plan limitleri geçerli olacak.\n            </AlertDescription>\n          </Alert>\n        </div>\n\n        <DialogFooter className=\"gap-2\">\n          <Button \n            onClick={handleClose} \n            variant=\"outline\"\n            disabled={isLoading}\n          >\n            Vazgeç\n          </Button>\n          <Button \n            onClick={handleCancel}\n            variant=\"destructive\"\n            disabled={isLoading || !confirmCancellation || !cancellationReason.trim()}\n          >\n            {isLoading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n            Planı İptal Et\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAXA;;;;;;;;;;;;AAmBO,SAAS,sBAAsB,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAA8B;IAC9F,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,MAAM,EAAE,MAAM,WAAW,EAAE,WAAW,kBAAkB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,6BAA0B,AAAD;IACtF,MAAM,EAAE,MAAM,SAAS,EAAE,WAAW,YAAY,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD;IACpE,MAAM,qBAAqB,CAAA,GAAA,4HAAA,CAAA,gBAAa,AAAD;IAEvC,MAAM,YAAY,sBAAsB,gBAAgB,mBAAmB,SAAS;IAEpF,MAAM,eAAe;QACnB,IAAI,CAAC,qBAAqB;YACxB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,mBAAmB,IAAI,IAAI;YAC9B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,mBAAmB,WAAW,CAAC;gBAClD,oBAAoB,mBAAmB,IAAI;gBAC3C,eAAe,iBAAiB,WAAW;YAC7C;YAEA,IAAI,QAAQ,SAAS;gBACnB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO,IAAI;gBAChC;gBACA;gBAEA,aAAa;gBACb,sBAAsB;gBACtB,iBAAiB;gBACjB,uBAAuB;YACzB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,QAAQ,WAAW;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,mBAAmB,SAAS,EAAE;QAElC,sBAAsB;QACtB,iBAAiB;QACjB,uBAAuB;QACvB;IACF;IAEA,IAAI,CAAC,aAAa,YAAY;QAC5B,qBACE,8OAAC,kIAAA,CAAA,SAAM;YAAC,MAAM;YAAQ,cAAc;sBAClC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACvB,8OAAC,kIAAA,CAAA,eAAY;;0CACX,8OAAC,kIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,4MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAyB;;;;;;;0CAG9C,8OAAC,kIAAA,CAAA,oBAAiB;0CACf,aAAa,UAAU;;;;;;;;;;;;kCAG5B,8OAAC,kIAAA,CAAA,eAAY;kCACX,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAa,SAAQ;sCAAU;;;;;;;;;;;;;;;;;;;;;;IAO1D;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAA4B;;;;;;;sCAGvD,8OAAC,kIAAA,CAAA,oBAAiB;;gCACf,aAAa;gCAAkB;;;;;;;;;;;;;8BAIpC,8OAAC;oBAAI,WAAU;;wBAEZ,WAAW,6BACV,8OAAC,iIAAA,CAAA,QAAK;;8CACJ,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;;wCAAC;wCACc,UAAU,cAAc;wCAAC;;;;;;;;;;;;;wBAO5D,aAAa,iCACZ,8OAAC,iIAAA,CAAA,QAAK;;8CACJ,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;;wCAAC;wCACqB,YAAY,uBAAuB;wCAAC;;;;;;;;;;;;;sCAM/E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;;wCAAsB;sDACtB,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAE9C,8OAAC,oIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,sBAAsB,EAAE,MAAM,CAAC,KAAK;oCACrD,MAAM;oCACN,WAAW;oCACX,UAAU;;;;;;8CAEZ,8OAAC;oCAAE,WAAU;;wCACV,mBAAmB,MAAM;wCAAC;;;;;;;;;;;;;wBAK9B,aAAa,iCACZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,SAAS;oCACT,iBAAiB,CAAC,UAAY,iBAAiB;oCAC/C,UAAU;;;;;;8CAEZ,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAiB,WAAU;;wCAAU;wCAC5B,YAAY,uBAAuB;wCAAC;;;;;;;;;;;;;sCAMhE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,SAAS;oCACT,iBAAiB,CAAC,UAAY,uBAAuB;oCACrD,UAAU;;;;;;8CAEZ,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAuB,WAAU;8CAAU;;;;;;;;;;;;sCAM5D,8OAAC,iIAAA,CAAA,QAAK;;8CACJ,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAC;;;;;;;;;;;;;;;;;;8BAOtB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,SAAQ;4BACR,UAAU;sCACX;;;;;;sCAGD,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,SAAQ;4BACR,UAAU,aAAa,CAAC,uBAAuB,CAAC,mBAAmB,IAAI;;gCAEtE,2BAAa,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAA+B;;;;;;;;;;;;;;;;;;;;;;;;AAO5E", "debugId": null}}, {"offset": {"line": 593, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/app/profile/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Badge } from '@/components/ui/badge'\nimport { Separator } from '@/components/ui/separator'\nimport { AuthGuard } from '@/components/auth-guard'\nimport { useUser, useSignOut } from '@/hooks/use-auth'\nimport { useProjects } from '@/hooks/use-projects'\nimport { useUserActivePlan, usePlanTypes, useChangePlan, useCancellationEligibility, useUserTrialInfo } from '@/hooks/use-plans'\nimport { supabase } from '@/lib/supabase'\nimport { ArrowLeft, User, Mail, Calendar, Shield, Key, LogOut, AlertCircle, CheckCircle, Crown, Star, XCircle } from 'lucide-react'\nimport Link from 'next/link'\nimport { PlanCancellationModal } from '@/components/plan-cancellation-modal'\n\nexport default function ProfilePage() {\n  const [newPassword, setNewPassword] = useState('')\n  const [confirmPassword, setConfirmPassword] = useState('')\n  const [isChangingPassword, setIsChangingPassword] = useState(false)\n  const [passwordMessage, setPasswordMessage] = useState('')\n  const [messageType, setMessageType] = useState<'success' | 'error' | null>(null)\n  const [showCancellationModal, setShowCancellationModal] = useState(false)\n\n  const { data: user } = useUser()\n  const { data: projects = [] } = useProjects()\n  const { data: activePlan } = useUserActivePlan()\n  const { data: planTypes = [] } = usePlanTypes()\n  const { data: cancellationEligibility } = useCancellationEligibility()\n  const { data: trialInfo } = useUserTrialInfo()\n  const changePlanMutation = useChangePlan()\n  const signOutMutation = useSignOut()\n\n  const handlePasswordChange = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setPasswordMessage('')\n    setMessageType(null)\n\n    if (newPassword !== confirmPassword) {\n      setPasswordMessage('Yeni şifreler eşleşmiyor!')\n      setMessageType('error')\n      return\n    }\n\n    if (newPassword.length < 6) {\n      setPasswordMessage('Yeni şifre en az 6 karakter olmalı!')\n      setMessageType('error')\n      return\n    }\n\n    setIsChangingPassword(true)\n\n    try {\n      const { error } = await supabase.auth.updateUser({\n        password: newPassword\n      })\n\n      if (error) {\n        throw error\n      }\n\n      setPasswordMessage('Şifre başarıyla güncellendi!')\n      setMessageType('success')\n      setNewPassword('')\n      setConfirmPassword('')\n    } catch (error) {\n      setPasswordMessage(error instanceof Error ? error.message : 'Şifre güncellenirken bir hata oluştu')\n      setMessageType('error')\n    } finally {\n      setIsChangingPassword(false)\n    }\n  }\n\n  const handleSignOut = () => {\n    signOutMutation.mutate()\n  }\n\n  const handlePlanChange = async (planName: string) => {\n    try {\n      // Validation checks before plan change\n      const targetPlan = planTypes.find(p => p.name === planName)\n      if (!targetPlan) {\n        throw new Error('Geçersiz plan seçimi')\n      }\n\n      // Check if downgrading and validate current usage\n      if (activePlan && targetPlan.max_projects < activePlan.max_projects) {\n        if (projects.length > targetPlan.max_projects) {\n          throw new Error(\n            `Bu plana geçmek için önce proje sayınızı ${targetPlan.max_projects}'e düşürmeniz gerekiyor. ` +\n            `Şu anda ${projects.length} projeniz var.`\n          )\n        }\n      }\n\n      // Confirm downgrade if applicable\n      if (activePlan && targetPlan.price_monthly < activePlan.price_monthly) {\n        const confirmed = window.confirm(\n          `${targetPlan.display_name} planına geçmek istediğinizden emin misiniz? ` +\n          'Bu işlem bazı özelliklerinizi kısıtlayabilir.'\n        )\n        if (!confirmed) return\n      }\n\n      await changePlanMutation.mutateAsync({\n        planName,\n        billingCycle: 'monthly'\n      })\n\n      setPasswordMessage('Plan başarıyla değiştirildi!')\n      setMessageType('success')\n\n      // Clear message after 5 seconds\n      setTimeout(() => {\n        setPasswordMessage('')\n        setMessageType(null)\n      }, 5000)\n\n    } catch (error) {\n      setPasswordMessage(error instanceof Error ? error.message : 'Plan değiştirilemedi')\n      setMessageType('error')\n\n      // Clear error message after 8 seconds\n      setTimeout(() => {\n        setPasswordMessage('')\n        setMessageType(null)\n      }, 8000)\n    }\n  }\n\n  const getPlanIcon = (planName: string) => {\n    switch (planName) {\n      case 'free': return <User className=\"h-5 w-5\" />\n      case 'professional': return <Crown className=\"h-5 w-5\" />\n      case 'enterprise': return <Star className=\"h-5 w-5\" />\n      default: return <Shield className=\"h-5 w-5\" />\n    }\n  }\n\n  const getPlanColor = (planName: string) => {\n    switch (planName) {\n      case 'free': return 'bg-gray-100 text-gray-800'\n      case 'professional': return 'bg-blue-100 text-blue-800'\n      case 'enterprise': return 'bg-purple-100 text-purple-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  if (!user) {\n    return null\n  }\n\n  return (\n    <AuthGuard>\n      <div className=\"min-h-screen bg-gray-50\">\n        <div className=\"max-w-4xl mx-auto p-6\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between mb-8\">\n            <div className=\"flex items-center gap-4\">\n              <Link href=\"/\">\n                <Button variant=\"outline\" size=\"sm\">\n                  <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                  Ana Sayfaya Dön\n                </Button>\n              </Link>\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900\">Profil Ayarları</h1>\n                <p className=\"text-gray-600\">Hesap bilgilerinizi yönetin</p>\n              </div>\n            </div>\n            <Button\n              variant=\"outline\"\n              onClick={handleSignOut}\n              className=\"text-red-600 hover:text-red-700\"\n            >\n              <LogOut className=\"h-4 w-4 mr-2\" />\n              Çıkış Yap\n            </Button>\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n            {/* Profil Bilgileri */}\n            <div className=\"lg:col-span-2 space-y-6\">\n              {/* Temel Bilgiler */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2\">\n                    <User className=\"h-5 w-5\" />\n                    Temel Bilgiler\n                  </CardTitle>\n                  <CardDescription>\n                    Hesap bilgilerinizi görüntüleyin\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <div>\n                      <Label htmlFor=\"email\">E-posta Adresi</Label>\n                      <div className=\"flex items-center gap-2 mt-1\">\n                        <Mail className=\"h-4 w-4 text-gray-400\" />\n                        <span className=\"text-gray-900\">{user.email}</span>\n                      </div>\n                    </div>\n                    <div>\n                      <Label>Kayıt Tarihi</Label>\n                      <div className=\"flex items-center gap-2 mt-1\">\n                        <Calendar className=\"h-4 w-4 text-gray-400\" />\n                        <span className=\"text-gray-900\">\n                          {new Date(user.created_at).toLocaleDateString('tr-TR')}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                  <div>\n                    <Label>Hesap Durumu</Label>\n                    <div className=\"flex items-center gap-2 mt-1\">\n                      <Shield className=\"h-4 w-4 text-green-500\" />\n                      <Badge variant=\"secondary\" className=\"bg-green-100 text-green-800\">\n                        Aktif\n                      </Badge>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Şifre Değiştirme */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2\">\n                    <Key className=\"h-5 w-5\" />\n                    Şifre Değiştir\n                  </CardTitle>\n                  <CardDescription>\n                    Güvenliğiniz için düzenli olarak şifrenizi değiştirin\n                  </CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <form onSubmit={handlePasswordChange} className=\"space-y-4\">\n                    <div>\n                      <Label htmlFor=\"newPassword\">Yeni Şifre</Label>\n                      <Input\n                        id=\"newPassword\"\n                        type=\"password\"\n                        placeholder=\"Yeni şifrenizi girin\"\n                        value={newPassword}\n                        onChange={(e) => setNewPassword(e.target.value)}\n                        disabled={isChangingPassword}\n                        autoComplete=\"new-password\"\n                        required\n                      />\n                    </div>\n                    <div>\n                      <Label htmlFor=\"confirmPassword\">Yeni Şifre (Tekrar)</Label>\n                      <Input\n                        id=\"confirmPassword\"\n                        type=\"password\"\n                        placeholder=\"Yeni şifrenizi tekrar girin\"\n                        value={confirmPassword}\n                        onChange={(e) => setConfirmPassword(e.target.value)}\n                        disabled={isChangingPassword}\n                        autoComplete=\"new-password\"\n                        required\n                      />\n                    </div>\n\n                    {passwordMessage && (\n                      <div className={`flex items-center gap-2 text-sm p-3 rounded-md ${\n                        messageType === 'success' \n                          ? 'bg-green-50 text-green-700' \n                          : 'bg-red-50 text-red-700'\n                      }`}>\n                        {messageType === 'success' ? (\n                          <CheckCircle className=\"h-4 w-4\" />\n                        ) : (\n                          <AlertCircle className=\"h-4 w-4\" />\n                        )}\n                        {passwordMessage}\n                      </div>\n                    )}\n\n                    <Button\n                      type=\"submit\"\n                      disabled={isChangingPassword || !newPassword || !confirmPassword}\n                      className=\"w-full\"\n                    >\n                      {isChangingPassword ? 'Güncelleniyor...' : 'Şifreyi Güncelle'}\n                    </Button>\n                  </form>\n                </CardContent>\n              </Card>\n            </div>\n\n            {/* Yan Panel - İstatistikler */}\n            <div className=\"space-y-6\">\n              {/* Hesap İstatistikleri */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-lg\">Hesap İstatistikleri</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-gray-600\">Toplam Proje</span>\n                    <Badge variant=\"outline\">{projects.length}</Badge>\n                  </div>\n                  <Separator />\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-gray-600\">Hesap Tipi</span>\n                    <Badge variant=\"secondary\" className={getPlanColor(activePlan?.plan_name || 'free')}>\n                      {activePlan?.display_name || 'Ücretsiz'}\n                    </Badge>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-gray-600\">Son Giriş</span>\n                    <span className=\"text-sm text-gray-900\">\n                      {new Date(user.last_sign_in_at || user.created_at).toLocaleDateString('tr-TR')}\n                    </span>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Plan Yönetimi */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2\">\n                    <Crown className=\"h-5 w-5 text-blue-600\" />\n                    Plan Yönetimi\n                  </CardTitle>\n                  <CardDescription>\n                    Mevcut planınızı görüntüleyin ve değiştirin\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  {/* Mevcut Plan */}\n                  <div className=\"p-4 bg-blue-50 rounded-lg border border-blue-200\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <div className=\"flex items-center gap-2\">\n                        {getPlanIcon(activePlan?.plan_name || 'free')}\n                        <span className=\"font-medium text-blue-900\">\n                          {activePlan?.display_name || 'Ücretsiz Plan'}\n                        </span>\n                      </div>\n                      <Badge className={getPlanColor(activePlan?.plan_name || 'free')}>\n                        Aktif\n                      </Badge>\n                    </div>\n                    <div className=\"text-sm text-blue-700 space-y-1\">\n                      <div>• {activePlan?.max_projects || 5} proje limiti</div>\n                      <div>• {activePlan?.max_prompts_per_project || 100} prompt/proje limiti</div>\n                      {activePlan?.expires_at && (\n                        <div>• Bitiş: {new Date(activePlan.expires_at).toLocaleDateString('tr-TR')}</div>\n                      )}\n                    </div>\n                  </div>\n\n                  {/* Plan Seçenekleri */}\n                  <div className=\"space-y-3\">\n                    <Label className=\"text-sm font-medium\">Mevcut Planlar</Label>\n                    {planTypes.map((plan) => (\n                      <div\n                        key={plan.id}\n                        className={`p-3 border rounded-lg transition-all ${\n                          plan.name === activePlan?.plan_name\n                            ? 'border-blue-500 bg-blue-50'\n                            : 'border-gray-200 hover:border-gray-300'\n                        }`}\n                      >\n                        <div className=\"flex items-center justify-between\">\n                          <div className=\"flex items-center gap-3\">\n                            {getPlanIcon(plan.name)}\n                            <div>\n                              <div className=\"font-medium text-sm\">{plan.display_name}</div>\n                              <div className=\"text-xs text-gray-600\">\n                                {plan.max_projects === -1 ? 'Sınırsız' : plan.max_projects} proje, {' '}\n                                {plan.max_prompts_per_project === -1 ? 'Sınırsız' : plan.max_prompts_per_project} prompt/proje\n                              </div>\n                            </div>\n                          </div>\n                          <div className=\"flex items-center gap-2\">\n                            <span className=\"text-sm font-medium\">\n                              {plan.price_monthly === 0 ? 'Ücretsiz' : `₺${plan.price_monthly}/ay`}\n                            </span>\n                            {plan.name !== activePlan?.plan_name && (\n                              <Button\n                                size=\"sm\"\n                                variant={plan.name === 'free' ? 'outline' : 'default'}\n                                onClick={() => handlePlanChange(plan.name)}\n                                disabled={changePlanMutation.isPending}\n                                className=\"ml-2\"\n                              >\n                                {changePlanMutation.isPending ? 'Değiştiriliyor...' : 'Seç'}\n                              </Button>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n\n                  {/* Trial Information */}\n                  {trialInfo?.is_in_trial && (\n                    <div className=\"p-3 bg-green-50 border border-green-200 rounded-lg\">\n                      <div className=\"flex items-center gap-2 mb-1\">\n                        <CheckCircle className=\"h-4 w-4 text-green-600\" />\n                        <span className=\"text-sm font-medium text-green-800\">Deneme Süresi Aktif</span>\n                      </div>\n                      <p className=\"text-xs text-green-700\">\n                        {trialInfo.days_remaining} gün deneme süreniz kaldı.\n                        Bu süre içinde planınızı iptal ederseniz tam iade alabilirsiniz.\n                      </p>\n                    </div>\n                  )}\n\n                  {/* Plan Cancellation */}\n                  {cancellationEligibility?.can_cancel && activePlan?.plan_name !== 'free' && (\n                    <div className=\"pt-4 border-t border-gray-200\">\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <h4 className=\"text-sm font-medium text-gray-900\">Plan İptal</h4>\n                          <p className=\"text-xs text-gray-600\">\n                            {trialInfo?.is_in_trial\n                              ? 'Deneme süresi içinde tam iade alabilirsiniz'\n                              : 'Planınızı istediğiniz zaman iptal edebilirsiniz'\n                            }\n                          </p>\n                        </div>\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => setShowCancellationModal(true)}\n                          className=\"text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300\"\n                        >\n                          <XCircle className=\"h-4 w-4 mr-1\" />\n                          Planı İptal Et\n                        </Button>\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Plan Değişiklik Mesajı */}\n                  {passwordMessage && messageType && (\n                    <div className={`p-3 rounded-lg flex items-center gap-2 ${\n                      messageType === 'success'\n                        ? 'bg-green-50 text-green-800 border border-green-200'\n                        : 'bg-red-50 text-red-800 border border-red-200'\n                    }`}>\n                      {messageType === 'success' ? (\n                        <CheckCircle className=\"h-4 w-4\" />\n                      ) : (\n                        <AlertCircle className=\"h-4 w-4\" />\n                      )}\n                      <span className=\"text-sm\">{passwordMessage}</span>\n                    </div>\n                  )}\n                </CardContent>\n              </Card>\n\n              {/* Güvenlik Önerileri */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-lg\">Güvenlik Önerileri</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-3\">\n                  <div className=\"flex items-start gap-3\">\n                    <Shield className=\"h-4 w-4 text-blue-500 mt-0.5\" />\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-900\">Güçlü Şifre</p>\n                      <p className=\"text-xs text-gray-600\">En az 8 karakter, büyük/küçük harf ve rakam kullanın</p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-start gap-3\">\n                    <Shield className=\"h-4 w-4 text-blue-500 mt-0.5\" />\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-900\">Düzenli Güncelleme</p>\n                      <p className=\"text-xs text-gray-600\">Şifrenizi 3-6 ayda bir değiştirin</p>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Plan Cancellation Modal */}\n      <PlanCancellationModal\n        isOpen={showCancellationModal}\n        onClose={() => setShowCancellationModal(false)}\n        onSuccess={() => {\n          setPasswordMessage('Plan başarıyla iptal edildi. Ücretsiz plana geçtiniz.')\n          setMessageType('success')\n        }}\n      />\n    </AuthGuard>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAhBA;;;;;;;;;;;;;;;;;AAkBe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B;IAC3E,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAC7B,MAAM,EAAE,MAAM,WAAW,EAAE,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD;IAC1C,MAAM,EAAE,MAAM,UAAU,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD;IAC7C,MAAM,EAAE,MAAM,YAAY,EAAE,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAC5C,MAAM,EAAE,MAAM,uBAAuB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,6BAA0B,AAAD;IACnE,MAAM,EAAE,MAAM,SAAS,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD;IAC3C,MAAM,qBAAqB,CAAA,GAAA,4HAAA,CAAA,gBAAa,AAAD;IACvC,MAAM,kBAAkB,CAAA,GAAA,2HAAA,CAAA,aAAU,AAAD;IAEjC,MAAM,uBAAuB,OAAO;QAClC,EAAE,cAAc;QAChB,mBAAmB;QACnB,eAAe;QAEf,IAAI,gBAAgB,iBAAiB;YACnC,mBAAmB;YACnB,eAAe;YACf;QACF;QAEA,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,mBAAmB;YACnB,eAAe;YACf;QACF;QAEA,sBAAsB;QAEtB,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,gLAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU,CAAC;gBAC/C,UAAU;YACZ;YAEA,IAAI,OAAO;gBACT,MAAM;YACR;YAEA,mBAAmB;YACnB,eAAe;YACf,eAAe;YACf,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,mBAAmB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC5D,eAAe;QACjB,SAAU;YACR,sBAAsB;QACxB;IACF;IAEA,MAAM,gBAAgB;QACpB,gBAAgB,MAAM;IACxB;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,uCAAuC;YACvC,MAAM,aAAa,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;YAClD,IAAI,CAAC,YAAY;gBACf,MAAM,IAAI,MAAM;YAClB;YAEA,kDAAkD;YAClD,IAAI,cAAc,WAAW,YAAY,GAAG,WAAW,YAAY,EAAE;gBACnE,IAAI,SAAS,MAAM,GAAG,WAAW,YAAY,EAAE;oBAC7C,MAAM,IAAI,MACR,CAAC,yCAAyC,EAAE,WAAW,YAAY,CAAC,yBAAyB,CAAC,GAC9F,CAAC,QAAQ,EAAE,SAAS,MAAM,CAAC,cAAc,CAAC;gBAE9C;YACF;YAEA,kCAAkC;YAClC,IAAI,cAAc,WAAW,aAAa,GAAG,WAAW,aAAa,EAAE;gBACrE,MAAM,YAAY,OAAO,OAAO,CAC9B,GAAG,WAAW,YAAY,CAAC,6CAA6C,CAAC,GACzE;gBAEF,IAAI,CAAC,WAAW;YAClB;YAEA,MAAM,mBAAmB,WAAW,CAAC;gBACnC;gBACA,cAAc;YAChB;YAEA,mBAAmB;YACnB,eAAe;YAEf,gCAAgC;YAChC,WAAW;gBACT,mBAAmB;gBACnB,eAAe;YACjB,GAAG;QAEL,EAAE,OAAO,OAAO;YACd,mBAAmB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC5D,eAAe;YAEf,sCAAsC;YACtC,WAAW;gBACT,mBAAmB;gBACnB,eAAe;YACjB,GAAG;QACL;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBAAQ,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACpC,KAAK;gBAAgB,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC7C,KAAK;gBAAc,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YAC1C;gBAAS,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;QACpC;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAc,OAAO;YAC1B;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC,mIAAA,CAAA,YAAS;;0BACR,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;;kEAC7B,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;sDAI1C,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;8CAGjC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAKvC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;;sEACT,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAY;;;;;;;sEAG9B,8OAAC,gIAAA,CAAA,kBAAe;sEAAC;;;;;;;;;;;;8DAInB,8OAAC,gIAAA,CAAA,cAAW;oDAAC,WAAU;;sEACrB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;sFAAQ;;;;;;sFACvB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,kMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;8FAChB,8OAAC;oFAAK,WAAU;8FAAiB,KAAK,KAAK;;;;;;;;;;;;;;;;;;8EAG/C,8OAAC;;sFACC,8OAAC,iIAAA,CAAA,QAAK;sFAAC;;;;;;sFACP,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0MAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;8FACpB,8OAAC;oFAAK,WAAU;8FACb,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;sEAKtD,8OAAC;;8EACC,8OAAC,iIAAA,CAAA,QAAK;8EAAC;;;;;;8EACP,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,sMAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;sFAClB,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAY,WAAU;sFAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAS3E,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;;sEACT,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEAAY;;;;;;;sEAG7B,8OAAC,gIAAA,CAAA,kBAAe;sEAAC;;;;;;;;;;;;8DAInB,8OAAC,gIAAA,CAAA,cAAW;8DACV,cAAA,8OAAC;wDAAK,UAAU;wDAAsB,WAAU;;0EAC9C,8OAAC;;kFACC,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAc;;;;;;kFAC7B,8OAAC,iIAAA,CAAA,QAAK;wEACJ,IAAG;wEACH,MAAK;wEACL,aAAY;wEACZ,OAAO;wEACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wEAC9C,UAAU;wEACV,cAAa;wEACb,QAAQ;;;;;;;;;;;;0EAGZ,8OAAC;;kFACC,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAkB;;;;;;kFACjC,8OAAC,iIAAA,CAAA,QAAK;wEACJ,IAAG;wEACH,MAAK;wEACL,aAAY;wEACZ,OAAO;wEACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wEAClD,UAAU;wEACV,cAAa;wEACb,QAAQ;;;;;;;;;;;;4DAIX,iCACC,8OAAC;gEAAI,WAAW,CAAC,+CAA+C,EAC9D,gBAAgB,YACZ,+BACA,0BACJ;;oEACC,gBAAgB,0BACf,8OAAC,2NAAA,CAAA,cAAW;wEAAC,WAAU;;;;;6FAEvB,8OAAC,oNAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;oEAExB;;;;;;;0EAIL,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,UAAU,sBAAsB,CAAC,eAAe,CAAC;gEACjD,WAAU;0EAET,qBAAqB,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQrD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;8DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAU;;;;;;;;;;;8DAEjC,8OAAC,gIAAA,CAAA,cAAW;oDAAC,WAAU;;sEACrB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAW,SAAS,MAAM;;;;;;;;;;;;sEAE3C,8OAAC,qIAAA,CAAA,YAAS;;;;;sEACV,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAY,WAAW,aAAa,YAAY,aAAa;8EACzE,YAAY,gBAAgB;;;;;;;;;;;;sEAGjC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,8OAAC;oEAAK,WAAU;8EACb,IAAI,KAAK,KAAK,eAAe,IAAI,KAAK,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;sDAO9E,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;;sEACT,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAA0B;;;;;;;sEAG7C,8OAAC,gIAAA,CAAA,kBAAe;sEAAC;;;;;;;;;;;;8DAInB,8OAAC,gIAAA,CAAA,cAAW;oDAAC,WAAU;;sEAErB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;gFACZ,YAAY,YAAY,aAAa;8FACtC,8OAAC;oFAAK,WAAU;8FACb,YAAY,gBAAgB;;;;;;;;;;;;sFAGjC,8OAAC,iIAAA,CAAA,QAAK;4EAAC,WAAW,aAAa,YAAY,aAAa;sFAAS;;;;;;;;;;;;8EAInE,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;;gFAAI;gFAAG,YAAY,gBAAgB;gFAAE;;;;;;;sFACtC,8OAAC;;gFAAI;gFAAG,YAAY,2BAA2B;gFAAI;;;;;;;wEAClD,YAAY,4BACX,8OAAC;;gFAAI;gFAAU,IAAI,KAAK,WAAW,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;sEAMxE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEAAC,WAAU;8EAAsB;;;;;;gEACtC,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC;wEAEC,WAAW,CAAC,qCAAqC,EAC/C,KAAK,IAAI,KAAK,YAAY,YACtB,+BACA,yCACJ;kFAEF,cAAA,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;wFACZ,YAAY,KAAK,IAAI;sGACtB,8OAAC;;8GACC,8OAAC;oGAAI,WAAU;8GAAuB,KAAK,YAAY;;;;;;8GACvD,8OAAC;oGAAI,WAAU;;wGACZ,KAAK,YAAY,KAAK,CAAC,IAAI,aAAa,KAAK,YAAY;wGAAC;wGAAS;wGACnE,KAAK,uBAAuB,KAAK,CAAC,IAAI,aAAa,KAAK,uBAAuB;wGAAC;;;;;;;;;;;;;;;;;;;8FAIvF,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAK,WAAU;sGACb,KAAK,aAAa,KAAK,IAAI,aAAa,CAAC,CAAC,EAAE,KAAK,aAAa,CAAC,GAAG,CAAC;;;;;;wFAErE,KAAK,IAAI,KAAK,YAAY,2BACzB,8OAAC,kIAAA,CAAA,SAAM;4FACL,MAAK;4FACL,SAAS,KAAK,IAAI,KAAK,SAAS,YAAY;4FAC5C,SAAS,IAAM,iBAAiB,KAAK,IAAI;4FACzC,UAAU,mBAAmB,SAAS;4FACtC,WAAU;sGAET,mBAAmB,SAAS,GAAG,sBAAsB;;;;;;;;;;;;;;;;;;uEA9BzD,KAAK,EAAE;;;;;;;;;;;wDAwCjB,WAAW,6BACV,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;sFACvB,8OAAC;4EAAK,WAAU;sFAAqC;;;;;;;;;;;;8EAEvD,8OAAC;oEAAE,WAAU;;wEACV,UAAU,cAAc;wEAAC;;;;;;;;;;;;;wDAO/B,yBAAyB,cAAc,YAAY,cAAc,wBAChE,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAoC;;;;;;0FAClD,8OAAC;gFAAE,WAAU;0FACV,WAAW,cACR,gDACA;;;;;;;;;;;;kFAIR,8OAAC,kIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,SAAS,IAAM,yBAAyB;wEACxC,WAAU;;0FAEV,8OAAC,4MAAA,CAAA,UAAO;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;;;;;;wDAQ3C,mBAAmB,6BAClB,8OAAC;4DAAI,WAAW,CAAC,uCAAuC,EACtD,gBAAgB,YACZ,uDACA,gDACJ;;gEACC,gBAAgB,0BACf,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;yFAEvB,8OAAC,oNAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;8EAEzB,8OAAC;oEAAK,WAAU;8EAAW;;;;;;;;;;;;;;;;;;;;;;;;sDAOnC,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;8DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAU;;;;;;;;;;;8DAEjC,8OAAC,gIAAA,CAAA,cAAW;oDAAC,WAAU;;sEACrB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,8OAAC;;sFACC,8OAAC;4EAAE,WAAU;sFAAoC;;;;;;sFACjD,8OAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;sEAGzC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,8OAAC;;sFACC,8OAAC;4EAAE,WAAU;sFAAoC;;;;;;sFACjD,8OAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWrD,8OAAC,mJAAA,CAAA,wBAAqB;gBACpB,QAAQ;gBACR,SAAS,IAAM,yBAAyB;gBACxC,WAAW;oBACT,mBAAmB;oBACnB,eAAe;gBACjB;;;;;;;;;;;;AAIR", "debugId": null}}]}
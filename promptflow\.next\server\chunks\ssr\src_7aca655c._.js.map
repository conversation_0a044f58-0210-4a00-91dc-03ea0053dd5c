{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/hooks/use-contexts.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useQuery, useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { supabaseBrowser as supabase } from \"@/lib/supabase-browser\";\r\nimport { Context, ContextCategory } from \"@/components/context-gallery\";\r\n\r\n// Context Categories Hooks\r\nexport function useContextCategories() {\r\n  return useQuery({\r\n    queryKey: [\"context-categories\"],\r\n    queryFn: async () => {\r\n      const { data, error } = await supabase\r\n        .from(\"context_categories\")\r\n        .select(\"*\")\r\n        .eq(\"is_active\", true)\r\n        .order(\"sort_order\", { ascending: true });\r\n\r\n      if (error) throw error;\r\n      return data as ContextCategory[];\r\n    },\r\n    staleTime: 5 * 60 * 1000, // 5 dakika\r\n  });\r\n}\r\n\r\n// Get all contexts with filters\r\nexport function useContexts(filters?: {\r\n  category_id?: string;\r\n  is_public?: boolean;\r\n  is_template?: boolean;\r\n  is_featured?: boolean;\r\n  search?: string;\r\n  author_id?: string;\r\n}) {\r\n  return useQuery({\r\n    queryKey: [\"contexts\", filters],\r\n    queryFn: async () => {\r\n      let query = supabase\r\n        .from(\"contexts\")\r\n        .select(`\r\n          *,\r\n          category:context_categories(*)\r\n        `)\r\n        .eq(\"status\", \"active\");\r\n\r\n      // Apply filters\r\n      if (filters?.category_id) {\r\n        query = query.eq(\"category_id\", filters.category_id);\r\n      }\r\n      \r\n      if (filters?.is_public !== undefined) {\r\n        query = query.eq(\"is_public\", filters.is_public);\r\n      }\r\n      \r\n      if (filters?.is_template !== undefined) {\r\n        query = query.eq(\"is_template\", filters.is_template);\r\n      }\r\n      \r\n      if (filters?.is_featured !== undefined) {\r\n        query = query.eq(\"is_featured\", filters.is_featured);\r\n      }\r\n\r\n      if (filters?.author_id) {\r\n        query = query.eq(\"author_id\", filters.author_id);\r\n      }\r\n\r\n      if (filters?.search) {\r\n        query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%,tags.cs.{${filters.search}}`);\r\n      }\r\n\r\n      // Order by featured first, then by usage count\r\n      query = query.order(\"is_featured\", { ascending: false })\r\n                   .order(\"usage_count\", { ascending: false });\r\n\r\n      const { data, error } = await query;\r\n\r\n      if (error) throw error;\r\n\r\n      // Transform data to match Context interface\r\n      return data.map((item) => ({\r\n        id: item.id,\r\n        title: item.title,\r\n        description: item.description,\r\n        content: item.content,\r\n        category: item.category,\r\n        author_id: item.author_id,\r\n        author_name: 'Kullanıcı', // Şimdilik static, sonra profiles tablosu eklenebilir\r\n        is_public: item.is_public,\r\n        is_featured: item.is_featured,\r\n        is_template: item.is_template,\r\n        tags: item.tags || [],\r\n        usage_count: item.usage_count,\r\n        like_count: item.like_count,\r\n        view_count: item.view_count,\r\n        approval_status: item.approval_status || 'approved',\r\n        approved_by: item.approved_by,\r\n        approved_at: item.approved_at,\r\n        approval_notes: item.approval_notes,\r\n        created_at: item.created_at,\r\n        updated_at: item.updated_at,\r\n      })) as Context[];\r\n    },\r\n    staleTime: 2 * 60 * 1000, // 2 dakika\r\n  });\r\n}\r\n\r\n// Get single context\r\nexport function useContext(id: string) {\r\n  return useQuery({\r\n    queryKey: [\"context\", id],\r\n    queryFn: async () => {\r\n      const { data, error } = await supabase\r\n        .from(\"contexts\")\r\n        .select(`\r\n          *,\r\n          category:context_categories(*)\r\n        `)\r\n        .eq(\"id\", id)\r\n        .single();\r\n\r\n      if (error) throw error;\r\n\r\n      return {\r\n        id: data.id,\r\n        title: data.title,\r\n        description: data.description,\r\n        content: data.content,\r\n        category: data.category,\r\n        author_id: data.author_id,\r\n        author_name: 'Kullanıcı', // Şimdilik static, sonra profiles tablosu eklenebilir\r\n        is_public: data.is_public,\r\n        is_featured: data.is_featured,\r\n        is_template: data.is_template,\r\n        tags: data.tags || [],\r\n        usage_count: data.usage_count,\r\n        like_count: data.like_count,\r\n        view_count: data.view_count,\r\n        created_at: data.created_at,\r\n        updated_at: data.updated_at,\r\n      } as Context;\r\n    },\r\n    enabled: !!id,\r\n  });\r\n}\r\n\r\n// Create context\r\nexport function useCreateContext() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: async (newContext: {\r\n      title: string;\r\n      description?: string;\r\n      content: string;\r\n      category_id: string;\r\n      is_public?: boolean;\r\n      is_template?: boolean;\r\n      tags?: string[];\r\n    }) => {\r\n      const { data: user } = await supabase.auth.getUser();\r\n      if (!user.user) throw new Error(\"Kullanıcı girişi gerekli\");\r\n\r\n      const { data, error } = await supabase\r\n        .from(\"contexts\")\r\n        .insert({\r\n          ...newContext,\r\n          author_id: user.user.id,\r\n        })\r\n        .select()\r\n        .single();\r\n\r\n      if (error) throw error;\r\n      return data;\r\n    },\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"contexts\"] });\r\n    },\r\n  });\r\n}\r\n\r\n// Update context\r\nexport function useUpdateContext() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: async ({\r\n      id,\r\n      updates\r\n    }: {\r\n      id: string;\r\n      updates: {\r\n        title?: string;\r\n        description?: string;\r\n        content?: string;\r\n        category_id?: string;\r\n        is_public?: boolean;\r\n        is_template?: boolean;\r\n        is_featured?: boolean;\r\n        tags?: string[];\r\n        status?: 'active' | 'inactive' | 'archived';\r\n      }\r\n    }) => {\r\n      const { data: user } = await supabase.auth.getUser();\r\n      if (!user.user) throw new Error(\"Kullanıcı girişi gerekli\");\r\n\r\n      const { data, error } = await supabase\r\n        .from(\"contexts\")\r\n        .update({\r\n          ...updates,\r\n          updated_at: new Date().toISOString(),\r\n        })\r\n        .eq(\"id\", id)\r\n        .eq(\"author_id\", user.user.id) // Sadece kendi context'lerini güncelleyebilir\r\n        .select()\r\n        .single();\r\n\r\n      if (error) throw error;\r\n      return data;\r\n    },\r\n    onSuccess: (data, variables) => {\r\n      queryClient.invalidateQueries({ queryKey: [\"contexts\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"context\", variables.id] });\r\n    },\r\n  });\r\n}\r\n\r\n// Delete context\r\nexport function useDeleteContext() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: async (id: string) => {\r\n      const { data: user } = await supabase.auth.getUser();\r\n      if (!user.user) throw new Error(\"Kullanıcı girişi gerekli\");\r\n\r\n      const { error } = await supabase\r\n        .from(\"contexts\")\r\n        .delete()\r\n        .eq(\"id\", id)\r\n        .eq(\"author_id\", user.user.id); // Sadece kendi context'lerini silebilir\r\n\r\n      if (error) throw error;\r\n    },\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"contexts\"] });\r\n    },\r\n  });\r\n}\r\n\r\n// Approve/Reject context (Admin only)\r\nexport function useApproveContext() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: async ({\r\n      contextId,\r\n      status,\r\n      notes\r\n    }: {\r\n      contextId: string;\r\n      status: 'approved' | 'rejected';\r\n      notes?: string\r\n    }) => {\r\n      const { error } = await supabase.rpc('approve_context', {\r\n        context_id_param: contextId,\r\n        approval_status_param: status,\r\n        approval_notes_param: notes\r\n      });\r\n\r\n      if (error) throw error;\r\n    },\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"contexts\"] });\r\n    },\r\n  });\r\n}\r\n\r\n// Use context (increment usage count)\r\nexport function useUseContext() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: async ({ \r\n      contextId, \r\n      projectId \r\n    }: { \r\n      contextId: string; \r\n      projectId?: string; \r\n    }) => {\r\n      const { data: user } = await supabase.auth.getUser();\r\n      if (!user.user) throw new Error(\"Kullanıcı girişi gerekli\");\r\n\r\n      const { error } = await supabase.rpc(\"increment_context_usage\", {\r\n        context_id_param: contextId,\r\n        user_id_param: user.user.id,\r\n        project_id_param: projectId || null,\r\n      });\r\n\r\n      if (error) throw error;\r\n    },\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"contexts\"] });\r\n    },\r\n  });\r\n}\r\n\r\n// Toggle like\r\nexport function useToggleContextLike() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: async (contextId: string) => {\r\n      const { data: user } = await supabase.auth.getUser();\r\n      if (!user.user) throw new Error(\"Kullanıcı girişi gerekli\");\r\n\r\n      const { data, error } = await supabase.rpc(\"toggle_context_like\", {\r\n        context_id_param: contextId,\r\n        user_id_param: user.user.id,\r\n      });\r\n\r\n      if (error) throw error;\r\n      return data; // Returns true if liked, false if unliked\r\n    },\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"contexts\"] });\r\n    },\r\n  });\r\n}\r\n\r\n// Get user's liked contexts\r\nexport function useUserLikedContexts() {\r\n  return useQuery({\r\n    queryKey: [\"user-liked-contexts\"],\r\n    queryFn: async () => {\r\n      const { data: user } = await supabase.auth.getUser();\r\n      if (!user.user) throw new Error(\"Kullanıcı girişi gerekli\");\r\n\r\n      const { data, error } = await supabase\r\n        .from(\"context_likes\")\r\n        .select(\"context_id\")\r\n        .eq(\"user_id\", user.user.id);\r\n\r\n      if (error) throw error;\r\n      return data.map(item => item.context_id);\r\n    },\r\n  });\r\n}\r\n\r\n// Get context statistics\r\nexport function useContextStats() {\r\n  return useQuery({\r\n    queryKey: [\"context-stats\"],\r\n    queryFn: async () => {\r\n      const { data, error } = await supabase\r\n        .from(\"contexts\")\r\n        .select(\"is_public, is_template, is_featured, status\")\r\n        .eq(\"status\", \"active\");\r\n\r\n      if (error) throw error;\r\n\r\n      const stats = {\r\n        total: data.length,\r\n        public: data.filter(c => c.is_public).length,\r\n        private: data.filter(c => !c.is_public).length,\r\n        templates: data.filter(c => c.is_template).length,\r\n        featured: data.filter(c => c.is_featured).length,\r\n      };\r\n\r\n      return stats;\r\n    },\r\n    staleTime: 5 * 60 * 1000, // 5 dakika\r\n  });\r\n} "], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAAA;AAAA;AACA;AAHA;;;AAOO,SAAS;IACd,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAqB;QAChC,SAAS;YACP,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,sBACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,MAChB,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAK;YAEzC,IAAI,OAAO,MAAM;YACjB,OAAO;QACT;QACA,WAAW,IAAI,KAAK;IACtB;AACF;AAGO,SAAS,YAAY,OAO3B;IACC,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAY;SAAQ;QAC/B,SAAS;YACP,IAAI,QAAQ,iIAAA,CAAA,kBAAQ,CACjB,IAAI,CAAC,YACL,MAAM,CAAC,CAAC;;;QAGT,CAAC,EACA,EAAE,CAAC,UAAU;YAEhB,gBAAgB;YAChB,IAAI,SAAS,aAAa;gBACxB,QAAQ,MAAM,EAAE,CAAC,eAAe,QAAQ,WAAW;YACrD;YAEA,IAAI,SAAS,cAAc,WAAW;gBACpC,QAAQ,MAAM,EAAE,CAAC,aAAa,QAAQ,SAAS;YACjD;YAEA,IAAI,SAAS,gBAAgB,WAAW;gBACtC,QAAQ,MAAM,EAAE,CAAC,eAAe,QAAQ,WAAW;YACrD;YAEA,IAAI,SAAS,gBAAgB,WAAW;gBACtC,QAAQ,MAAM,EAAE,CAAC,eAAe,QAAQ,WAAW;YACrD;YAEA,IAAI,SAAS,WAAW;gBACtB,QAAQ,MAAM,EAAE,CAAC,aAAa,QAAQ,SAAS;YACjD;YAEA,IAAI,SAAS,QAAQ;gBACnB,QAAQ,MAAM,EAAE,CAAC,CAAC,aAAa,EAAE,QAAQ,MAAM,CAAC,qBAAqB,EAAE,QAAQ,MAAM,CAAC,WAAW,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC;YACtH;YAEA,+CAA+C;YAC/C,QAAQ,MAAM,KAAK,CAAC,eAAe;gBAAE,WAAW;YAAM,GACxC,KAAK,CAAC,eAAe;gBAAE,WAAW;YAAM;YAEtD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;YAE9B,IAAI,OAAO,MAAM;YAEjB,4CAA4C;YAC5C,OAAO,KAAK,GAAG,CAAC,CAAC,OAAS,CAAC;oBACzB,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,aAAa,KAAK,WAAW;oBAC7B,SAAS,KAAK,OAAO;oBACrB,UAAU,KAAK,QAAQ;oBACvB,WAAW,KAAK,SAAS;oBACzB,aAAa;oBACb,WAAW,KAAK,SAAS;oBACzB,aAAa,KAAK,WAAW;oBAC7B,aAAa,KAAK,WAAW;oBAC7B,MAAM,KAAK,IAAI,IAAI,EAAE;oBACrB,aAAa,KAAK,WAAW;oBAC7B,YAAY,KAAK,UAAU;oBAC3B,YAAY,KAAK,UAAU;oBAC3B,iBAAiB,KAAK,eAAe,IAAI;oBACzC,aAAa,KAAK,WAAW;oBAC7B,aAAa,KAAK,WAAW;oBAC7B,gBAAgB,KAAK,cAAc;oBACnC,YAAY,KAAK,UAAU;oBAC3B,YAAY,KAAK,UAAU;gBAC7B,CAAC;QACH;QACA,WAAW,IAAI,KAAK;IACtB;AACF;AAGO,SAAS,WAAW,EAAU;IACnC,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;SAAG;QACzB,SAAS;YACP,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,CAAC;;;QAGT,CAAC,EACA,EAAE,CAAC,MAAM,IACT,MAAM;YAET,IAAI,OAAO,MAAM;YAEjB,OAAO;gBACL,IAAI,KAAK,EAAE;gBACX,OAAO,KAAK,KAAK;gBACjB,aAAa,KAAK,WAAW;gBAC7B,SAAS,KAAK,OAAO;gBACrB,UAAU,KAAK,QAAQ;gBACvB,WAAW,KAAK,SAAS;gBACzB,aAAa;gBACb,WAAW,KAAK,SAAS;gBACzB,aAAa,KAAK,WAAW;gBAC7B,aAAa,KAAK,WAAW;gBAC7B,MAAM,KAAK,IAAI,IAAI,EAAE;gBACrB,aAAa,KAAK,WAAW;gBAC7B,YAAY,KAAK,UAAU;gBAC3B,YAAY,KAAK,UAAU;gBAC3B,YAAY,KAAK,UAAU;gBAC3B,YAAY,KAAK,UAAU;YAC7B;QACF;QACA,SAAS,CAAC,CAAC;IACb;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO;YASjB,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;YAClD,IAAI,CAAC,KAAK,IAAI,EAAE,MAAM,IAAI,MAAM;YAEhC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC;gBACN,GAAG,UAAU;gBACb,WAAW,KAAK,IAAI,CAAC,EAAE;YACzB,GACC,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YACjB,OAAO;QACT;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;QACzD;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EACjB,EAAE,EACF,OAAO,EAcR;YACC,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;YAClD,IAAI,CAAC,KAAK,IAAI,EAAE,MAAM,IAAI,MAAM;YAEhC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC;gBACN,GAAG,OAAO;gBACV,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,EAAE,CAAC,MAAM,IACT,EAAE,CAAC,aAAa,KAAK,IAAI,CAAC,EAAE,EAAE,8CAA8C;aAC5E,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YACjB,OAAO;QACT;QACA,WAAW,CAAC,MAAM;YAChB,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACvD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAW,UAAU,EAAE;iBAAC;YAAC;QACtE;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO;YACjB,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;YAClD,IAAI,CAAC,KAAK,IAAI,EAAE,MAAM,IAAI,MAAM;YAEhC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAC7B,IAAI,CAAC,YACL,MAAM,GACN,EAAE,CAAC,MAAM,IACT,EAAE,CAAC,aAAa,KAAK,IAAI,CAAC,EAAE,GAAG,wCAAwC;YAE1E,IAAI,OAAO,MAAM;QACnB;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;QACzD;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EACjB,SAAS,EACT,MAAM,EACN,KAAK,EAKN;YACC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,mBAAmB;gBACtD,kBAAkB;gBAClB,uBAAuB;gBACvB,sBAAsB;YACxB;YAEA,IAAI,OAAO,MAAM;QACnB;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;QACzD;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EACjB,SAAS,EACT,SAAS,EAIV;YACC,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;YAClD,IAAI,CAAC,KAAK,IAAI,EAAE,MAAM,IAAI,MAAM;YAEhC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,2BAA2B;gBAC9D,kBAAkB;gBAClB,eAAe,KAAK,IAAI,CAAC,EAAE;gBAC3B,kBAAkB,aAAa;YACjC;YAEA,IAAI,OAAO,MAAM;QACnB;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;QACzD;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO;YACjB,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;YAClD,IAAI,CAAC,KAAK,IAAI,EAAE,MAAM,IAAI,MAAM;YAEhC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,uBAAuB;gBAChE,kBAAkB;gBAClB,eAAe,KAAK,IAAI,CAAC,EAAE;YAC7B;YAEA,IAAI,OAAO,MAAM;YACjB,OAAO,MAAM,0CAA0C;QACzD;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;QACzD;IACF;AACF;AAGO,SAAS;IACd,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAsB;QACjC,SAAS;YACP,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;YAClD,IAAI,CAAC,KAAK,IAAI,EAAE,MAAM,IAAI,MAAM;YAEhC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,iBACL,MAAM,CAAC,cACP,EAAE,CAAC,WAAW,KAAK,IAAI,CAAC,EAAE;YAE7B,IAAI,OAAO,MAAM;YACjB,OAAO,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,UAAU;QACzC;IACF;AACF;AAGO,SAAS;IACd,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAgB;QAC3B,SAAS;YACP,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,+CACP,EAAE,CAAC,UAAU;YAEhB,IAAI,OAAO,MAAM;YAEjB,MAAM,QAAQ;gBACZ,OAAO,KAAK,MAAM;gBAClB,QAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM;gBAC5C,SAAS,KAAK,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,SAAS,EAAE,MAAM;gBAC9C,WAAW,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,MAAM;gBACjD,UAAU,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,MAAM;YAClD;YAEA,OAAO;QACT;QACA,WAAW,IAAI,KAAK;IACtB;AACF", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/hooks/use-context-to-prompt.ts"], "sourcesContent": ["'use client'\n\nimport { useMutation, useQueryClient } from '@tanstack/react-query'\nimport { useCreatePrompt } from './use-prompts'\nimport { useProjects } from './use-projects'\nimport { Context } from '@/components/context-gallery'\nimport { toast } from 'sonner'\n\ninterface ContextToPromptOptions {\n  /** Target project ID. If not provided, uses active project */\n  projectId?: string\n  /** Custom title for the prompt. If not provided, uses context title */\n  customTitle?: string\n  /** Additional tags to add to the prompt */\n  additionalTags?: string[]\n  /** Whether to mark as favorite */\n  markAsFavorite?: boolean\n  /** Custom category for the prompt */\n  customCategory?: string\n}\n\ninterface ContextToPromptResult {\n  success: boolean\n  promptId?: string\n  error?: string\n}\n\n/**\n * Hook for converting Context Gallery contexts to prompts in current project\n * Handles validation, limits checking, and proper data transformation\n */\nexport function useContextToPrompt() {\n  const queryClient = useQueryClient()\n  const createPromptMutation = useCreatePrompt()\n  const { data: projects } = useProjects()\n\n  return useMutation({\n    mutationFn: async ({\n      context,\n      options = {}\n    }: {\n      context: Context\n      options?: ContextToPromptOptions\n    }): Promise<ContextToPromptResult> => {\n      try {\n        // 1. Determine target project\n        const targetProjectId = options.projectId || getActiveProjectId(projects || [])\n        \n        if (!targetProjectId) {\n          throw new Error('Lütfen önce bir proje seçin')\n        }\n\n        // 2. Prepare prompt data\n        const promptData = {\n          project_id: targetProjectId,\n          prompt_text: context.content,\n          title: options.customTitle || context.title || 'Context Gallery Prompt',\n          category: options.customCategory || extractCategoryFromContext(context),\n          tags: combineTagsFromContext(context, options.additionalTags),\n          order_index: await getNextOrderIndex(targetProjectId),\n          is_used: false,\n          is_favorite: options.markAsFavorite || false\n        }\n\n        // 3. Create prompt using existing hook\n        const newPrompt = await createPromptMutation.mutateAsync(promptData)\n\n        // 4. Track context usage\n        await trackContextUsage(context.id, targetProjectId)\n\n        // 5. Update UI state\n        queryClient.invalidateQueries({ queryKey: ['prompts', targetProjectId] })\n        queryClient.invalidateQueries({ queryKey: ['projects'] })\n\n        return {\n          success: true,\n          promptId: newPrompt.id\n        }\n\n      } catch (error) {\n        console.error('Context to prompt conversion error:', error)\n        \n        const errorMessage = error instanceof Error \n          ? error.message \n          : 'Context prompt olarak eklenirken hata oluştu'\n\n        return {\n          success: false,\n          error: errorMessage\n        }\n      }\n    },\n    onSuccess: (result) => {\n      if (result.success) {\n        toast.success('Context başarıyla projeye eklendi!')\n      } else {\n        toast.error(result.error || 'Bir hata oluştu')\n      }\n    },\n    onError: (error) => {\n      console.error('Context to prompt mutation error:', error)\n      toast.error('Context eklenirken beklenmeyen bir hata oluştu')\n    }\n  })\n}\n\n/**\n * Simplified hook for quick context addition to active project\n */\nexport function useAddContextToProject() {\n  const contextToPromptMutation = useContextToPrompt()\n\n  return {\n    addContext: async (context: Context, customTitle?: string) => {\n      return contextToPromptMutation.mutateAsync({\n        context,\n        options: { customTitle }\n      })\n    },\n    isLoading: contextToPromptMutation.isPending,\n    error: contextToPromptMutation.error\n  }\n}\n\n/**\n * Hook for batch adding multiple contexts to project\n */\nexport function useBatchAddContexts() {\n  const contextToPromptMutation = useContextToPrompt()\n  // const queryClient = useQueryClient() // Unused for now\n\n  return useMutation({\n    mutationFn: async ({\n      contexts,\n      projectId,\n      options = {}\n    }: {\n      contexts: Context[]\n      projectId?: string\n      options?: Omit<ContextToPromptOptions, 'projectId'>\n    }) => {\n      const results = []\n      \n      for (const context of contexts) {\n        try {\n          const result = await contextToPromptMutation.mutateAsync({\n            context,\n            options: { ...options, projectId }\n          })\n          results.push({ context: context.id, result })\n        } catch (error) {\n          results.push({ \n            context: context.id, \n            result: { success: false, error: error instanceof Error ? error.message : 'Unknown error' }\n          })\n        }\n      }\n\n      return results\n    },\n    onSuccess: (results) => {\n      const successCount = results.filter(r => r.result.success).length\n      const totalCount = results.length\n      \n      if (successCount === totalCount) {\n        toast.success(`${successCount} context başarıyla eklendi!`)\n      } else {\n        toast.warning(`${successCount}/${totalCount} context eklendi. Bazı hatalar oluştu.`)\n      }\n    }\n  })\n}\n\n// Helper functions\ninterface ProjectLike {\n  id: string\n}\n\nfunction getActiveProjectId(projects: unknown[]): string | null {\n  if (!projects || projects.length === 0) return null\n\n  // Try to get from localStorage or URL params\n  const stored = localStorage.getItem('activeProjectId')\n  if (stored && projects.find((p: unknown) => (p as ProjectLike)?.id === stored)) {\n    return stored\n  }\n\n  // Fallback to first project\n  return (projects[0] as ProjectLike)?.id || null\n}\n\nfunction extractCategoryFromContext(context: Context): string | undefined {\n  // Extract category from context metadata or tags\n  if (context.category) return context.category.name || context.category.toString()\n  if (context.tags && context.tags.length > 0) {\n    // Use first tag as category if it looks like a category\n    const firstTag = context.tags[0]\n    if (firstTag.startsWith('/')) return firstTag\n  }\n  return undefined\n}\n\nfunction combineTagsFromContext(context: Context, additionalTags?: string[]): string[] {\n  const contextTags = context.tags || []\n  const additional = additionalTags || []\n  \n  // Combine and deduplicate tags\n  const allTags = [...contextTags, ...additional]\n  return Array.from(new Set(allTags)).filter(tag => tag.trim().length > 0)\n}\n\nasync function getNextOrderIndex(_projectId: string): Promise<number> {\n  // This would typically query the database to get the next order index\n  // For now, return a timestamp-based index\n  return Date.now()\n}\n\nasync function trackContextUsage(contextId: string, projectId: string): Promise<void> {\n  try {\n    // Track context usage in analytics\n    // This could be implemented with Supabase or analytics service\n    console.log(`Context ${contextId} used in project ${projectId}`)\n  } catch (error) {\n    console.warn('Failed to track context usage:', error)\n    // Don't throw error for analytics failure\n  }\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AACA;AACA;AAEA;AANA;;;;;AA+BO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,uBAAuB,CAAA,GAAA,8HAAA,CAAA,kBAAe,AAAD;IAC3C,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD;IAErC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EACjB,OAAO,EACP,UAAU,CAAC,CAAC,EAIb;YACC,IAAI;gBACF,8BAA8B;gBAC9B,MAAM,kBAAkB,QAAQ,SAAS,IAAI,mBAAmB,YAAY,EAAE;gBAE9E,IAAI,CAAC,iBAAiB;oBACpB,MAAM,IAAI,MAAM;gBAClB;gBAEA,yBAAyB;gBACzB,MAAM,aAAa;oBACjB,YAAY;oBACZ,aAAa,QAAQ,OAAO;oBAC5B,OAAO,QAAQ,WAAW,IAAI,QAAQ,KAAK,IAAI;oBAC/C,UAAU,QAAQ,cAAc,IAAI,2BAA2B;oBAC/D,MAAM,uBAAuB,SAAS,QAAQ,cAAc;oBAC5D,aAAa,MAAM,kBAAkB;oBACrC,SAAS;oBACT,aAAa,QAAQ,cAAc,IAAI;gBACzC;gBAEA,uCAAuC;gBACvC,MAAM,YAAY,MAAM,qBAAqB,WAAW,CAAC;gBAEzD,yBAAyB;gBACzB,MAAM,kBAAkB,QAAQ,EAAE,EAAE;gBAEpC,qBAAqB;gBACrB,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAW;qBAAgB;gBAAC;gBACvE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBAEvD,OAAO;oBACL,SAAS;oBACT,UAAU,UAAU,EAAE;gBACxB;YAEF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uCAAuC;gBAErD,MAAM,eAAe,iBAAiB,QAClC,MAAM,OAAO,GACb;gBAEJ,OAAO;oBACL,SAAS;oBACT,OAAO;gBACT;YACF;QACF;QACA,WAAW,CAAC;YACV,IAAI,OAAO,OAAO,EAAE;gBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;YAC9B;QACF;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,qCAAqC;YACnD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;AACF;AAKO,SAAS;IACd,MAAM,0BAA0B;IAEhC,OAAO;QACL,YAAY,OAAO,SAAkB;YACnC,OAAO,wBAAwB,WAAW,CAAC;gBACzC;gBACA,SAAS;oBAAE;gBAAY;YACzB;QACF;QACA,WAAW,wBAAwB,SAAS;QAC5C,OAAO,wBAAwB,KAAK;IACtC;AACF;AAKO,SAAS;IACd,MAAM,0BAA0B;IAChC,yDAAyD;IAEzD,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EACjB,QAAQ,EACR,SAAS,EACT,UAAU,CAAC,CAAC,EAKb;YACC,MAAM,UAAU,EAAE;YAElB,KAAK,MAAM,WAAW,SAAU;gBAC9B,IAAI;oBACF,MAAM,SAAS,MAAM,wBAAwB,WAAW,CAAC;wBACvD;wBACA,SAAS;4BAAE,GAAG,OAAO;4BAAE;wBAAU;oBACnC;oBACA,QAAQ,IAAI,CAAC;wBAAE,SAAS,QAAQ,EAAE;wBAAE;oBAAO;gBAC7C,EAAE,OAAO,OAAO;oBACd,QAAQ,IAAI,CAAC;wBACX,SAAS,QAAQ,EAAE;wBACnB,QAAQ;4BAAE,SAAS;4BAAO,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAAgB;oBAC5F;gBACF;YACF;YAEA,OAAO;QACT;QACA,WAAW,CAAC;YACV,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM;YACjE,MAAM,aAAa,QAAQ,MAAM;YAEjC,IAAI,iBAAiB,YAAY;gBAC/B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,aAAa,2BAA2B,CAAC;YAC5D,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,aAAa,CAAC,EAAE,WAAW,sCAAsC,CAAC;YACrF;QACF;IACF;AACF;AAOA,SAAS,mBAAmB,QAAmB;IAC7C,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG,OAAO;IAE/C,6CAA6C;IAC7C,MAAM,SAAS,aAAa,OAAO,CAAC;IACpC,IAAI,UAAU,SAAS,IAAI,CAAC,CAAC,IAAe,AAAC,GAAmB,OAAO,SAAS;QAC9E,OAAO;IACT;IAEA,4BAA4B;IAC5B,OAAO,AAAC,QAAQ,CAAC,EAAE,EAAkB,MAAM;AAC7C;AAEA,SAAS,2BAA2B,OAAgB;IAClD,iDAAiD;IACjD,IAAI,QAAQ,QAAQ,EAAE,OAAO,QAAQ,QAAQ,CAAC,IAAI,IAAI,QAAQ,QAAQ,CAAC,QAAQ;IAC/E,IAAI,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,GAAG;QAC3C,wDAAwD;QACxD,MAAM,WAAW,QAAQ,IAAI,CAAC,EAAE;QAChC,IAAI,SAAS,UAAU,CAAC,MAAM,OAAO;IACvC;IACA,OAAO;AACT;AAEA,SAAS,uBAAuB,OAAgB,EAAE,cAAyB;IACzE,MAAM,cAAc,QAAQ,IAAI,IAAI,EAAE;IACtC,MAAM,aAAa,kBAAkB,EAAE;IAEvC,+BAA+B;IAC/B,MAAM,UAAU;WAAI;WAAgB;KAAW;IAC/C,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,UAAU,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,GAAG,MAAM,GAAG;AACxE;AAEA,eAAe,kBAAkB,UAAkB;IACjD,sEAAsE;IACtE,0CAA0C;IAC1C,OAAO,KAAK,GAAG;AACjB;AAEA,eAAe,kBAAkB,SAAiB,EAAE,SAAiB;IACnE,IAAI;QACF,mCAAmC;QACnC,+DAA+D;QAC/D,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,UAAU,iBAAiB,EAAE,WAAW;IACjE,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,kCAAkC;IAC/C,0CAA0C;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/supabase.ts"], "sourcesContent": ["// Import and re-export the browser client as the main client to avoid multiple instances\nimport { supabaseBrowser } from './supabase-browser'\nexport { supabaseBrowser as supabase } from './supabase-browser'\n\n// Session debug için\nif (typeof window !== 'undefined') {\n  supabaseBrowser.auth.onAuthStateChange((event, session) => {\n    console.log(`🔐 [SUPABASE_CLIENT] Auth state change: ${event}`, {\n      hasSession: !!session,\n      userId: session?.user?.id,\n      email: session?.user?.email,\n      expiresAt: session?.expires_at ? new Date(session.expires_at * 1000).toISOString() : null\n    })\n  })\n}\n\n// Global auth error handler\nif (typeof window !== 'undefined') {\n  supabaseBrowser.auth.onAuthStateChange((event) => {\n    if (event === 'SIGNED_OUT') {\n      // Oturum sonlandığında localStorage'ı temizle\n      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\n      if (supabaseUrl) {\n        localStorage.removeItem('sb-' + supabaseUrl.split('//')[1].split('.')[0] + '-auth-token')\n      }\n    }\n  })\n}\n\n// Database types\nexport interface Database {\n  public: {\n    Tables: {\n      projects: {\n        Row: {\n          id: string\n          user_id: string\n          name: string\n          context_text: string\n          description?: string\n          status?: string\n          tags?: string[]\n          is_public?: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          name: string\n          context_text?: string\n          description?: string\n          status?: string\n          tags?: string[]\n          is_public?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          name?: string\n          context_text?: string\n          description?: string\n          status?: string\n          tags?: string[]\n          is_public?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      prompts: {\n        Row: {\n          id: string\n          project_id: string\n          user_id: string\n          prompt_text: string\n          title: string | null\n          description: string | null\n          category: string | null\n          tags: string[]\n          order_index: number\n          is_used: boolean\n          is_favorite: boolean\n          usage_count: number\n          last_used_at: string | null\n          task_code: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          project_id: string\n          user_id: string\n          prompt_text: string\n          title?: string\n          description?: string\n          category?: string\n          tags?: string[]\n          order_index: number\n          is_used?: boolean\n          is_favorite?: boolean\n          usage_count?: number\n          last_used_at?: string\n          task_code?: string\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          project_id?: string\n          user_id?: string\n          prompt_text?: string\n          title?: string\n          description?: string\n          category?: string\n          tags?: string[]\n          order_index?: number\n          is_used?: boolean\n          is_favorite?: boolean\n          usage_count?: number\n          last_used_at?: string\n          task_code?: string\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      plan_types: {\n        Row: {\n          id: string\n          name: string\n          display_name: string\n          description: string | null\n          price_monthly: number\n          price_yearly: number\n          max_projects: number\n          max_prompts_per_project: number\n          features: Record<string, boolean | string | number>\n          is_active: boolean\n          sort_order: number\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          name: string\n          display_name: string\n          description?: string\n          price_monthly?: number\n          price_yearly?: number\n          max_projects?: number\n          max_prompts_per_project?: number\n          features?: Record<string, boolean | string | number>\n          is_active?: boolean\n          sort_order?: number\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          name?: string\n          display_name?: string\n          description?: string\n          price_monthly?: number\n          price_yearly?: number\n          max_projects?: number\n          max_prompts_per_project?: number\n          features?: Record<string, boolean | string | number>\n          is_active?: boolean\n          sort_order?: number\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      user_plans: {\n        Row: {\n          id: string\n          user_id: string\n          plan_type_id: string\n          status: string\n          billing_cycle: string\n          started_at: string\n          expires_at: string | null\n          cancelled_at: string | null\n          trial_ends_at: string | null\n          cancellation_reason: string | null\n          refund_status: string\n          refund_amount: number\n          auto_renew: boolean\n          payment_method: string | null\n          subscription_id: string | null\n          metadata: Record<string, boolean | string | number>\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          plan_type_id: string\n          status?: string\n          billing_cycle?: string\n          started_at?: string\n          expires_at?: string | null\n          cancelled_at?: string | null\n          trial_ends_at?: string | null\n          cancellation_reason?: string | null\n          refund_status?: string\n          refund_amount?: number\n          auto_renew?: boolean\n          payment_method?: string | null\n          subscription_id?: string | null\n          metadata?: Record<string, boolean | string | number>\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          plan_type_id?: string\n          status?: string\n          billing_cycle?: string\n          started_at?: string\n          expires_at?: string | null\n          cancelled_at?: string | null\n          trial_ends_at?: string | null\n          cancellation_reason?: string | null\n          refund_status?: string\n          refund_amount?: number\n          auto_renew?: boolean\n          payment_method?: string | null\n          subscription_id?: string | null\n          metadata?: Record<string, boolean | string | number>\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      usage_stats: {\n        Row: {\n          id: string\n          user_id: string\n          stat_date: string\n          projects_count: number\n          prompts_count: number\n          api_calls_count: number\n          storage_used_mb: number\n          last_activity_at: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          stat_date?: string\n          projects_count?: number\n          prompts_count?: number\n          api_calls_count?: number\n          storage_used_mb?: number\n          last_activity_at?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          stat_date?: string\n          projects_count?: number\n          prompts_count?: number\n          api_calls_count?: number\n          storage_used_mb?: number\n          last_activity_at?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      plan_transactions: {\n        Row: {\n          id: string\n          user_id: string\n          from_plan_id: string | null\n          to_plan_id: string\n          transaction_type: string\n          amount: number\n          currency: string\n          payment_status: string\n          payment_provider: string | null\n          payment_reference: string | null\n          notes: string | null\n          processed_at: string | null\n          created_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          from_plan_id?: string | null\n          to_plan_id: string\n          transaction_type: string\n          amount?: number\n          currency?: string\n          payment_status?: string\n          payment_provider?: string | null\n          payment_reference?: string | null\n          notes?: string | null\n          processed_at?: string | null\n          created_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          from_plan_id?: string | null\n          to_plan_id?: string\n          transaction_type?: string\n          amount?: number\n          currency?: string\n          payment_status?: string\n          payment_provider?: string | null\n          payment_reference?: string | null\n          notes?: string | null\n          processed_at?: string | null\n          created_at?: string\n        }\n      }\n    }\n    Functions: {\n      get_user_active_plan: {\n        Args: { user_uuid: string }\n        Returns: {\n          plan_id: string\n          plan_name: string\n          display_name: string\n          max_projects: number\n          max_prompts_per_project: number\n          features: Record<string, boolean | string | number>\n          status: string\n          expires_at: string | null\n        }[]\n      }\n      check_user_limits: {\n        Args: { user_uuid: string }\n        Returns: {\n          current_projects: number\n          current_prompts: number\n          max_projects: number\n          max_prompts_per_project: number\n          can_create_project: boolean\n          can_create_prompt: boolean\n        }[]\n      }\n      change_user_plan: {\n        Args: {\n          user_uuid: string\n          new_plan_name: string\n          billing_cycle_param?: string\n          payment_reference_param?: string\n        }\n        Returns: string\n      }\n      shared_prompts: {\n        Row: {\n          id: string\n          prompt_id: string\n          user_id: string\n          share_token: string\n          title: string | null\n          description: string | null\n          is_public: boolean\n          password_hash: string | null\n          expires_at: string | null\n          view_count: number\n          copy_count: number\n          is_active: boolean\n          metadata: any\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          prompt_id: string\n          user_id: string\n          share_token: string\n          title?: string | null\n          description?: string | null\n          is_public?: boolean\n          password_hash?: string | null\n          expires_at?: string | null\n          view_count?: number\n          copy_count?: number\n          is_active?: boolean\n          metadata?: any\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          prompt_id?: string\n          user_id?: string\n          share_token?: string\n          title?: string | null\n          description?: string | null\n          is_public?: boolean\n          password_hash?: string | null\n          expires_at?: string | null\n          view_count?: number\n          copy_count?: number\n          is_active?: boolean\n          metadata?: any\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      shared_prompt_views: {\n        Row: {\n          id: string\n          shared_prompt_id: string\n          viewer_ip: string | null\n          viewer_user_agent: string | null\n          referrer: string | null\n          viewed_at: string\n          session_id: string | null\n        }\n        Insert: {\n          id?: string\n          shared_prompt_id: string\n          viewer_ip?: string | null\n          viewer_user_agent?: string | null\n          referrer?: string | null\n          viewed_at?: string\n          session_id?: string | null\n        }\n        Update: {\n          id?: string\n          shared_prompt_id?: string\n          viewer_ip?: string | null\n          viewer_user_agent?: string | null\n          referrer?: string | null\n          viewed_at?: string\n          session_id?: string | null\n        }\n      }\n    }\n  }\n}"], "names": [], "mappings": "AAAA,yFAAyF;;AACzF;;;AAGA,qBAAqB;AACrB;;AAWA,4BAA4B;AAC5B", "debugId": null}}, {"offset": {"line": 526, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 749, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/context-creation-modal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Badge } from '@/components/ui/badge'\nimport { \n  Dialog, \n  DialogContent, \n  DialogDescription, \n  DialogHeader, \n  DialogTitle \n} from '@/components/ui/dialog'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { \n  Plus, \n  X, \n  Loader2, \n  AlertCircle, \n  Globe, \n  Lock, \n  Tag,\n  FileText\n} from 'lucide-react'\nimport { useContextCategories, useCreateContext } from '@/hooks/use-contexts'\nimport { useAddContextToProject } from '@/hooks/use-context-to-prompt'\nimport { useProjects } from '@/hooks/use-projects'\nimport { toast } from 'sonner'\n\ninterface ContextCreationModalProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  onSuccess?: () => void\n  /** Show option to add directly to current project */\n  showAddToProject?: boolean\n}\n\nexport function ContextCreationModal({\n  open,\n  onOpenChange,\n  onSuccess,\n  showAddToProject = true\n}: ContextCreationModalProps) {\n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    content: '',\n    category_id: '',\n    is_public: false,\n    is_template: false,\n    tags: [] as string[]\n  })\n  const [newTag, setNewTag] = useState('')\n  const [errors, setErrors] = useState<Record<string, string>>({})\n  const [addToProject, setAddToProject] = useState(false)\n\n  const { data: categories = [], isLoading: categoriesLoading, error: categoriesError } = useContextCategories()\n  const { data: projects = [] } = useProjects()\n  const createContextMutation = useCreateContext()\n  const { addContext: addContextToProject, isLoading: isAddingToProject } = useAddContextToProject()\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {}\n\n    if (!formData.title.trim()) {\n      newErrors.title = 'Başlık gereklidir'\n    }\n\n    if (!formData.content.trim()) {\n      newErrors.content = 'İçerik gereklidir'\n    }\n\n    if (!formData.category_id) {\n      newErrors.category_id = 'Kategori seçimi gereklidir'\n    }\n\n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n\n    if (!validateForm()) {\n      toast.error('Lütfen tüm gerekli alanları doldurun')\n      return\n    }\n\n    try {\n      // Create context first\n      const newContext = await createContextMutation.mutateAsync({\n        title: formData.title.trim(),\n        description: formData.description.trim() || undefined,\n        content: formData.content.trim(),\n        category_id: formData.category_id,\n        is_public: formData.is_public,\n        is_template: formData.is_template,\n        tags: formData.tags\n      })\n\n      // If \"Add to Project\" is checked, add to current project\n      if (addToProject && showAddToProject) {\n        try {\n          await addContextToProject(newContext, formData.title.trim())\n          toast.success('Context oluşturuldu ve projeye eklendi!')\n        } catch (projectError) {\n          console.error('Failed to add to project:', projectError)\n          toast.warning('Context oluşturuldu ancak projeye eklenirken hata oluştu')\n        }\n      } else {\n        toast.success('Context başarıyla oluşturuldu!')\n      }\n\n      // Reset form\n      setFormData({\n        title: '',\n        description: '',\n        content: '',\n        category_id: '',\n        is_public: false,\n        is_template: false,\n        tags: []\n      })\n      setErrors({})\n      setAddToProject(false)\n\n      onSuccess?.()\n      onOpenChange(false)\n    } catch (error) {\n      console.error('Context creation error:', error)\n      toast.error('Context oluşturulurken bir hata oluştu')\n    }\n  }\n\n  const addTag = () => {\n    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {\n      setFormData(prev => ({\n        ...prev,\n        tags: [...prev.tags, newTag.trim()]\n      }))\n      setNewTag('')\n    }\n  }\n\n  const removeTag = (tagToRemove: string) => {\n    setFormData(prev => ({\n      ...prev,\n      tags: prev.tags.filter(tag => tag !== tagToRemove)\n    }))\n  }\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter') {\n      e.preventDefault()\n      addTag()\n    }\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-2xl max-h-[90vh] overflow-hidden flex flex-col z-[60]\">\n        <DialogHeader className=\"flex-shrink-0\">\n          <DialogTitle className=\"flex items-center gap-2\">\n            <FileText className=\"h-5 w-5\" />\n            Yeni Context Oluştur\n          </DialogTitle>\n          <DialogDescription>\n            Yeni bir context oluşturun. Herkese açık contextler admin onayı gerektirir.\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"flex-1 overflow-y-auto\">\n          <form onSubmit={handleSubmit} className=\"space-y-6 p-1\">\n            {/* Title */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"title\">\n                Başlık <span className=\"text-red-500\">*</span>\n              </Label>\n              <Input\n                id=\"title\"\n                placeholder=\"Context başlığını girin...\"\n                value={formData.title}\n                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}\n                className={errors.title ? 'border-red-500' : ''}\n              />\n              {errors.title && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-3 w-3\" />\n                  {errors.title}\n                </p>\n              )}\n            </div>\n\n            {/* Description */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"description\">Açıklama</Label>\n              <Input\n                id=\"description\"\n                placeholder=\"Context açıklaması (isteğe bağlı)...\"\n                value={formData.description}\n                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n              />\n            </div>\n\n            {/* Category */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"category\">\n                Kategori <span className=\"text-red-500\">*</span>\n              </Label>\n              <Select\n                value={formData.category_id}\n                onValueChange={(value) => setFormData(prev => ({ ...prev, category_id: value }))}\n              >\n                <SelectTrigger className={errors.category_id ? 'border-red-500' : ''}>\n                  <SelectValue placeholder=\"Kategori seçin...\" />\n                </SelectTrigger>\n                <SelectContent className=\"z-[70]\">\n                  {categoriesLoading ? (\n                    <SelectItem value=\"loading\" disabled>\n                      Kategoriler yükleniyor...\n                    </SelectItem>\n                  ) : categoriesError ? (\n                    <SelectItem value=\"error\" disabled>\n                      Kategori yükleme hatası: {categoriesError.message}\n                    </SelectItem>\n                  ) : categories.length === 0 ? (\n                    <SelectItem value=\"empty\" disabled>\n                      Kategori bulunamadı\n                    </SelectItem>\n                  ) : (\n                    categories.map((category) => (\n                      <SelectItem key={category.id} value={category.id}>\n                        <span className=\"flex items-center gap-2\">\n                          <span>{category.icon || '📁'}</span>\n                          {category.name}\n                        </span>\n                      </SelectItem>\n                    ))\n                  )}\n                </SelectContent>\n              </Select>\n              {errors.category_id && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-3 w-3\" />\n                  {errors.category_id}\n                </p>\n              )}\n            </div>\n\n            {/* Content */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"content\">\n                İçerik <span className=\"text-red-500\">*</span>\n              </Label>\n              <Textarea\n                id=\"content\"\n                placeholder=\"Context içeriğini girin...\"\n                value={formData.content}\n                onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}\n                className={`min-h-[120px] resize-none ${errors.content ? 'border-red-500' : ''}`}\n              />\n              {errors.content && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-3 w-3\" />\n                  {errors.content}\n                </p>\n              )}\n            </div>\n\n            {/* Tags */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"tags\">Etiketler</Label>\n              <div className=\"flex gap-2\">\n                <Input\n                  id=\"tags\"\n                  placeholder=\"Etiket ekle...\"\n                  value={newTag}\n                  onChange={(e) => setNewTag(e.target.value)}\n                  onKeyPress={handleKeyPress}\n                  className=\"flex-1\"\n                />\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={addTag}\n                  disabled={!newTag.trim()}\n                >\n                  <Plus className=\"h-4 w-4\" />\n                </Button>\n              </div>\n              {formData.tags.length > 0 && (\n                <div className=\"flex flex-wrap gap-2 mt-2\">\n                  {formData.tags.map((tag) => (\n                    <Badge key={tag} variant=\"secondary\" className=\"flex items-center gap-1\">\n                      <Tag className=\"h-3 w-3\" />\n                      {tag}\n                      <button\n                        type=\"button\"\n                        onClick={() => removeTag(tag)}\n                        className=\"ml-1 hover:text-red-500\"\n                      >\n                        <X className=\"h-3 w-3\" />\n                      </button>\n                    </Badge>\n                  ))}\n                </div>\n              )}\n            </div>\n\n            {/* Visibility Options */}\n            <div className=\"space-y-3\">\n              <Label>Görünürlük Ayarları</Label>\n              <div className=\"space-y-2\">\n                <label className=\"flex items-center gap-3 cursor-pointer\">\n                  <input\n                    type=\"radio\"\n                    name=\"visibility\"\n                    checked={!formData.is_public}\n                    onChange={() => setFormData(prev => ({ ...prev, is_public: false }))}\n                    className=\"w-4 h-4\"\n                  />\n                  <div className=\"flex items-center gap-2\">\n                    <Lock className=\"h-4 w-4 text-gray-500\" />\n                    <div className=\"flex flex-col\">\n                      <span className=\"text-sm\">Özel</span>\n                      <span className=\"text-xs text-gray-500\">Sadece ben görebilirim</span>\n                    </div>\n                  </div>\n                </label>\n                <label className=\"flex items-center gap-3 cursor-pointer\">\n                  <input\n                    type=\"radio\"\n                    name=\"visibility\"\n                    checked={formData.is_public}\n                    onChange={() => setFormData(prev => ({ ...prev, is_public: true }))}\n                    className=\"w-4 h-4\"\n                  />\n                  <div className=\"flex items-center gap-2\">\n                    <Globe className=\"h-4 w-4 text-blue-500\" />\n                    <div className=\"flex flex-col\">\n                      <span className=\"text-sm\">Herkese Açık</span>\n                      <span className=\"text-xs text-gray-500\">Tüm kullanıcılar görebilir</span>\n                    </div>\n                  </div>\n                </label>\n              </div>\n            </div>\n\n            {/* Template Option */}\n            <div className=\"space-y-2\">\n              <label className=\"flex items-center gap-3 cursor-pointer\">\n                <input\n                  type=\"checkbox\"\n                  checked={formData.is_template}\n                  onChange={(e) => setFormData(prev => ({ ...prev, is_template: e.target.checked }))}\n                  className=\"w-4 h-4\"\n                />\n                <span className=\"text-sm\">Bu contexti şablon olarak işaretle</span>\n              </label>\n            </div>\n\n            {/* Add to Project Option */}\n            {showAddToProject && projects.length > 0 && (\n              <div className=\"space-y-2 p-3 bg-blue-50 rounded-lg border border-blue-200\">\n                <label className=\"flex items-center gap-3 cursor-pointer\">\n                  <input\n                    type=\"checkbox\"\n                    checked={addToProject}\n                    onChange={(e) => setAddToProject(e.target.checked)}\n                    className=\"w-4 h-4 text-blue-600\"\n                  />\n                  <div className=\"flex items-center gap-2\">\n                    <FileText className=\"h-4 w-4 text-blue-600\" />\n                    <div className=\"flex flex-col\">\n                      <span className=\"text-sm font-medium text-blue-900\">Mevcut projeye ekle</span>\n                      <span className=\"text-xs text-blue-700\">Context oluşturulduktan sonra aktif projeye prompt olarak eklenecek</span>\n                    </div>\n                  </div>\n                </label>\n              </div>\n            )}\n\n            {/* Submit Buttons */}\n            <div className=\"flex gap-3 pt-4\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => onOpenChange(false)}\n                className=\"flex-1\"\n              >\n                İptal\n              </Button>\n              <Button\n                type=\"submit\"\n                disabled={createContextMutation.isPending || isAddingToProject}\n                className=\"flex-1\"\n              >\n                {(createContextMutation.isPending || isAddingToProject) && (\n                  <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                )}\n                {addToProject ? 'Oluştur ve Ekle' : 'Oluştur'}\n              </Button>\n            </div>\n          </form>\n        </div>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AACA;AAnCA;;;;;;;;;;;;;;;AA6CO,SAAS,qBAAqB,EACnC,IAAI,EACJ,YAAY,EACZ,SAAS,EACT,mBAAmB,IAAI,EACG;IAC1B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,aAAa;QACb,SAAS;QACT,aAAa;QACb,WAAW;QACX,aAAa;QACb,MAAM,EAAE;IACV;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,EAAE,MAAM,aAAa,EAAE,EAAE,WAAW,iBAAiB,EAAE,OAAO,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,uBAAoB,AAAD;IAC3G,MAAM,EAAE,MAAM,WAAW,EAAE,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD;IAC1C,MAAM,wBAAwB,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD;IAC7C,MAAM,EAAE,YAAY,mBAAmB,EAAE,WAAW,iBAAiB,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,yBAAsB,AAAD;IAE/F,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,UAAU,OAAO,GAAG;QACtB;QAEA,IAAI,CAAC,SAAS,WAAW,EAAE;YACzB,UAAU,WAAW,GAAG;QAC1B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,uBAAuB;YACvB,MAAM,aAAa,MAAM,sBAAsB,WAAW,CAAC;gBACzD,OAAO,SAAS,KAAK,CAAC,IAAI;gBAC1B,aAAa,SAAS,WAAW,CAAC,IAAI,MAAM;gBAC5C,SAAS,SAAS,OAAO,CAAC,IAAI;gBAC9B,aAAa,SAAS,WAAW;gBACjC,WAAW,SAAS,SAAS;gBAC7B,aAAa,SAAS,WAAW;gBACjC,MAAM,SAAS,IAAI;YACrB;YAEA,yDAAyD;YACzD,IAAI,gBAAgB,kBAAkB;gBACpC,IAAI;oBACF,MAAM,oBAAoB,YAAY,SAAS,KAAK,CAAC,IAAI;oBACzD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB,EAAE,OAAO,cAAc;oBACrB,QAAQ,KAAK,CAAC,6BAA6B;oBAC3C,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB;YACF,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;YAEA,aAAa;YACb,YAAY;gBACV,OAAO;gBACP,aAAa;gBACb,SAAS;gBACT,aAAa;gBACb,WAAW;gBACX,aAAa;gBACb,MAAM,EAAE;YACV;YACA,UAAU,CAAC;YACX,gBAAgB;YAEhB;YACA,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,SAAS;QACb,IAAI,OAAO,IAAI,MAAM,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,KAAK;YAC3D,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,MAAM;2BAAI,KAAK,IAAI;wBAAE,OAAO,IAAI;qBAAG;gBACrC,CAAC;YACD,UAAU;QACZ;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,QAAQ;YACxC,CAAC;IACH;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGlC,8OAAC,kIAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAEtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;;4CAAQ;0DACd,8OAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAExC,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAY;wCACZ,OAAO,SAAS,KAAK;wCACrB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCACxE,WAAW,OAAO,KAAK,GAAG,mBAAmB;;;;;;oCAE9C,OAAO,KAAK,kBACX,8OAAC;wCAAE,WAAU;;0DACX,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,KAAK;;;;;;;;;;;;;0CAMnB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAc;;;;;;kDAC7B,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAY;wCACZ,OAAO,SAAS,WAAW;wCAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;;;;;;;;;;;;0CAKlF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;;4CAAW;0DACf,8OAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAE1C,8OAAC,kIAAA,CAAA,SAAM;wCACL,OAAO,SAAS,WAAW;wCAC3B,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,aAAa;gDAAM,CAAC;;0DAE9E,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAW,OAAO,WAAW,GAAG,mBAAmB;0DAChE,cAAA,8OAAC,kIAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACtB,kCACC,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;oDAAU,QAAQ;8DAAC;;;;;2DAGnC,gCACF,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;oDAAQ,QAAQ;;wDAAC;wDACP,gBAAgB,OAAO;;;;;;2DAEjD,WAAW,MAAM,KAAK,kBACxB,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;oDAAQ,QAAQ;8DAAC;;;;;2DAInC,WAAW,GAAG,CAAC,CAAC,yBACd,8OAAC,kIAAA,CAAA,aAAU;wDAAmB,OAAO,SAAS,EAAE;kEAC9C,cAAA,8OAAC;4DAAK,WAAU;;8EACd,8OAAC;8EAAM,SAAS,IAAI,IAAI;;;;;;gEACvB,SAAS,IAAI;;;;;;;uDAHD,SAAS,EAAE;;;;;;;;;;;;;;;;oCAUnC,OAAO,WAAW,kBACjB,8OAAC;wCAAE,WAAU;;0DACX,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,WAAW;;;;;;;;;;;;;0CAMzB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;;4CAAU;0DAChB,8OAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAExC,8OAAC,oIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,aAAY;wCACZ,OAAO,SAAS,OAAO;wCACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC1E,WAAW,CAAC,0BAA0B,EAAE,OAAO,OAAO,GAAG,mBAAmB,IAAI;;;;;;oCAEjF,OAAO,OAAO,kBACb,8OAAC;wCAAE,WAAU;;0DACX,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,OAAO;;;;;;;;;;;;;0CAMrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAO;;;;;;kDACtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gDACzC,YAAY;gDACZ,WAAU;;;;;;0DAEZ,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC,OAAO,IAAI;0DAEtB,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;oCAGnB,SAAS,IAAI,CAAC,MAAM,GAAG,mBACtB,8OAAC;wCAAI,WAAU;kDACZ,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,oBAClB,8OAAC,iIAAA,CAAA,QAAK;gDAAW,SAAQ;gDAAY,WAAU;;kEAC7C,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDACd;kEACD,8OAAC;wDACC,MAAK;wDACL,SAAS,IAAM,UAAU;wDACzB,WAAU;kEAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4DAAC,WAAU;;;;;;;;;;;;+CARL;;;;;;;;;;;;;;;;0CAiBpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,SAAS,CAAC,SAAS,SAAS;wDAC5B,UAAU,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,WAAW;gEAAM,CAAC;wDAClE,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAU;;;;;;kFAC1B,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;0DAI9C,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,SAAS,SAAS,SAAS;wDAC3B,UAAU,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,WAAW;gEAAK,CAAC;wDACjE,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAU;;;;;;kFAC1B,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQlD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CACC,MAAK;4CACL,SAAS,SAAS,WAAW;4CAC7B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,aAAa,EAAE,MAAM,CAAC,OAAO;oDAAC,CAAC;4CAChF,WAAU;;;;;;sDAEZ,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;4BAK7B,oBAAoB,SAAS,MAAM,GAAG,mBACrC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,OAAO;4CACjD,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAoC;;;;;;sEACpD,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQlD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,SAAS,IAAM,aAAa;wCAC5B,WAAU;kDACX;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,UAAU,sBAAsB,SAAS,IAAI;wCAC7C,WAAU;;4CAET,CAAC,sBAAsB,SAAS,IAAI,iBAAiB,mBACpD,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAEpB,eAAe,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpD", "debugId": null}}, {"offset": {"line": 1631, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/context-edit-modal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Badge } from '@/components/ui/badge'\nimport { \n  Dialog, \n  DialogContent, \n  DialogDescription, \n  DialogHeader, \n  DialogTitle \n} from '@/components/ui/dialog'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { \n  Plus, \n  X, \n  Loader2, \n  AlertCircle, \n  Globe, \n  Lock, \n  Tag,\n  Edit3\n} from 'lucide-react'\nimport { useContextCategories, useUpdateContext } from '@/hooks/use-contexts'\nimport { Context } from './context-gallery'\nimport { toast } from 'sonner'\n\ninterface ContextEditModalProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  context: Context | null\n  onSuccess?: () => void\n}\n\nexport function ContextEditModal({ \n  open, \n  onOpenChange, \n  context,\n  onSuccess \n}: ContextEditModalProps) {\n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    content: '',\n    category_id: '',\n    is_public: false,\n    is_template: false,\n    tags: [] as string[]\n  })\n  const [newTag, setNewTag] = useState('')\n  const [errors, setErrors] = useState<Record<string, string>>({})\n\n  const { data: categories = [], isLoading: categoriesLoading } = useContextCategories()\n  const updateContextMutation = useUpdateContext()\n\n  // Initialize form data when context changes\n  useEffect(() => {\n    if (context) {\n      setFormData({\n        title: context.title,\n        description: context.description || '',\n        content: context.content,\n        category_id: context.category.id,\n        is_public: context.is_public,\n        is_template: context.is_template,\n        tags: context.tags || []\n      })\n    }\n  }, [context])\n\n  // Reset form when modal closes\n  useEffect(() => {\n    if (!open) {\n      setErrors({})\n      setNewTag('')\n    }\n  }, [open])\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {}\n\n    if (!formData.title.trim()) {\n      newErrors.title = 'Başlık gereklidir'\n    }\n\n    if (!formData.content.trim()) {\n      newErrors.content = 'İçerik gereklidir'\n    }\n\n    if (!formData.category_id) {\n      newErrors.category_id = 'Kategori seçimi gereklidir'\n    }\n\n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!context) return\n    \n    if (!validateForm()) {\n      toast.error('Lütfen tüm gerekli alanları doldurun')\n      return\n    }\n\n    try {\n      await updateContextMutation.mutateAsync({\n        id: context.id,\n        updates: {\n          title: formData.title.trim(),\n          description: formData.description.trim() || undefined,\n          content: formData.content.trim(),\n          category_id: formData.category_id,\n          is_public: formData.is_public,\n          is_template: formData.is_template,\n          tags: formData.tags\n        }\n      })\n\n      toast.success('Context başarıyla güncellendi!')\n      \n      onSuccess?.()\n      onOpenChange(false)\n    } catch (error) {\n      console.error('Context update error:', error)\n      toast.error('Context güncellenirken bir hata oluştu')\n    }\n  }\n\n  const addTag = () => {\n    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {\n      setFormData(prev => ({\n        ...prev,\n        tags: [...prev.tags, newTag.trim()]\n      }))\n      setNewTag('')\n    }\n  }\n\n  const removeTag = (tagToRemove: string) => {\n    setFormData(prev => ({\n      ...prev,\n      tags: prev.tags.filter(tag => tag !== tagToRemove)\n    }))\n  }\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter') {\n      e.preventDefault()\n      addTag()\n    }\n  }\n\n  if (!context) return null\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-2xl max-h-[90vh] overflow-hidden flex flex-col z-[60]\">\n        <DialogHeader className=\"flex-shrink-0\">\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Edit3 className=\"h-5 w-5\" />\n            Context Düzenle\n          </DialogTitle>\n          <DialogDescription>\n            Context bilgilerini düzenleyin. Herkese açık contextler admin onayı gerektirir.\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"flex-1 overflow-y-auto\">\n          <form onSubmit={handleSubmit} className=\"space-y-6 p-1\">\n            {/* Title */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"title\">\n                Başlık <span className=\"text-red-500\">*</span>\n              </Label>\n              <Input\n                id=\"title\"\n                placeholder=\"Context başlığını girin...\"\n                value={formData.title}\n                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}\n                className={errors.title ? 'border-red-500' : ''}\n              />\n              {errors.title && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-3 w-3\" />\n                  {errors.title}\n                </p>\n              )}\n            </div>\n\n            {/* Description */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"description\">Açıklama</Label>\n              <Input\n                id=\"description\"\n                placeholder=\"Context açıklaması (isteğe bağlı)...\"\n                value={formData.description}\n                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n              />\n            </div>\n\n            {/* Category */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"category\">\n                Kategori <span className=\"text-red-500\">*</span>\n              </Label>\n              <Select\n                value={formData.category_id}\n                onValueChange={(value) => setFormData(prev => ({ ...prev, category_id: value }))}\n              >\n                <SelectTrigger className={errors.category_id ? 'border-red-500' : ''}>\n                  <SelectValue placeholder=\"Kategori seçin...\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {categoriesLoading ? (\n                    <SelectItem value=\"loading\" disabled>\n                      Kategoriler yükleniyor...\n                    </SelectItem>\n                  ) : (\n                    categories.map((category) => (\n                      <SelectItem key={category.id} value={category.id}>\n                        <span className=\"flex items-center gap-2\">\n                          <span>{category.icon}</span>\n                          {category.name}\n                        </span>\n                      </SelectItem>\n                    ))\n                  )}\n                </SelectContent>\n              </Select>\n              {errors.category_id && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-3 w-3\" />\n                  {errors.category_id}\n                </p>\n              )}\n            </div>\n\n            {/* Content */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"content\">\n                İçerik <span className=\"text-red-500\">*</span>\n              </Label>\n              <Textarea\n                id=\"content\"\n                placeholder=\"Context içeriğini girin...\"\n                value={formData.content}\n                onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}\n                className={`min-h-[120px] resize-none ${errors.content ? 'border-red-500' : ''}`}\n              />\n              {errors.content && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-3 w-3\" />\n                  {errors.content}\n                </p>\n              )}\n            </div>\n\n            {/* Tags */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"tags\">Etiketler</Label>\n              <div className=\"flex gap-2\">\n                <Input\n                  id=\"tags\"\n                  placeholder=\"Etiket ekle...\"\n                  value={newTag}\n                  onChange={(e) => setNewTag(e.target.value)}\n                  onKeyPress={handleKeyPress}\n                  className=\"flex-1\"\n                />\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={addTag}\n                  disabled={!newTag.trim()}\n                >\n                  <Plus className=\"h-4 w-4\" />\n                </Button>\n              </div>\n              {formData.tags.length > 0 && (\n                <div className=\"flex flex-wrap gap-2 mt-2\">\n                  {formData.tags.map((tag) => (\n                    <Badge key={tag} variant=\"secondary\" className=\"flex items-center gap-1\">\n                      <Tag className=\"h-3 w-3\" />\n                      {tag}\n                      <button\n                        type=\"button\"\n                        onClick={() => removeTag(tag)}\n                        className=\"ml-1 hover:text-red-500\"\n                      >\n                        <X className=\"h-3 w-3\" />\n                      </button>\n                    </Badge>\n                  ))}\n                </div>\n              )}\n            </div>\n\n            {/* Visibility Options */}\n            <div className=\"space-y-3\">\n              <Label>Görünürlük Ayarları</Label>\n              <div className=\"space-y-2\">\n                <label className=\"flex items-center gap-3 cursor-pointer\">\n                  <input\n                    type=\"radio\"\n                    name=\"visibility\"\n                    checked={!formData.is_public}\n                    onChange={() => setFormData(prev => ({ ...prev, is_public: false }))}\n                    className=\"w-4 h-4\"\n                  />\n                  <div className=\"flex items-center gap-2\">\n                    <Lock className=\"h-4 w-4 text-gray-500\" />\n                    <div className=\"flex flex-col\">\n                      <span className=\"text-sm\">Özel</span>\n                      <span className=\"text-xs text-gray-500\">Sadece ben görebilirim</span>\n                    </div>\n                  </div>\n                </label>\n                <label className=\"flex items-center gap-3 cursor-pointer\">\n                  <input\n                    type=\"radio\"\n                    name=\"visibility\"\n                    checked={formData.is_public}\n                    onChange={() => setFormData(prev => ({ ...prev, is_public: true }))}\n                    className=\"w-4 h-4\"\n                  />\n                  <div className=\"flex items-center gap-2\">\n                    <Globe className=\"h-4 w-4 text-blue-500\" />\n                    <div className=\"flex flex-col\">\n                      <span className=\"text-sm\">Herkese Açık</span>\n                      <span className=\"text-xs text-gray-500\">Tüm kullanıcılar görebilir</span>\n                    </div>\n                  </div>\n                </label>\n              </div>\n            </div>\n\n            {/* Template Option */}\n            <div className=\"space-y-2\">\n              <label className=\"flex items-center gap-3 cursor-pointer\">\n                <input\n                  type=\"checkbox\"\n                  checked={formData.is_template}\n                  onChange={(e) => setFormData(prev => ({ ...prev, is_template: e.target.checked }))}\n                  className=\"w-4 h-4\"\n                />\n                <span className=\"text-sm\">Bu contexti şablon olarak işaretle</span>\n              </label>\n            </div>\n\n            {/* Submit Buttons */}\n            <div className=\"flex gap-3 pt-4\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => onOpenChange(false)}\n                className=\"flex-1\"\n              >\n                İptal\n              </Button>\n              <Button\n                type=\"submit\"\n                disabled={updateContextMutation.isPending}\n                className=\"flex-1\"\n              >\n                {updateContextMutation.isPending && (\n                  <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                )}\n                Güncelle\n              </Button>\n            </div>\n          </form>\n        </div>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAEA;AAlCA;;;;;;;;;;;;;AA2CO,SAAS,iBAAiB,EAC/B,IAAI,EACJ,YAAY,EACZ,OAAO,EACP,SAAS,EACa;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,aAAa;QACb,SAAS;QACT,aAAa;QACb,WAAW;QACX,aAAa;QACb,MAAM,EAAE;IACV;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,MAAM,EAAE,MAAM,aAAa,EAAE,EAAE,WAAW,iBAAiB,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,uBAAoB,AAAD;IACnF,MAAM,wBAAwB,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD;IAE7C,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;YACX,YAAY;gBACV,OAAO,QAAQ,KAAK;gBACpB,aAAa,QAAQ,WAAW,IAAI;gBACpC,SAAS,QAAQ,OAAO;gBACxB,aAAa,QAAQ,QAAQ,CAAC,EAAE;gBAChC,WAAW,QAAQ,SAAS;gBAC5B,aAAa,QAAQ,WAAW;gBAChC,MAAM,QAAQ,IAAI,IAAI,EAAE;YAC1B;QACF;IACF,GAAG;QAAC;KAAQ;IAEZ,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,MAAM;YACT,UAAU,CAAC;YACX,UAAU;QACZ;IACF,GAAG;QAAC;KAAK;IAET,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,UAAU,OAAO,GAAG;QACtB;QAEA,IAAI,CAAC,SAAS,WAAW,EAAE;YACzB,UAAU,WAAW,GAAG;QAC1B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS;QAEd,IAAI,CAAC,gBAAgB;YACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,sBAAsB,WAAW,CAAC;gBACtC,IAAI,QAAQ,EAAE;gBACd,SAAS;oBACP,OAAO,SAAS,KAAK,CAAC,IAAI;oBAC1B,aAAa,SAAS,WAAW,CAAC,IAAI,MAAM;oBAC5C,SAAS,SAAS,OAAO,CAAC,IAAI;oBAC9B,aAAa,SAAS,WAAW;oBACjC,WAAW,SAAS,SAAS;oBAC7B,aAAa,SAAS,WAAW;oBACjC,MAAM,SAAS,IAAI;gBACrB;YACF;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAEd;YACA,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,SAAS;QACb,IAAI,OAAO,IAAI,MAAM,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,KAAK;YAC3D,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,MAAM;2BAAI,KAAK,IAAI;wBAAE,OAAO,IAAI;qBAAG;gBACrC,CAAC;YACD,UAAU;QACZ;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,QAAQ;YACxC,CAAC;IACH;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB,EAAE,cAAc;YAChB;QACF;IACF;IAEA,IAAI,CAAC,SAAS,OAAO;IAErB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,0MAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAG/B,8OAAC,kIAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAEtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;;4CAAQ;0DACd,8OAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAExC,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAY;wCACZ,OAAO,SAAS,KAAK;wCACrB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCACxE,WAAW,OAAO,KAAK,GAAG,mBAAmB;;;;;;oCAE9C,OAAO,KAAK,kBACX,8OAAC;wCAAE,WAAU;;0DACX,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,KAAK;;;;;;;;;;;;;0CAMnB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAc;;;;;;kDAC7B,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAY;wCACZ,OAAO,SAAS,WAAW;wCAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;;;;;;;;;;;;0CAKlF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;;4CAAW;0DACf,8OAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAE1C,8OAAC,kIAAA,CAAA,SAAM;wCACL,OAAO,SAAS,WAAW;wCAC3B,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,aAAa;gDAAM,CAAC;;0DAE9E,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAW,OAAO,WAAW,GAAG,mBAAmB;0DAChE,cAAA,8OAAC,kIAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,8OAAC,kIAAA,CAAA,gBAAa;0DACX,kCACC,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;oDAAU,QAAQ;8DAAC;;;;;2DAIrC,WAAW,GAAG,CAAC,CAAC,yBACd,8OAAC,kIAAA,CAAA,aAAU;wDAAmB,OAAO,SAAS,EAAE;kEAC9C,cAAA,8OAAC;4DAAK,WAAU;;8EACd,8OAAC;8EAAM,SAAS,IAAI;;;;;;gEACnB,SAAS,IAAI;;;;;;;uDAHD,SAAS,EAAE;;;;;;;;;;;;;;;;oCAUnC,OAAO,WAAW,kBACjB,8OAAC;wCAAE,WAAU;;0DACX,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,WAAW;;;;;;;;;;;;;0CAMzB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;;4CAAU;0DAChB,8OAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAExC,8OAAC,oIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,aAAY;wCACZ,OAAO,SAAS,OAAO;wCACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC1E,WAAW,CAAC,0BAA0B,EAAE,OAAO,OAAO,GAAG,mBAAmB,IAAI;;;;;;oCAEjF,OAAO,OAAO,kBACb,8OAAC;wCAAE,WAAU;;0DACX,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,OAAO;;;;;;;;;;;;;0CAMrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAO;;;;;;kDACtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gDACzC,YAAY;gDACZ,WAAU;;;;;;0DAEZ,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC,OAAO,IAAI;0DAEtB,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;oCAGnB,SAAS,IAAI,CAAC,MAAM,GAAG,mBACtB,8OAAC;wCAAI,WAAU;kDACZ,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,oBAClB,8OAAC,iIAAA,CAAA,QAAK;gDAAW,SAAQ;gDAAY,WAAU;;kEAC7C,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDACd;kEACD,8OAAC;wDACC,MAAK;wDACL,SAAS,IAAM,UAAU;wDACzB,WAAU;kEAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4DAAC,WAAU;;;;;;;;;;;;+CARL;;;;;;;;;;;;;;;;0CAiBpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,SAAS,CAAC,SAAS,SAAS;wDAC5B,UAAU,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,WAAW;gEAAM,CAAC;wDAClE,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAU;;;;;;kFAC1B,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;0DAI9C,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,SAAS,SAAS,SAAS;wDAC3B,UAAU,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,WAAW;gEAAK,CAAC;wDACjE,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAU;;;;;;kFAC1B,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQlD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CACC,MAAK;4CACL,SAAS,SAAS,WAAW;4CAC7B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,aAAa,EAAE,MAAM,CAAC,OAAO;oDAAC,CAAC;4CAChF,WAAU;;;;;;sDAEZ,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;0CAK9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,SAAS,IAAM,aAAa;wCAC5B,WAAU;kDACX;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,UAAU,sBAAsB,SAAS;wCACzC,WAAU;;4CAET,sBAAsB,SAAS,kBAC9B,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlB", "debugId": null}}, {"offset": {"line": 2425, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/context-gallery.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, use<PERSON>emo, useEffect, useRef } from \"react\";\r\nimport { Search, Plus, Heart, Eye, Copy, Filter, Tag, User, Globe, Lock, Star, Edit3, Clock, CheckCircle, X } from \"lucide-react\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>T<PERSON>le, DialogTrigger } from \"./ui/dialog\";\r\nimport { Button } from \"./ui/button\";\r\nimport { Input } from \"./ui/input\";\r\nimport { Badge } from \"./ui/badge\";\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"./ui/card\";\r\nimport { ScrollArea } from \"./ui/scroll-area\";\r\nimport { Separator } from \"./ui/separator\";\r\nimport { useContexts, useContextCategories, useUseContext, useToggleContextLike, useUserLikedContexts } from \"@/hooks/use-contexts\";\r\nimport { useAddContextToProject } from \"@/hooks/use-context-to-prompt\";\r\nimport { useUser } from \"@/hooks/use-auth\";\r\nimport { supabase } from \"@/lib/supabase\";\r\nimport { useQueryClient } from \"@tanstack/react-query\";\r\nimport { ContextCreationModal } from \"./context-creation-modal\";\r\nimport { ContextEditModal } from \"./context-edit-modal\";\r\nimport { toast } from \"sonner\";\r\n\r\n// Types\r\nexport interface ContextCategory {\r\n  id: string;\r\n  name: string;\r\n  icon: string;\r\n  color: string;\r\n}\r\n\r\nexport interface Context {\r\n  id: string;\r\n  title: string;\r\n  description: string;\r\n  content: string;\r\n  category: ContextCategory;\r\n  author_id: string;\r\n  author_name: string;\r\n  is_public: boolean;\r\n  is_featured: boolean;\r\n  is_template: boolean;\r\n  tags: string[];\r\n  usage_count: number;\r\n  like_count: number;\r\n  view_count: number;\r\n  approval_status: 'pending' | 'approved' | 'rejected';\r\n  approved_by?: string;\r\n  approved_at?: string;\r\n  approval_notes?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\n// Mock data removed - using real API data from Supabase\r\n\r\ninterface ContextGalleryProps {\r\n  onSelectContext: (context: Context) => void;\r\n  // Optional props for external control (when used without Dialog wrapper)\r\n  open?: boolean;\r\n  onOpenChange?: (open: boolean) => void;\r\n  // New prop to render content only (without Dialog wrapper)\r\n  contentOnly?: boolean;\r\n}\r\n\r\nexport default function ContextGallery({ onSelectContext, open, onOpenChange, contentOnly = false }: ContextGalleryProps) {\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [selectedCategory, setSelectedCategory] = useState(\"all\");\r\n  const [filterType, setFilterType] = useState<\"all\" | \"public\" | \"private\" | \"templates\" | \"featured\">(\"all\");\r\n  const [selectedContext, setSelectedContext] = useState<Context | null>(null);\r\n  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);\r\n  const [editingContext, setEditingContext] = useState<Context | null>(null);\r\n  const [isEditModalOpen, setIsEditModalOpen] = useState(false);\r\n\r\n  // Refs for scroll handling\r\n  const scrollAreaRef = useRef<HTMLDivElement>(null);\r\n\r\n  // Hooks\r\n  const { data: user } = useUser();\r\n  const { data: categories = [] } = useContextCategories();\r\n  const useContextMutation = useUseContext();\r\n  const toggleLikeMutation = useToggleContextLike();\r\n  const { addContext: addContextToProject, isLoading: isAddingToProject } = useAddContextToProject();\r\n  const queryClient = useQueryClient();\r\n  const { data: likedContexts = [] } = useUserLikedContexts();\r\n\r\n  // Context filters\r\n  const contextFilters = useMemo(() => {\r\n    const filters: {\r\n      category_id?: string;\r\n      search?: string;\r\n      is_public?: boolean;\r\n      is_template?: boolean;\r\n      is_featured?: boolean;\r\n      author_id?: string;\r\n    } = {};\r\n    \r\n    if (selectedCategory !== \"all\") {\r\n      filters.category_id = selectedCategory;\r\n    }\r\n    \r\n    if (searchTerm) {\r\n      filters.search = searchTerm;\r\n    }\r\n\r\n    switch (filterType) {\r\n      case \"public\":\r\n        filters.is_public = true;\r\n        break;\r\n      case \"private\":\r\n        filters.is_public = false;\r\n        filters.author_id = user?.id;\r\n        break;\r\n      case \"templates\":\r\n        filters.is_template = true;\r\n        break;\r\n      case \"featured\":\r\n        filters.is_featured = true;\r\n        break;\r\n    }\r\n    \r\n    return filters;\r\n  }, [searchTerm, selectedCategory, filterType, user?.id]);\r\n\r\n  const { data: contexts = [], isLoading } = useContexts(contextFilters);\r\n\r\n  // Realtime subscription for contexts\r\n  useEffect(() => {\r\n    const subscription = supabase\r\n      .channel('context_changes')\r\n      .on('postgres_changes', \r\n          { event: '*', schema: 'public', table: 'contexts' }, \r\n          (payload: object) => {\r\n        console.log('Context change received:', payload);\r\n        queryClient.invalidateQueries({ queryKey: ['contexts'] });\r\n        queryClient.invalidateQueries({ queryKey: ['context-categories'] });\r\n      })\r\n      .subscribe();\r\n\r\n    return () => {\r\n      subscription.unsubscribe();\r\n    };\r\n  }, [queryClient]);\r\n\r\n  // Enhanced scroll handling for better user experience\r\n  useEffect(() => {\r\n    const scrollElement = scrollAreaRef.current?.querySelector('[data-radix-scroll-area-viewport]');\r\n\r\n    if (!scrollElement) return;\r\n\r\n    const handleWheel = (e: Event) => {\r\n      // Allow natural scrolling behavior\r\n      e.stopPropagation();\r\n    };\r\n\r\n    const handleTouchStart = (e: Event) => {\r\n      // Enable touch scrolling\r\n      e.stopPropagation();\r\n    };\r\n\r\n    scrollElement.addEventListener('wheel', handleWheel, { passive: true });\r\n    scrollElement.addEventListener('touchstart', handleTouchStart, { passive: true });\r\n\r\n    return () => {\r\n      scrollElement.removeEventListener('wheel', handleWheel);\r\n      scrollElement.removeEventListener('touchstart', handleTouchStart);\r\n    };\r\n  }, []);\r\n\r\n  const handleSelectContext = async (context: Context) => {\r\n    try {\r\n      // Add context to current project as prompt\r\n      await addContextToProject(context);\r\n\r\n      // Also call the original onSelectContext for backward compatibility\r\n      onSelectContext(context);\r\n      setSelectedContext(null);\r\n    } catch (error) {\r\n      console.error('Failed to add context to project:', error);\r\n      toast.error('Context projeye eklenirken hata oluştu');\r\n    }\r\n  };\r\n\r\n  const handleCopyContext = async (context: Context, e: React.MouseEvent) => {\r\n    e.stopPropagation();\r\n    try {\r\n      await navigator.clipboard.writeText(context.content);\r\n\r\n      // Increment usage count\r\n      await useContextMutation.mutateAsync({\r\n        contextId: context.id,\r\n        projectId: undefined // Context gallery'de proje ID'si yok\r\n      });\r\n\r\n      toast.success('Context panoya kopyalandı!');\r\n    } catch (error) {\r\n      console.error(\"Copy failed:\", error);\r\n      toast.error('Kopyalama başarısız oldu');\r\n    }\r\n  };\r\n\r\n  const handleLikeContext = async (contextId: string, e: React.MouseEvent) => {\r\n    e.stopPropagation();\r\n    try {\r\n      await toggleLikeMutation.mutateAsync(contextId);\r\n      toast.success('Beğeni durumu güncellendi!');\r\n    } catch (error) {\r\n      console.error(\"Like toggle failed:\", error);\r\n      toast.error('Beğeni güncellenemedi');\r\n    }\r\n  };\r\n\r\n  const handleEditContext = (context: Context, e: React.MouseEvent) => {\r\n    e.stopPropagation();\r\n    setEditingContext(context);\r\n    setIsEditModalOpen(true);\r\n  };\r\n\r\n  // Determine if this is being used as a controlled component (external Dialog control)\r\n  const isControlled = open !== undefined && onOpenChange !== undefined;\r\n\r\n  // If contentOnly mode, render just the content without Dialog wrapper\r\n  if (contentOnly) {\r\n    return (\r\n      <div className=\"space-y-4\">\r\n        {/* Header with Create Button */}\r\n        <div className=\"flex items-center justify-between\">\r\n          <Button\r\n            size=\"sm\"\r\n            onClick={() => setIsCreateModalOpen(true)}\r\n            className=\"gap-2 ml-auto\"\r\n          >\r\n            <Plus className=\"h-4 w-4\" />\r\n            Yeni Ekle\r\n          </Button>\r\n        </div>\r\n\r\n        {/* Search and Filters */}\r\n        <div className=\"flex flex-col gap-4\">\r\n          {/* Search Bar */}\r\n          <div className=\"relative\">\r\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\r\n            <Input\r\n              placeholder=\"Context ara... (başlık, açıklama, etiketler)\"\r\n              value={searchTerm}\r\n              onChange={(e) => setSearchTerm(e.target.value)}\r\n              className=\"pl-10\"\r\n            />\r\n          </div>\r\n\r\n          {/* Category Filters */}\r\n          <div className=\"flex flex-wrap gap-2\">\r\n            <Button\r\n              variant={selectedCategory === \"all\" ? \"default\" : \"outline\"}\r\n              size=\"sm\"\r\n              onClick={() => setSelectedCategory(\"all\")}\r\n              className=\"gap-2\"\r\n            >\r\n              <span>📂</span>\r\n              Tümü\r\n            </Button>\r\n            {categories.map((category: ContextCategory) => (\r\n              <Button\r\n                key={category.id}\r\n                variant={selectedCategory === category.id ? \"default\" : \"outline\"}\r\n                size=\"sm\"\r\n                onClick={() => setSelectedCategory(category.id)}\r\n                className=\"gap-2\"\r\n                style={selectedCategory === category.id ? { backgroundColor: category.color } : {}}\r\n              >\r\n                <span>{category.icon}</span>\r\n                {category.name}\r\n              </Button>\r\n            ))}\r\n          </div>\r\n\r\n          {/* Type Filters */}\r\n          <div className=\"flex flex-wrap gap-2\">\r\n            <Button\r\n              variant={filterType === \"all\" ? \"default\" : \"outline\"}\r\n              size=\"sm\"\r\n              onClick={() => setFilterType(\"all\")}\r\n              className=\"gap-2\"\r\n            >\r\n              <Filter className=\"h-4 w-4\" />\r\n              Tümü\r\n            </Button>\r\n            <Button\r\n              variant={filterType === \"featured\" ? \"default\" : \"outline\"}\r\n              size=\"sm\"\r\n              onClick={() => setFilterType(\"featured\")}\r\n              className=\"gap-2\"\r\n            >\r\n              <Star className=\"h-4 w-4\" />\r\n              Öne Çıkan\r\n            </Button>\r\n            <Button\r\n              variant={filterType === \"templates\" ? \"default\" : \"outline\"}\r\n              size=\"sm\"\r\n              onClick={() => setFilterType(\"templates\")}\r\n              className=\"gap-2\"\r\n            >\r\n              <Tag className=\"h-4 w-4\" />\r\n              Şablonlar\r\n            </Button>\r\n            <Button\r\n              variant={filterType === \"public\" ? \"default\" : \"outline\"}\r\n              size=\"sm\"\r\n              onClick={() => setFilterType(\"public\")}\r\n              className=\"gap-2\"\r\n            >\r\n              <Globe className=\"h-4 w-4\" />\r\n              Herkese Açık\r\n            </Button>\r\n            <Button\r\n              variant={filterType === \"private\" ? \"default\" : \"outline\"}\r\n              size=\"sm\"\r\n              onClick={() => setFilterType(\"private\")}\r\n              className=\"gap-2\"\r\n            >\r\n              <Lock className=\"h-4 w-4\" />\r\n              Özel\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Context Grid */}\r\n        <div className=\"space-y-4\">\r\n          {isLoading ? (\r\n            <div className=\"flex items-center justify-center py-12\">\r\n              <div className=\"flex flex-col items-center gap-4\">\r\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\r\n                <p className=\"text-gray-500\">Contextler yükleniyor...</p>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pb-4\">\r\n              {contexts.map((context: Context) => (\r\n              <Card\r\n                key={context.id}\r\n                className=\"cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-[1.02] group\"\r\n                onClick={() => setSelectedContext(context)}\r\n              >\r\n                <CardHeader className=\"pb-2\">\r\n                  <div className=\"flex items-start justify-between\">\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <span style={{ color: context.category.color }}>\r\n                        {context.category.icon}\r\n                      </span>\r\n                      <Badge variant=\"secondary\" className=\"text-xs\">\r\n                        {context.category.name}\r\n                      </Badge>\r\n                    </div>\r\n                    <div className=\"flex items-center gap-1\">\r\n                      {context.is_featured && (\r\n                        <div title=\"Öne Çıkan\">\r\n                          <Star className=\"h-4 w-4 text-yellow-500 fill-current\" />\r\n                        </div>\r\n                      )}\r\n                      {context.is_public ? (\r\n                        <div className=\"flex items-center gap-1\">\r\n                          <div title=\"Herkese Açık\">\r\n                            <Globe className=\"h-4 w-4 text-green-500\" />\r\n                          </div>\r\n                          {context.approval_status === 'approved' && (\r\n                            <div title=\"Onaylanmış\">\r\n                              <CheckCircle className=\"h-3 w-3 text-green-500\" />\r\n                            </div>\r\n                          )}\r\n                          {context.approval_status === 'pending' && (\r\n                            <div title=\"Onay Bekliyor\">\r\n                              <Clock className=\"h-3 w-3 text-yellow-500\" />\r\n                            </div>\r\n                          )}\r\n                          {context.approval_status === 'rejected' && (\r\n                            <div title=\"Reddedildi\">\r\n                              <X className=\"h-3 w-3 text-red-500\" />\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n                      ) : (\r\n                        <div title=\"Özel\">\r\n                          <Lock className=\"h-4 w-4 text-gray-500\" />\r\n                        </div>\r\n                      )}\r\n                      {context.is_template && (\r\n                        <div title=\"Şablon\">\r\n                          <Tag className=\"h-4 w-4 text-blue-500\" />\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                  <CardTitle className=\"text-lg line-clamp-2\">{context.title}</CardTitle>\r\n                  <CardDescription className=\"line-clamp-2\">{context.description}</CardDescription>\r\n                </CardHeader>\r\n                <CardContent className=\"space-y-3\">\r\n                  {/* Tags */}\r\n                  <div className=\"flex flex-wrap gap-1\">\r\n                    {context.tags.slice(0, 3).map((tag: string, index: number) => (\r\n                      <Badge key={index} variant=\"outline\" className=\"text-xs\">\r\n                        {tag}\r\n                      </Badge>\r\n                    ))}\r\n                    {context.tags.length > 3 && (\r\n                      <Badge variant=\"outline\" className=\"text-xs\">\r\n                        +{context.tags.length - 3}\r\n                      </Badge>\r\n                    )}\r\n                  </div>\r\n\r\n                  {/* Stats */}\r\n                  <div className=\"flex items-center justify-between text-sm text-gray-500\">\r\n                    <div className=\"flex items-center gap-3\">\r\n                      <div className=\"flex items-center gap-1\">\r\n                        <Eye className=\"h-3 w-3\" />\r\n                        {context.view_count}\r\n                      </div>\r\n                      <div className=\"flex items-center gap-1\">\r\n                        <Copy className=\"h-3 w-3\" />\r\n                        {context.usage_count}\r\n                      </div>\r\n                      <div className=\"flex items-center gap-1\">\r\n                        <Heart className=\"h-3 w-3\" />\r\n                        {context.like_count}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Author */}\r\n                  <div className=\"flex items-center gap-2 text-sm text-gray-500\">\r\n                    <User className=\"h-3 w-3\" />\r\n                    {context.author_name}\r\n                  </div>\r\n\r\n                  {/* Action Buttons */}\r\n                  <div className=\"flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity\">\r\n                    <Button\r\n                      size=\"sm\"\r\n                      onClick={(e) => {\r\n                        e.stopPropagation();\r\n                        handleSelectContext(context);\r\n                      }}\r\n                      disabled={isAddingToProject}\r\n                      className=\"flex-1\"\r\n                    >\r\n                      {isAddingToProject ? 'Ekleniyor...' : 'Projeye Ekle'}\r\n                    </Button>\r\n                    <Button\r\n                      size=\"sm\"\r\n                      variant=\"outline\"\r\n                      onClick={(e) => handleCopyContext(context, e)}\r\n                    >\r\n                      <Copy className=\"h-4 w-4\" />\r\n                    </Button>\r\n                    {/* Edit button - only show for user's own contexts */}\r\n                    {user && context.author_id === user.id && (\r\n                      <Button\r\n                        size=\"sm\"\r\n                        variant=\"outline\"\r\n                        onClick={(e) => handleEditContext(context, e)}\r\n                        title=\"Düzenle\"\r\n                      >\r\n                        <Edit3 className=\"h-4 w-4\" />\r\n                      </Button>\r\n                    )}\r\n                    <Button\r\n                      size=\"sm\"\r\n                      variant=\"outline\"\r\n                      onClick={(e) => handleLikeContext(context.id, e)}\r\n                      className={likedContexts.includes(context.id) ? \"text-red-500\" : \"\"}\r\n                    >\r\n                      <Heart\r\n                        className={`h-4 w-4 ${likedContexts.includes(context.id) ? \"fill-current\" : \"\"}`}\r\n                      />\r\n                    </Button>\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n              ))}\r\n              \r\n              {contexts.length === 0 && (\r\n                <div className=\"text-center py-12\">\r\n                  <Search className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\r\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Context bulunamadı</h3>\r\n                  <p className=\"text-gray-500\">\r\n                    Arama kriterlerinizi değiştirmeyi deneyin veya yeni bir context oluşturun.\r\n                  </p>\r\n                </div>\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Context Detail Modal */}\r\n        {selectedContext && (\r\n          <Dialog open={!!selectedContext} onOpenChange={() => setSelectedContext(null)}>\r\n            <DialogContent className=\"max-w-4xl max-h-[90vh]\">\r\n              <DialogHeader>\r\n                <DialogTitle className=\"flex items-center gap-2\">\r\n                  <span style={{ color: selectedContext.category.color }}>\r\n                    {selectedContext.category.icon}\r\n                  </span>\r\n                  {selectedContext.title}\r\n                </DialogTitle>\r\n                <div className=\"flex items-start justify-between\">\r\n                  <div>\r\n                    <div className=\"flex items-center gap-2 mb-2\">\r\n                      <Badge variant=\"secondary\">{selectedContext.category.name}</Badge>\r\n                      {selectedContext.is_featured && (\r\n                        <Badge className=\"bg-yellow-100 text-yellow-800\">\r\n                          <Star className=\"h-3 w-3 mr-1\" />\r\n                          Öne Çıkan\r\n                        </Badge>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </DialogHeader>\r\n            </DialogContent>\r\n          </Dialog>\r\n        )}\r\n\r\n        {/* Context Creation Modal */}\r\n        <ContextCreationModal\r\n          open={isCreateModalOpen}\r\n          onOpenChange={setIsCreateModalOpen}\r\n          onSuccess={() => {\r\n            // Refresh contexts list\r\n            queryClient.invalidateQueries({ queryKey: ['contexts'] });\r\n          }}\r\n        />\r\n\r\n        {/* Context Edit Modal */}\r\n        <ContextEditModal\r\n          open={isEditModalOpen}\r\n          onOpenChange={setIsEditModalOpen}\r\n          context={editingContext}\r\n          onSuccess={() => {\r\n            // Refresh contexts list\r\n            queryClient.invalidateQueries({ queryKey: ['contexts'] });\r\n          }}\r\n        />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={onOpenChange}>\r\n      {!isControlled && (\r\n        <DialogTrigger asChild>\r\n          <Button variant=\"outline\" className=\"gap-2 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 hover:from-blue-100 hover:to-indigo-100\">\r\n            <Search className=\"h-4 w-4\" />\r\n            Context Galerisi\r\n          </Button>\r\n        </DialogTrigger>\r\n      )}\r\n      <DialogContent className=\"max-w-6xl max-h-[90vh] flex flex-col\">\r\n        <DialogHeader className=\"flex-shrink-0\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <DialogTitle className=\"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\">\r\n              Context Galerisi\r\n            </DialogTitle>\r\n            <Button\r\n              size=\"sm\"\r\n              onClick={() => setIsCreateModalOpen(true)}\r\n              className=\"gap-2\"\r\n            >\r\n              <Plus className=\"h-4 w-4\" />\r\n              Yeni Ekle\r\n            </Button>\r\n          </div>\r\n        </DialogHeader>\r\n\r\n        {/* Search and Filters */}\r\n        <div className=\"flex flex-col gap-4 p-2 flex-shrink-0\">\r\n          {/* Search Bar */}\r\n          <div className=\"relative\">\r\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\r\n            <Input\r\n              placeholder=\"Context ara... (başlık, açıklama, etiketler)\"\r\n              value={searchTerm}\r\n              onChange={(e) => setSearchTerm(e.target.value)}\r\n              className=\"pl-10\"\r\n            />\r\n          </div>\r\n\r\n          {/* Category Filters */}\r\n          <div className=\"flex flex-wrap gap-2\">\r\n            <Button\r\n              variant={selectedCategory === \"all\" ? \"default\" : \"outline\"}\r\n              size=\"sm\"\r\n              onClick={() => setSelectedCategory(\"all\")}\r\n              className=\"gap-2\"\r\n            >\r\n              <span>📂</span>\r\n              Tümü\r\n            </Button>\r\n            {categories.map((category: ContextCategory) => (\r\n              <Button\r\n                key={category.id}\r\n                variant={selectedCategory === category.id ? \"default\" : \"outline\"}\r\n                size=\"sm\"\r\n                onClick={() => setSelectedCategory(category.id)}\r\n                className=\"gap-2\"\r\n                style={selectedCategory === category.id ? { backgroundColor: category.color } : {}}\r\n              >\r\n                <span>{category.icon}</span>\r\n                {category.name}\r\n              </Button>\r\n            ))}\r\n          </div>\r\n\r\n          {/* Type Filters */}\r\n          <div className=\"flex flex-wrap gap-2\">\r\n            <Button\r\n              variant={filterType === \"all\" ? \"default\" : \"outline\"}\r\n              size=\"sm\"\r\n              onClick={() => setFilterType(\"all\")}\r\n              className=\"gap-2\"\r\n            >\r\n              <Filter className=\"h-4 w-4\" />\r\n              Tümü\r\n            </Button>\r\n            <Button\r\n              variant={filterType === \"featured\" ? \"default\" : \"outline\"}\r\n              size=\"sm\"\r\n              onClick={() => setFilterType(\"featured\")}\r\n              className=\"gap-2\"\r\n            >\r\n              <Star className=\"h-4 w-4\" />\r\n              Öne Çıkan\r\n            </Button>\r\n            <Button\r\n              variant={filterType === \"templates\" ? \"default\" : \"outline\"}\r\n              size=\"sm\"\r\n              onClick={() => setFilterType(\"templates\")}\r\n              className=\"gap-2\"\r\n            >\r\n              <Tag className=\"h-4 w-4\" />\r\n              Şablonlar\r\n            </Button>\r\n            <Button\r\n              variant={filterType === \"public\" ? \"default\" : \"outline\"}\r\n              size=\"sm\"\r\n              onClick={() => setFilterType(\"public\")}\r\n              className=\"gap-2\"\r\n            >\r\n              <Globe className=\"h-4 w-4\" />\r\n              Herkese Açık\r\n            </Button>\r\n            <Button\r\n              variant={filterType === \"private\" ? \"default\" : \"outline\"}\r\n              size=\"sm\"\r\n              onClick={() => setFilterType(\"private\")}\r\n              className=\"gap-2\"\r\n            >\r\n              <Lock className=\"h-4 w-4\" />\r\n              Özel\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        <Separator className=\"flex-shrink-0\" />\r\n\r\n        {/* Context Grid - Improved scrolling container */}\r\n        <div className=\"flex-1 min-h-0\">\r\n          <ScrollArea\r\n            ref={scrollAreaRef}\r\n            className=\"h-full w-full\"\r\n            style={{\r\n              scrollBehavior: 'smooth',\r\n              WebkitOverflowScrolling: 'touch' // Enable momentum scrolling on iOS\r\n            }}\r\n          >\r\n            <div className=\"p-4 space-y-4\">\r\n              {isLoading ? (\r\n                <div className=\"flex items-center justify-center py-12\">\r\n                  <div className=\"flex flex-col items-center gap-4\">\r\n                    <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\r\n                    <p className=\"text-gray-500\">Contextler yükleniyor...</p>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pb-4\">\r\n                  {contexts.map((context: Context) => (\r\n                    <Card\r\n                      key={context.id}\r\n                      className=\"cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-[1.02] group\"\r\n                      onClick={() => setSelectedContext(context)}\r\n                    >\r\n                      <CardHeader className=\"pb-2\">\r\n                        <div className=\"flex items-start justify-between\">\r\n                          <div className=\"flex items-center gap-2\">\r\n                            <span style={{ color: context.category.color }}>\r\n                              {context.category.icon}\r\n                            </span>\r\n                            <Badge variant=\"secondary\" className=\"text-xs\">\r\n                              {context.category.name}\r\n                            </Badge>\r\n                          </div>\r\n                          <div className=\"flex items-center gap-1\">\r\n                            {context.is_featured && (\r\n                              <div title=\"Öne Çıkan\">\r\n                                <Star className=\"h-4 w-4 text-yellow-500 fill-current\" />\r\n                              </div>\r\n                            )}\r\n                            {context.is_public ? (\r\n                              <div className=\"flex items-center gap-1\">\r\n                                <div title=\"Herkese Açık\">\r\n                                  <Globe className=\"h-4 w-4 text-green-500\" />\r\n                                </div>\r\n                                {context.approval_status === 'approved' && (\r\n                                  <div title=\"Onaylanmış\">\r\n                                    <CheckCircle className=\"h-3 w-3 text-green-500\" />\r\n                                  </div>\r\n                                )}\r\n                                {context.approval_status === 'pending' && (\r\n                                  <div title=\"Onay Bekliyor\">\r\n                                    <Clock className=\"h-3 w-3 text-yellow-500\" />\r\n                                  </div>\r\n                                )}\r\n                                {context.approval_status === 'rejected' && (\r\n                                  <div title=\"Reddedildi\">\r\n                                    <X className=\"h-3 w-3 text-red-500\" />\r\n                                  </div>\r\n                                )}\r\n                              </div>\r\n                            ) : (\r\n                              <div title=\"Özel\">\r\n                                <Lock className=\"h-4 w-4 text-gray-500\" />\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                      </CardHeader>\r\n                    </Card>\r\n                  ))}\r\n\r\n                  {contexts.length === 0 && (\r\n                    <div className=\"text-center py-12\">\r\n                      <Search className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\r\n                      <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Context bulunamadı</h3>\r\n                      <p className=\"text-gray-500\">\r\n                        Arama kriterlerinizi değiştirmeyi deneyin veya yeni bir context oluşturun.\r\n                      </p>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </ScrollArea>\r\n        </div>\r\n      </DialogContent>\r\n\r\n      {/* Context Detail Modal */}\r\n      {selectedContext && (\r\n        <Dialog open={!!selectedContext} onOpenChange={() => setSelectedContext(null)}>\r\n          <DialogContent className=\"max-w-4xl max-h-[90vh]\">\r\n            <DialogHeader>\r\n              <DialogTitle className=\"flex items-center gap-2\">\r\n                <span style={{ color: selectedContext.category.color }}>\r\n                  {selectedContext.category.icon}\r\n                </span>\r\n                {selectedContext.title}\r\n              </DialogTitle>\r\n              <div className=\"flex items-start justify-between\">\r\n                <div>\r\n                  <div className=\"flex items-center gap-2 mb-2\">\r\n                    <Badge variant=\"secondary\">{selectedContext.category.name}</Badge>\r\n                    {selectedContext.is_featured && (\r\n                      <Badge className=\"bg-yellow-100 text-yellow-800\">\r\n                        <Star className=\"h-3 w-3 mr-1\" />\r\n                        Öne Çıkan\r\n                      </Badge>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </DialogHeader>\r\n          </DialogContent>\r\n        </Dialog>\r\n      )}\r\n\r\n      {/* Context Creation Modal */}\r\n      <ContextCreationModal\r\n        open={isCreateModalOpen}\r\n        onOpenChange={setIsCreateModalOpen}\r\n        onSuccess={() => {\r\n          // Refresh contexts list\r\n          queryClient.invalidateQueries({ queryKey: ['contexts'] });\r\n        }}\r\n      />\r\n\r\n      {/* Context Edit Modal */}\r\n      <ContextEditModal\r\n        open={isEditModalOpen}\r\n        onOpenChange={setIsEditModalOpen}\r\n        context={editingContext}\r\n        onSuccess={() => {\r\n          // Refresh contexts list\r\n          queryClient.invalidateQueries({ queryKey: ['contexts'] });\r\n        }}\r\n      />\r\n    </Dialog>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAlBA;;;;;;;;;;;;;;;;;;;AA8De,SAAS,eAAe,EAAE,eAAe,EAAE,IAAI,EAAE,YAAY,EAAE,cAAc,KAAK,EAAuB;IACtH,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2D;IACtG,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,2BAA2B;IAC3B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE7C,QAAQ;IACR,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAC7B,MAAM,EAAE,MAAM,aAAa,EAAE,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,uBAAoB,AAAD;IACrD,MAAM,qBAAqB,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD;IACvC,MAAM,qBAAqB,CAAA,GAAA,+HAAA,CAAA,uBAAoB,AAAD;IAC9C,MAAM,EAAE,YAAY,mBAAmB,EAAE,WAAW,iBAAiB,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,yBAAsB,AAAD;IAC/F,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,MAAM,gBAAgB,EAAE,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,uBAAoB,AAAD;IAExD,kBAAkB;IAClB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC7B,MAAM,UAOF,CAAC;QAEL,IAAI,qBAAqB,OAAO;YAC9B,QAAQ,WAAW,GAAG;QACxB;QAEA,IAAI,YAAY;YACd,QAAQ,MAAM,GAAG;QACnB;QAEA,OAAQ;YACN,KAAK;gBACH,QAAQ,SAAS,GAAG;gBACpB;YACF,KAAK;gBACH,QAAQ,SAAS,GAAG;gBACpB,QAAQ,SAAS,GAAG,MAAM;gBAC1B;YACF,KAAK;gBACH,QAAQ,WAAW,GAAG;gBACtB;YACF,KAAK;gBACH,QAAQ,WAAW,GAAG;gBACtB;QACJ;QAEA,OAAO;IACT,GAAG;QAAC;QAAY;QAAkB;QAAY,MAAM;KAAG;IAEvD,MAAM,EAAE,MAAM,WAAW,EAAE,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD,EAAE;IAEvD,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,gLAAA,CAAA,WAAQ,CAC1B,OAAO,CAAC,mBACR,EAAE,CAAC,oBACA;YAAE,OAAO;YAAK,QAAQ;YAAU,OAAO;QAAW,GAClD,CAAC;YACH,QAAQ,GAAG,CAAC,4BAA4B;YACxC,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACvD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAqB;YAAC;QACnE,GACC,SAAS;QAEZ,OAAO;YACL,aAAa,WAAW;QAC1B;IACF,GAAG;QAAC;KAAY;IAEhB,sDAAsD;IACtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,cAAc,OAAO,EAAE,cAAc;QAE3D,IAAI,CAAC,eAAe;QAEpB,MAAM,cAAc,CAAC;YACnB,mCAAmC;YACnC,EAAE,eAAe;QACnB;QAEA,MAAM,mBAAmB,CAAC;YACxB,yBAAyB;YACzB,EAAE,eAAe;QACnB;QAEA,cAAc,gBAAgB,CAAC,SAAS,aAAa;YAAE,SAAS;QAAK;QACrE,cAAc,gBAAgB,CAAC,cAAc,kBAAkB;YAAE,SAAS;QAAK;QAE/E,OAAO;YACL,cAAc,mBAAmB,CAAC,SAAS;YAC3C,cAAc,mBAAmB,CAAC,cAAc;QAClD;IACF,GAAG,EAAE;IAEL,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,2CAA2C;YAC3C,MAAM,oBAAoB;YAE1B,oEAAoE;YACpE,gBAAgB;YAChB,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,oBAAoB,OAAO,SAAkB;QACjD,EAAE,eAAe;QACjB,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC,QAAQ,OAAO;YAEnD,wBAAwB;YACxB,MAAM,mBAAmB,WAAW,CAAC;gBACnC,WAAW,QAAQ,EAAE;gBACrB,WAAW,UAAU,qCAAqC;YAC5D;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,oBAAoB,OAAO,WAAmB;QAClD,EAAE,eAAe;QACjB,IAAI;YACF,MAAM,mBAAmB,WAAW,CAAC;YACrC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,oBAAoB,CAAC,SAAkB;QAC3C,EAAE,eAAe;QACjB,kBAAkB;QAClB,mBAAmB;IACrB;IAEA,sFAAsF;IACtF,MAAM,eAAe,SAAS,aAAa,iBAAiB;IAE5D,sEAAsE;IACtE,IAAI,aAAa;QACf,qBACE,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAS,IAAM,qBAAqB;wBACpC,WAAU;;0CAEV,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAMhC,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC,iIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,qBAAqB,QAAQ,YAAY;oCAClD,MAAK;oCACL,SAAS,IAAM,oBAAoB;oCACnC,WAAU;;sDAEV,8OAAC;sDAAK;;;;;;wCAAS;;;;;;;gCAGhB,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,kIAAA,CAAA,SAAM;wCAEL,SAAS,qBAAqB,SAAS,EAAE,GAAG,YAAY;wCACxD,MAAK;wCACL,SAAS,IAAM,oBAAoB,SAAS,EAAE;wCAC9C,WAAU;wCACV,OAAO,qBAAqB,SAAS,EAAE,GAAG;4CAAE,iBAAiB,SAAS,KAAK;wCAAC,IAAI,CAAC;;0DAEjF,8OAAC;0DAAM,SAAS,IAAI;;;;;;4CACnB,SAAS,IAAI;;uCART,SAAS,EAAE;;;;;;;;;;;sCActB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,eAAe,QAAQ,YAAY;oCAC5C,MAAK;oCACL,SAAS,IAAM,cAAc;oCAC7B,WAAU;;sDAEV,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGhC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,eAAe,aAAa,YAAY;oCACjD,MAAK;oCACL,SAAS,IAAM,cAAc;oCAC7B,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAG9B,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,eAAe,cAAc,YAAY;oCAClD,MAAK;oCACL,SAAS,IAAM,cAAc;oCAC7B,WAAU;;sDAEV,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAG7B,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,eAAe,WAAW,YAAY;oCAC/C,MAAK;oCACL,SAAS,IAAM,cAAc;oCAC7B,WAAU;;sDAEV,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAG/B,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,eAAe,YAAY,YAAY;oCAChD,MAAK;oCACL,SAAS,IAAM,cAAc;oCAC7B,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;;8BAOlC,8OAAC;oBAAI,WAAU;8BACZ,0BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;6CAIjC,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,wBACf,8OAAC,gIAAA,CAAA,OAAI;oCAEH,WAAU;oCACV,SAAS,IAAM,mBAAmB;;sDAElC,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,OAAO;wEAAE,OAAO,QAAQ,QAAQ,CAAC,KAAK;oEAAC;8EAC1C,QAAQ,QAAQ,CAAC,IAAI;;;;;;8EAExB,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAY,WAAU;8EAClC,QAAQ,QAAQ,CAAC,IAAI;;;;;;;;;;;;sEAG1B,8OAAC;4DAAI,WAAU;;gEACZ,QAAQ,WAAW,kBAClB,8OAAC;oEAAI,OAAM;8EACT,cAAA,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;gEAGnB,QAAQ,SAAS,iBAChB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,OAAM;sFACT,cAAA,8OAAC,oMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;;;;;;wEAElB,QAAQ,eAAe,KAAK,4BAC3B,8OAAC;4EAAI,OAAM;sFACT,cAAA,8OAAC,2NAAA,CAAA,cAAW;gFAAC,WAAU;;;;;;;;;;;wEAG1B,QAAQ,eAAe,KAAK,2BAC3B,8OAAC;4EAAI,OAAM;sFACT,cAAA,8OAAC,oMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;;;;;;wEAGpB,QAAQ,eAAe,KAAK,4BAC3B,8OAAC;4EAAI,OAAM;sFACT,cAAA,8OAAC,4LAAA,CAAA,IAAC;gFAAC,WAAU;;;;;;;;;;;;;;;;yFAKnB,8OAAC;oEAAI,OAAM;8EACT,cAAA,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;gEAGnB,QAAQ,WAAW,kBAClB,8OAAC;oEAAI,OAAM;8EACT,cAAA,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8DAKvB,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAwB,QAAQ,KAAK;;;;;;8DAC1D,8OAAC,gIAAA,CAAA,kBAAe;oDAAC,WAAU;8DAAgB,QAAQ,WAAW;;;;;;;;;;;;sDAEhE,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DAErB,8OAAC;oDAAI,WAAU;;wDACZ,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAa,sBAC1C,8OAAC,iIAAA,CAAA,QAAK;gEAAa,SAAQ;gEAAU,WAAU;0EAC5C;+DADS;;;;;wDAIb,QAAQ,IAAI,CAAC,MAAM,GAAG,mBACrB,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;;gEAAU;gEACzC,QAAQ,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;8DAM9B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEACd,QAAQ,UAAU;;;;;;;0EAErB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEACf,QAAQ,WAAW;;;;;;;0EAEtB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAChB,QAAQ,UAAU;;;;;;;;;;;;;;;;;;8DAMzB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDACf,QAAQ,WAAW;;;;;;;8DAItB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAS,CAAC;gEACR,EAAE,eAAe;gEACjB,oBAAoB;4DACtB;4DACA,UAAU;4DACV,WAAU;sEAET,oBAAoB,iBAAiB;;;;;;sEAExC,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,CAAC,IAAM,kBAAkB,SAAS;sEAE3C,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;wDAGjB,QAAQ,QAAQ,SAAS,KAAK,KAAK,EAAE,kBACpC,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,CAAC,IAAM,kBAAkB,SAAS;4DAC3C,OAAM;sEAEN,cAAA,8OAAC,0MAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;sEAGrB,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,CAAC,IAAM,kBAAkB,QAAQ,EAAE,EAAE;4DAC9C,WAAW,cAAc,QAAQ,CAAC,QAAQ,EAAE,IAAI,iBAAiB;sEAEjE,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEACJ,WAAW,CAAC,QAAQ,EAAE,cAAc,QAAQ,CAAC,QAAQ,EAAE,IAAI,iBAAiB,IAAI;;;;;;;;;;;;;;;;;;;;;;;;mCArInF,QAAQ,EAAE;;;;;4BA6IhB,SAAS,MAAM,KAAK,mBACnB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;gBAUtC,iCACC,8OAAC,kIAAA,CAAA,SAAM;oBAAC,MAAM,CAAC,CAAC;oBAAiB,cAAc,IAAM,mBAAmB;8BACtE,cAAA,8OAAC,kIAAA,CAAA,gBAAa;wBAAC,WAAU;kCACvB,cAAA,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAK,OAAO;gDAAE,OAAO,gBAAgB,QAAQ,CAAC,KAAK;4CAAC;sDAClD,gBAAgB,QAAQ,CAAC,IAAI;;;;;;wCAE/B,gBAAgB,KAAK;;;;;;;8CAExB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;kDACC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAa,gBAAgB,QAAQ,CAAC,IAAI;;;;;;gDACxD,gBAAgB,WAAW,kBAC1B,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;;sEACf,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAanD,8OAAC,kJAAA,CAAA,uBAAoB;oBACnB,MAAM;oBACN,cAAc;oBACd,WAAW;wBACT,wBAAwB;wBACxB,YAAY,iBAAiB,CAAC;4BAAE,UAAU;gCAAC;6BAAW;wBAAC;oBACzD;;;;;;8BAIF,8OAAC,8IAAA,CAAA,mBAAgB;oBACf,MAAM;oBACN,cAAc;oBACd,SAAS;oBACT,WAAW;wBACT,wBAAwB;wBACxB,YAAY,iBAAiB,CAAC;4BAAE,UAAU;gCAAC;6BAAW;wBAAC;oBACzD;;;;;;;;;;;;IAIR;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;;YAC/B,CAAC,8BACA,8OAAC,kIAAA,CAAA,gBAAa;gBAAC,OAAO;0BACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,WAAU;;sCAClC,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBAAY;;;;;;;;;;;;0BAKpC,8OAAC,kIAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACvB,8OAAC,kIAAA,CAAA,eAAY;wBAAC,WAAU;kCACtB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,cAAW;oCAAC,WAAU;8CAAgG;;;;;;8CAGvH,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS,IAAM,qBAAqB;oCACpC,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;kCAOlC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC,iIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;0CAKd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,qBAAqB,QAAQ,YAAY;wCAClD,MAAK;wCACL,SAAS,IAAM,oBAAoB;wCACnC,WAAU;;0DAEV,8OAAC;0DAAK;;;;;;4CAAS;;;;;;;oCAGhB,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,kIAAA,CAAA,SAAM;4CAEL,SAAS,qBAAqB,SAAS,EAAE,GAAG,YAAY;4CACxD,MAAK;4CACL,SAAS,IAAM,oBAAoB,SAAS,EAAE;4CAC9C,WAAU;4CACV,OAAO,qBAAqB,SAAS,EAAE,GAAG;gDAAE,iBAAiB,SAAS,KAAK;4CAAC,IAAI,CAAC;;8DAEjF,8OAAC;8DAAM,SAAS,IAAI;;;;;;gDACnB,SAAS,IAAI;;2CART,SAAS,EAAE;;;;;;;;;;;0CActB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,eAAe,QAAQ,YAAY;wCAC5C,MAAK;wCACL,SAAS,IAAM,cAAc;wCAC7B,WAAU;;0DAEV,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGhC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,eAAe,aAAa,YAAY;wCACjD,MAAK;wCACL,SAAS,IAAM,cAAc;wCAC7B,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAG9B,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,eAAe,cAAc,YAAY;wCAClD,MAAK;wCACL,SAAS,IAAM,cAAc;wCAC7B,WAAU;;0DAEV,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAG7B,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,eAAe,WAAW,YAAY;wCAC/C,MAAK;wCACL,SAAS,IAAM,cAAc;wCAC7B,WAAU;;0DAEV,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAG/B,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,eAAe,YAAY,YAAY;wCAChD,MAAK;wCACL,SAAS,IAAM,cAAc;wCAC7B,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;kCAMlC,8OAAC,qIAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCAGrB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0IAAA,CAAA,aAAU;4BACT,KAAK;4BACL,WAAU;4BACV,OAAO;gCACL,gBAAgB;gCAChB,yBAAyB,QAAQ,mCAAmC;4BACtE;sCAEA,cAAA,8OAAC;gCAAI,WAAU;0CACZ,0BACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;yDAIjC,8OAAC;oCAAI,WAAU;;wCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,gIAAA,CAAA,OAAI;gDAEH,WAAU;gDACV,SAAS,IAAM,mBAAmB;0DAElC,cAAA,8OAAC,gIAAA,CAAA,aAAU;oDAAC,WAAU;8DACpB,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,OAAO;4EAAE,OAAO,QAAQ,QAAQ,CAAC,KAAK;wEAAC;kFAC1C,QAAQ,QAAQ,CAAC,IAAI;;;;;;kFAExB,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAY,WAAU;kFAClC,QAAQ,QAAQ,CAAC,IAAI;;;;;;;;;;;;0EAG1B,8OAAC;gEAAI,WAAU;;oEACZ,QAAQ,WAAW,kBAClB,8OAAC;wEAAI,OAAM;kFACT,cAAA,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;oEAGnB,QAAQ,SAAS,iBAChB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,OAAM;0FACT,cAAA,8OAAC,oMAAA,CAAA,QAAK;oFAAC,WAAU;;;;;;;;;;;4EAElB,QAAQ,eAAe,KAAK,4BAC3B,8OAAC;gFAAI,OAAM;0FACT,cAAA,8OAAC,2NAAA,CAAA,cAAW;oFAAC,WAAU;;;;;;;;;;;4EAG1B,QAAQ,eAAe,KAAK,2BAC3B,8OAAC;gFAAI,OAAM;0FACT,cAAA,8OAAC,oMAAA,CAAA,QAAK;oFAAC,WAAU;;;;;;;;;;;4EAGpB,QAAQ,eAAe,KAAK,4BAC3B,8OAAC;gFAAI,OAAM;0FACT,cAAA,8OAAC,4LAAA,CAAA,IAAC;oFAAC,WAAU;;;;;;;;;;;;;;;;6FAKnB,8OAAC;wEAAI,OAAM;kFACT,cAAA,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;+CA3CrB,QAAQ,EAAE;;;;;wCAoDlB,SAAS,MAAM,KAAK,mBACnB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;8DACvD,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAa5C,iCACC,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM,CAAC,CAAC;gBAAiB,cAAc,IAAM,mBAAmB;0BACtE,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;8BACvB,cAAA,8OAAC,kIAAA,CAAA,eAAY;;0CACX,8OAAC,kIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAK,OAAO;4CAAE,OAAO,gBAAgB,QAAQ,CAAC,KAAK;wCAAC;kDAClD,gBAAgB,QAAQ,CAAC,IAAI;;;;;;oCAE/B,gBAAgB,KAAK;;;;;;;0CAExB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;8CACC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAa,gBAAgB,QAAQ,CAAC,IAAI;;;;;;4CACxD,gBAAgB,WAAW,kBAC1B,8OAAC,iIAAA,CAAA,QAAK;gDAAC,WAAU;;kEACf,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAanD,8OAAC,kJAAA,CAAA,uBAAoB;gBACnB,MAAM;gBACN,cAAc;gBACd,WAAW;oBACT,wBAAwB;oBACxB,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;yBAAW;oBAAC;gBACzD;;;;;;0BAIF,8OAAC,8IAAA,CAAA,mBAAgB;gBACf,MAAM;gBACN,cAAc;gBACd,SAAS;gBACT,WAAW;oBACT,wBAAwB;oBACxB,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;yBAAW;oBAAC;gBACzD;;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 4108, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/enhanced-context-gallery-modal.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useEffect, useRef } from 'react';\nimport { X } from 'lucide-react';\nimport { Button } from './ui/button';\n\nimport ContextGallery, { Context } from './context-gallery';\n\n/**\n * Enhanced Context Gallery Modal Component\n *\n * Features:\n * - Smooth upward animation from \"Add New Prompt\" area\n * - Responsive positioning that adapts to screen sizes\n * - Fills available space except for \"Add New Prompt\" area\n * - Proper accessibility with ARIA attributes and keyboard navigation\n * - Touch-friendly scrolling on mobile devices\n * - Focus management and screen reader support\n */\ninterface EnhancedContextGalleryModalProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  onSelectContext: (context: Context) => void;\n}\n\nexport function EnhancedContextGalleryModal({\n  open,\n  onOpenChange,\n  onSelectContext\n}: EnhancedContextGalleryModalProps) {\n  const modalRef = useRef<HTMLDivElement>(null);\n  const backdropRef = useRef<HTMLDivElement>(null);\n\n  // Handle keyboard navigation and accessibility\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if (!open) return;\n\n      switch (e.key) {\n        case 'Escape':\n          onOpenChange(false);\n          break;\n        case 'Tab':\n          // Trap focus within modal\n          if (modalRef.current) {\n            const focusableElements = modalRef.current.querySelectorAll(\n              'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\n            );\n            const firstElement = focusableElements[0] as HTMLElement;\n            const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;\n\n            if (e.shiftKey) {\n              if (document.activeElement === firstElement) {\n                e.preventDefault();\n                lastElement?.focus();\n              }\n            } else {\n              if (document.activeElement === lastElement) {\n                e.preventDefault();\n                firstElement?.focus();\n              }\n            }\n          }\n          break;\n      }\n    };\n\n    if (open) {\n      document.addEventListener('keydown', handleKeyDown);\n      // Prevent body scroll when modal is open\n      document.body.style.overflow = 'hidden';\n      // Announce modal opening to screen readers\n      const announcement = document.createElement('div');\n      announcement.setAttribute('aria-live', 'polite');\n      announcement.setAttribute('aria-atomic', 'true');\n      announcement.className = 'sr-only';\n      announcement.textContent = 'Context Gallery açıldı. Escape tuşu ile kapatabilirsiniz.';\n      document.body.appendChild(announcement);\n\n      // Clean up announcement after screen reader has time to read it\n      setTimeout(() => {\n        document.body.removeChild(announcement);\n      }, 1000);\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n      document.body.style.overflow = 'unset';\n    };\n  }, [open, onOpenChange]);\n\n  // Focus management\n  useEffect(() => {\n    if (open && modalRef.current) {\n      // Focus the modal container\n      modalRef.current.focus();\n    }\n  }, [open]);\n\n  // Handle backdrop click\n  const handleBackdropClick = (e: React.MouseEvent) => {\n    if (e.target === backdropRef.current) {\n      onOpenChange(false);\n    }\n  };\n\n  if (!open) return null;\n\n  return (\n    <>\n      {/* CSS for responsive heights and smooth animations */}\n      <style jsx>{`\n        .enhanced-modal {\n          height: calc(100vh - 200px);\n          max-height: calc(100vh - 200px);\n        }\n        @media (min-width: 768px) {\n          .enhanced-modal {\n            height: calc(100vh - 180px);\n            max-height: calc(100vh - 180px);\n          }\n        }\n        @media (min-width: 1024px) {\n          .enhanced-modal {\n            height: calc(100vh - 160px);\n            max-height: calc(100vh - 160px);\n          }\n        }\n        .enhanced-modal-content {\n          height: calc(100vh - 200px - 80px);\n          max-height: calc(100vh - 200px - 80px);\n        }\n        @media (min-width: 768px) {\n          .enhanced-modal-content {\n            height: calc(100vh - 180px - 80px);\n            max-height: calc(100vh - 180px - 80px);\n          }\n        }\n        @media (min-width: 1024px) {\n          .enhanced-modal-content {\n            height: calc(100vh - 160px - 80px);\n            max-height: calc(100vh - 160px - 80px);\n          }\n        }\n\n        /* Smooth scrolling for better UX */\n        .enhanced-modal-content {\n          scroll-behavior: smooth;\n          -webkit-overflow-scrolling: touch;\n        }\n\n        /* Focus styles for accessibility */\n        .enhanced-modal:focus {\n          outline: 2px solid #3b82f6;\n          outline-offset: -2px;\n        }\n      `}</style>\n\n      {/* Backdrop */}\n      <div\n        ref={backdropRef}\n        className={`\n          fixed inset-0 backdrop-blur-sm z-50 transition-all duration-300\n          ${open ? 'bg-black/50 opacity-100' : 'bg-black/0 opacity-0'}\n        `}\n        onClick={handleBackdropClick}\n        aria-hidden=\"true\"\n        role=\"presentation\"\n      />\n\n      {/* Modal Container */}\n      <div\n        ref={modalRef}\n        role=\"dialog\"\n        aria-modal=\"true\"\n        aria-labelledby=\"context-gallery-title\"\n        tabIndex={-1}\n        className={`\n          enhanced-modal\n          fixed left-0 right-0 z-[55]\n          bg-white border-t border-gray-200 shadow-2xl\n          transition-all duration-300 ease-in-out\n          ${open\n            ? 'bottom-[200px] md:bottom-[180px] lg:bottom-[160px] opacity-100 translate-y-0'\n            : 'bottom-0 opacity-0 translate-y-full pointer-events-none'\n          }\n        `}\n      >\n        {/* Modal Header */}\n        <header className=\"flex-shrink-0 p-4 lg:p-6 border-b border-gray-200 bg-white\">\n          <div className=\"flex items-center justify-between\">\n            <h2\n              id=\"context-gallery-title\"\n              className=\"text-xl lg:text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\"\n            >\n              Context Galerisi\n            </h2>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => onOpenChange(false)}\n              className=\"h-8 w-8 p-0 hover:bg-gray-100 rounded-full transition-colors duration-200\"\n              aria-label=\"Context Gallery'yi kapat (Escape tuşu ile de kapatabilirsiniz)\"\n              title=\"Kapat\"\n            >\n              <X className=\"h-4 w-4\" />\n            </Button>\n          </div>\n          <p className=\"sr-only\">\n            Context Gallery modal açık. Tab tuşu ile navigasyon yapabilir, Escape tuşu ile kapatabilirsiniz.\n          </p>\n        </header>\n\n        {/* Scrollable Content Area */}\n        <main\n          className=\"flex-1 overflow-hidden\"\n          role=\"main\"\n          aria-label=\"Context Gallery içeriği\"\n        >\n          <div\n            className=\"enhanced-modal-content p-4 lg:p-6 overflow-y-auto focus:outline-none\"\n            tabIndex={-1}\n            style={{\n              scrollBehavior: 'smooth',\n              WebkitOverflowScrolling: 'touch'\n            }}\n          >\n            {/* Use existing ContextGallery with contentOnly prop */}\n            <ContextGallery\n              onSelectContext={onSelectContext}\n              contentOnly={true}\n            />\n          </div>\n        </main>\n      </div>\n    </>\n  );\n}\n\n\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;;AAyBO,SAAS,4BAA4B,EAC1C,IAAI,EACJ,YAAY,EACZ,eAAe,EACkB;IACjC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACxC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,IAAI,CAAC,MAAM;YAEX,OAAQ,EAAE,GAAG;gBACX,KAAK;oBACH,aAAa;oBACb;gBACF,KAAK;oBACH,0BAA0B;oBAC1B,IAAI,SAAS,OAAO,EAAE;wBACpB,MAAM,oBAAoB,SAAS,OAAO,CAAC,gBAAgB,CACzD;wBAEF,MAAM,eAAe,iBAAiB,CAAC,EAAE;wBACzC,MAAM,cAAc,iBAAiB,CAAC,kBAAkB,MAAM,GAAG,EAAE;wBAEnE,IAAI,EAAE,QAAQ,EAAE;4BACd,IAAI,SAAS,aAAa,KAAK,cAAc;gCAC3C,EAAE,cAAc;gCAChB,aAAa;4BACf;wBACF,OAAO;4BACL,IAAI,SAAS,aAAa,KAAK,aAAa;gCAC1C,EAAE,cAAc;gCAChB,cAAc;4BAChB;wBACF;oBACF;oBACA;YACJ;QACF;QAEA,IAAI,MAAM;YACR,SAAS,gBAAgB,CAAC,WAAW;YACrC,yCAAyC;YACzC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YAC/B,2CAA2C;YAC3C,MAAM,eAAe,SAAS,aAAa,CAAC;YAC5C,aAAa,YAAY,CAAC,aAAa;YACvC,aAAa,YAAY,CAAC,eAAe;YACzC,aAAa,SAAS,GAAG;YACzB,aAAa,WAAW,GAAG;YAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;YAE1B,gEAAgE;YAChE,WAAW;gBACT,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B,GAAG;QACL,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;YACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;QAAM;KAAa;IAEvB,mBAAmB;IACnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,SAAS,OAAO,EAAE;YAC5B,4BAA4B;YAC5B,SAAS,OAAO,CAAC,KAAK;QACxB;IACF,GAAG;QAAC;KAAK;IAET,wBAAwB;IACxB,MAAM,sBAAsB,CAAC;QAC3B,IAAI,EAAE,MAAM,KAAK,YAAY,OAAO,EAAE;YACpC,aAAa;QACf;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE;;;;;;0BAkDE,8OAAC;gBACC,KAAK;gBAKL,SAAS;gBACT,eAAY;gBACZ,MAAK;0DANM,CAAC;;UAEV,EAAE,OAAO,4BAA4B,uBAAuB;QAC9D,CAAC;;;;;;0BAOH,8OAAC;gBACC,KAAK;gBACL,MAAK;gBACL,cAAW;gBACX,mBAAgB;gBAChB,UAAU,CAAC;0DACA,CAAC;;;;;UAKV,EAAE,OACE,iFACA,0DACH;QACH,CAAC;;kCAGD,8OAAC;kEAAiB;;0CAChB,8OAAC;0EAAc;;kDACb,8OAAC;wCACC,IAAG;kFACO;kDACX;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,aAAa;wCAC5B,WAAU;wCACV,cAAW;wCACX,OAAM;kDAEN,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGjB,8OAAC;0EAAY;0CAAU;;;;;;;;;;;;kCAMzB,8OAAC;wBAEC,MAAK;wBACL,cAAW;kEAFD;kCAIV,cAAA,8OAAC;4BAEC,UAAU,CAAC;4BACX,OAAO;gCACL,gBAAgB;gCAChB,yBAAyB;4BAC3B;sEALU;sCAQV,cAAA,8OAAC,wIAAA,CAAA,UAAc;gCACb,iBAAiB;gCACjB,aAAa;;;;;;;;;;;;;;;;;;;;;;;;AAO3B", "debugId": null}}]}
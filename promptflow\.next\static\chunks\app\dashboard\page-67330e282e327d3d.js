(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5105],{24944:(e,s,a)=>{"use strict";a.d(s,{k:()=>i});var r=a(95155),t=a(12115),l=a(55863),n=a(59434);let i=t.forwardRef((e,s)=>{let{className:a,value:t,indicatorClassName:i,...c}=e;return(0,r.jsx)(l.bL,{ref:s,className:(0,n.cn)("relative h-2 w-full overflow-hidden rounded-full bg-gray-100",a),...c,children:(0,r.jsx)(l.C1,{className:(0,n.cn)("h-full w-full flex-1 bg-gray-900 transition-all",i),style:{transform:"translateX(-".concat(100-(t||0),"%)")}})})});i.displayName=l.bL.displayName},25296:(e,s,a)=>{Promise.resolve().then(a.bind(a,32385))},26042:(e,s,a)=>{"use strict";a.d(s,{LD:()=>i});var r=a(95155),t=a(1243),l=a(33109),n=a(30285);function i(e){let{type:s,current:a,max:i,onUpgrade:c}=e;return -1===i||a<i?null:(0,r.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-red-50 border border-red-200 rounded text-red-800 text-sm",children:[(0,r.jsx)(t.A,{className:"h-4 w-4 text-red-500"}),(0,r.jsx)("span",{className:"flex-1",children:"project"===s?"Proje limiti doldu":"Prompt limiti doldu"}),c&&(0,r.jsxs)(n.$,{size:"sm",variant:"outline",onClick:c,className:"h-6 text-xs",children:[(0,r.jsx)(l.A,{className:"h-3 w-3 mr-1"}),"Y\xfckselt"]})]})}a(66695),a(26126),a(12115)},32385:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>er});var r=a(95155),t=a(12115),l=a(30285),n=a(66695),i=a(62523),c=a(66424),o=a(13052),d=a(42355),m=a(54416),x=a(71007),u=a(34835),h=a(47924),p=a(84616),j=a(21380),g=a(6874),f=a.n(g),b=a(41978),y=a(67238),N=a(22100),v=a(74178),w=a(26042),k=a(26126),_=a(24944),P=a(22346),C=a(71539),E=a(17951),z=a(75525),A=a(1243),D=a(33109),R=a(54165),S=a(57701),L=a(9428),T=a(59434);let I=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(S.bL,{className:(0,T.cn)("grid gap-2",a),...t,ref:s})});I.displayName=S.bL.displayName;let $=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(S.q7,{ref:s,className:(0,T.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),...t,children:(0,r.jsx)(S.C1,{className:"flex items-center justify-center",children:(0,r.jsx)(L.A,{className:"h-2.5 w-2.5 fill-current text-current"})})})});$.displayName=S.q7.displayName;var B=a(85057),O=a(5196),Y=a(56671);function M(e){let{open:s,onOpenChange:a,currentPlan:i,planTypes:c}=e,[o,d]=(0,t.useState)(""),[x,u]=(0,t.useState)("monthly"),h=(0,v.Jd)(),p=async()=>{if(!o)return void Y.oR.error("L\xfctfen bir plan se\xe7in");try{await h.mutateAsync({planName:o,billingCycle:x}),Y.oR.success("Planınız başarıyla g\xfcncellendi!"),a(!1)}catch(e){Y.oR.error(e instanceof Error?e.message:"Plan g\xfcncellenirken hata oluştu")}},j=e=>(null==i?void 0:i.plan_name)===e;return(0,r.jsx)(R.lG,{open:s,onOpenChange:a,children:(0,r.jsxs)(R.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,r.jsxs)(R.c7,{children:[(0,r.jsx)(R.L3,{className:"text-2xl",children:"Planınızı Y\xfckseltin"}),(0,r.jsx)(R.rr,{children:"İhtiya\xe7larınıza en uygun planı se\xe7in ve daha fazla \xf6zellikten yararlanın"})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(B.J,{className:"text-base font-medium",children:"Faturalama D\xf6ng\xfcs\xfc"}),(0,r.jsxs)(I,{value:x,onValueChange:e=>u(e),className:"flex gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)($,{value:"monthly",id:"monthly"}),(0,r.jsx)(B.J,{htmlFor:"monthly",children:"Aylık"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)($,{value:"yearly",id:"yearly"}),(0,r.jsxs)(B.J,{htmlFor:"yearly",className:"flex items-center gap-2",children:["Yıllık",(0,r.jsx)(k.E,{variant:"secondary",className:"text-xs",children:"%17 İndirim"})]})]})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:c.map(e=>{var s;let a=(e=>{if("free"===e.name||"enterprise"===e.name)return null;let s=12*e.price_monthly,a=s-e.price_yearly,r=Math.round(a/s*100);return{amount:a,percentage:r}})(e),t=j(e.name),l="enterprise"!==(s=e.name)&&!j(s);return(0,r.jsxs)(n.Zp,{className:(0,T.cn)("relative cursor-pointer transition-all duration-200",(e=>{switch(e){case"free":return"border-blue-200 bg-blue-50";case"professional":return"border-yellow-200 bg-yellow-50";case"enterprise":return"border-purple-200 bg-purple-50";default:return"border-gray-200 bg-gray-50"}})(e.name),o===e.name&&"ring-2 ring-blue-500",t&&"ring-2 ring-green-500",!l&&"opacity-60 cursor-not-allowed"),onClick:()=>l&&d(e.name),children:[t&&(0,r.jsx)(k.E,{className:"absolute -top-2 left-1/2 transform -translate-x-1/2 bg-green-500",children:"Mevcut Plan"}),"professional"===e.name&&(0,r.jsx)(k.E,{className:"absolute -top-2 right-4 bg-blue-500",children:"Pop\xfcler"}),(0,r.jsxs)(n.aR,{className:"text-center pb-2",children:[(0,r.jsx)("div",{className:"flex justify-center mb-2",children:(e=>{switch(e){case"free":return(0,r.jsx)(C.A,{className:"h-6 w-6 text-blue-500"});case"professional":return(0,r.jsx)(E.A,{className:"h-6 w-6 text-yellow-500"});case"enterprise":return(0,r.jsx)(z.A,{className:"h-6 w-6 text-purple-500"});default:return(0,r.jsx)(C.A,{className:"h-6 w-6 text-gray-500"})}})(e.name)}),(0,r.jsx)(n.ZB,{className:"text-xl",children:e.display_name}),(0,r.jsx)(n.BT,{className:"text-sm",children:e.description}),(0,r.jsxs)("div",{className:"pt-2",children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-gray-900",children:(e=>{if("enterprise"===e.name)return"\xd6zel Fiyat";if("free"===e.name)return"\xdccretsiz";let s="monthly"===x?e.price_monthly:e.price_yearly,a="monthly"===x?"ay":"yıl";return"₺".concat(s,"/").concat(a)})(e)}),"yearly"===x&&a&&(0,r.jsxs)("div",{className:"text-sm text-green-600 font-medium",children:["Yıllık ₺",a.amount," tasarruf"]})]})]}),(0,r.jsxs)(n.Wu,{className:"space-y-4",children:[(0,r.jsx)(P.w,{}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsx)("span",{children:"Proje Limiti"}),(0,r.jsx)("span",{className:"font-medium",children:-1===e.max_projects?"Sınırsız":e.max_projects})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsx)("span",{children:"Prompt Limiti"}),(0,r.jsx)("span",{className:"font-medium",children:-1===e.max_prompts_per_project?"Sınırsız":"".concat(e.max_prompts_per_project,"/proje")})]})]}),(0,r.jsx)(P.w,{}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h4",{className:"font-medium text-sm",children:"\xd6zellikler"}),(0,r.jsx)("div",{className:"space-y-1",children:Object.entries(e.features).map(e=>{let[s,a]=e;return(0,r.jsxs)("div",{className:"flex items-center gap-2 text-xs",children:[a?(0,r.jsx)(O.A,{className:"h-3 w-3 text-green-500"}):(0,r.jsx)(m.A,{className:"h-3 w-3 text-gray-400"}),(0,r.jsxs)("span",{className:a?"text-gray-900":"text-gray-500",children:["context_gallery"===s&&"Context Gallery","api_access"===s&&"API Erişimi","support"===s&&"".concat(a," Destek"),"team_features"===s&&"Takım \xd6zellikleri","advanced_analytics"===s&&"Gelişmiş Analitik","sso"===s&&"SSO Entegrasyonu","custom_deployment"===s&&"\xd6zel Deployment"]})]},s)})})]}),"enterprise"===e.name&&(0,r.jsx)("div",{className:"text-xs text-gray-600 bg-gray-50 p-2 rounded",children:"Kurumsal plan i\xe7in satış ekibimizle iletişime ge\xe7in"})]})]},e.id)})}),(0,r.jsxs)("div",{className:"flex justify-end gap-3 pt-4",children:[(0,r.jsx)(l.$,{variant:"outline",onClick:()=>a(!1),children:"İptal"}),(0,r.jsx)(l.$,{onClick:p,disabled:!o||h.isPending,children:h.isPending?"G\xfcncelleniyor...":"Planı G\xfcncelle"})]})]})]})})}function G(){let[e,s]=(0,t.useState)(!1),{data:a,isLoading:i}=(0,v.EU)(),{data:c,isLoading:o}=(0,v.p$)(),{data:d}=(0,v.C)();if(i||o)return(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-2/3"})]}),(0,r.jsx)(n.Wu,{children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-3/4"})]})})]});let m=(e,s)=>{if(-1===s)return"text-green-600";let a=e/s*100;return a>=90?"text-red-600":a>=70?"text-yellow-600":"text-green-600"},x=(e,s)=>{if(-1===s)return"bg-green-500";let a=e/s*100;return a>=90?"bg-red-500":a>=70?"bg-yellow-500":"bg-green-500"},u=()=>{if(!c||!a||"enterprise"===a.plan_name)return!1;let e=-1===c.max_projects?0:c.current_projects/c.max_projects*100,s=-1===c.max_prompts_per_project?0:c.current_prompts/c.max_prompts_per_project*100;return e>=80||s>=80};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(n.Zp,{className:"w-full",children:[(0,r.jsx)(n.aR,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[a&&(e=>{switch(e){case"free":return(0,r.jsx)(C.A,{className:"h-5 w-5 text-blue-500"});case"professional":return(0,r.jsx)(E.A,{className:"h-5 w-5 text-yellow-500"});case"enterprise":return(0,r.jsx)(z.A,{className:"h-5 w-5 text-purple-500"});default:return(0,r.jsx)(C.A,{className:"h-5 w-5 text-gray-500"})}})(a.plan_name),(0,r.jsxs)("div",{children:[(0,r.jsx)(n.ZB,{className:"text-lg",children:(null==a?void 0:a.display_name)||"Plan Y\xfckleniyor..."}),(0,r.jsx)(n.BT,{children:"Mevcut planınız ve kullanım durumunuz"})]})]}),a&&(0,r.jsx)(k.E,{variant:"outline",className:(0,T.cn)("font-medium",(e=>{switch(e){case"free":return"bg-blue-50 text-blue-700 border-blue-200";case"professional":return"bg-yellow-50 text-yellow-700 border-yellow-200";case"enterprise":return"bg-purple-50 text-purple-700 border-purple-200";default:return"bg-gray-50 text-gray-700 border-gray-200"}})(a.plan_name)),children:"active"===a.status?"Aktif":a.status})]})}),(0,r.jsxs)(n.Wu,{className:"space-y-4",children:[c&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsx)("span",{className:"font-medium",children:"Projeler"}),(0,r.jsxs)("span",{className:m(c.current_projects,c.max_projects),children:[c.current_projects," / ",-1===c.max_projects?"∞":c.max_projects]})]}),(0,r.jsx)(_.k,{value:-1===c.max_projects?0:c.current_projects/c.max_projects*100,className:"h-2",style:{"--progress-background":x(c.current_projects,c.max_projects)}})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsx)("span",{className:"font-medium",children:"Prompt'lar (Proje başına max)"}),(0,r.jsxs)("span",{className:m(c.current_prompts,c.max_prompts_per_project),children:[c.current_prompts," / ",-1===c.max_prompts_per_project?"∞":c.max_prompts_per_project]})]}),(0,r.jsx)(_.k,{value:-1===c.max_prompts_per_project?0:c.current_prompts/c.max_prompts_per_project*100,className:"h-2",style:{"--progress-background":x(c.current_prompts,c.max_prompts_per_project)}})]})]}),u()&&(0,r.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:[(0,r.jsx)(A.A,{className:"h-4 w-4 text-yellow-600"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-yellow-800",children:"Limitinize yaklaşıyorsunuz"}),(0,r.jsx)("p",{className:"text-xs text-yellow-700",children:"Daha fazla \xf6zellik i\xe7in planınızı y\xfckseltin"})]})]}),(0,r.jsx)(P.w,{}),(null==a?void 0:a.features)&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:"Plan \xd6zellikleri"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[a.features.context_gallery&&(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)("div",{className:"w-1.5 h-1.5 bg-green-500 rounded-full"}),(0,r.jsx)("span",{children:"Context Gallery"})]}),a.features.api_access&&(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)("div",{className:"w-1.5 h-1.5 bg-green-500 rounded-full"}),(0,r.jsx)("span",{children:"API Erişimi"})]}),a.features.team_features&&(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)("div",{className:"w-1.5 h-1.5 bg-green-500 rounded-full"}),(0,r.jsx)("span",{children:"Takım \xd6zellikleri"})]}),a.features.advanced_analytics&&(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)("div",{className:"w-1.5 h-1.5 bg-green-500 rounded-full"}),(0,r.jsx)("span",{children:"Gelişmiş Analitik"})]})]})]}),(null==a?void 0:a.plan_name)!=="enterprise"&&(0,r.jsxs)(l.$,{onClick:()=>s(!0),className:"w-full",variant:u()?"default":"outline",children:[(0,r.jsx)(D.A,{className:"h-4 w-4 mr-2"}),"Planı Y\xfckselt"]})]})]}),(0,r.jsx)(M,{open:e,onOpenChange:s,currentPlan:a,planTypes:d||[]})]})}var K=a(69949);function F(e){let{onClose:s,isCollapsed:a=!1,onToggleCollapse:g}=e,[k,_]=(0,t.useState)(""),[P,C]=(0,t.useState)(!1),[E,z]=(0,t.useState)(""),{activeProjectId:A,setActiveProjectId:D}=(0,b.C)(),{data:R=[],isLoading:S,error:L}=(0,y.YK)(),{data:T}=(0,N.Jd)(),{data:I}=(0,v.p$)(),$=(0,y.bL)(),B=(0,N.Rt)();console.log("\uD83D\uDCC1 [PROJECT_SIDEBAR] Projects:",{count:R.length,loading:S,error:L,projects:R.map(e=>({id:e.id,name:e.name}))});let O=R.filter(e=>e.name.toLowerCase().includes(k.toLowerCase())),M=async()=>{if(E.trim()){if(I&&!I.can_create_project)return void Y.oR.error("Proje oluşturma limitinize ulaştınız (".concat(I.current_projects,"/").concat(I.max_projects,"). Planınızı y\xfckseltin."));try{let e=await $.mutateAsync({name:E.trim(),context_text:""});D(e.id),z(""),C(!1)}catch(e){console.error("Proje oluşturma hatası:",e)}}},F=async()=>{try{console.log("Logout button clicked"),await B.mutateAsync()}catch(e){console.error("Logout error in component:",e)}};return(0,r.jsxs)("div",{className:"flex flex-col h-full",children:[(0,r.jsxs)("div",{className:"border-b border-gray-200 ".concat(a?"p-2":"p-4 lg:p-6"),children:[(0,r.jsxs)("div",{className:"flex items-center ".concat(a?"justify-center mb-2":"justify-between mb-4"),children:[!a&&(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("img",{src:"/logo.png",alt:"Promptbir Logo",className:"h-6 w-auto"}),(0,r.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"Promptbir"})]}),a&&(0,r.jsx)("img",{src:"/logo.png",alt:"Promptbir Logo",className:"h-6 w-auto",title:"Promptbir"}),(0,r.jsxs)("div",{className:"flex items-center gap-2 ".concat(a?"flex-col":""),children:[g&&(0,r.jsx)(l.$,{variant:"ghost",size:"sm",onClick:g,className:"hidden lg:flex",title:a?"Proje panelini genişlet":"Proje panelini daralt",children:a?(0,r.jsx)(o.A,{className:"h-4 w-4"}):(0,r.jsx)(d.A,{className:"h-4 w-4"})}),s&&(0,r.jsx)(l.$,{variant:"ghost",size:"sm",onClick:s,className:"lg:hidden",children:(0,r.jsx)(m.A,{className:"h-4 w-4"})}),(0,r.jsx)(f(),{href:"/profile",children:(0,r.jsx)(l.$,{variant:"ghost",size:"sm",title:(null==T?void 0:T.email)||"Kullanıcı",children:(0,r.jsx)(x.A,{className:"h-4 w-4"})})}),(0,r.jsx)(l.$,{variant:"ghost",size:"sm",onClick:F,disabled:B.isPending,title:"\xc7ıkış Yap",children:(0,r.jsx)(u.A,{className:"h-4 w-4"})})]})]}),!a&&(0,r.jsxs)("div",{className:"relative mb-4",children:[(0,r.jsx)(h.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)(i.p,{placeholder:"Projelerde ara...",value:k,onChange:e=>_(e.target.value),className:"pl-10"})]}),!a&&I&&(0,r.jsx)(w.LD,{type:"project",current:I.current_projects,max:I.max_projects,onUpgrade:()=>{}}),(0,r.jsxs)(l.$,{onClick:()=>C(!0),className:"".concat(a?"w-8 h-8 p-0":"w-full"),size:"sm",title:a?"Yeni Proje":void 0,disabled:I&&!I.can_create_project,children:[(0,r.jsx)(p.A,{className:"h-4 w-4"}),!a&&(0,r.jsx)("span",{className:"ml-2",children:"Yeni Proje"})]})]}),(0,r.jsx)(c.F,{className:"flex-1 ".concat(a?"p-2":"p-4"),children:(0,r.jsxs)("div",{className:"space-y-2 pb-24 sm:pb-20 lg:pb-24",children:[P&&!a&&(0,r.jsx)(n.Zp,{className:"border-blue-200 bg-blue-50",children:(0,r.jsxs)(n.Wu,{className:"p-3",children:[(0,r.jsx)(i.p,{placeholder:"Proje adı...",value:E,onChange:e=>z(e.target.value),onKeyPress:e=>"Enter"===e.key&&M(),className:"mb-2",autoFocus:!0}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(l.$,{size:"sm",onClick:M,children:"Oluştur"}),(0,r.jsx)(l.$,{size:"sm",variant:"outline",onClick:()=>{C(!1),z("")},children:"İptal"})]})]})}),O.map(e=>(0,r.jsx)(n.Zp,{className:"transition-all hover:shadow-md cursor-pointer ".concat(A===e.id?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"," ").concat(a?"p-2":""),onClick:a?()=>{D(e.id),null==s||s()}:()=>{D(e.id),null==s||s()},title:a?e.name:void 0,children:a?(0,r.jsx)("div",{className:"flex items-center justify-center",children:(0,r.jsx)(j.A,{className:"h-4 w-4 ".concat(A===e.id?"text-blue-600":"text-gray-600")})}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.aR,{className:"pb-2",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(j.A,{className:"h-4 w-4 text-blue-600 flex-shrink-0"}),(0,r.jsx)(K.ProjectNameEditor,{projectId:e.id,currentName:e.name,onNameUpdated:s=>{console.log("\uD83D\uDCDD [PROJECT_SIDEBAR] Project name updated:",{projectId:e.id,oldName:e.name,newName:s})},className:"flex-1 min-w-0"})]})}),(0,r.jsx)(n.Wu,{className:"pt-0",children:(0,r.jsx)("p",{className:"text-xs text-gray-500",children:new Date(e.created_at).toLocaleDateString("tr-TR")})})]})},e.id))]})}),!a&&(0,r.jsx)("div",{className:"p-4 border-t border-gray-200",children:(0,r.jsx)(G,{})})]})}var Z=a(9454),W=a(84573);let H={sm:640,md:768,lg:1024,xl:1280,"2xl":1536};function J(e){return e>=H["2xl"]?"2xl":e>=H.xl?"xl":e>=H.lg?"lg":e>=H.md?"md":e>=H.sm?"sm":"xs"}function U(e){return e<H.md?"mobile":e<H.lg?"tablet":"desktop"}var q=a(74783),V=a(381);let X=new Map,Q=async(e,s)=>{if(window.performance)try{performance.mark("dynamic-import-".concat(e,"-start"))}catch(e){}if(X.has(e))return X.get(e);let a=Date.now(),r=s().then(s=>(ea(e,a),s));return X.set(e,r),r};(0,t.lazy)(()=>Q("context-gallery",()=>Promise.all([a.e(2123),a.e(8611),a.e(8779),a.e(8650),a.e(8523),a.e(8405),a.e(9420),a.e(4939),a.e(1486),a.e(2662),a.e(3240),a.e(8669),a.e(3189),a.e(622),a.e(4696),a.e(1277),a.e(7979),a.e(1899),a.e(7098),a.e(4450),a.e(9744),a.e(4495),a.e(9592),a.e(433),a.e(2652),a.e(5030),a.e(465),a.e(2903),a.e(2663),a.e(9173),a.e(408),a.e(558),a.e(1356),a.e(6475),a.e(2130),a.e(4207),a.e(4191),a.e(6489),a.e(5230),a.e(7339),a.e(957),a.e(5677),a.e(6691),a.e(1151),a.e(7114),a.e(5803),a.e(3976),a.e(3492),a.e(2608),a.e(5644),a.e(2789),a.e(9824),a.e(4075),a.e(9473),a.e(7530),a.e(6759),a.e(0),a.e(6325),a.e(6077),a.e(4409),a.e(9184),a.e(7650),a.e(1595),a.e(4532),a.e(1911),a.e(5565),a.e(1038),a.e(6772),a.e(8451)]).then(a.bind(a,86772)).then(e=>({default:e.default||e}))));let ee=(0,t.lazy)(()=>Q("prompt-workspace",()=>Promise.all([a.e(2123),a.e(8611),a.e(8779),a.e(8650),a.e(8523),a.e(8405),a.e(9420),a.e(4939),a.e(1486),a.e(2662),a.e(3240),a.e(8669),a.e(3189),a.e(622),a.e(4696),a.e(1277),a.e(7979),a.e(1899),a.e(7098),a.e(4450),a.e(9744),a.e(4495),a.e(9592),a.e(433),a.e(2652),a.e(5030),a.e(465),a.e(2903),a.e(2663),a.e(9173),a.e(408),a.e(558),a.e(1356),a.e(6475),a.e(2130),a.e(4207),a.e(4191),a.e(6489),a.e(5230),a.e(7339),a.e(957),a.e(5677),a.e(6691),a.e(1151),a.e(7114),a.e(5803),a.e(3976),a.e(3492),a.e(2608),a.e(5644),a.e(2789),a.e(9824),a.e(4075),a.e(9473),a.e(7530),a.e(6759),a.e(0),a.e(6325),a.e(6077),a.e(4409),a.e(9184),a.e(7650),a.e(1595),a.e(4532),a.e(1911),a.e(8159)]).then(a.bind(a,8159)).then(e=>({default:e.default||e})))),es=(0,t.lazy)(()=>Q("context-sidebar",()=>Promise.all([a.e(2123),a.e(686)]).then(a.bind(a,98305)).then(e=>({default:e.default||e}))));(0,t.lazy)(()=>Q("project-name-editor",()=>Promise.resolve().then(a.bind(a,69949)).then(e=>({default:e.ProjectNameEditor})))),(0,t.lazy)(()=>Q("context-creation-modal",()=>Promise.all([a.e(2123),a.e(8611),a.e(8779),a.e(8650),a.e(8523),a.e(8405),a.e(9420),a.e(4939),a.e(1486),a.e(2662),a.e(3240),a.e(8669),a.e(3189),a.e(622),a.e(4696),a.e(1277),a.e(7979),a.e(1899),a.e(7098),a.e(4450),a.e(9744),a.e(4495),a.e(9592),a.e(433),a.e(2652),a.e(5030),a.e(465),a.e(2903),a.e(2663),a.e(9173),a.e(408),a.e(558),a.e(1356),a.e(6475),a.e(2130),a.e(4207),a.e(4191),a.e(6489),a.e(5230),a.e(7339),a.e(957),a.e(5677),a.e(6691),a.e(1151),a.e(7114),a.e(5803),a.e(3976),a.e(3492),a.e(2608),a.e(5644),a.e(2789),a.e(9824),a.e(4075),a.e(9473),a.e(7530),a.e(6759),a.e(0),a.e(6325),a.e(6077),a.e(4409),a.e(9184),a.e(7650),a.e(1595),a.e(4532),a.e(1911),a.e(5565),a.e(832)]).then(a.bind(a,35565)).then(e=>({default:e.default||e})))),(0,t.lazy)(()=>Q("context-edit-modal",()=>Promise.all([a.e(2123),a.e(8611),a.e(8779),a.e(8650),a.e(8523),a.e(8405),a.e(9420),a.e(4939),a.e(1486),a.e(2662),a.e(3240),a.e(8669),a.e(3189),a.e(622),a.e(4696),a.e(1277),a.e(7979),a.e(1899),a.e(7098),a.e(4450),a.e(9744),a.e(4495),a.e(9592),a.e(433),a.e(2652),a.e(5030),a.e(465),a.e(2903),a.e(2663),a.e(9173),a.e(408),a.e(558),a.e(1356),a.e(6475),a.e(2130),a.e(4207),a.e(4191),a.e(6489),a.e(5230),a.e(7339),a.e(957),a.e(5677),a.e(6691),a.e(1151),a.e(7114),a.e(5803),a.e(3976),a.e(3492),a.e(2608),a.e(5644),a.e(2789),a.e(9824),a.e(4075),a.e(9473),a.e(7530),a.e(6759),a.e(0),a.e(6325),a.e(6077),a.e(4409),a.e(9184),a.e(7650),a.e(1595),a.e(4532),a.e(1911),a.e(1038),a.e(5068)]).then(a.bind(a,41038)).then(e=>({default:e.default||e})))),(0,t.lazy)(()=>Q("dashboard-page",()=>Promise.resolve().then(a.bind(a,32385)).then(e=>({default:e.default||e})))),(0,t.lazy)(()=>Q("profile-page",()=>Promise.all([a.e(2123),a.e(8611),a.e(6935)]).then(a.bind(a,66935)).then(e=>({default:e.default||e})))),(0,t.lazy)(()=>Q("auth-page",()=>Promise.all([a.e(2123),a.e(1579)]).then(a.bind(a,31579)).then(e=>({default:e.default||e}))));let ea=(e,s)=>{let a=Date.now()-s;if(window.performance)try{let s="dynamic-import-".concat(e,"-start"),a="dynamic-import-".concat(e,"-end"),r="dynamic-import-".concat(e);performance.mark(a);try{if(performance.getEntriesByName(s,"mark").length>0)performance.measure(r,s,a);else{let e=performance.timeOrigin||Date.now();performance.measure(r,{start:e,end:performance.now()})}}catch(e){console.debug("Performance measurement skipped:",e)}}catch(s){console.warn("Performance tracking failed for ".concat(e,":"),s)}console.log("\uD83D\uDE80 [DYNAMIC_IMPORT] ".concat(e," loaded in ").concat(a,"ms"))},er=(0,t.memo)(function(){let[e,s]=(0,t.useState)(!1),[a,n]=(0,t.useState)(!1),[i,c]=(0,t.useState)(!1),{isMobile:o,orientation:d}=function(){let[e,s]=(0,t.useState)(()=>{let e=window.innerWidth,s=window.innerHeight,a=J(e),r=U(e),t=e>s?"landscape":"portrait";return{width:e,height:s,breakpoint:a,deviceType:r,orientation:t,isMobile:"mobile"===r,isTablet:"tablet"===r,isDesktop:"desktop"===r,isTouch:"ontouchstart"in window||navigator.maxTouchPoints>0,pixelRatio:window.devicePixelRatio||1}}),a=(0,t.useCallback)(()=>{let e=window.innerWidth,a=window.innerHeight,r=J(e),t=U(e),l=e>a?"landscape":"portrait";s({width:e,height:a,breakpoint:r,deviceType:t,orientation:l,isMobile:"mobile"===t,isTablet:"tablet"===t,isDesktop:"desktop"===t,isTouch:"ontouchstart"in window||navigator.maxTouchPoints>0,pixelRatio:window.devicePixelRatio||1})},[]);return(0,t.useEffect)(()=>{let e,s=()=>{clearTimeout(e),e=setTimeout(a,150)};return window.addEventListener("resize",s),window.addEventListener("orientationchange",a),()=>{window.removeEventListener("resize",s),window.removeEventListener("orientationchange",a),clearTimeout(e)}},[a]),e}(),{isProjectSidebarCollapsed:m,setIsProjectSidebarCollapsed:x,isContextSidebarCollapsed:u,setIsContextSidebarCollapsed:h}=(0,b.C)();return(0,t.useEffect)(()=>{},[]),(0,t.useEffect)(()=>{o&&(s(!1),n(!1))},[d,o]),(0,r.jsx)(Z.q,{children:(0,r.jsx)(W.ErrorBoundary,{level:"page",showDetails:!1,children:(0,r.jsxs)("div",{className:"\n        flex full-height-mobile bg-gray-50 relative\n        ".concat(o?"flex-col":"flex-row","\n      "),children:[(e||a)&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black/50 z-40 lg:hidden mobile-transition",onClick:()=>{s(!1),n(!1)}}),(0,r.jsx)("div",{className:"\n          fixed lg:relative inset-y-0 left-0 z-50\n          w-[85vw] sm:w-80\n          ".concat(m?"lg:w-16":"lg:w-80","\n          transform transition-all duration-300 ease-in-out\n          lg:transform-none lg:translate-x-0\n          border-r border-gray-200 bg-white\n          ").concat(e?"translate-x-0":"-translate-x-full lg:translate-x-0","\n          ").concat(e?"block":"hidden lg:block","\n        "),children:(0,r.jsx)(F,{onClose:()=>s(!1),isCollapsed:m,onToggleCollapse:()=>x(!m)})}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col min-w-0",children:[(0,r.jsxs)("div",{className:"lg:hidden flex items-center justify-between p-4 bg-white border-b border-gray-200 safe-area-top",children:[(0,r.jsxs)(l.$,{variant:"ghost",size:"sm",onClick:()=>s(!0),className:"flex items-center gap-2 touch-target focus-visible-enhanced",children:[(0,r.jsx)(q.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{className:"font-medium mobile-text-base",children:"Projeler"})]}),(0,r.jsx)("h1",{className:"text-lg font-semibold text-gray-900 mobile-text-base",children:"Promptbir"}),(0,r.jsxs)(l.$,{variant:"ghost",size:"sm",onClick:()=>n(!0),className:"flex items-center gap-2 touch-target focus-visible-enhanced",children:[(0,r.jsx)(V.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{className:"font-medium mobile-text-base",children:"Ayarlar"})]})]}),(0,r.jsx)(t.Suspense,{fallback:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}),children:(0,r.jsx)(ee,{isContextGalleryOpen:i,onToggleContextGallery:()=>c(!i)})})]}),(0,r.jsx)("div",{className:"\n          fixed xl:relative inset-y-0 right-0 z-50\n          w-[90vw] sm:w-96\n          ".concat(u?"xl:w-16":"xl:w-96","\n          transform transition-all duration-300 ease-in-out\n          xl:transform-none xl:translate-x-0\n          border-l border-gray-200 bg-white\n          ").concat(a?"translate-x-0":"translate-x-full xl:translate-x-0","\n          ").concat(a?"block":"hidden xl:block","\n        "),children:(0,r.jsx)(t.Suspense,{fallback:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}),children:(0,r.jsx)(es,{onClose:()=>n(!1),isCollapsed:u,onToggleCollapse:()=>h(!u),isContextGalleryOpen:i,onToggleContextGallery:()=>c(!i)})})})]})})})})},41978:(e,s,a)=>{"use strict";a.d(s,{C:()=>l});var r=a(65453),t=a(46786);let l=(0,r.v)()((0,t.Zr)(e=>({activeProjectId:null,setActiveProjectId:s=>e({activeProjectId:s}),isContextEnabled:!0,setIsContextEnabled:s=>e({isContextEnabled:s}),isProjectSidebarCollapsed:!1,setIsProjectSidebarCollapsed:s=>e({isProjectSidebarCollapsed:s}),isContextSidebarCollapsed:!1,setIsContextSidebarCollapsed:s=>e({isContextSidebarCollapsed:s})}),{name:"promptflow-app-store"}))},66424:(e,s,a)=>{"use strict";a.d(s,{F:()=>n});var r=a(95155);a(12115);var t=a(47655),l=a(59434);function n(e){let{className:s,children:a,...n}=e;return(0,r.jsxs)(t.bL,{"data-slot":"scroll-area",className:(0,l.cn)("relative",s),...n,children:[(0,r.jsx)(t.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:a}),(0,r.jsx)(i,{}),(0,r.jsx)(t.OK,{})]})}function i(e){let{className:s,orientation:a="vertical",...n}=e;return(0,r.jsx)(t.VM,{"data-slot":"scroll-area-scrollbar",orientation:a,className:(0,l.cn)("flex touch-none p-px transition-colors select-none","vertical"===a&&"h-full w-2.5 border-l border-l-transparent","horizontal"===a&&"h-2.5 flex-col border-t border-t-transparent",s),...n,children:(0,r.jsx)(t.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}},69949:(e,s,a)=>{"use strict";a.r(s),a.d(s,{ProjectNameEditor:()=>j});var r=a(95155),t=a(12115),l=a(30285),n=a(62523),i=a(51154),c=a(54416),o=a(40646),d=a(5196),m=a(56287),x=a(67238),u=a(86730),h=a(56671),p=a(59434);let j=(0,t.memo)(function(e){let{projectId:s,currentName:a,onNameUpdated:j,className:g,disabled:f=!1}=e,[b,y]=(0,t.useState)(!1),[N,v]=(0,t.useState)(a),[w,k]=(0,t.useState)(""),[_,P]=(0,t.useState)(!1),[C,E]=(0,t.useState)(!1),z=(0,t.useRef)(null),A=(0,x.eo)(),{data:D=[]}=(0,x.YK)(),R=(0,t.useCallback)(()=>(0,u.qj)(D,s,300),[D,s])(),S=(0,t.useCallback)(()=>{f||A.isPending||(y(!0),v(a),k(""),P(!1),E(!1))},[f,A.isPending,a]),L=(0,t.useCallback)(()=>{y(!1),v(a),k(""),P(!1),E(!1)},[a]),T=(0,t.useCallback)(async()=>{if(N.trim()&&!w&&!_){if((0,u.Br)(N,a))return void L();try{let e=await A.mutateAsync({projectId:s,newName:N.trim()});h.oR.success("Proje adı başarıyla g\xfcncellendi!"),y(!1),null==j||j(e.name)}catch(s){console.error("Project name update error:",s);let e=s instanceof Error?s.message:"Proje adı g\xfcncellenirken bir hata oluştu";h.oR.error(e),setTimeout(()=>{var e;null==(e=z.current)||e.focus()},100)}}},[N,w,_,a,s,A,j,L]),I=(0,t.useCallback)(e=>{let s=e.target.value;v(s),s.trim()?(P(!0),E(!1),R(s,e=>{k(e.isValid?"":e.error||""),E(e.isValid&&!(0,u.Br)(s,a)),P(!1)})):(k("Proje adı boş olamaz"),E(!1),P(!1))},[R,a]),$=(0,t.useCallback)(e=>{"Enter"===e.key?(e.preventDefault(),T()):"Escape"===e.key&&(e.preventDefault(),L())},[T,L]);return((0,t.useEffect)(()=>{b&&z.current&&(z.current.focus(),z.current.select())},[b]),(0,t.useEffect)(()=>{b||v(a)},[a,b]),b)?(0,r.jsxs)("div",{className:(0,p.cn)("flex items-center gap-2 w-full",g),children:[(0,r.jsxs)("div",{className:"flex-1 relative",children:[(0,r.jsx)(n.p,{ref:z,value:N,onChange:I,onKeyDown:$,className:(0,p.cn)("text-sm font-medium",w&&"border-red-500 focus:border-red-500",C&&"border-green-500 focus:border-green-500","focus:ring-2 focus:ring-blue-500/20"),placeholder:"Proje adı...",disabled:A.isPending,maxLength:50,"aria-label":"Proje adını d\xfczenle","aria-describedby":"project-name-feedback-".concat(s),"aria-invalid":!!w}),(0,r.jsxs)("div",{className:"absolute top-full right-0 mt-1 text-xs text-gray-400 z-10",children:[(0,r.jsx)("span",{className:"sr-only",children:"Karakter sayısı: "}),N.length,"/50"]}),(w||_||C)&&(0,r.jsx)("div",{id:"project-name-feedback-".concat(s),className:"absolute top-full left-0 mt-1 text-xs z-10",role:"status","aria-live":"polite",children:_?(0,r.jsxs)("span",{className:"text-gray-500 flex items-center gap-1",children:[(0,r.jsx)(i.A,{className:"h-3 w-3 animate-spin"}),"Kontrol ediliyor..."]}):w?(0,r.jsxs)("span",{className:"text-red-500 flex items-center gap-1",children:[(0,r.jsx)(c.A,{className:"h-3 w-3"}),w]}):C?(0,r.jsxs)("span",{className:"text-green-600 flex items-center gap-1",children:[(0,r.jsx)(o.A,{className:"h-3 w-3"}),"Proje adı kullanılabilir"]}):null})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(l.$,{size:"sm",variant:"ghost",onClick:T,disabled:A.isPending||!!w||_||!N.trim()||(0,u.Br)(N,a),className:"h-8 w-8 p-0 text-green-600 hover:text-green-700 hover:bg-green-50 touch-target",title:"Kaydet (Enter)","aria-label":"Proje adını kaydet",children:A.isPending?(0,r.jsx)(i.A,{className:"h-4 w-4 animate-spin"}):(0,r.jsx)(d.A,{className:"h-4 w-4"})}),(0,r.jsx)(l.$,{size:"sm",variant:"ghost",onClick:L,disabled:A.isPending,className:"h-8 w-8 p-0 text-gray-500 hover:text-gray-700 hover:bg-gray-50 touch-target",title:"İptal (Esc)","aria-label":"D\xfczenlemeyi iptal et",children:(0,r.jsx)(c.A,{className:"h-4 w-4"})})]})]}):(0,r.jsxs)("div",{className:(0,p.cn)("flex items-center gap-2 group w-full",g),children:[(0,r.jsx)("span",{className:"flex-1 text-sm font-medium text-gray-900 truncate",title:a,children:a}),(0,r.jsx)(l.$,{size:"sm",variant:"ghost",onClick:e=>{e.stopPropagation(),S()},disabled:f||A.isPending,className:(0,p.cn)("h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity touch-target","text-gray-400 hover:text-gray-600 hover:bg-gray-50","focus:opacity-100 focus:ring-2 focus:ring-blue-500/20","lg:opacity-0 opacity-100"),title:"Proje adını d\xfczenle","aria-label":"Proje adını d\xfczenle",children:(0,r.jsx)(m.A,{className:"h-3 w-3"})})]})})},84573:(e,s,a)=>{"use strict";a.d(s,{ErrorBoundary:()=>d});var r=a(95155),t=a(12115),l=a(30285),n=a(66695),i=a(1243),c=a(53904),o=a(57340);class d extends t.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e,errorId:"error-".concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9))}}componentDidCatch(e,s){var a;e.message.includes("Hydration")||e.message.includes("hydrating")||e.message.includes("Text content did not match")||null==(a=s.componentStack)||a.includes("hydration"),console.error("\uD83D\uDEA8 [ERROR_BOUNDARY] Error caught:",{error:e.message,stack:e.stack,componentStack:s.componentStack,errorId:this.state.errorId,level:this.props.level||"component",timestamp:new Date().toISOString()}),this.setState({errorInfo:s}),this.props.onError&&this.props.onError(e,s),this.reportError(e,s)}render(){if(this.state.hasError){var e;if(this.props.fallback)return this.props.fallback;let s=this.getErrorLevel(),a=this.retryCount<this.maxRetries,{error:t}=this.state;return(0,r.jsx)("div",{className:"min-h-[400px] flex items-center justify-center p-4",children:(0,r.jsxs)(n.Zp,{className:"w-full max-w-lg",children:[(0,r.jsxs)(n.aR,{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto mb-4",children:(0,r.jsx)(i.A,{className:"h-12 w-12 ".concat("high"===s?"text-red-500":"medium"===s?"text-yellow-500":"text-orange-500")})}),(0,r.jsx)(n.ZB,{className:"text-xl",children:"high"===s?"Kritik Hata":"medium"===s?"Bir Sorun Oluştu":"Beklenmeyen Hata"}),(0,r.jsx)(n.BT,{children:(null==t?void 0:t.name)==="ChunkLoadError"?"Uygulama g\xfcncellenmiş olabilir. Sayfayı yenilemeyi deneyin.":(null==t||null==(e=t.message)?void 0:e.includes("Network"))?"Ağ bağlantısı sorunu. İnternet bağlantınızı kontrol edin.":"Bir hata oluştu. L\xfctfen tekrar deneyin veya sayfayı yenileyin."})]}),(0,r.jsxs)(n.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2",children:[a&&(0,r.jsxs)(l.$,{onClick:this.handleRetry,className:"flex-1",variant:"default",children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Tekrar Dene (",this.maxRetries-this.retryCount," kalan)"]}),(0,r.jsxs)(l.$,{onClick:this.handleReload,variant:"outline",className:"flex-1",children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Sayfayı Yenile"]}),(0,r.jsxs)(l.$,{onClick:this.handleGoHome,variant:"outline",className:"flex-1",children:[(0,r.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Ana Sayfa"]})]}),!1,this.renderErrorDetails()]})]})})}return this.props.children}constructor(e){super(e),this.retryCount=0,this.maxRetries=3,this.reportError=(e,s)=>{},this.handleRetry=()=>{this.retryCount<this.maxRetries?(this.retryCount++,console.log("\uD83D\uDD04 [ERROR_BOUNDARY] Retry attempt ".concat(this.retryCount,"/").concat(this.maxRetries)),this.setState({hasError:!1,error:null,errorInfo:null,errorId:null})):console.warn("⚠️ [ERROR_BOUNDARY] Max retries exceeded")},this.handleReload=()=>{window.location.reload()},this.handleGoHome=()=>{window.location.href="/"},this.getErrorLevel=()=>{var e,s;let{level:a}=this.props,{error:r}=this.state;return"critical"===a?"high":"page"===a||(null==r?void 0:r.name)==="ChunkLoadError"||(null==r||null==(e=r.message)?void 0:e.includes("Network"))?"medium":(null==r||null==(s=r.message)||s.includes("TypeError"),"low")},this.renderErrorDetails=()=>{let{error:e,errorInfo:s,errorId:a}=this.state,{showDetails:t=!1}=this.props;return t&&e?(0,r.jsxs)("details",{className:"mt-4 p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("summary",{className:"cursor-pointer font-medium text-gray-700 mb-2",children:"Teknik Detaylar"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm font-mono",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Error ID:"})," ",a]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Error:"})," ",e.name]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Message:"})," ",e.message]}),e.stack&&(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Stack Trace:"}),(0,r.jsx)("pre",{className:"mt-1 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-32",children:e.stack})]}),(null==s?void 0:s.componentStack)&&(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Component Stack:"}),(0,r.jsx)("pre",{className:"mt-1 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-32",children:s.componentStack})]})]})]}):null},this.state={hasError:!1,error:null,errorInfo:null,errorId:null}}}}},e=>{e.O(0,[2123,8611,1024,8779,8650,8523,8405,9420,4939,1486,2662,3240,8669,3189,622,4696,1277,7979,1899,7098,4450,9744,4495,9592,433,2652,5030,465,2903,2663,9173,408,558,1356,6475,2130,4207,4191,6489,5230,7339,957,5677,6691,1151,7114,5803,3976,3492,2608,5644,2789,9824,4075,9473,7530,6759,0,6325,6077,4409,9184,7650,1595,4532,1911,1664,8496,7358],()=>e(e.s=25296)),_N_E=e.O()}]);
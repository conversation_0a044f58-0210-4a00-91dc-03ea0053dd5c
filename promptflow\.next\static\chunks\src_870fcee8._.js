(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/ui/badge.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Badge": ()=>Badge,
    "badgeVariants": ()=>badgeVariants
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-slot/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/class-variance-authority/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
;
;
;
;
const badgeVariants = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cva"])("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden", {
    variants: {
        variant: {
            default: "border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",
            secondary: "border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",
            destructive: "border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
            outline: "text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"
        }
    },
    defaultVariants: {
        variant: "default"
    }
});
function Badge(param) {
    let { className, variant, asChild = false, ...props } = param;
    const Comp = asChild ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Slot"] : "span";
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Comp, {
        "data-slot": "badge",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])(badgeVariants({
            variant
        }), className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/badge.tsx",
        lineNumber: 38,
        columnNumber: 5
    }, this);
}
_c = Badge;
;
var _c;
__turbopack_context__.k.register(_c, "Badge");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/supabase-browser.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "supabaseBrowser": ()=>supabaseBrowser
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createBrowserClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createBrowserClient.js [app-client] (ecmascript)");
;
const supabaseUrl = ("TURBOPACK compile-time value", "https://iqehopwgrczylqliajww.supabase.co");
const supabaseAnonKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlxZWhvcHdncmN6eWxxbGlhand3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI2OTQyNzksImV4cCI6MjA2ODI3MDI3OX0.9zrZ8Zot6SvsjxTGMyh3IYFqrr_QXQkKSNU4rJNz0VM");
const supabaseBrowser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createBrowserClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createBrowserClient"])(supabaseUrl, supabaseAnonKey, {
    auth: {
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: true,
        flowType: 'pkce',
        debug: ("TURBOPACK compile-time value", "development") === 'development',
        storageKey: 'sb-iqehopwgrczylqliajww-auth-token',
        storage: ("TURBOPACK compile-time truthy", 1) ? window.localStorage : "TURBOPACK unreachable"
    },
    db: {
        schema: 'public'
    },
    realtime: {
        params: {
            eventsPerSecond: 10
        }
    },
    global: {
        headers: {
            'X-Client-Info': 'promptflow-web',
            'X-Client-Version': '1.0.0'
        }
    }
});
// Session debug için
if ("TURBOPACK compile-time truthy", 1) {
    supabaseBrowser.auth.onAuthStateChange((event, session)=>{
        var _session_user, _session_user1;
        console.log("🔐 [SUPABASE_BROWSER] Auth state change: ".concat(event), {
            hasSession: !!session,
            userId: session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id,
            email: session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.email,
            expiresAt: (session === null || session === void 0 ? void 0 : session.expires_at) ? new Date(session.expires_at * 1000).toISOString() : null
        });
        // Manually set cookies for server-side access
        if (session) {
            const maxAge = Math.round((session.expires_at * 1000 - Date.now()) / 1000);
            document.cookie = "sb-iqehopwgrczylqliajww-auth-token=".concat(JSON.stringify(session), "; path=/; max-age=").concat(maxAge, "; SameSite=Lax; secure=").concat(location.protocol === 'https:');
            console.log("🍪 [SUPABASE_BROWSER] Set auth cookie with maxAge: ".concat(maxAge, "s"));
        } else {
            document.cookie = 'sb-iqehopwgrczylqliajww-auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
            console.log("🍪 [SUPABASE_BROWSER] Cleared auth cookie");
        }
    });
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/supabase.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Import and re-export the browser client as the main client to avoid multiple instances
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase-browser.ts [app-client] (ecmascript)");
;
;
// Session debug için
if ("TURBOPACK compile-time truthy", 1) {
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabaseBrowser"].auth.onAuthStateChange((event, session)=>{
        var _session_user, _session_user1;
        console.log("🔐 [SUPABASE_CLIENT] Auth state change: ".concat(event), {
            hasSession: !!session,
            userId: session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id,
            email: session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.email,
            expiresAt: (session === null || session === void 0 ? void 0 : session.expires_at) ? new Date(session.expires_at * 1000).toISOString() : null
        });
    });
}
// Global auth error handler
if ("TURBOPACK compile-time truthy", 1) {
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabaseBrowser"].auth.onAuthStateChange((event)=>{
        if (event === 'SIGNED_OUT') {
            // Oturum sonlandığında localStorage'ı temizle
            const supabaseUrl = ("TURBOPACK compile-time value", "https://iqehopwgrczylqliajww.supabase.co");
            if ("TURBOPACK compile-time truthy", 1) {
                localStorage.removeItem('sb-' + supabaseUrl.split('//')[1].split('.')[0] + '-auth-token');
            }
        }
    });
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/supabase.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase-browser.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-client] (ecmascript) <locals>");
}),
"[project]/src/lib/supabase-browser.ts [app-client] (ecmascript) <export supabaseBrowser as supabase>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "supabase": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabaseBrowser"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase-browser.ts [app-client] (ecmascript)");
}),
"[project]/src/lib/database-optimization.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Database Query Optimization
 * Supabase query optimization, indexing and connection pooling
 */ __turbopack_context__.s({
    "ConnectionOptimizer": ()=>ConnectionOptimizer,
    "DatabasePerformance": ()=>DatabasePerformance,
    "OptimizedQueries": ()=>OptimizedQueries,
    "QUERY_CONFIGS": ()=>QUERY_CONFIGS,
    "QueryPerformanceMonitor": ()=>QueryPerformanceMonitor
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__supabaseBrowser__as__supabase$3e$__ = __turbopack_context__.i("[project]/src/lib/supabase-browser.ts [app-client] (ecmascript) <export supabaseBrowser as supabase>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$strategies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/cache-strategies.ts [app-client] (ecmascript)");
;
;
;
const QUERY_CONFIGS = {
    // Pagination settings
    pagination: {
        defaultLimit: 20,
        maxLimit: 100,
        prefetchPages: 2
    },
    // Field selection for different use cases
    fields: {
        // Minimal fields for lists
        projectList: 'id, name, description, created_at, updated_at',
        promptList: 'id, title, content, hashtags, created_at, updated_at, project_id',
        userBasic: 'id, email, created_at',
        // Detailed fields for single items
        projectDetail: "\n      id, name, description, created_at, updated_at, user_id,\n      prompts(count),\n      user:users(email)\n    ",
        promptDetail: "\n      id, title, content, hashtags, created_at, updated_at, project_id,\n      project:projects(name),\n      user:projects(user:users(email))\n    ",
        // Aggregated data
        userStats: "\n      id,\n      projects(count),\n      prompts:projects(prompts(count))\n    "
    },
    // Index hints for complex queries
    indexHints: {
        projectsByUser: 'user_id, updated_at',
        promptsByProject: 'project_id, created_at',
        searchPrompts: 'title, content, hashtags'
    }
};
// Query performance monitoring
class QueryPerformanceMonitor {
    static startQuery(queryKey) {
        const startTime = Date.now();
        return ()=>{
            const endTime = Date.now();
            const duration = endTime - startTime;
            this.recordQuery(queryKey, duration);
            // Log slow queries
            if (duration > 1000) {
                console.warn("🐌 [SLOW_QUERY] ".concat(queryKey, " took ").concat(duration, "ms"));
            }
        };
    }
    static recordQuery(queryKey, duration) {
        const existing = this.metrics.get(queryKey) || {
            count: 0,
            totalTime: 0,
            avgTime: 0,
            slowQueries: 0
        };
        existing.count++;
        existing.totalTime += duration;
        existing.avgTime = existing.totalTime / existing.count;
        if (duration > 1000) {
            existing.slowQueries++;
        }
        this.metrics.set(queryKey, existing);
        // Record in cache performance system
        if (duration < 100) {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$strategies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CachePerformance"].recordCacheHit(queryKey, duration);
        } else {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$strategies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CachePerformance"].recordCacheMiss(queryKey, duration);
        }
    }
    static getMetrics() {
        return Array.from(this.metrics.entries()).map((param)=>{
            let [key, metric] = param;
            return {
                query: key,
                ...metric,
                slowQueryRate: metric.slowQueries / metric.count
            };
        }).sort((a, b)=>b.avgTime - a.avgTime);
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(QueryPerformanceMonitor, "metrics", new Map());
class OptimizedQueries {
    // Projects with optimized field selection
    static async getProjects() {
        let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
        const endTimer = QueryPerformanceMonitor.startQuery('getProjects');
        try {
            const { userId, limit = QUERY_CONFIGS.pagination.defaultLimit, offset = 0, search, orderBy = 'updated_at', ascending = false } = options;
            let query = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__supabaseBrowser__as__supabase$3e$__["supabase"].from('projects').select(QUERY_CONFIGS.fields.projectList);
            // Apply filters
            if (userId) {
                query = query.eq('user_id', userId);
            }
            if (search) {
                query = query.or("name.ilike.%".concat(search, "%,description.ilike.%").concat(search, "%"));
            }
            // Apply ordering and pagination
            query = query.order(orderBy, {
                ascending
            }).range(offset, offset + limit - 1);
            const { data, error, count } = await query;
            if (error) throw error;
            return {
                data,
                count,
                hasMore: (count || 0) > offset + limit
            };
        } finally{
            endTimer();
        }
    }
    // Prompts with optimized joins
    static async getPrompts() {
        let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
        const endTimer = QueryPerformanceMonitor.startQuery('getPrompts');
        try {
            const { projectId, userId, limit = QUERY_CONFIGS.pagination.defaultLimit, offset = 0, search, hashtags } = options;
            let query = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__supabaseBrowser__as__supabase$3e$__["supabase"].from('prompts').select(QUERY_CONFIGS.fields.promptList);
            // Apply filters
            if (projectId) {
                query = query.eq('project_id', projectId);
            }
            if (userId) {
                query = query.eq('user_id', userId);
            }
            if (search) {
                query = query.or("title.ilike.%".concat(search, "%,content.ilike.%").concat(search, "%"));
            }
            if (hashtags && hashtags.length > 0) {
                query = query.overlaps('hashtags', hashtags);
            }
            // Apply ordering and pagination
            query = query.order('updated_at', {
                ascending: false
            }).range(offset, offset + limit - 1);
            const { data, error, count } = await query;
            if (error) throw error;
            return {
                data,
                count,
                hasMore: (count || 0) > offset + limit
            };
        } finally{
            endTimer();
        }
    }
    // User statistics with aggregation
    static async getUserStats(userId) {
        const endTimer = QueryPerformanceMonitor.startQuery('getUserStats');
        try {
            var _data_projects_, _data_projects, _data_prompts;
            // Check cache first
            const cacheKey = "user-stats-".concat(userId);
            const cached = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$strategies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BrowserCache"].getMemory(cacheKey);
            if (cached) {
                endTimer();
                return cached;
            }
            const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__supabaseBrowser__as__supabase$3e$__["supabase"].from('users').select(QUERY_CONFIGS.fields.userStats).eq('id', userId).single();
            if (error) throw error;
            // Process aggregated data
            const stats = {
                projectCount: ((_data_projects = data.projects) === null || _data_projects === void 0 ? void 0 : (_data_projects_ = _data_projects[0]) === null || _data_projects_ === void 0 ? void 0 : _data_projects_.count) || 0,
                promptCount: ((_data_prompts = data.prompts) === null || _data_prompts === void 0 ? void 0 : _data_prompts.reduce((total, project)=>{
                    var _project_prompts_, _project_prompts;
                    return total + (((_project_prompts = project.prompts) === null || _project_prompts === void 0 ? void 0 : (_project_prompts_ = _project_prompts[0]) === null || _project_prompts_ === void 0 ? void 0 : _project_prompts_.count) || 0);
                }, 0)) || 0
            };
            // Cache for 5 minutes
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$strategies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BrowserCache"].setMemory(cacheKey, stats, 5 * 60 * 1000);
            return stats;
        } finally{
            endTimer();
        }
    }
    // Batch operations for better performance
    static async batchCreatePrompts(prompts) {
        const endTimer = QueryPerformanceMonitor.startQuery('batchCreatePrompts');
        try {
            const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__supabaseBrowser__as__supabase$3e$__["supabase"].from('prompts').insert(prompts).select(QUERY_CONFIGS.fields.promptList);
            if (error) throw error;
            return data;
        } finally{
            endTimer();
        }
    }
    // Optimized search with full-text search
    static async searchContent(options) {
        const endTimer = QueryPerformanceMonitor.startQuery('searchContent');
        try {
            const { query: searchQuery, type = 'all', userId, limit = 20 } = options;
            const results = {
                projects: [],
                prompts: []
            };
            if (type === 'projects' || type === 'all') {
                let projectQuery = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__supabaseBrowser__as__supabase$3e$__["supabase"].from('projects').select(QUERY_CONFIGS.fields.projectList).or("name.ilike.%".concat(searchQuery, "%,description.ilike.%").concat(searchQuery, "%")).limit(limit);
                if (userId) {
                    projectQuery = projectQuery.eq('user_id', userId);
                }
                const { data: projects } = await projectQuery;
                results.projects = projects || [];
            }
            if (type === 'prompts' || type === 'all') {
                let promptQuery = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__supabaseBrowser__as__supabase$3e$__["supabase"].from('prompts').select(QUERY_CONFIGS.fields.promptList).or("title.ilike.%".concat(searchQuery, "%,content.ilike.%").concat(searchQuery, "%")).limit(limit);
                if (userId) {
                    promptQuery = promptQuery.eq('user_id', userId);
                }
                const { data: prompts } = await promptQuery;
                results.prompts = prompts || [];
            }
            return results;
        } finally{
            endTimer();
        }
    }
}
class ConnectionOptimizer {
    static async getOptimizedConnection() {
        let key = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'default';
        const existing = this.connectionPool.get(key);
        if (existing && Date.now() - existing.created < this.connectionTimeout) {
            return existing.client;
        }
        // Create new optimized connection
        const client = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__supabaseBrowser__as__supabase$3e$__["supabase"];
        this.connectionPool.set(key, {
            client,
            created: Date.now()
        });
        // Cleanup old connections
        if (this.connectionPool.size > this.maxConnections) {
            const oldest = Array.from(this.connectionPool.entries()).sort((param, param1)=>{
                let [, a] = param, [, b] = param1;
                return a.created - b.created;
            })[0];
            this.connectionPool.delete(oldest[0]);
        }
        return client;
    }
    static clearConnections() {
        this.connectionPool.clear();
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(ConnectionOptimizer, "connectionPool", new Map());
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(ConnectionOptimizer, "maxConnections", 10);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(ConnectionOptimizer, "connectionTimeout", 30000); // 30 seconds
class DatabasePerformance {
    static getPerformanceReport() {
        return {
            queryMetrics: QueryPerformanceMonitor.getMetrics(),
            connectionPool: {
                active: ConnectionOptimizer['connectionPool'].size,
                max: ConnectionOptimizer['maxConnections']
            },
            cacheMetrics: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$strategies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CachePerformance"].getCacheMetrics()
        };
    }
    static logSlowQueries() {
        const metrics = QueryPerformanceMonitor.getMetrics();
        const slowQueries = metrics.filter((m)=>m.avgTime > 500);
        if (slowQueries.length > 0) {
            console.warn('🐌 [SLOW_QUERIES] Found slow queries:', slowQueries);
        }
    }
}
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/dynamic-imports.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Dynamic Imports for Performance Optimization
 * Lazy loading heavy components and libraries
 */ __turbopack_context__.s({
    "LazyAuthPage": ()=>LazyAuthPage,
    "LazyContextCreationModal": ()=>LazyContextCreationModal,
    "LazyContextEditModal": ()=>LazyContextEditModal,
    "LazyContextGallery": ()=>LazyContextGallery,
    "LazyContextSidebar": ()=>LazyContextSidebar,
    "LazyDashboardPage": ()=>LazyDashboardPage,
    "LazyProfilePage": ()=>LazyProfilePage,
    "LazyProjectNameEditor": ()=>LazyProjectNameEditor,
    "LazyPromptWorkspace": ()=>LazyPromptWorkspace,
    "cachedDynamicImport": ()=>cachedDynamicImport,
    "getBundleInfo": ()=>getBundleInfo,
    "loadAnimationLibrary": ()=>loadAnimationLibrary,
    "loadChartLibrary": ()=>loadChartLibrary,
    "loadDateUtils": ()=>loadDateUtils,
    "loadFileProcessing": ()=>loadFileProcessing,
    "loadFormValidation": ()=>loadFormValidation,
    "loadImageProcessing": ()=>loadImageProcessing,
    "loadRichTextEditor": ()=>loadRichTextEditor,
    "loadSyntaxHighlighter": ()=>loadSyntaxHighlighter,
    "preloadCriticalImports": ()=>preloadCriticalImports,
    "preloadOnHover": ()=>preloadOnHover,
    "preloadOnVisible": ()=>preloadOnVisible,
    "preloadPageImports": ()=>preloadPageImports,
    "trackDynamicImport": ()=>trackDynamicImport
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
// Cache for dynamic imports to avoid re-loading
const importCache = new Map();
const cachedDynamicImport = async (cacheKey, importFn)=>{
    // Create performance start mark
    if ("object" !== 'undefined' && window.performance) {
        try {
            performance.mark("dynamic-import-".concat(cacheKey, "-start"));
        } catch (error) {
        // Ignore performance marking errors
        }
    }
    if (importCache.has(cacheKey)) {
        return importCache.get(cacheKey);
    }
    const startTime = Date.now();
    const importPromise = importFn().then((result)=>{
        // Track import completion
        trackDynamicImport(cacheKey, startTime);
        return result;
    });
    importCache.set(cacheKey, importPromise);
    return importPromise;
};
const LazyContextGallery = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["lazy"])(_c = ()=>cachedDynamicImport('context-gallery', ()=>__turbopack_context__.r("[project]/src/components/context-gallery.tsx [app-client] (ecmascript, async loader)")(__turbopack_context__.i).then((module)=>({
                default: module.default || module
            }))));
_c1 = LazyContextGallery;
const LazyPromptWorkspace = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["lazy"])(_c2 = ()=>cachedDynamicImport('prompt-workspace', ()=>__turbopack_context__.r("[project]/src/components/prompt-workspace.tsx [app-client] (ecmascript, async loader)")(__turbopack_context__.i).then((module)=>({
                default: module.default || module
            }))));
_c3 = LazyPromptWorkspace;
const LazyContextSidebar = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["lazy"])(_c4 = ()=>cachedDynamicImport('context-sidebar', ()=>__turbopack_context__.r("[project]/src/components/context-sidebar.tsx [app-client] (ecmascript, async loader)")(__turbopack_context__.i).then((module)=>({
                default: module.default || module
            }))));
_c5 = LazyContextSidebar;
const LazyProjectNameEditor = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["lazy"])(_c6 = ()=>cachedDynamicImport('project-name-editor', ()=>__turbopack_context__.r("[project]/src/components/project-name-editor.tsx [app-client] (ecmascript, async loader)")(__turbopack_context__.i).then((module)=>({
                default: module.ProjectNameEditor
            }))));
_c7 = LazyProjectNameEditor;
const LazyContextCreationModal = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["lazy"])(_c8 = ()=>cachedDynamicImport('context-creation-modal', ()=>__turbopack_context__.r("[project]/src/components/context-creation-modal.tsx [app-client] (ecmascript, async loader)")(__turbopack_context__.i).then((module)=>({
                default: module.default || module
            }))));
_c9 = LazyContextCreationModal;
const LazyContextEditModal = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["lazy"])(_c10 = ()=>cachedDynamicImport('context-edit-modal', ()=>__turbopack_context__.r("[project]/src/components/context-edit-modal.tsx [app-client] (ecmascript, async loader)")(__turbopack_context__.i).then((module)=>({
                default: module.default || module
            }))));
_c11 = LazyContextEditModal;
const loadChartLibrary = ()=>cachedDynamicImport('recharts', ()=>__turbopack_context__.r("[project]/node_modules/recharts/es6/index.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i));
const loadDateUtils = ()=>cachedDynamicImport('date-fns', async ()=>{
        const { format, parseISO, formatDistanceToNow } = await __turbopack_context__.r("[project]/node_modules/date-fns/index.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
        const { tr } = await __turbopack_context__.r("[project]/node_modules/date-fns/locale.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
        return {
            format,
            parseISO,
            formatDistanceToNow,
            tr
        };
    });
const loadFormValidation = ()=>cachedDynamicImport('zod', async ()=>{
        const { z } = await __turbopack_context__.r("[project]/node_modules/zod/index.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
        return {
            z
        };
    });
const loadFileProcessing = ()=>cachedDynamicImport('file-processing', async ()=>{
        const Papa = await __turbopack_context__.r("[project]/node_modules/papaparse/papaparse.min.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
        return {
            Papa: Papa.default
        };
    });
const loadRichTextEditor = ()=>cachedDynamicImport('rich-text-editor', async ()=>{
        // Placeholder for future rich text editor
        return {
            RichTextEditor: null
        };
    });
const loadSyntaxHighlighter = ()=>cachedDynamicImport('syntax-highlighter', async ()=>{
        const { Prism } = await __turbopack_context__.r("[project]/node_modules/react-syntax-highlighter/dist/esm/index.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
        const { tomorrow } = await __turbopack_context__.r("[project]/node_modules/react-syntax-highlighter/dist/esm/styles/prism/index.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
        return {
            Prism,
            tomorrow
        };
    });
const loadImageProcessing = ()=>cachedDynamicImport('image-processing', async ()=>{
        // Placeholder for future image processing
        return {
            ImageProcessor: null
        };
    });
const loadAnimationLibrary = ()=>cachedDynamicImport('framer-motion', ()=>__turbopack_context__.r("[project]/node_modules/framer-motion/dist/es/index.mjs [app-client] (ecmascript, async loader)")(__turbopack_context__.i));
const preloadCriticalImports = ()=>{
    // Preload commonly used utilities
    loadDateUtils();
    loadFormValidation();
};
const preloadPageImports = (page)=>{
    switch(page){
        case 'dashboard':
            LazyPromptWorkspace;
            LazyContextSidebar;
            break;
        case 'profile':
            loadFormValidation();
            break;
        case 'templates':
            LazyContextGallery;
            break;
    }
};
const LazyDashboardPage = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["lazy"])(_c12 = ()=>cachedDynamicImport('dashboard-page', ()=>__turbopack_context__.r("[project]/src/app/dashboard/page.tsx [app-client] (ecmascript, async loader)")(__turbopack_context__.i).then((module)=>({
                default: module.default || module
            }))));
_c13 = LazyDashboardPage;
const LazyProfilePage = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["lazy"])(_c14 = ()=>cachedDynamicImport('profile-page', ()=>__turbopack_context__.r("[project]/src/app/profile/page.tsx [app-client] (ecmascript, async loader)")(__turbopack_context__.i).then((module)=>({
                default: module.default || module
            }))));
_c15 = LazyProfilePage;
const LazyAuthPage = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["lazy"])(_c16 = ()=>cachedDynamicImport('auth-page', ()=>__turbopack_context__.r("[project]/src/app/auth/page.tsx [app-client] (ecmascript, async loader)")(__turbopack_context__.i).then((module)=>({
                default: module.default || module
            }))));
_c17 = LazyAuthPage;
const trackDynamicImport = (componentName, startTime)=>{
    const loadTime = Date.now() - startTime;
    if ("object" !== 'undefined' && window.performance) {
        try {
            // Track loading performance with defensive programming
            const startMarkName = "dynamic-import-".concat(componentName, "-start");
            const endMarkName = "dynamic-import-".concat(componentName, "-end");
            const measureName = "dynamic-import-".concat(componentName);
            // Create end mark
            performance.mark(endMarkName);
            // Check if start mark exists before measuring - use safer approach
            try {
                const marks = performance.getEntriesByName(startMarkName, 'mark');
                if (marks.length > 0) {
                    performance.measure(measureName, startMarkName, endMarkName);
                } else {
                    // Create a simple measure without deprecated navigationStart
                    const startTime = performance.timeOrigin || Date.now();
                    performance.measure(measureName, {
                        start: startTime,
                        end: performance.now()
                    });
                }
            } catch (error) {
                // Ignore measurement errors to prevent deprecated API warnings
                console.debug('Performance measurement skipped:', error);
            }
        } catch (error) {
            // Graceful degradation - performance tracking fails silently
            console.warn("Performance tracking failed for ".concat(componentName, ":"), error);
        }
    }
    console.log("🚀 [DYNAMIC_IMPORT] ".concat(componentName, " loaded in ").concat(loadTime, "ms"));
};
const preloadOnHover = (importFn)=>{
    let preloaded = false;
    return {
        onMouseEnter: ()=>{
            if (!preloaded) {
                preloaded = true;
                importFn();
            }
        }
    };
};
const preloadOnVisible = (importFn)=>{
    if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
    ;
    const observer = new IntersectionObserver((entries)=>{
        entries.forEach((entry)=>{
            if (entry.isIntersecting) {
                importFn();
                observer.disconnect();
            }
        });
    }, {
        threshold: 0.1
    });
    return observer;
};
const getBundleInfo = ()=>{
    if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
    ;
    return {
        cacheSize: importCache.size,
        loadedComponents: Array.from(importCache.keys()),
        memoryUsage: performance.memory ? {
            used: performance.memory.usedJSHeapSize,
            total: performance.memory.totalJSHeapSize,
            limit: performance.memory.jsHeapSizeLimit
        } : null
    };
};
var _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17;
__turbopack_context__.k.register(_c, "LazyContextGallery$lazy");
__turbopack_context__.k.register(_c1, "LazyContextGallery");
__turbopack_context__.k.register(_c2, "LazyPromptWorkspace$lazy");
__turbopack_context__.k.register(_c3, "LazyPromptWorkspace");
__turbopack_context__.k.register(_c4, "LazyContextSidebar$lazy");
__turbopack_context__.k.register(_c5, "LazyContextSidebar");
__turbopack_context__.k.register(_c6, "LazyProjectNameEditor$lazy");
__turbopack_context__.k.register(_c7, "LazyProjectNameEditor");
__turbopack_context__.k.register(_c8, "LazyContextCreationModal$lazy");
__turbopack_context__.k.register(_c9, "LazyContextCreationModal");
__turbopack_context__.k.register(_c10, "LazyContextEditModal$lazy");
__turbopack_context__.k.register(_c11, "LazyContextEditModal");
__turbopack_context__.k.register(_c12, "LazyDashboardPage$lazy");
__turbopack_context__.k.register(_c13, "LazyDashboardPage");
__turbopack_context__.k.register(_c14, "LazyProfilePage$lazy");
__turbopack_context__.k.register(_c15, "LazyProfilePage");
__turbopack_context__.k.register(_c16, "LazyAuthPage$lazy");
__turbopack_context__.k.register(_c17, "LazyAuthPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/performance-monitor.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Performance Monitor Component
 * Real-time performance monitoring and metrics display
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/badge.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$activity$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Activity$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/activity.js [app-client] (ecmascript) <export default as Activity>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$database$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Database$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/database.js [app-client] (ecmascript) <export default as Database>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-client] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$zap$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Zap$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/zap.js [app-client] (ecmascript) <export default as Zap>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trending-up.js [app-client] (ecmascript) <export default as TrendingUp>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/triangle-alert.js [app-client] (ecmascript) <export default as AlertTriangle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-check-big.js [app-client] (ecmascript) <export default as CheckCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__XCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-x.js [app-client] (ecmascript) <export default as XCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2d$optimization$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database-optimization.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$strategies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/cache-strategies.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dynamic$2d$imports$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/dynamic-imports.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
const PerformanceMonitor = /*#__PURE__*/ _s((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(_c = _s(function PerformanceMonitor() {
    _s();
    const [metrics, setMetrics] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isVisible, setIsVisible] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [autoRefresh, setAutoRefresh] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    // Collect performance metrics
    const collectMetrics = ()=>{
        const dbPerformance = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2d$optimization$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DatabasePerformance"].getPerformanceReport();
        const cacheInfo = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$strategies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BrowserCache"].getCacheInfo();
        const bundleInfo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dynamic$2d$imports$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getBundleInfo"])();
        // Web Vitals
        const vitals = {};
        if ("object" !== 'undefined' && 'performance' in window) {
            try {
                // Get LCP (Largest Contentful Paint) - use PerformanceObserver instead
                if ('PerformanceObserver' in window) {
                    // Use modern PerformanceObserver API instead of deprecated getEntriesByType
                    const observer = new PerformanceObserver((list)=>{
                        const entries = list.getEntries();
                        if (entries.length > 0) {
                            vitals.lcp = entries[entries.length - 1].startTime;
                        }
                    });
                    try {
                        observer.observe({
                            entryTypes: [
                                'largest-contentful-paint'
                            ]
                        });
                        // Disconnect after first measurement
                        setTimeout(()=>observer.disconnect(), 1000);
                    } catch (e) {
                        // Fallback to legacy API if needed
                        const lcpEntries = performance.getEntriesByType('largest-contentful-paint');
                        if (lcpEntries.length > 0) {
                            vitals.lcp = lcpEntries[lcpEntries.length - 1].startTime;
                        }
                    }
                }
                // Get navigation timing safely
                if (performance.timing) {
                    vitals.fid = performance.timing.loadEventEnd - performance.timing.loadEventStart;
                }
                // CLS would need layout shift observer
                vitals.cls = 0; // Placeholder
            } catch (error) {
                // Silently handle performance API errors
                console.debug('Performance monitoring error:', error);
            }
        }
        setMetrics({
            queries: dbPerformance.queryMetrics,
            cache: cacheInfo,
            bundle: bundleInfo,
            vitals
        });
    };
    // Auto-refresh metrics
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PerformanceMonitor.PerformanceMonitor.useEffect": ()=>{
            if (!autoRefresh) return;
            const interval = setInterval(collectMetrics, 5000) // Every 5 seconds
            ;
            collectMetrics(); // Initial collection
            return ({
                "PerformanceMonitor.PerformanceMonitor.useEffect": ()=>clearInterval(interval)
            })["PerformanceMonitor.PerformanceMonitor.useEffect"];
        }
    }["PerformanceMonitor.PerformanceMonitor.useEffect"], [
        autoRefresh
    ]);
    // Performance status indicators
    const getPerformanceStatus = (avgTime)=>{
        if (avgTime < 100) return {
            color: 'green',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"],
            label: 'Excellent'
        };
        if (avgTime < 500) return {
            color: 'yellow',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__["AlertTriangle"],
            label: 'Good'
        };
        return {
            color: 'red',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__XCircle$3e$__["XCircle"],
            label: 'Needs Improvement'
        };
    };
    const getVitalsStatus = (metric, value)=>{
        if (!value) return {
            color: 'gray',
            label: 'N/A'
        };
        switch(metric){
            case 'lcp':
                if (value < 2500) return {
                    color: 'green',
                    label: 'Good'
                };
                if (value < 4000) return {
                    color: 'yellow',
                    label: 'Needs Improvement'
                };
                return {
                    color: 'red',
                    label: 'Poor'
                };
            case 'fid':
                if (value < 100) return {
                    color: 'green',
                    label: 'Good'
                };
                if (value < 300) return {
                    color: 'yellow',
                    label: 'Needs Improvement'
                };
                return {
                    color: 'red',
                    label: 'Poor'
                };
            case 'cls':
                if (value < 0.1) return {
                    color: 'green',
                    label: 'Good'
                };
                if (value < 0.25) return {
                    color: 'yellow',
                    label: 'Needs Improvement'
                };
                return {
                    color: 'red',
                    label: 'Poor'
                };
            default:
                return {
                    color: 'gray',
                    label: 'Unknown'
                };
        }
    };
    if (!isVisible) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
            variant: "outline",
            size: "sm",
            onClick: ()=>setIsVisible(true),
            className: "fixed bottom-4 right-4 z-50 bg-white shadow-lg",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$activity$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Activity$3e$__["Activity"], {
                    className: "h-4 w-4 mr-2"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/performance-monitor.tsx",
                    lineNumber: 155,
                    columnNumber: 9
                }, this),
                "Performance"
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/ui/performance-monitor.tsx",
            lineNumber: 149,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed bottom-4 right-4 z-50 w-96 max-h-[80vh] overflow-y-auto",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
            className: "bg-white shadow-xl border-2",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                    className: "pb-3",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                                className: "text-lg flex items-center gap-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$activity$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Activity$3e$__["Activity"], {
                                        className: "h-5 w-5"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                        lineNumber: 167,
                                        columnNumber: 15
                                    }, this),
                                    "Performance Monitor"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                lineNumber: 166,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex gap-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        size: "sm",
                                        onClick: ()=>setAutoRefresh(!autoRefresh),
                                        children: autoRefresh ? 'Pause' : 'Resume'
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                        lineNumber: 171,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        size: "sm",
                                        onClick: collectMetrics,
                                        children: "Refresh"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                        lineNumber: 178,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        size: "sm",
                                        onClick: ()=>setIsVisible(false),
                                        children: "×"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                        lineNumber: 185,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                lineNumber: 170,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/performance-monitor.tsx",
                        lineNumber: 165,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/performance-monitor.tsx",
                    lineNumber: 164,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                    className: "space-y-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                    className: "font-semibold flex items-center gap-2 mb-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$zap$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Zap$3e$__["Zap"], {
                                            className: "h-4 w-4"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                            lineNumber: 200,
                                            columnNumber: 15
                                        }, this),
                                        "Core Web Vitals"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                    lineNumber: 199,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "grid grid-cols-3 gap-2",
                                    children: Object.entries((metrics === null || metrics === void 0 ? void 0 : metrics.vitals) || {}).map((param)=>{
                                        let [key, value] = param;
                                        const status = getVitalsStatus(key, value);
                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-center",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-xs text-gray-500 uppercase",
                                                    children: key
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                                    lineNumber: 208,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "font-mono text-sm",
                                                    children: value ? "".concat(Math.round(value), "ms") : 'N/A'
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                                    lineNumber: 209,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                                                    variant: status.color,
                                                    className: "text-xs",
                                                    children: status.label
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                                    lineNumber: 212,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, key, true, {
                                            fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                            lineNumber: 207,
                                            columnNumber: 19
                                        }, this);
                                    })
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                    lineNumber: 203,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ui/performance-monitor.tsx",
                            lineNumber: 198,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                    className: "font-semibold flex items-center gap-2 mb-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$database$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Database$3e$__["Database"], {
                                            className: "h-4 w-4"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                            lineNumber: 224,
                                            columnNumber: 15
                                        }, this),
                                        "Query Performance"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                    lineNumber: 223,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-2 max-h-32 overflow-y-auto",
                                    children: metrics === null || metrics === void 0 ? void 0 : metrics.queries.slice(0, 5).map((query, index)=>{
                                        const status = getPerformanceStatus(query.avgTime);
                                        const StatusIcon = status.icon;
                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center justify-between text-sm",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center gap-2 flex-1 min-w-0",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(StatusIcon, {
                                                            className: "h-3 w-3 text-".concat(status.color, "-500")
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                                            lineNumber: 234,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "truncate font-mono text-xs",
                                                            children: query.query
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                                            lineNumber: 235,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                                    lineNumber: 233,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex gap-2 text-xs",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "text-gray-500",
                                                            children: [
                                                                query.count,
                                                                "x"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                                            lineNumber: 240,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "font-mono",
                                                            children: [
                                                                Math.round(query.avgTime),
                                                                "ms"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                                            lineNumber: 241,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                                    lineNumber: 239,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, index, true, {
                                            fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                            lineNumber: 232,
                                            columnNumber: 19
                                        }, this);
                                    })
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                    lineNumber: 227,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ui/performance-monitor.tsx",
                            lineNumber: 222,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                    className: "font-semibold flex items-center gap-2 mb-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"], {
                                            className: "h-4 w-4"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                            lineNumber: 252,
                                            columnNumber: 15
                                        }, this),
                                        "Cache Status"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                    lineNumber: 251,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "grid grid-cols-2 gap-2 text-sm",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-xs text-gray-500",
                                                    children: "LocalStorage"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                                    lineNumber: 257,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "font-mono",
                                                    children: [
                                                        (metrics === null || metrics === void 0 ? void 0 : metrics.cache.localStorage.items) || 0,
                                                        " items"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                                    lineNumber: 258,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-xs text-gray-400",
                                                    children: [
                                                        Math.round(((metrics === null || metrics === void 0 ? void 0 : metrics.cache.localStorage.size) || 0) / 1024),
                                                        "KB"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                                    lineNumber: 261,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                            lineNumber: 256,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-xs text-gray-500",
                                                    children: "Memory Cache"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                                    lineNumber: 266,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "font-mono",
                                                    children: [
                                                        (metrics === null || metrics === void 0 ? void 0 : metrics.cache.memory.items) || 0,
                                                        " items"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                                    lineNumber: 267,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                            lineNumber: 265,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                    lineNumber: 255,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ui/performance-monitor.tsx",
                            lineNumber: 250,
                            columnNumber: 11
                        }, this),
                        (metrics === null || metrics === void 0 ? void 0 : metrics.bundle) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                    className: "font-semibold flex items-center gap-2 mb-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                                            className: "h-4 w-4"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                            lineNumber: 278,
                                            columnNumber: 17
                                        }, this),
                                        "Bundle Status"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                    lineNumber: 277,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-sm space-y-1",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex justify-between",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-gray-500",
                                                    children: "Loaded Components:"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                                    lineNumber: 283,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "font-mono",
                                                    children: metrics.bundle.loadedComponents.length
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                                    lineNumber: 284,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                            lineNumber: 282,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex justify-between",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-gray-500",
                                                    children: "Cache Size:"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                                    lineNumber: 287,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "font-mono",
                                                    children: metrics.bundle.cacheSize
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                                    lineNumber: 288,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                            lineNumber: 286,
                                            columnNumber: 17
                                        }, this),
                                        metrics.bundle.memoryUsage && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex justify-between",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-gray-500",
                                                    children: "Memory Used:"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                                    lineNumber: 292,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "font-mono",
                                                    children: [
                                                        Math.round(metrics.bundle.memoryUsage.used / 1024 / 1024),
                                                        "MB"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                                    lineNumber: 293,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                            lineNumber: 291,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                    lineNumber: 281,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ui/performance-monitor.tsx",
                            lineNumber: 276,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "pt-2 border-t",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex gap-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        size: "sm",
                                        onClick: ()=>{
                                            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cache$2d$strategies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BrowserCache"].clearAll();
                                            collectMetrics();
                                        },
                                        className: "flex-1",
                                        children: "Clear Cache"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                        lineNumber: 305,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        size: "sm",
                                        onClick: ()=>{
                                            console.log('Performance Report:', metrics);
                                        },
                                        className: "flex-1",
                                        children: "Log Report"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                        lineNumber: 316,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ui/performance-monitor.tsx",
                                lineNumber: 304,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/performance-monitor.tsx",
                            lineNumber: 303,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/performance-monitor.tsx",
                    lineNumber: 196,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/ui/performance-monitor.tsx",
            lineNumber: 163,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/performance-monitor.tsx",
        lineNumber: 162,
        columnNumber: 5
    }, this);
}, "CbxHj2DfeuFP33eI0em7zMbJWrA=")), "CbxHj2DfeuFP33eI0em7zMbJWrA=");
_c1 = PerformanceMonitor;
const __TURBOPACK__default__export__ = PerformanceMonitor;
var _c, _c1;
__turbopack_context__.k.register(_c, "PerformanceMonitor$memo");
__turbopack_context__.k.register(_c1, "PerformanceMonitor");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_870fcee8._.js.map
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/cache-strategies.ts"], "sourcesContent": ["/**\n * Advanced Caching Strategies\n * Multi-layer caching for optimal performance\n */\n\nimport { QueryClient } from '@tanstack/react-query'\n\n// Cache configurations for different data types\nexport const CACHE_CONFIGS = {\n  // User data - rarely changes\n  user: {\n    staleTime: 5 * 60 * 1000, // 5 minutes\n    gcTime: 30 * 60 * 1000, // 30 minutes\n  },\n  \n  // Projects - moderate changes\n  projects: {\n    staleTime: 2 * 60 * 1000, // 2 minutes\n    gcTime: 15 * 60 * 1000, // 15 minutes\n  },\n  \n  // Prompts - frequent changes\n  prompts: {\n    staleTime: 30 * 1000, // 30 seconds\n    gcTime: 5 * 60 * 1000, // 5 minutes\n  },\n  \n  // Context templates - rarely changes\n  templates: {\n    staleTime: 10 * 60 * 1000, // 10 minutes\n    gcTime: 60 * 60 * 1000, // 1 hour\n  },\n  \n  // User plans - rarely changes\n  plans: {\n    staleTime: 15 * 60 * 1000, // 15 minutes\n    gcTime: 60 * 60 * 1000, // 1 hour\n  },\n  \n  // Real-time data - very fresh\n  realtime: {\n    staleTime: 0, // Always stale\n    gcTime: 1 * 60 * 1000, // 1 minute\n  }\n} as const\n\n// Enhanced Query Client with optimized defaults\nexport const createOptimizedQueryClient = () => {\n  return new QueryClient({\n    defaultOptions: {\n      queries: {\n        // Default cache settings\n        staleTime: 1 * 60 * 1000, // 1 minute\n        gcTime: 5 * 60 * 1000, // 5 minutes\n        \n        // Retry configuration\n        retry: (failureCount, error: any) => {\n          // Don't retry on 4xx errors\n          if (error?.status >= 400 && error?.status < 500) {\n            return false\n          }\n          // Retry up to 3 times for other errors\n          return failureCount < 3\n        },\n        \n        // Retry delay with exponential backoff\n        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),\n        \n        // Background refetch settings\n        refetchOnWindowFocus: false,\n        refetchOnReconnect: true,\n        refetchOnMount: true,\n      },\n      \n      mutations: {\n        // Retry failed mutations\n        retry: 1,\n        retryDelay: 1000,\n      }\n    }\n  })\n}\n\n// Browser cache utilities\nexport class BrowserCache {\n  private static readonly PREFIX = 'promptflow_'\n  \n  // Local Storage cache (persistent)\n  static setLocal<T>(key: string, data: T, ttl?: number): void {\n    try {\n      const item = {\n        data,\n        timestamp: Date.now(),\n        ttl: ttl || 24 * 60 * 60 * 1000 // 24 hours default\n      }\n      localStorage.setItem(this.PREFIX + key, JSON.stringify(item))\n    } catch (error) {\n      console.warn('Failed to set localStorage cache:', error)\n    }\n  }\n  \n  static getLocal<T>(key: string): T | null {\n    try {\n      const item = localStorage.getItem(this.PREFIX + key)\n      if (!item) return null\n      \n      const parsed = JSON.parse(item)\n      const now = Date.now()\n      \n      // Check if expired\n      if (now - parsed.timestamp > parsed.ttl) {\n        localStorage.removeItem(this.PREFIX + key)\n        return null\n      }\n      \n      return parsed.data\n    } catch (error) {\n      console.warn('Failed to get localStorage cache:', error)\n      return null\n    }\n  }\n  \n  // Session Storage cache (session-only)\n  static setSession<T>(key: string, data: T): void {\n    try {\n      const item = {\n        data,\n        timestamp: Date.now()\n      }\n      sessionStorage.setItem(this.PREFIX + key, JSON.stringify(item))\n    } catch (error) {\n      console.warn('Failed to set sessionStorage cache:', error)\n    }\n  }\n  \n  static getSession<T>(key: string): T | null {\n    try {\n      const item = sessionStorage.getItem(this.PREFIX + key)\n      if (!item) return null\n      \n      const parsed = JSON.parse(item)\n      return parsed.data\n    } catch (error) {\n      console.warn('Failed to get sessionStorage cache:', error)\n      return null\n    }\n  }\n  \n  // Memory cache (in-memory, fastest)\n  private static memoryCache = new Map<string, { data: any; timestamp: number; ttl: number }>()\n  \n  static setMemory<T>(key: string, data: T, ttl: number = 5 * 60 * 1000): void {\n    this.memoryCache.set(key, {\n      data,\n      timestamp: Date.now(),\n      ttl\n    })\n  }\n  \n  static getMemory<T>(key: string): T | null {\n    const item = this.memoryCache.get(key)\n    if (!item) return null\n    \n    const now = Date.now()\n    if (now - item.timestamp > item.ttl) {\n      this.memoryCache.delete(key)\n      return null\n    }\n    \n    return item.data\n  }\n  \n  // Clear all caches\n  static clearAll(): void {\n    // Clear localStorage\n    Object.keys(localStorage).forEach(key => {\n      if (key.startsWith(this.PREFIX)) {\n        localStorage.removeItem(key)\n      }\n    })\n    \n    // Clear sessionStorage\n    Object.keys(sessionStorage).forEach(key => {\n      if (key.startsWith(this.PREFIX)) {\n        sessionStorage.removeItem(key)\n      }\n    })\n    \n    // Clear memory cache\n    this.memoryCache.clear()\n  }\n  \n  // Cache size monitoring\n  static getCacheInfo() {\n    const localStorageSize = Object.keys(localStorage)\n      .filter(key => key.startsWith(this.PREFIX))\n      .reduce((size, key) => size + localStorage.getItem(key)!.length, 0)\n    \n    const sessionStorageSize = Object.keys(sessionStorage)\n      .filter(key => key.startsWith(this.PREFIX))\n      .reduce((size, key) => size + sessionStorage.getItem(key)!.length, 0)\n    \n    return {\n      localStorage: {\n        size: localStorageSize,\n        items: Object.keys(localStorage).filter(key => key.startsWith(this.PREFIX)).length\n      },\n      sessionStorage: {\n        size: sessionStorageSize,\n        items: Object.keys(sessionStorage).filter(key => key.startsWith(this.PREFIX)).length\n      },\n      memory: {\n        items: this.memoryCache.size\n      }\n    }\n  }\n}\n\n// Service Worker cache utilities\nexport class ServiceWorkerCache {\n  private static readonly CACHE_NAME = 'promptflow-v1'\n  \n  static async cacheResources(urls: string[]): Promise<void> {\n    if (!('serviceWorker' in navigator)) return\n    \n    try {\n      const cache = await caches.open(this.CACHE_NAME)\n      await cache.addAll(urls)\n      console.log('✅ [SW_CACHE] Resources cached:', urls.length)\n    } catch (error) {\n      console.warn('Failed to cache resources:', error)\n    }\n  }\n  \n  static async getCachedResponse(url: string): Promise<Response | null> {\n    if (!('caches' in window)) return null\n    \n    try {\n      const cache = await caches.open(this.CACHE_NAME)\n      const response = await cache.match(url)\n      return response || null\n    } catch (error) {\n      console.warn('Failed to get cached response:', error)\n      return null\n    }\n  }\n  \n  static async clearCache(): Promise<void> {\n    if (!('caches' in window)) return\n    \n    try {\n      await caches.delete(this.CACHE_NAME)\n      console.log('✅ [SW_CACHE] Cache cleared')\n    } catch (error) {\n      console.warn('Failed to clear cache:', error)\n    }\n  }\n}\n\n// Optimistic updates helper\nexport class OptimisticUpdates {\n  static updateQueryData<T>(\n    queryClient: QueryClient,\n    queryKey: string[],\n    updater: (oldData: T | undefined) => T\n  ): T | undefined {\n    const previousData = queryClient.getQueryData<T>(queryKey)\n    \n    queryClient.setQueryData<T>(queryKey, updater)\n    \n    return previousData\n  }\n  \n  static rollbackQueryData<T>(\n    queryClient: QueryClient,\n    queryKey: string[],\n    previousData: T | undefined\n  ): void {\n    queryClient.setQueryData<T>(queryKey, previousData)\n  }\n}\n\n// Cache invalidation strategies\nexport class CacheInvalidation {\n  static invalidateRelatedQueries(\n    queryClient: QueryClient,\n    entityType: string,\n    entityId?: string\n  ): void {\n    const patterns = {\n      project: ['projects', 'prompts', 'user-stats'],\n      prompt: ['prompts', 'project-prompts'],\n      user: ['user', 'user-plans', 'user-stats'],\n      template: ['templates', 'context-templates']\n    }\n    \n    const relatedPatterns = patterns[entityType as keyof typeof patterns] || []\n    \n    relatedPatterns.forEach(pattern => {\n      queryClient.invalidateQueries({\n        queryKey: entityId ? [pattern, entityId] : [pattern]\n      })\n    })\n  }\n  \n  static prefetchRelatedData(\n    queryClient: QueryClient,\n    entityType: string,\n    entityId: string\n  ): void {\n    // Prefetch related data based on entity type\n    switch (entityType) {\n      case 'project':\n        queryClient.prefetchQuery({\n          queryKey: ['prompts', entityId],\n          staleTime: CACHE_CONFIGS.prompts.staleTime\n        })\n        break\n      case 'user':\n        queryClient.prefetchQuery({\n          queryKey: ['user-plans', entityId],\n          staleTime: CACHE_CONFIGS.plans.staleTime\n        })\n        break\n    }\n  }\n}\n\n// Performance monitoring\nexport class CachePerformance {\n  private static metrics = new Map<string, { hits: number; misses: number; totalTime: number }>()\n  \n  static recordCacheHit(key: string, responseTime: number): void {\n    const metric = this.metrics.get(key) || { hits: 0, misses: 0, totalTime: 0 }\n    metric.hits++\n    metric.totalTime += responseTime\n    this.metrics.set(key, metric)\n  }\n  \n  static recordCacheMiss(key: string, responseTime: number): void {\n    const metric = this.metrics.get(key) || { hits: 0, misses: 0, totalTime: 0 }\n    metric.misses++\n    metric.totalTime += responseTime\n    this.metrics.set(key, metric)\n  }\n  \n  static getCacheMetrics() {\n    const results = Array.from(this.metrics.entries()).map(([key, metric]) => ({\n      key,\n      hitRate: metric.hits / (metric.hits + metric.misses),\n      avgResponseTime: metric.totalTime / (metric.hits + metric.misses),\n      totalRequests: metric.hits + metric.misses\n    }))\n    \n    return results.sort((a, b) => b.totalRequests - a.totalRequests)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;AAED;;;AAGO,MAAM,gBAAgB;IAC3B,6BAA6B;IAC7B,MAAM;QACJ,WAAW,IAAI,KAAK;QACpB,QAAQ,KAAK,KAAK;IACpB;IAEA,8BAA8B;IAC9B,UAAU;QACR,WAAW,IAAI,KAAK;QACpB,QAAQ,KAAK,KAAK;IACpB;IAEA,6BAA6B;IAC7B,SAAS;QACP,WAAW,KAAK;QAChB,QAAQ,IAAI,KAAK;IACnB;IAEA,qCAAqC;IACrC,WAAW;QACT,WAAW,KAAK,KAAK;QACrB,QAAQ,KAAK,KAAK;IACpB;IAEA,8BAA8B;IAC9B,OAAO;QACL,WAAW,KAAK,KAAK;QACrB,QAAQ,KAAK,KAAK;IACpB;IAEA,8BAA8B;IAC9B,UAAU;QACR,WAAW;QACX,QAAQ,IAAI,KAAK;IACnB;AACF;AAGO,MAAM,6BAA6B;IACxC,OAAO,IAAI,gLAAA,CAAA,cAAW,CAAC;QACrB,gBAAgB;YACd,SAAS;gBACP,yBAAyB;gBACzB,WAAW,IAAI,KAAK;gBACpB,QAAQ,IAAI,KAAK;gBAEjB,sBAAsB;gBACtB,OAAO,CAAC,cAAc;oBACpB,4BAA4B;oBAC5B,IAAI,CAAA,kBAAA,4BAAA,MAAO,MAAM,KAAI,OAAO,CAAA,kBAAA,4BAAA,MAAO,MAAM,IAAG,KAAK;wBAC/C,OAAO;oBACT;oBACA,uCAAuC;oBACvC,OAAO,eAAe;gBACxB;gBAEA,uCAAuC;gBACvC,YAAY,CAAC,eAAiB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;gBAEjE,8BAA8B;gBAC9B,sBAAsB;gBACtB,oBAAoB;gBACpB,gBAAgB;YAClB;YAEA,WAAW;gBACT,yBAAyB;gBACzB,OAAO;gBACP,YAAY;YACd;QACF;IACF;AACF;AAGO,MAAM;IAGX,mCAAmC;IACnC,OAAO,SAAY,GAAW,EAAE,IAAO,EAAE,GAAY,EAAQ;QAC3D,IAAI;YACF,MAAM,OAAO;gBACX;gBACA,WAAW,KAAK,GAAG;gBACnB,KAAK,OAAO,KAAK,KAAK,KAAK,KAAK,mBAAmB;YACrD;YACA,aAAa,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,KAAK,SAAS,CAAC;QACzD,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,qCAAqC;QACpD;IACF;IAEA,OAAO,SAAY,GAAW,EAAY;QACxC,IAAI;YACF,MAAM,OAAO,aAAa,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG;YAChD,IAAI,CAAC,MAAM,OAAO;YAElB,MAAM,SAAS,KAAK,KAAK,CAAC;YAC1B,MAAM,MAAM,KAAK,GAAG;YAEpB,mBAAmB;YACnB,IAAI,MAAM,OAAO,SAAS,GAAG,OAAO,GAAG,EAAE;gBACvC,aAAa,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG;gBACtC,OAAO;YACT;YAEA,OAAO,OAAO,IAAI;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,qCAAqC;YAClD,OAAO;QACT;IACF;IAEA,uCAAuC;IACvC,OAAO,WAAc,GAAW,EAAE,IAAO,EAAQ;QAC/C,IAAI;YACF,MAAM,OAAO;gBACX;gBACA,WAAW,KAAK,GAAG;YACrB;YACA,eAAe,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,KAAK,SAAS,CAAC;QAC3D,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,uCAAuC;QACtD;IACF;IAEA,OAAO,WAAc,GAAW,EAAY;QAC1C,IAAI;YACF,MAAM,OAAO,eAAe,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG;YAClD,IAAI,CAAC,MAAM,OAAO;YAElB,MAAM,SAAS,KAAK,KAAK,CAAC;YAC1B,OAAO,OAAO,IAAI;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,uCAAuC;YACpD,OAAO;QACT;IACF;IAKA,OAAO,UAAa,GAAW,EAAE,IAAO,EAAqC;YAAnC,MAAA,iEAAc,IAAI,KAAK;QAC/D,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK;YACxB;YACA,WAAW,KAAK,GAAG;YACnB;QACF;IACF;IAEA,OAAO,UAAa,GAAW,EAAY;QACzC,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;QAClC,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,MAAM,KAAK,SAAS,GAAG,KAAK,GAAG,EAAE;YACnC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACxB,OAAO;QACT;QAEA,OAAO,KAAK,IAAI;IAClB;IAEA,mBAAmB;IACnB,OAAO,WAAiB;QACtB,qBAAqB;QACrB,OAAO,IAAI,CAAC,cAAc,OAAO,CAAC,CAAA;YAChC,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG;gBAC/B,aAAa,UAAU,CAAC;YAC1B;QACF;QAEA,uBAAuB;QACvB,OAAO,IAAI,CAAC,gBAAgB,OAAO,CAAC,CAAA;YAClC,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG;gBAC/B,eAAe,UAAU,CAAC;YAC5B;QACF;QAEA,qBAAqB;QACrB,IAAI,CAAC,WAAW,CAAC,KAAK;IACxB;IAEA,wBAAwB;IACxB,OAAO,eAAe;QACpB,MAAM,mBAAmB,OAAO,IAAI,CAAC,cAClC,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,GACxC,MAAM,CAAC,CAAC,MAAM,MAAQ,OAAO,aAAa,OAAO,CAAC,KAAM,MAAM,EAAE;QAEnE,MAAM,qBAAqB,OAAO,IAAI,CAAC,gBACpC,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,GACxC,MAAM,CAAC,CAAC,MAAM,MAAQ,OAAO,eAAe,OAAO,CAAC,KAAM,MAAM,EAAE;QAErE,OAAO;YACL,cAAc;gBACZ,MAAM;gBACN,OAAO,OAAO,IAAI,CAAC,cAAc,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM;YACpF;YACA,gBAAgB;gBACd,MAAM;gBACN,OAAO,OAAO,IAAI,CAAC,gBAAgB,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM;YACtF;YACA,QAAQ;gBACN,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI;YAC9B;QACF;IACF;AACF;AAnIE,yKADW,cACa,UAAS;AA+DjC,oCAAoC;AACpC,yKAjEW,cAiEI,eAAc,IAAI;AAsE5B,MAAM;IAGX,aAAa,eAAe,IAAc,EAAiB;QACzD,IAAI,CAAC,CAAC,mBAAmB,SAAS,GAAG;QAErC,IAAI;YACF,MAAM,QAAQ,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU;YAC/C,MAAM,MAAM,MAAM,CAAC;YACnB,QAAQ,GAAG,CAAC,kCAAkC,KAAK,MAAM;QAC3D,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,8BAA8B;QAC7C;IACF;IAEA,aAAa,kBAAkB,GAAW,EAA4B;QACpE,IAAI,CAAC,CAAC,YAAY,MAAM,GAAG,OAAO;QAElC,IAAI;YACF,MAAM,QAAQ,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU;YAC/C,MAAM,WAAW,MAAM,MAAM,KAAK,CAAC;YACnC,OAAO,YAAY;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,kCAAkC;YAC/C,OAAO;QACT;IACF;IAEA,aAAa,aAA4B;QACvC,IAAI,CAAC,CAAC,YAAY,MAAM,GAAG;QAE3B,IAAI;YACF,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU;YACnC,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,0BAA0B;QACzC;IACF;AACF;AArCE,yKADW,oBACa,cAAa;AAwChC,MAAM;IACX,OAAO,gBACL,WAAwB,EACxB,QAAkB,EAClB,OAAsC,EACvB;QACf,MAAM,eAAe,YAAY,YAAY,CAAI;QAEjD,YAAY,YAAY,CAAI,UAAU;QAEtC,OAAO;IACT;IAEA,OAAO,kBACL,WAAwB,EACxB,QAAkB,EAClB,YAA2B,EACrB;QACN,YAAY,YAAY,CAAI,UAAU;IACxC;AACF;AAGO,MAAM;IACX,OAAO,yBACL,WAAwB,EACxB,UAAkB,EAClB,QAAiB,EACX;QACN,MAAM,WAAW;YACf,SAAS;gBAAC;gBAAY;gBAAW;aAAa;YAC9C,QAAQ;gBAAC;gBAAW;aAAkB;YACtC,MAAM;gBAAC;gBAAQ;gBAAc;aAAa;YAC1C,UAAU;gBAAC;gBAAa;aAAoB;QAC9C;QAEA,MAAM,kBAAkB,QAAQ,CAAC,WAAoC,IAAI,EAAE;QAE3E,gBAAgB,OAAO,CAAC,CAAA;YACtB,YAAY,iBAAiB,CAAC;gBAC5B,UAAU,WAAW;oBAAC;oBAAS;iBAAS,GAAG;oBAAC;iBAAQ;YACtD;QACF;IACF;IAEA,OAAO,oBACL,WAAwB,EACxB,UAAkB,EAClB,QAAgB,EACV;QACN,6CAA6C;QAC7C,OAAQ;YACN,KAAK;gBACH,YAAY,aAAa,CAAC;oBACxB,UAAU;wBAAC;wBAAW;qBAAS;oBAC/B,WAAW,cAAc,OAAO,CAAC,SAAS;gBAC5C;gBACA;YACF,KAAK;gBACH,YAAY,aAAa,CAAC;oBACxB,UAAU;wBAAC;wBAAc;qBAAS;oBAClC,WAAW,cAAc,KAAK,CAAC,SAAS;gBAC1C;gBACA;QACJ;IACF;AACF;AAGO,MAAM;IAGX,OAAO,eAAe,GAAW,EAAE,YAAoB,EAAQ;QAC7D,MAAM,SAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ;YAAE,MAAM;YAAG,QAAQ;YAAG,WAAW;QAAE;QAC3E,OAAO,IAAI;QACX,OAAO,SAAS,IAAI;QACpB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK;IACxB;IAEA,OAAO,gBAAgB,GAAW,EAAE,YAAoB,EAAQ;QAC9D,MAAM,SAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ;YAAE,MAAM;YAAG,QAAQ;YAAG,WAAW;QAAE;QAC3E,OAAO,MAAM;QACb,OAAO,SAAS,IAAI;QACpB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK;IACxB;IAEA,OAAO,kBAAkB;QACvB,MAAM,UAAU,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,GAAG,CAAC;gBAAC,CAAC,KAAK,OAAO;mBAAM;gBACzE;gBACA,SAAS,OAAO,IAAI,GAAG,CAAC,OAAO,IAAI,GAAG,OAAO,MAAM;gBACnD,iBAAiB,OAAO,SAAS,GAAG,CAAC,OAAO,IAAI,GAAG,OAAO,MAAM;gBAChE,eAAe,OAAO,IAAI,GAAG,OAAO,MAAM;YAC5C;;QAEA,OAAO,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,aAAa,GAAG,EAAE,aAAa;IACjE;AACF;AA1BE,yKADW,kBACI,WAAU,IAAI", "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/providers/query-provider.tsx"], "sourcesContent": ["'use client'\n\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query'\nimport { ReactQueryDevtools } from '@tanstack/react-query-devtools'\nimport { useState, useEffect } from 'react'\nimport { createOptimizedQueryClient, BrowserCache, CachePerformance } from '@/lib/cache-strategies'\n\nexport function QueryProvider({ children }: { children: React.ReactNode }) {\n  const [queryClient] = useState(() => createOptimizedQueryClient())\n\n  // Initialize cache monitoring\n  useEffect(() => {\n    // Clear old cache on app start\n    const lastClearTime = BrowserCache.getLocal<number>('cache-clear-time')\n    const now = Date.now()\n    const oneDay = 24 * 60 * 60 * 1000\n\n    if (!lastClearTime || now - lastClearTime > oneDay) {\n      BrowserCache.clearAll()\n      BrowserCache.setLocal('cache-clear-time', now)\n      console.log('🧹 [CACHE] Daily cache cleanup completed')\n    }\n\n    // Performance monitoring in development\n    if (process.env.NODE_ENV === 'development') {\n      const interval = setInterval(() => {\n        const metrics = CachePerformance.getCacheMetrics()\n        const cacheInfo = BrowserCache.getCacheInfo()\n\n        console.log('📊 [CACHE_METRICS]', {\n          performance: metrics.slice(0, 5), // Top 5 queries\n          storage: cacheInfo\n        })\n      }, 30000) // Every 30 seconds\n\n      return () => clearInterval(interval)\n    }\n  }, [])\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      {children}\n      {process.env.NODE_ENV === 'development' && (\n        <ReactQueryDevtools\n          initialIsOpen={false}\n          position={\"bottom-right\" as any}\n        />\n      )}\n    </QueryClientProvider>\n  )\n}"], "names": [], "mappings": ";;;AAwBQ;;AAtBR;AACA;AACA;AACA;;;AALA;;;;;AAOO,SAAS,cAAc,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IAC5B,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;kCAAE,IAAM,CAAA,GAAA,oIAAA,CAAA,6BAA0B,AAAD;;IAE9D,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,+BAA+B;YAC/B,MAAM,gBAAgB,oIAAA,CAAA,eAAY,CAAC,QAAQ,CAAS;YACpD,MAAM,MAAM,KAAK,GAAG;YACpB,MAAM,SAAS,KAAK,KAAK,KAAK;YAE9B,IAAI,CAAC,iBAAiB,MAAM,gBAAgB,QAAQ;gBAClD,oIAAA,CAAA,eAAY,CAAC,QAAQ;gBACrB,oIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,oBAAoB;gBAC1C,QAAQ,GAAG,CAAC;YACd;YAEA,wCAAwC;YACxC,wCAA4C;gBAC1C,MAAM,WAAW;wDAAY;wBAC3B,MAAM,UAAU,oIAAA,CAAA,mBAAgB,CAAC,eAAe;wBAChD,MAAM,YAAY,oIAAA,CAAA,eAAY,CAAC,YAAY;wBAE3C,QAAQ,GAAG,CAAC,sBAAsB;4BAChC,aAAa,QAAQ,KAAK,CAAC,GAAG;4BAC9B,SAAS;wBACX;oBACF;uDAAG,OAAO,mBAAmB;;gBAE7B;+CAAO,IAAM,cAAc;;YAC7B;QACF;kCAAG,EAAE;IAEL,qBACE,6LAAC,yLAAA,CAAA,sBAAmB;QAAC,QAAQ;;YAC1B;YACA,oDAAyB,+BACxB,6LAAC,uLAAA,CAAA,qBAAkB;gBACjB,eAAe;gBACf,UAAU;;;;;;;;;;;;AAKpB;GA3CgB;KAAA", "debugId": null}}, {"offset": {"line": 427, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useTheme } from \"next-themes\"\nimport { Toaster as Son<PERSON>, ToasterP<PERSON> } from \"sonner\"\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme()\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps[\"theme\"]}\n      className=\"toaster group\"\n      style={\n        {\n          \"--normal-bg\": \"var(--popover)\",\n          \"--normal-text\": \"var(--popover-foreground)\",\n          \"--normal-border\": \"var(--border)\",\n        } as React.CSSProperties\n      }\n      {...props}\n    />\n  )\n}\n\nexport { Toaster }\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,UAAU;QAAC,EAAE,GAAG,OAAqB;;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,6LAAC,2IAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf;GAjBM;;QACyB,mJAAA,CAAA,WAAQ;;;KADjC", "debugId": null}}, {"offset": {"line": 475, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/service-worker-registration.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { toast } from 'sonner'\n\n// PWA install prompt event interface\ninterface BeforeInstallPromptEvent extends Event {\n  prompt(): Promise<void>\n  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>\n}\n\ninterface ServiceWorkerState {\n  isSupported: boolean\n  isRegistered: boolean\n  isUpdateAvailable: boolean\n  registration: ServiceWorkerRegistration | null\n}\n\nexport function ServiceWorkerRegistration() {\n  const [swState, setSwState] = useState<ServiceWorkerState>({\n    isSupported: false,\n    isRegistered: false,\n    isUpdateAvailable: false,\n    registration: null\n  })\n\n  useEffect(() => {\n    // Check if service workers are supported\n    if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {\n      setSwState(prev => ({ ...prev, isSupported: true }))\n      registerServiceWorker()\n    }\n  }, [])\n\n  const registerServiceWorker = async () => {\n    try {\n      const registration = await navigator.serviceWorker.register('/sw.js', {\n        scope: '/',\n        updateViaCache: 'none' // Always check for updates\n      })\n\n      console.log('Service Worker registered successfully:', registration)\n      \n      setSwState(prev => ({\n        ...prev,\n        isRegistered: true,\n        registration\n      }))\n\n      // Check for updates\n      registration.addEventListener('updatefound', () => {\n        const newWorker = registration.installing\n        \n        if (newWorker) {\n          newWorker.addEventListener('statechange', () => {\n            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {\n              // New version available\n              setSwState(prev => ({ ...prev, isUpdateAvailable: true }))\n              \n              toast('Yeni güncelleme mevcut!', {\n                description: 'Uygulamayı yenilemek için tıklayın',\n                action: {\n                  label: 'Yenile',\n                  onClick: () => updateServiceWorker(newWorker)\n                },\n                duration: 10000\n              })\n            }\n          })\n        }\n      })\n\n      // Listen for controlling service worker changes\n      navigator.serviceWorker.addEventListener('controllerchange', () => {\n        window.location.reload()\n      })\n\n      // Check for waiting service worker\n      if (registration.waiting) {\n        setSwState(prev => ({ ...prev, isUpdateAvailable: true }))\n      }\n\n      // Periodic update check (every 30 minutes)\n      setInterval(() => {\n        registration.update()\n      }, 30 * 60 * 1000)\n\n    } catch (error) {\n      console.error('Service Worker registration failed:', error)\n      \n      toast.error('Çevrimdışı özellikler kullanılamıyor', {\n        description: 'Service Worker kaydedilemedi'\n      })\n    }\n  }\n\n  const updateServiceWorker = (newWorker: ServiceWorker) => {\n    newWorker.postMessage({ type: 'SKIP_WAITING' })\n  }\n\n  const forceUpdate = () => {\n    if (swState.registration?.waiting) {\n      updateServiceWorker(swState.registration.waiting)\n    }\n  }\n\n  // Cache management functions\n  const clearCache = async () => {\n    try {\n      const cacheNames = await caches.keys()\n      await Promise.all(\n        cacheNames.map(cacheName => caches.delete(cacheName))\n      )\n      \n      toast.success('Önbellek temizlendi', {\n        description: 'Uygulama yeniden yüklenecek'\n      })\n      \n      setTimeout(() => window.location.reload(), 1000)\n    } catch (error) {\n      console.error('Cache clear failed:', error)\n      toast.error('Önbellek temizlenemedi')\n    }\n  }\n\n  const getCacheSize = async () => {\n    try {\n      if ('storage' in navigator && 'estimate' in navigator.storage) {\n        const estimate = await navigator.storage.estimate()\n        return {\n          used: estimate.usage || 0,\n          quota: estimate.quota || 0,\n          usedMB: Math.round((estimate.usage || 0) / 1024 / 1024 * 100) / 100,\n          quotaMB: Math.round((estimate.quota || 0) / 1024 / 1024 * 100) / 100\n        }\n      }\n    } catch (error) {\n      console.error('Storage estimate failed:', error)\n    }\n    return null\n  }\n\n  // Preload critical resources\n  const preloadResources = (urls: string[]) => {\n    if (swState.registration) {\n      swState.registration.active?.postMessage({\n        type: 'CACHE_URLS',\n        urls\n      })\n    }\n  }\n\n  // Background sync registration\n  const registerBackgroundSync = async (tag: string) => {\n    try {\n      if (swState.registration && 'sync' in swState.registration) {\n        await (swState.registration as any).sync.register(tag)\n        console.log('Background sync registered:', tag)\n      }\n    } catch (error) {\n      console.error('Background sync registration failed:', error)\n    }\n  }\n\n  // Push notification subscription\n  const subscribeToPushNotifications = async () => {\n    try {\n      if (!swState.registration) return null\n\n      const subscription = await swState.registration.pushManager.subscribe({\n        userVisibleOnly: true,\n        applicationServerKey: process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY\n      })\n\n      console.log('Push subscription:', subscription)\n      return subscription\n    } catch (error) {\n      console.error('Push subscription failed:', error)\n      return null\n    }\n  }\n\n  // Expose utilities globally for debugging\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      (window as unknown as { swUtils: unknown }).swUtils = {\n        state: swState,\n        forceUpdate,\n        clearCache,\n        getCacheSize,\n        preloadResources,\n        registerBackgroundSync,\n        subscribeToPushNotifications\n      }\n    }\n  }, [swState])\n\n  // Don't render anything - this is just for registration\n  return null\n}\n\n// Hook for using service worker features\nexport function useServiceWorker() {\n  const [isOnline, setIsOnline] = useState(true)\n  const [isInstallable, setIsInstallable] = useState(false)\n  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null)\n\n  useEffect(() => {\n    // Online/offline detection\n    const handleOnline = () => setIsOnline(true)\n    const handleOffline = () => setIsOnline(false)\n\n    window.addEventListener('online', handleOnline)\n    window.addEventListener('offline', handleOffline)\n\n    // PWA install prompt\n    const handleBeforeInstallPrompt = (e: BeforeInstallPromptEvent) => {\n      e.preventDefault()\n      setDeferredPrompt(e)\n      setIsInstallable(true)\n    }\n\n    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt as EventListener)\n\n    return () => {\n      window.removeEventListener('online', handleOnline)\n      window.removeEventListener('offline', handleOffline)\n      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt as EventListener)\n    }\n  }, [])\n\n  const installPWA = async () => {\n    if (deferredPrompt) {\n      deferredPrompt.prompt()\n      const { outcome } = await deferredPrompt.userChoice\n      \n      if (outcome === 'accepted') {\n        toast.success('Uygulama yüklendi!')\n      }\n      \n      setDeferredPrompt(null)\n      setIsInstallable(false)\n    }\n  }\n\n  const addToBackgroundSync = async (tag: string, data: unknown) => {\n    try {\n      // Store data in IndexedDB for background sync\n      // This would integrate with a proper IndexedDB wrapper\n      console.log('Adding to background sync:', tag, data)\n      \n      if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {\n        const registration = await navigator.serviceWorker.ready\n        if ('sync' in registration && registration.sync) {\n          await (registration.sync as any).register(tag)\n        }\n      }\n    } catch (error) {\n      console.error('Background sync failed:', error)\n    }\n  }\n\n  return {\n    isOnline,\n    isInstallable,\n    installPWA,\n    addToBackgroundSync\n  }\n}\n"], "names": [], "mappings": ";;;;AA2K8B;AAzK9B;AACA;;AAHA;;;AAkBO,SAAS;;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;QACzD,aAAa;QACb,cAAc;QACd,mBAAmB;QACnB,cAAc;IAChB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+CAAE;YACR,yCAAyC;YACzC,IAAI,aAAkB,eAAe,mBAAmB,WAAW;gBACjE;2DAAW,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,aAAa;wBAAK,CAAC;;gBAClD;YACF;QACF;8CAAG,EAAE;IAEL,MAAM,wBAAwB;QAC5B,IAAI;YACF,MAAM,eAAe,MAAM,UAAU,aAAa,CAAC,QAAQ,CAAC,UAAU;gBACpE,OAAO;gBACP,gBAAgB,OAAO,2BAA2B;YACpD;YAEA,QAAQ,GAAG,CAAC,2CAA2C;YAEvD,WAAW,CAAA,OAAQ,CAAC;oBAClB,GAAG,IAAI;oBACP,cAAc;oBACd;gBACF,CAAC;YAED,oBAAoB;YACpB,aAAa,gBAAgB,CAAC,eAAe;gBAC3C,MAAM,YAAY,aAAa,UAAU;gBAEzC,IAAI,WAAW;oBACb,UAAU,gBAAgB,CAAC,eAAe;wBACxC,IAAI,UAAU,KAAK,KAAK,eAAe,UAAU,aAAa,CAAC,UAAU,EAAE;4BACzE,wBAAwB;4BACxB,WAAW,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,mBAAmB;gCAAK,CAAC;4BAExD,CAAA,GAAA,2IAAA,CAAA,QAAK,AAAD,EAAE,2BAA2B;gCAC/B,aAAa;gCACb,QAAQ;oCACN,OAAO;oCACP,SAAS,IAAM,oBAAoB;gCACrC;gCACA,UAAU;4BACZ;wBACF;oBACF;gBACF;YACF;YAEA,gDAAgD;YAChD,UAAU,aAAa,CAAC,gBAAgB,CAAC,oBAAoB;gBAC3D,OAAO,QAAQ,CAAC,MAAM;YACxB;YAEA,mCAAmC;YACnC,IAAI,aAAa,OAAO,EAAE;gBACxB,WAAW,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,mBAAmB;oBAAK,CAAC;YAC1D;YAEA,2CAA2C;YAC3C,YAAY;gBACV,aAAa,MAAM;YACrB,GAAG,KAAK,KAAK;QAEf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YAErD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,wCAAwC;gBAClD,aAAa;YACf;QACF;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,UAAU,WAAW,CAAC;YAAE,MAAM;QAAe;IAC/C;IAEA,MAAM,cAAc;YACd;QAAJ,KAAI,wBAAA,QAAQ,YAAY,cAApB,4CAAA,sBAAsB,OAAO,EAAE;YACjC,oBAAoB,QAAQ,YAAY,CAAC,OAAO;QAClD;IACF;IAEA,6BAA6B;IAC7B,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,aAAa,MAAM,OAAO,IAAI;YACpC,MAAM,QAAQ,GAAG,CACf,WAAW,GAAG,CAAC,CAAA,YAAa,OAAO,MAAM,CAAC;YAG5C,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,uBAAuB;gBACnC,aAAa;YACf;YAEA,WAAW,IAAM,OAAO,QAAQ,CAAC,MAAM,IAAI;QAC7C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,IAAI,aAAa,aAAa,cAAc,UAAU,OAAO,EAAE;gBAC7D,MAAM,WAAW,MAAM,UAAU,OAAO,CAAC,QAAQ;gBACjD,OAAO;oBACL,MAAM,SAAS,KAAK,IAAI;oBACxB,OAAO,SAAS,KAAK,IAAI;oBACzB,QAAQ,KAAK,KAAK,CAAC,CAAC,SAAS,KAAK,IAAI,CAAC,IAAI,OAAO,OAAO,OAAO;oBAChE,SAAS,KAAK,KAAK,CAAC,CAAC,SAAS,KAAK,IAAI,CAAC,IAAI,OAAO,OAAO,OAAO;gBACnE;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;QACA,OAAO;IACT;IAEA,6BAA6B;IAC7B,MAAM,mBAAmB,CAAC;QACxB,IAAI,QAAQ,YAAY,EAAE;gBACxB;aAAA,+BAAA,QAAQ,YAAY,CAAC,MAAM,cAA3B,mDAAA,6BAA6B,WAAW,CAAC;gBACvC,MAAM;gBACN;YACF;QACF;IACF;IAEA,+BAA+B;IAC/B,MAAM,yBAAyB,OAAO;QACpC,IAAI;YACF,IAAI,QAAQ,YAAY,IAAI,UAAU,QAAQ,YAAY,EAAE;gBAC1D,MAAM,AAAC,QAAQ,YAAY,CAAS,IAAI,CAAC,QAAQ,CAAC;gBAClD,QAAQ,GAAG,CAAC,+BAA+B;YAC7C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;QACxD;IACF;IAEA,iCAAiC;IACjC,MAAM,+BAA+B;QACnC,IAAI;YACF,IAAI,CAAC,QAAQ,YAAY,EAAE,OAAO;YAElC,MAAM,eAAe,MAAM,QAAQ,YAAY,CAAC,WAAW,CAAC,SAAS,CAAC;gBACpE,iBAAiB;gBACjB,sBAAsB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,4BAA4B;YAChE;YAEA,QAAQ,GAAG,CAAC,sBAAsB;YAClC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;QACT;IACF;IAEA,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+CAAE;YACR,wCAAmC;gBAChC,OAA2C,OAAO,GAAG;oBACpD,OAAO;oBACP;oBACA;oBACA;oBACA;oBACA;oBACA;gBACF;YACF;QACF;8CAAG;QAAC;KAAQ;IAEZ,wDAAwD;IACxD,OAAO;AACT;GArLgB;KAAA;AAwLT,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC;IAEtF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,2BAA2B;YAC3B,MAAM;2DAAe,IAAM,YAAY;;YACvC,MAAM;4DAAgB,IAAM,YAAY;;YAExC,OAAO,gBAAgB,CAAC,UAAU;YAClC,OAAO,gBAAgB,CAAC,WAAW;YAEnC,qBAAqB;YACrB,MAAM;wEAA4B,CAAC;oBACjC,EAAE,cAAc;oBAChB,kBAAkB;oBAClB,iBAAiB;gBACnB;;YAEA,OAAO,gBAAgB,CAAC,uBAAuB;YAE/C;8CAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,OAAO,mBAAmB,CAAC,WAAW;oBACtC,OAAO,mBAAmB,CAAC,uBAAuB;gBACpD;;QACF;qCAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI,gBAAgB;YAClB,eAAe,MAAM;YACrB,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,eAAe,UAAU;YAEnD,IAAI,YAAY,YAAY;gBAC1B,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;YAEA,kBAAkB;YAClB,iBAAiB;QACnB;IACF;IAEA,MAAM,sBAAsB,OAAO,KAAa;QAC9C,IAAI;YACF,8CAA8C;YAC9C,uDAAuD;YACvD,QAAQ,GAAG,CAAC,8BAA8B,KAAK;YAE/C,IAAI,mBAAmB,aAAa,UAAU,aAAa,CAAC,UAAU,EAAE;gBACtE,MAAM,eAAe,MAAM,UAAU,aAAa,CAAC,KAAK;gBACxD,IAAI,UAAU,gBAAgB,aAAa,IAAI,EAAE;oBAC/C,MAAM,AAAC,aAAa,IAAI,CAAS,QAAQ,CAAC;gBAC5C;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;IAlEgB", "debugId": null}}, {"offset": {"line": 744, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 766, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 830, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 952, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/error-boundary.tsx"], "sourcesContent": ["/**\n * Error Boundary Component\n * Comprehensive error handling with fallback UI and error reporting\n */\n\n'use client'\n\nimport React, { Compo<PERSON>, ErrorInfo, ReactNode } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react'\n\ninterface Props {\n  children: ReactNode\n  fallback?: ReactNode\n  onError?: (error: Error, errorInfo: ErrorInfo) => void\n  showDetails?: boolean\n  level?: 'page' | 'component' | 'critical'\n}\n\ninterface State {\n  hasError: boolean\n  error: Error | null\n  errorInfo: ErrorInfo | null\n  errorId: string | null\n}\n\nexport class ErrorBoundary extends Component<Props, State> {\n  private retryCount = 0\n  private maxRetries = 3\n\n  constructor(props: Props) {\n    super(props)\n    this.state = {\n      hasError: false,\n      error: null,\n      errorInfo: null,\n      errorId: null\n    }\n  }\n\n  static getDerivedStateFromError(error: Error): Partial<State> {\n    // Update state so the next render will show the fallback UI\n    return {\n      hasError: true,\n      error,\n      errorId: `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`\n    }\n  }\n\n  componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    // Filter out hydration errors in development\n    const isHydrationError = error.message.includes('Hydration') ||\n                            error.message.includes('hydrating') ||\n                            error.message.includes('Text content did not match') ||\n                            errorInfo.componentStack?.includes('hydration')\n\n    if (isHydrationError && process.env.NODE_ENV === 'development') {\n      // Log hydration errors with less severity\n      console.warn('⚠️ [ERROR_BOUNDARY] Hydration mismatch detected:', {\n        error: error.message,\n        level: 'hydration',\n        timestamp: new Date().toISOString()\n      })\n      return // Don't show error UI for hydration mismatches\n    }\n\n    // Log error details for real errors\n    console.error('🚨 [ERROR_BOUNDARY] Error caught:', {\n      error: error.message,\n      stack: error.stack,\n      componentStack: errorInfo.componentStack,\n      errorId: this.state.errorId,\n      level: this.props.level || 'component',\n      timestamp: new Date().toISOString()\n    })\n\n    // Update state with error info\n    this.setState({\n      errorInfo\n    })\n\n    // Call custom error handler\n    if (this.props.onError) {\n      this.props.onError(error, errorInfo)\n    }\n\n    // Report error to monitoring service\n    this.reportError(error, errorInfo)\n  }\n\n  private reportError = (error: Error, errorInfo: ErrorInfo) => {\n    // In production, send to error monitoring service\n    if (process.env.NODE_ENV === 'production') {\n      // Example: Sentry, LogRocket, etc.\n      try {\n        // window.Sentry?.captureException(error, {\n        //   contexts: {\n        //     react: {\n        //       componentStack: errorInfo.componentStack\n        //     }\n        //   },\n        //   tags: {\n        //     errorBoundary: true,\n        //     level: this.props.level || 'component'\n        //   }\n        // })\n      } catch (reportingError) {\n        console.error('Failed to report error:', reportingError)\n      }\n    }\n  }\n\n  private handleRetry = () => {\n    if (this.retryCount < this.maxRetries) {\n      this.retryCount++\n      console.log(`🔄 [ERROR_BOUNDARY] Retry attempt ${this.retryCount}/${this.maxRetries}`)\n      \n      this.setState({\n        hasError: false,\n        error: null,\n        errorInfo: null,\n        errorId: null\n      })\n    } else {\n      console.warn('⚠️ [ERROR_BOUNDARY] Max retries exceeded')\n    }\n  }\n\n  private handleReload = () => {\n    window.location.reload()\n  }\n\n  private handleGoHome = () => {\n    window.location.href = '/'\n  }\n\n  private getErrorLevel = (): 'low' | 'medium' | 'high' => {\n    const { level } = this.props\n    const { error } = this.state\n\n    if (level === 'critical') return 'high'\n    if (level === 'page') return 'medium'\n    \n    // Analyze error type\n    if (error?.name === 'ChunkLoadError') return 'medium'\n    if (error?.message?.includes('Network')) return 'medium'\n    if (error?.message?.includes('TypeError')) return 'low'\n    \n    return 'low'\n  }\n\n  private renderErrorDetails = () => {\n    const { error, errorInfo, errorId } = this.state\n    const { showDetails = process.env.NODE_ENV === 'development' } = this.props\n\n    if (!showDetails || !error) return null\n\n    return (\n      <details className=\"mt-4 p-4 bg-gray-50 rounded-lg\">\n        <summary className=\"cursor-pointer font-medium text-gray-700 mb-2\">\n          Teknik Detaylar\n        </summary>\n        <div className=\"space-y-2 text-sm font-mono\">\n          <div>\n            <strong>Error ID:</strong> {errorId}\n          </div>\n          <div>\n            <strong>Error:</strong> {error.name}\n          </div>\n          <div>\n            <strong>Message:</strong> {error.message}\n          </div>\n          {error.stack && (\n            <div>\n              <strong>Stack Trace:</strong>\n              <pre className=\"mt-1 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-32\">\n                {error.stack}\n              </pre>\n            </div>\n          )}\n          {errorInfo?.componentStack && (\n            <div>\n              <strong>Component Stack:</strong>\n              <pre className=\"mt-1 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-32\">\n                {errorInfo.componentStack}\n              </pre>\n            </div>\n          )}\n        </div>\n      </details>\n    )\n  }\n\n  render() {\n    if (this.state.hasError) {\n      // Custom fallback UI\n      if (this.props.fallback) {\n        return this.props.fallback\n      }\n\n      const errorLevel = this.getErrorLevel()\n      const canRetry = this.retryCount < this.maxRetries\n      const { error } = this.state\n\n      return (\n        <div className=\"min-h-[400px] flex items-center justify-center p-4\">\n          <Card className=\"w-full max-w-lg\">\n            <CardHeader className=\"text-center\">\n              <div className=\"mx-auto mb-4\">\n                <AlertTriangle \n                  className={`h-12 w-12 ${\n                    errorLevel === 'high' ? 'text-red-500' :\n                    errorLevel === 'medium' ? 'text-yellow-500' :\n                    'text-orange-500'\n                  }`} \n                />\n              </div>\n              <CardTitle className=\"text-xl\">\n                {errorLevel === 'high' ? 'Kritik Hata' :\n                 errorLevel === 'medium' ? 'Bir Sorun Oluştu' :\n                 'Beklenmeyen Hata'}\n              </CardTitle>\n              <CardDescription>\n                {error?.name === 'ChunkLoadError' \n                  ? 'Uygulama güncellenmiş olabilir. Sayfayı yenilemeyi deneyin.'\n                  : error?.message?.includes('Network')\n                    ? 'Ağ bağlantısı sorunu. İnternet bağlantınızı kontrol edin.'\n                    : 'Bir hata oluştu. Lütfen tekrar deneyin veya sayfayı yenileyin.'\n                }\n              </CardDescription>\n            </CardHeader>\n\n            <CardContent className=\"space-y-4\">\n              <div className=\"flex flex-col sm:flex-row gap-2\">\n                {canRetry && (\n                  <Button \n                    onClick={this.handleRetry}\n                    className=\"flex-1\"\n                    variant=\"default\"\n                  >\n                    <RefreshCw className=\"h-4 w-4 mr-2\" />\n                    Tekrar Dene ({this.maxRetries - this.retryCount} kalan)\n                  </Button>\n                )}\n                \n                <Button \n                  onClick={this.handleReload}\n                  variant=\"outline\"\n                  className=\"flex-1\"\n                >\n                  <RefreshCw className=\"h-4 w-4 mr-2\" />\n                  Sayfayı Yenile\n                </Button>\n                \n                <Button \n                  onClick={this.handleGoHome}\n                  variant=\"outline\"\n                  className=\"flex-1\"\n                >\n                  <Home className=\"h-4 w-4 mr-2\" />\n                  Ana Sayfa\n                </Button>\n              </div>\n\n              {process.env.NODE_ENV === 'development' && (\n                <Button \n                  onClick={() => console.log('Error details:', this.state)}\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  className=\"w-full\"\n                >\n                  <Bug className=\"h-4 w-4 mr-2\" />\n                  Console'da Detayları Gör\n                </Button>\n              )}\n\n              {this.renderErrorDetails()}\n            </CardContent>\n          </Card>\n        </div>\n      )\n    }\n\n    return this.props.children\n  }\n}\n\n// HOC for wrapping components with error boundary\nexport function withErrorBoundary<P extends object>(\n  Component: React.ComponentType<P>,\n  errorBoundaryProps?: Omit<Props, 'children'>\n) {\n  const WrappedComponent = (props: P) => (\n    <ErrorBoundary {...errorBoundaryProps}>\n      <Component {...props} />\n    </ErrorBoundary>\n  )\n\n  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`\n  \n  return WrappedComponent\n}\n\n// Hook for error reporting\nexport function useErrorHandler() {\n  const reportError = (error: Error, context?: string) => {\n    console.error(`🚨 [ERROR_HANDLER] ${context || 'Unhandled error'}:`, error)\n    \n    // Report to monitoring service\n    if (process.env.NODE_ENV === 'production') {\n      // window.Sentry?.captureException(error, { tags: { context } })\n    }\n  }\n\n  return { reportError }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAsD2B;;;AAlD5B;AACA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;;;AAsBO,MAAM,sBAAsB,6JAAA,CAAA,YAAS;IAc1C,OAAO,yBAAyB,KAAY,EAAkB;QAC5D,4DAA4D;QAC5D,OAAO;YACL,UAAU;YACV;YACA,SAAS,AAAC,SAAsB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QACvE;IACF;IAEA,kBAAkB,KAAY,EAAE,SAAoB,EAAE;YAK5B;QAJxB,6CAA6C;QAC7C,MAAM,mBAAmB,MAAM,OAAO,CAAC,QAAQ,CAAC,gBACxB,MAAM,OAAO,CAAC,QAAQ,CAAC,gBACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,mCACvB,4BAAA,UAAU,cAAc,cAAxB,gDAAA,0BAA0B,QAAQ,CAAC;QAE3D,IAAI,oBAAoB,oDAAyB,eAAe;YAC9D,0CAA0C;YAC1C,QAAQ,IAAI,CAAC,oDAAoD;gBAC/D,OAAO,MAAM,OAAO;gBACpB,OAAO;gBACP,WAAW,IAAI,OAAO,WAAW;YACnC;YACA,QAAO,+CAA+C;QACxD;QAEA,oCAAoC;QACpC,QAAQ,KAAK,CAAC,qCAAqC;YACjD,OAAO,MAAM,OAAO;YACpB,OAAO,MAAM,KAAK;YAClB,gBAAgB,UAAU,cAAc;YACxC,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO;YAC3B,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI;YAC3B,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,+BAA+B;QAC/B,IAAI,CAAC,QAAQ,CAAC;YACZ;QACF;QAEA,4BAA4B;QAC5B,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;YACtB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;QAC5B;QAEA,qCAAqC;QACrC,IAAI,CAAC,WAAW,CAAC,OAAO;IAC1B;IAyGA,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBA+BT;YA9Bd,qBAAqB;YACrB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC5B;YAEA,MAAM,aAAa,IAAI,CAAC,aAAa;YACrC,MAAM,WAAW,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU;YAClD,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,KAAK;YAE5B,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC,mIAAA,CAAA,aAAU;4BAAC,WAAU;;8CACpB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;wCACZ,WAAW,AAAC,aAIX,OAHC,eAAe,SAAS,iBACxB,eAAe,WAAW,oBAC1B;;;;;;;;;;;8CAIN,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;8CAClB,eAAe,SAAS,gBACxB,eAAe,WAAW,qBAC1B;;;;;;8CAEH,6LAAC,mIAAA,CAAA,kBAAe;8CACb,CAAA,kBAAA,4BAAA,MAAO,IAAI,MAAK,mBACb,gEACA,CAAA,kBAAA,6BAAA,iBAAA,MAAO,OAAO,cAAd,qCAAA,eAAgB,QAAQ,CAAC,cACvB,8DACA;;;;;;;;;;;;sCAKV,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;wCACZ,0BACC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,IAAI,CAAC,WAAW;4CACzB,WAAU;4CACV,SAAQ;;8DAER,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;gDACxB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU;gDAAC;;;;;;;sDAIpD,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,IAAI,CAAC,YAAY;4CAC1B,SAAQ;4CACR,WAAU;;8DAEV,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAIxC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,IAAI,CAAC,YAAY;4CAC1B,SAAQ;4CACR,WAAU;;8DAEV,6LAAC,sMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;gCAKpC,oDAAyB,+BACxB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,QAAQ,GAAG,CAAC,kBAAkB,IAAI,CAAC,KAAK;oCACvD,SAAQ;oCACR,MAAK;oCACL,WAAU;;sDAEV,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;gCAKnC,IAAI,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;QAKlC;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;IA9PA,YAAY,KAAY,CAAE;QACxB,KAAK,CAAC,QAJR,+KAAQ,cAAa,IACrB,+KAAQ,cAAa,IA8DrB,+KAAQ,eAAc,CAAC,OAAc;YACnC,kDAAkD;YAClD;;QAkBF,IAEA,+KAAQ,eAAc;YACpB,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE;gBACrC,IAAI,CAAC,UAAU;gBACf,QAAQ,GAAG,CAAC,AAAC,qCAAuD,OAAnB,IAAI,CAAC,UAAU,EAAC,KAAmB,OAAhB,IAAI,CAAC,UAAU;gBAEnF,IAAI,CAAC,QAAQ,CAAC;oBACZ,UAAU;oBACV,OAAO;oBACP,WAAW;oBACX,SAAS;gBACX;YACF,OAAO;gBACL,QAAQ,IAAI,CAAC;YACf;QACF,IAEA,+KAAQ,gBAAe;YACrB,OAAO,QAAQ,CAAC,MAAM;QACxB,IAEA,+KAAQ,gBAAe;YACrB,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB,IAEA,+KAAQ,iBAAgB;gBASlB,gBACA;YATJ,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,KAAK;YAC5B,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,KAAK;YAE5B,IAAI,UAAU,YAAY,OAAO;YACjC,IAAI,UAAU,QAAQ,OAAO;YAE7B,qBAAqB;YACrB,IAAI,CAAA,kBAAA,4BAAA,MAAO,IAAI,MAAK,kBAAkB,OAAO;YAC7C,IAAI,kBAAA,6BAAA,iBAAA,MAAO,OAAO,cAAd,qCAAA,eAAgB,QAAQ,CAAC,YAAY,OAAO;YAChD,IAAI,kBAAA,6BAAA,kBAAA,MAAO,OAAO,cAAd,sCAAA,gBAAgB,QAAQ,CAAC,cAAc,OAAO;YAElD,OAAO;QACT,IAEA,+KAAQ,sBAAqB;YAC3B,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK;YAChD,MAAM,EAAE,cAAc,oDAAyB,aAAa,EAAE,GAAG,IAAI,CAAC,KAAK;YAE3E,IAAI,CAAC,eAAe,CAAC,OAAO,OAAO;YAEnC,qBACE,6LAAC;gBAAQ,WAAU;;kCACjB,6LAAC;wBAAQ,WAAU;kCAAgD;;;;;;kCAGnE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;kDAAO;;;;;;oCAAkB;oCAAE;;;;;;;0CAE9B,6LAAC;;kDACC,6LAAC;kDAAO;;;;;;oCAAe;oCAAE,MAAM,IAAI;;;;;;;0CAErC,6LAAC;;kDACC,6LAAC;kDAAO;;;;;;oCAAiB;oCAAE,MAAM,OAAO;;;;;;;4BAEzC,MAAM,KAAK,kBACV,6LAAC;;kDACC,6LAAC;kDAAO;;;;;;kDACR,6LAAC;wCAAI,WAAU;kDACZ,MAAM,KAAK;;;;;;;;;;;;4BAIjB,CAAA,sBAAA,gCAAA,UAAW,cAAc,mBACxB,6LAAC;;kDACC,6LAAC;kDAAO;;;;;;kDACR,6LAAC;wCAAI,WAAU;kDACZ,UAAU,cAAc;;;;;;;;;;;;;;;;;;;;;;;;QAOvC;QA/JE,IAAI,CAAC,KAAK,GAAG;YACX,UAAU;YACV,OAAO;YACP,WAAW;YACX,SAAS;QACX;IACF;AAuPF;AAGO,SAAS,kBACd,SAAiC,EACjC,kBAA4C;IAE5C,MAAM,mBAAmB,CAAC,sBACxB,6LAAC;YAAe,GAAG,kBAAkB;sBACnC,cAAA,6LAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAIxB,iBAAiB,WAAW,GAAG,AAAC,qBAA4D,OAAxC,UAAU,WAAW,IAAI,UAAU,IAAI,EAAC;IAE5F,OAAO;AACT;AAGO,SAAS;IACd,MAAM,cAAc,CAAC,OAAc;QACjC,QAAQ,KAAK,CAAC,AAAC,sBAAkD,OAA7B,WAAW,mBAAkB,MAAI;QAErE,+BAA+B;QAC/B,IAAI,oDAAyB,cAAc;QACzC,gEAAgE;QAClE;IACF;IAEA,OAAO;QAAE;IAAY;AACvB", "debugId": null}}, {"offset": {"line": 1392, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/accessibility-utils.ts"], "sourcesContent": ["/**\n * Accessibility Utilities\n * WCAG 2.1 AA compliance tools and helpers\n */\n\n// ARIA live region manager\nexport class AriaLiveRegion {\n  private static regions = new Map<string, HTMLElement>()\n\n  static announce(message: string, priority: 'polite' | 'assertive' = 'polite') {\n    if (typeof window === 'undefined') return\n\n    const regionId = `aria-live-${priority}`\n    let region = this.regions.get(regionId)\n\n    if (!region) {\n      region = document.createElement('div')\n      region.id = regionId\n      region.setAttribute('aria-live', priority)\n      region.setAttribute('aria-atomic', 'true')\n      region.className = 'sr-only'\n      region.style.cssText = `\n        position: absolute !important;\n        width: 1px !important;\n        height: 1px !important;\n        padding: 0 !important;\n        margin: -1px !important;\n        overflow: hidden !important;\n        clip: rect(0, 0, 0, 0) !important;\n        white-space: nowrap !important;\n        border: 0 !important;\n      `\n      \n      document.body.appendChild(region)\n      this.regions.set(regionId, region)\n    }\n\n    // Clear previous message and set new one\n    region.textContent = ''\n    setTimeout(() => {\n      region!.textContent = message\n    }, 100)\n  }\n\n  static clear(priority: 'polite' | 'assertive' = 'polite') {\n    const regionId = `aria-live-${priority}`\n    const region = this.regions.get(regionId)\n    if (region) {\n      region.textContent = ''\n    }\n  }\n}\n\n// Color contrast checker\nexport class ColorContrast {\n  // Convert hex to RGB\n  static hexToRgb(hex: string): { r: number; g: number; b: number } | null {\n    const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex)\n    return result ? {\n      r: parseInt(result[1], 16),\n      g: parseInt(result[2], 16),\n      b: parseInt(result[3], 16)\n    } : null\n  }\n\n  // Calculate relative luminance\n  static getLuminance(r: number, g: number, b: number): number {\n    const [rs, gs, bs] = [r, g, b].map(c => {\n      c = c / 255\n      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)\n    })\n    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs\n  }\n\n  // Calculate contrast ratio\n  static getContrastRatio(color1: string, color2: string): number {\n    const rgb1 = this.hexToRgb(color1)\n    const rgb2 = this.hexToRgb(color2)\n    \n    if (!rgb1 || !rgb2) return 0\n\n    const lum1 = this.getLuminance(rgb1.r, rgb1.g, rgb1.b)\n    const lum2 = this.getLuminance(rgb2.r, rgb2.g, rgb2.b)\n    \n    const brightest = Math.max(lum1, lum2)\n    const darkest = Math.min(lum1, lum2)\n    \n    return (brightest + 0.05) / (darkest + 0.05)\n  }\n\n  // Check WCAG compliance\n  static checkWCAGCompliance(foreground: string, background: string): {\n    ratio: number\n    aa: boolean\n    aaa: boolean\n    aaLarge: boolean\n    aaaLarge: boolean\n  } {\n    const ratio = this.getContrastRatio(foreground, background)\n    \n    return {\n      ratio,\n      aa: ratio >= 4.5,\n      aaa: ratio >= 7,\n      aaLarge: ratio >= 3,\n      aaaLarge: ratio >= 4.5\n    }\n  }\n}\n\n// Keyboard navigation helper\nexport class KeyboardNavigation {\n  private static trapStack: HTMLElement[] = []\n\n  // Focus trap for modals and dialogs\n  static trapFocus(element: HTMLElement) {\n    this.trapStack.push(element)\n    \n    const focusableElements = this.getFocusableElements(element)\n    if (focusableElements.length === 0) return\n\n    const firstElement = focusableElements[0]\n    const lastElement = focusableElements[focusableElements.length - 1]\n\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if (e.key !== 'Tab') return\n\n      if (e.shiftKey) {\n        if (document.activeElement === firstElement) {\n          e.preventDefault()\n          lastElement.focus()\n        }\n      } else {\n        if (document.activeElement === lastElement) {\n          e.preventDefault()\n          firstElement.focus()\n        }\n      }\n    }\n\n    element.addEventListener('keydown', handleKeyDown)\n    firstElement.focus()\n\n    return () => {\n      element.removeEventListener('keydown', handleKeyDown)\n      this.trapStack.pop()\n    }\n  }\n\n  // Get all focusable elements\n  static getFocusableElements(container: HTMLElement): HTMLElement[] {\n    const focusableSelectors = [\n      'a[href]',\n      'button:not([disabled])',\n      'input:not([disabled])',\n      'select:not([disabled])',\n      'textarea:not([disabled])',\n      '[tabindex]:not([tabindex=\"-1\"])',\n      '[contenteditable=\"true\"]'\n    ].join(', ')\n\n    return Array.from(container.querySelectorAll(focusableSelectors))\n      .filter(el => this.isVisible(el as HTMLElement)) as HTMLElement[]\n  }\n\n  // Check if element is visible\n  private static isVisible(element: HTMLElement): boolean {\n    const style = window.getComputedStyle(element)\n    return style.display !== 'none' && \n           style.visibility !== 'hidden' && \n           style.opacity !== '0'\n  }\n\n  // Restore focus to previous element\n  static restoreFocus() {\n    const lastTrap = this.trapStack[this.trapStack.length - 1]\n    if (lastTrap) {\n      const focusableElements = this.getFocusableElements(lastTrap)\n      if (focusableElements.length > 0) {\n        focusableElements[0].focus()\n      }\n    }\n  }\n}\n\n// Screen reader utilities\nexport class ScreenReader {\n  // Generate unique IDs for ARIA relationships\n  static generateId(prefix: string = 'aria'): string {\n    return `${prefix}-${Math.random().toString(36).substr(2, 9)}`\n  }\n\n  // Create describedby relationship\n  static createDescribedBy(element: HTMLElement, description: string): string {\n    const descId = this.generateId('desc')\n    \n    // Create description element if it doesn't exist\n    let descElement = document.getElementById(descId)\n    if (!descElement) {\n      descElement = document.createElement('div')\n      descElement.id = descId\n      descElement.className = 'sr-only'\n      descElement.textContent = description\n      document.body.appendChild(descElement)\n    }\n\n    // Add to aria-describedby\n    const existingDescribedBy = element.getAttribute('aria-describedby')\n    const newDescribedBy = existingDescribedBy \n      ? `${existingDescribedBy} ${descId}`\n      : descId\n    \n    element.setAttribute('aria-describedby', newDescribedBy)\n    \n    return descId\n  }\n\n  // Create labelledby relationship\n  static createLabelledBy(element: HTMLElement, labelText: string): string {\n    const labelId = this.generateId('label')\n    \n    // Create label element if it doesn't exist\n    let labelElement = document.getElementById(labelId)\n    if (!labelElement) {\n      labelElement = document.createElement('div')\n      labelElement.id = labelId\n      labelElement.className = 'sr-only'\n      labelElement.textContent = labelText\n      document.body.appendChild(labelElement)\n    }\n\n    element.setAttribute('aria-labelledby', labelId)\n    \n    return labelId\n  }\n\n  // Announce status changes\n  static announceStatus(message: string, priority: 'polite' | 'assertive' = 'polite') {\n    AriaLiveRegion.announce(message, priority)\n  }\n}\n\n// Form accessibility helpers\nexport class FormAccessibility {\n  // Add error announcement to form field\n  static addFieldError(field: HTMLElement, errorMessage: string): string {\n    const errorId = ScreenReader.generateId('error')\n    \n    // Create error element\n    const errorElement = document.createElement('div')\n    errorElement.id = errorId\n    errorElement.className = 'text-red-600 text-sm mt-1'\n    errorElement.setAttribute('role', 'alert')\n    errorElement.textContent = errorMessage\n    \n    // Insert after field\n    field.parentNode?.insertBefore(errorElement, field.nextSibling)\n    \n    // Update field attributes\n    field.setAttribute('aria-invalid', 'true')\n    field.setAttribute('aria-describedby', errorId)\n    \n    // Announce error\n    ScreenReader.announceStatus(`Hata: ${errorMessage}`, 'assertive')\n    \n    return errorId\n  }\n\n  // Remove field error\n  static removeFieldError(field: HTMLElement, errorId: string) {\n    const errorElement = document.getElementById(errorId)\n    if (errorElement) {\n      errorElement.remove()\n    }\n    \n    field.removeAttribute('aria-invalid')\n    field.removeAttribute('aria-describedby')\n  }\n\n  // Add success feedback\n  static addFieldSuccess(field: HTMLElement, successMessage: string): string {\n    const successId = ScreenReader.generateId('success')\n    \n    // Create success element\n    const successElement = document.createElement('div')\n    successElement.id = successId\n    successElement.className = 'text-green-600 text-sm mt-1'\n    successElement.setAttribute('role', 'status')\n    successElement.textContent = successMessage\n    \n    // Insert after field\n    field.parentNode?.insertBefore(successElement, field.nextSibling)\n    \n    // Update field attributes\n    field.setAttribute('aria-describedby', successId)\n    \n    return successId\n  }\n}\n\n// Accessibility testing utilities\nexport class AccessibilityTester {\n  // Test color contrast on page\n  static testColorContrast(): Array<{\n    element: HTMLElement\n    foreground: string\n    background: string\n    ratio: number\n    passes: boolean\n  }> {\n    const results: Array<any> = []\n    \n    // Get all text elements\n    const textElements = document.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6, a, button, label')\n    \n    textElements.forEach(element => {\n      const styles = window.getComputedStyle(element as HTMLElement)\n      const foreground = styles.color\n      const background = styles.backgroundColor\n      \n      if (foreground && background && background !== 'rgba(0, 0, 0, 0)') {\n        // Convert to hex for testing (simplified)\n        const ratio = ColorContrast.getContrastRatio(foreground, background)\n        const passes = ratio >= 4.5\n        \n        results.push({\n          element: element as HTMLElement,\n          foreground,\n          background,\n          ratio,\n          passes\n        })\n      }\n    })\n    \n    return results\n  }\n\n  // Test keyboard navigation\n  static testKeyboardNavigation(): {\n    focusableElements: number\n    tabOrder: HTMLElement[]\n    issues: string[]\n  } {\n    const focusableElements = KeyboardNavigation.getFocusableElements(document.body)\n    const issues: string[] = []\n    \n    // Check for missing focus indicators\n    focusableElements.forEach(element => {\n      const styles = window.getComputedStyle(element)\n      if (!styles.outline && !styles.boxShadow) {\n        issues.push(`Element missing focus indicator: ${element.tagName}`)\n      }\n    })\n    \n    return {\n      focusableElements: focusableElements.length,\n      tabOrder: focusableElements,\n      issues\n    }\n  }\n\n  // Generate accessibility report\n  static generateReport(): {\n    colorContrast: ReturnType<typeof AccessibilityTester.testColorContrast>\n    keyboardNavigation: ReturnType<typeof AccessibilityTester.testKeyboardNavigation>\n    ariaLabels: number\n    headingStructure: Array<{ level: number; text: string }>\n  } {\n    const colorContrast = this.testColorContrast()\n    const keyboardNavigation = this.testKeyboardNavigation()\n    \n    // Count ARIA labels\n    const ariaLabels = document.querySelectorAll('[aria-label], [aria-labelledby]').length\n    \n    // Check heading structure\n    const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'))\n    const headingStructure = headings.map(heading => ({\n      level: parseInt(heading.tagName.charAt(1)),\n      text: heading.textContent || ''\n    }))\n    \n    return {\n      colorContrast,\n      keyboardNavigation,\n      ariaLabels,\n      headingStructure\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,2BAA2B;;;;;;;;;;;AACpB,MAAM;IAGX,OAAO,SAAS,OAAe,EAA+C;YAA7C,WAAA,iEAAmC;QAClE;;QAEA,MAAM,WAAW,AAAC,aAAqB,OAAT;QAC9B,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QAE9B,IAAI,CAAC,QAAQ;YACX,SAAS,SAAS,aAAa,CAAC;YAChC,OAAO,EAAE,GAAG;YACZ,OAAO,YAAY,CAAC,aAAa;YACjC,OAAO,YAAY,CAAC,eAAe;YACnC,OAAO,SAAS,GAAG;YACnB,OAAO,KAAK,CAAC,OAAO,GAAI;YAYxB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU;QAC7B;QAEA,yCAAyC;QACzC,OAAO,WAAW,GAAG;QACrB,WAAW;YACT,OAAQ,WAAW,GAAG;QACxB,GAAG;IACL;IAEA,OAAO,QAAmD;YAA7C,WAAA,iEAAmC;QAC9C,MAAM,WAAW,AAAC,aAAqB,OAAT;QAC9B,MAAM,SAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QAChC,IAAI,QAAQ;YACV,OAAO,WAAW,GAAG;QACvB;IACF;AACF;AA5CE,yKADW,gBACI,WAAU,IAAI;AA+CxB,MAAM;IACX,qBAAqB;IACrB,OAAO,SAAS,GAAW,EAA8C;QACvE,MAAM,SAAS,4CAA4C,IAAI,CAAC;QAChE,OAAO,SAAS;YACd,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;YACvB,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;YACvB,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;QACzB,IAAI;IACN;IAEA,+BAA+B;IAC/B,OAAO,aAAa,CAAS,EAAE,CAAS,EAAE,CAAS,EAAU;QAC3D,MAAM,CAAC,IAAI,IAAI,GAAG,GAAG;YAAC;YAAG;YAAG;SAAE,CAAC,GAAG,CAAC,CAAA;YACjC,IAAI,IAAI;YACR,OAAO,KAAK,UAAU,IAAI,QAAQ,KAAK,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,OAAO;QAClE;QACA,OAAO,SAAS,KAAK,SAAS,KAAK,SAAS;IAC9C;IAEA,2BAA2B;IAC3B,OAAO,iBAAiB,MAAc,EAAE,MAAc,EAAU;QAC9D,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAC;QAC3B,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAC;QAE3B,IAAI,CAAC,QAAQ,CAAC,MAAM,OAAO;QAE3B,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC;QACrD,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC;QAErD,MAAM,YAAY,KAAK,GAAG,CAAC,MAAM;QACjC,MAAM,UAAU,KAAK,GAAG,CAAC,MAAM;QAE/B,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,UAAU,IAAI;IAC7C;IAEA,wBAAwB;IACxB,OAAO,oBAAoB,UAAkB,EAAE,UAAkB,EAM/D;QACA,MAAM,QAAQ,IAAI,CAAC,gBAAgB,CAAC,YAAY;QAEhD,OAAO;YACL;YACA,IAAI,SAAS;YACb,KAAK,SAAS;YACd,SAAS,SAAS;YAClB,UAAU,SAAS;QACrB;IACF;AACF;AAGO,MAAM;IAGX,oCAAoC;IACpC,OAAO,UAAU,OAAoB,EAAE;QACrC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QAEpB,MAAM,oBAAoB,IAAI,CAAC,oBAAoB,CAAC;QACpD,IAAI,kBAAkB,MAAM,KAAK,GAAG;QAEpC,MAAM,eAAe,iBAAiB,CAAC,EAAE;QACzC,MAAM,cAAc,iBAAiB,CAAC,kBAAkB,MAAM,GAAG,EAAE;QAEnE,MAAM,gBAAgB,CAAC;YACrB,IAAI,EAAE,GAAG,KAAK,OAAO;YAErB,IAAI,EAAE,QAAQ,EAAE;gBACd,IAAI,SAAS,aAAa,KAAK,cAAc;oBAC3C,EAAE,cAAc;oBAChB,YAAY,KAAK;gBACnB;YACF,OAAO;gBACL,IAAI,SAAS,aAAa,KAAK,aAAa;oBAC1C,EAAE,cAAc;oBAChB,aAAa,KAAK;gBACpB;YACF;QACF;QAEA,QAAQ,gBAAgB,CAAC,WAAW;QACpC,aAAa,KAAK;QAElB,OAAO;YACL,QAAQ,mBAAmB,CAAC,WAAW;YACvC,IAAI,CAAC,SAAS,CAAC,GAAG;QACpB;IACF;IAEA,6BAA6B;IAC7B,OAAO,qBAAqB,SAAsB,EAAiB;QACjE,MAAM,qBAAqB;YACzB;YACA;YACA;YACA;YACA;YACA;YACA;SACD,CAAC,IAAI,CAAC;QAEP,OAAO,MAAM,IAAI,CAAC,UAAU,gBAAgB,CAAC,qBAC1C,MAAM,CAAC,CAAA,KAAM,IAAI,CAAC,SAAS,CAAC;IACjC;IAEA,8BAA8B;IAC9B,OAAe,UAAU,OAAoB,EAAW;QACtD,MAAM,QAAQ,OAAO,gBAAgB,CAAC;QACtC,OAAO,MAAM,OAAO,KAAK,UAClB,MAAM,UAAU,KAAK,YACrB,MAAM,OAAO,KAAK;IAC3B;IAEA,oCAAoC;IACpC,OAAO,eAAe;QACpB,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,EAAE;QAC1D,IAAI,UAAU;YACZ,MAAM,oBAAoB,IAAI,CAAC,oBAAoB,CAAC;YACpD,IAAI,kBAAkB,MAAM,GAAG,GAAG;gBAChC,iBAAiB,CAAC,EAAE,CAAC,KAAK;YAC5B;QACF;IACF;AACF;AAvEE,yKADW,oBACI,aAA2B,EAAE;AA0EvC,MAAM;IACX,6CAA6C;IAC7C,OAAO,aAA4C;YAAjC,SAAA,iEAAiB;QACjC,OAAO,AAAC,GAAY,OAAV,QAAO,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;IAC3D;IAEA,kCAAkC;IAClC,OAAO,kBAAkB,OAAoB,EAAE,WAAmB,EAAU;QAC1E,MAAM,SAAS,IAAI,CAAC,UAAU,CAAC;QAE/B,iDAAiD;QACjD,IAAI,cAAc,SAAS,cAAc,CAAC;QAC1C,IAAI,CAAC,aAAa;YAChB,cAAc,SAAS,aAAa,CAAC;YACrC,YAAY,EAAE,GAAG;YACjB,YAAY,SAAS,GAAG;YACxB,YAAY,WAAW,GAAG;YAC1B,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B;QAEA,0BAA0B;QAC1B,MAAM,sBAAsB,QAAQ,YAAY,CAAC;QACjD,MAAM,iBAAiB,sBACnB,AAAC,GAAyB,OAAvB,qBAAoB,KAAU,OAAP,UAC1B;QAEJ,QAAQ,YAAY,CAAC,oBAAoB;QAEzC,OAAO;IACT;IAEA,iCAAiC;IACjC,OAAO,iBAAiB,OAAoB,EAAE,SAAiB,EAAU;QACvE,MAAM,UAAU,IAAI,CAAC,UAAU,CAAC;QAEhC,2CAA2C;QAC3C,IAAI,eAAe,SAAS,cAAc,CAAC;QAC3C,IAAI,CAAC,cAAc;YACjB,eAAe,SAAS,aAAa,CAAC;YACtC,aAAa,EAAE,GAAG;YAClB,aAAa,SAAS,GAAG;YACzB,aAAa,WAAW,GAAG;YAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B;QAEA,QAAQ,YAAY,CAAC,mBAAmB;QAExC,OAAO;IACT;IAEA,0BAA0B;IAC1B,OAAO,eAAe,OAAe,EAA+C;YAA7C,WAAA,iEAAmC;QACxE,eAAe,QAAQ,CAAC,SAAS;IACnC;AACF;AAGO,MAAM;IACX,uCAAuC;IACvC,OAAO,cAAc,KAAkB,EAAE,YAAoB,EAAU;YAUrE,qBAAqB;QACrB;QAVA,MAAM,UAAU,aAAa,UAAU,CAAC;QAExC,uBAAuB;QACvB,MAAM,eAAe,SAAS,aAAa,CAAC;QAC5C,aAAa,EAAE,GAAG;QAClB,aAAa,SAAS,GAAG;QACzB,aAAa,YAAY,CAAC,QAAQ;QAClC,aAAa,WAAW,GAAG;SAG3B,oBAAA,MAAM,UAAU,cAAhB,wCAAA,kBAAkB,YAAY,CAAC,cAAc,MAAM,WAAW;QAE9D,0BAA0B;QAC1B,MAAM,YAAY,CAAC,gBAAgB;QACnC,MAAM,YAAY,CAAC,oBAAoB;QAEvC,iBAAiB;QACjB,aAAa,cAAc,CAAC,AAAC,SAAqB,OAAb,eAAgB;QAErD,OAAO;IACT;IAEA,qBAAqB;IACrB,OAAO,iBAAiB,KAAkB,EAAE,OAAe,EAAE;QAC3D,MAAM,eAAe,SAAS,cAAc,CAAC;QAC7C,IAAI,cAAc;YAChB,aAAa,MAAM;QACrB;QAEA,MAAM,eAAe,CAAC;QACtB,MAAM,eAAe,CAAC;IACxB;IAEA,uBAAuB;IACvB,OAAO,gBAAgB,KAAkB,EAAE,cAAsB,EAAU;YAUzE,qBAAqB;QACrB;QAVA,MAAM,YAAY,aAAa,UAAU,CAAC;QAE1C,yBAAyB;QACzB,MAAM,iBAAiB,SAAS,aAAa,CAAC;QAC9C,eAAe,EAAE,GAAG;QACpB,eAAe,SAAS,GAAG;QAC3B,eAAe,YAAY,CAAC,QAAQ;QACpC,eAAe,WAAW,GAAG;SAG7B,oBAAA,MAAM,UAAU,cAAhB,wCAAA,kBAAkB,YAAY,CAAC,gBAAgB,MAAM,WAAW;QAEhE,0BAA0B;QAC1B,MAAM,YAAY,CAAC,oBAAoB;QAEvC,OAAO;IACT;AACF;AAGO,MAAM;IACX,8BAA8B;IAC9B,OAAO,oBAMJ;QACD,MAAM,UAAsB,EAAE;QAE9B,wBAAwB;QACxB,MAAM,eAAe,SAAS,gBAAgB,CAAC;QAE/C,aAAa,OAAO,CAAC,CAAA;YACnB,MAAM,SAAS,OAAO,gBAAgB,CAAC;YACvC,MAAM,aAAa,OAAO,KAAK;YAC/B,MAAM,aAAa,OAAO,eAAe;YAEzC,IAAI,cAAc,cAAc,eAAe,oBAAoB;gBACjE,0CAA0C;gBAC1C,MAAM,QAAQ,cAAc,gBAAgB,CAAC,YAAY;gBACzD,MAAM,SAAS,SAAS;gBAExB,QAAQ,IAAI,CAAC;oBACX,SAAS;oBACT;oBACA;oBACA;oBACA;gBACF;YACF;QACF;QAEA,OAAO;IACT;IAEA,2BAA2B;IAC3B,OAAO,yBAIL;QACA,MAAM,oBAAoB,mBAAmB,oBAAoB,CAAC,SAAS,IAAI;QAC/E,MAAM,SAAmB,EAAE;QAE3B,qCAAqC;QACrC,kBAAkB,OAAO,CAAC,CAAA;YACxB,MAAM,SAAS,OAAO,gBAAgB,CAAC;YACvC,IAAI,CAAC,OAAO,OAAO,IAAI,CAAC,OAAO,SAAS,EAAE;gBACxC,OAAO,IAAI,CAAC,AAAC,oCAAmD,OAAhB,QAAQ,OAAO;YACjE;QACF;QAEA,OAAO;YACL,mBAAmB,kBAAkB,MAAM;YAC3C,UAAU;YACV;QACF;IACF;IAEA,gCAAgC;IAChC,OAAO,iBAKL;QACA,MAAM,gBAAgB,IAAI,CAAC,iBAAiB;QAC5C,MAAM,qBAAqB,IAAI,CAAC,sBAAsB;QAEtD,oBAAoB;QACpB,MAAM,aAAa,SAAS,gBAAgB,CAAC,mCAAmC,MAAM;QAEtF,0BAA0B;QAC1B,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,gBAAgB,CAAC;QACtD,MAAM,mBAAmB,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;gBAChD,OAAO,SAAS,QAAQ,OAAO,CAAC,MAAM,CAAC;gBACvC,MAAM,QAAQ,WAAW,IAAI;YAC/B,CAAC;QAED,OAAO;YACL;YACA;YACA;YACA;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 1705, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/hooks/use-accessibility.ts"], "sourcesContent": ["/**\n * Accessibility Hooks\n * React hooks for accessibility features and WCAG compliance\n */\n\n'use client'\n\nimport { useEffect, useRef, useState, useCallback } from 'react'\nimport { \n  AriaLiveRegion, \n  KeyboardNavigation, \n  ScreenReader, \n  FormAccessibility,\n  AccessibilityTester \n} from '@/lib/accessibility-utils'\n\n// Hook for ARIA live announcements\nexport function useAriaLive() {\n  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {\n    AriaLiveRegion.announce(message, priority)\n  }, [])\n\n  const clear = useCallback((priority: 'polite' | 'assertive' = 'polite') => {\n    AriaLiveRegion.clear(priority)\n  }, [])\n\n  return { announce, clear }\n}\n\n// Hook for focus management\nexport function useFocusManagement() {\n  const [focusedElement, setFocusedElement] = useState<HTMLElement | null>(null)\n  \n  const saveFocus = useCallback(() => {\n    setFocusedElement(document.activeElement as HTMLElement)\n  }, [])\n\n  const restoreFocus = useCallback(() => {\n    if (focusedElement && focusedElement.focus) {\n      focusedElement.focus()\n    }\n  }, [focusedElement])\n\n  const focusFirst = useCallback((container: HTMLElement) => {\n    const focusableElements = KeyboardNavigation.getFocusableElements(container)\n    if (focusableElements.length > 0) {\n      focusableElements[0].focus()\n    }\n  }, [])\n\n  return {\n    saveFocus,\n    restoreFocus,\n    focusFirst,\n    focusedElement\n  }\n}\n\n// Hook for focus trap (modals, dialogs)\nexport function useFocusTrap(isActive: boolean = false) {\n  const containerRef = useRef<HTMLElement>(null)\n  const cleanupRef = useRef<(() => void) | null>(null)\n\n  useEffect(() => {\n    if (isActive && containerRef.current) {\n      const cleanup = KeyboardNavigation.trapFocus(containerRef.current)\n      cleanupRef.current = cleanup || null\n    }\n\n    return () => {\n      if (cleanupRef.current) {\n        cleanupRef.current()\n        cleanupRef.current = null\n      }\n    }\n  }, [isActive])\n\n  return containerRef\n}\n\n// Hook for keyboard navigation\nexport function useKeyboardNavigation(handlers: {\n  onEscape?: () => void\n  onEnter?: () => void\n  onArrowUp?: () => void\n  onArrowDown?: () => void\n  onArrowLeft?: () => void\n  onArrowRight?: () => void\n  onTab?: (e: KeyboardEvent) => void\n} = {}) {\n  const elementRef = useRef<HTMLElement>(null)\n\n  useEffect(() => {\n    const element = elementRef.current\n    if (!element) return\n\n    const handleKeyDown = (e: KeyboardEvent) => {\n      switch (e.key) {\n        case 'Escape':\n          handlers.onEscape?.()\n          break\n        case 'Enter':\n          handlers.onEnter?.()\n          break\n        case 'ArrowUp':\n          e.preventDefault()\n          handlers.onArrowUp?.()\n          break\n        case 'ArrowDown':\n          e.preventDefault()\n          handlers.onArrowDown?.()\n          break\n        case 'ArrowLeft':\n          handlers.onArrowLeft?.()\n          break\n        case 'ArrowRight':\n          handlers.onArrowRight?.()\n          break\n        case 'Tab':\n          handlers.onTab?.(e)\n          break\n      }\n    }\n\n    element.addEventListener('keydown', handleKeyDown)\n    return () => element.removeEventListener('keydown', handleKeyDown)\n  }, [handlers])\n\n  return elementRef\n}\n\n// Hook for form accessibility\nexport function useFormAccessibility() {\n  const addError = useCallback((field: HTMLElement, message: string) => {\n    return FormAccessibility.addFieldError(field, message)\n  }, [])\n\n  const removeError = useCallback((field: HTMLElement, errorId: string) => {\n    FormAccessibility.removeFieldError(field, errorId)\n  }, [])\n\n  const addSuccess = useCallback((field: HTMLElement, message: string) => {\n    return FormAccessibility.addFieldSuccess(field, message)\n  }, [])\n\n  return {\n    addError,\n    removeError,\n    addSuccess\n  }\n}\n\n// Hook for screen reader announcements\nexport function useScreenReader() {\n  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {\n    ScreenReader.announceStatus(message, priority)\n  }, [])\n\n  const createDescribedBy = useCallback((element: HTMLElement, description: string) => {\n    return ScreenReader.createDescribedBy(element, description)\n  }, [])\n\n  const createLabelledBy = useCallback((element: HTMLElement, labelText: string) => {\n    return ScreenReader.createLabelledBy(element, labelText)\n  }, [])\n\n  return {\n    announce,\n    createDescribedBy,\n    createLabelledBy\n  }\n}\n\n// Hook for accessibility testing\nexport function useAccessibilityTesting() {\n  const [report, setReport] = useState<ReturnType<typeof AccessibilityTester.generateReport> | null>(null)\n  const [isLoading, setIsLoading] = useState(false)\n\n  const runTests = useCallback(async () => {\n    setIsLoading(true)\n    \n    // Wait for DOM to be ready\n    await new Promise(resolve => setTimeout(resolve, 100))\n    \n    try {\n      const testReport = AccessibilityTester.generateReport()\n      setReport(testReport)\n    } catch (error) {\n      console.error('Accessibility testing failed:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }, [])\n\n  const getScore = useCallback(() => {\n    if (!report) return null\n\n    const colorContrastPassed = report.colorContrast.filter(test => test.passes).length\n    const colorContrastTotal = report.colorContrast.length\n    const colorContrastScore = colorContrastTotal > 0 ? (colorContrastPassed / colorContrastTotal) * 100 : 100\n\n    const keyboardIssues = report.keyboardNavigation.issues.length\n    const keyboardScore = Math.max(0, 100 - (keyboardIssues * 10))\n\n    const ariaScore = report.ariaLabels > 0 ? 100 : 50\n\n    // Check heading structure\n    let headingScore = 100\n    for (let i = 1; i < report.headingStructure.length; i++) {\n      const current = report.headingStructure[i]\n      const previous = report.headingStructure[i - 1]\n      if (current.level > previous.level + 1) {\n        headingScore -= 20 // Penalty for skipping heading levels\n      }\n    }\n\n    const overallScore = (colorContrastScore + keyboardScore + ariaScore + headingScore) / 4\n\n    return {\n      overall: Math.round(overallScore),\n      colorContrast: Math.round(colorContrastScore),\n      keyboard: Math.round(keyboardScore),\n      aria: Math.round(ariaScore),\n      headings: Math.round(headingScore)\n    }\n  }, [report])\n\n  return {\n    report,\n    isLoading,\n    runTests,\n    getScore\n  }\n}\n\n// Hook for reduced motion preference\nexport function useReducedMotion() {\n  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false)\n\n  useEffect(() => {\n    if (typeof window === 'undefined') return\n\n    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')\n    setPrefersReducedMotion(mediaQuery.matches)\n\n    const handleChange = (e: MediaQueryListEvent) => {\n      setPrefersReducedMotion(e.matches)\n    }\n\n    mediaQuery.addEventListener('change', handleChange)\n    return () => mediaQuery.removeEventListener('change', handleChange)\n  }, [])\n\n  return prefersReducedMotion\n}\n\n// Hook for high contrast preference\nexport function useHighContrast() {\n  const [prefersHighContrast, setPrefersHighContrast] = useState(false)\n\n  useEffect(() => {\n    if (typeof window === 'undefined') return\n\n    const mediaQuery = window.matchMedia('(prefers-contrast: high)')\n    setPrefersHighContrast(mediaQuery.matches)\n\n    const handleChange = (e: MediaQueryListEvent) => {\n      setPrefersHighContrast(e.matches)\n    }\n\n    mediaQuery.addEventListener('change', handleChange)\n    return () => mediaQuery.removeEventListener('change', handleChange)\n  }, [])\n\n  return prefersHighContrast\n}\n\n// Hook for color scheme preference\nexport function useColorScheme() {\n  const [colorScheme, setColorScheme] = useState<'light' | 'dark'>('light')\n\n  useEffect(() => {\n    if (typeof window === 'undefined') return\n\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')\n    setColorScheme(mediaQuery.matches ? 'dark' : 'light')\n\n    const handleChange = (e: MediaQueryListEvent) => {\n      setColorScheme(e.matches ? 'dark' : 'light')\n    }\n\n    mediaQuery.addEventListener('change', handleChange)\n    return () => mediaQuery.removeEventListener('change', handleChange)\n  }, [])\n\n  return colorScheme\n}\n\n// Hook for comprehensive accessibility state\nexport function useAccessibility() {\n  const { announce, clear } = useAriaLive()\n  const { saveFocus, restoreFocus, focusFirst } = useFocusManagement()\n  const { announce: screenReaderAnnounce } = useScreenReader()\n  const prefersReducedMotion = useReducedMotion()\n  const prefersHighContrast = useHighContrast()\n  const colorScheme = useColorScheme()\n\n  return {\n    // Announcements\n    announce,\n    clear,\n    screenReaderAnnounce,\n    \n    // Focus management\n    saveFocus,\n    restoreFocus,\n    focusFirst,\n    \n    // User preferences\n    prefersReducedMotion,\n    prefersHighContrast,\n    colorScheme,\n    \n    // Utilities\n    generateId: ScreenReader.generateId\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;AAID;AACA;;AAHA;;;AAYO,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE,SAAC;gBAAiB,4EAAmC;YAChF,uIAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC,SAAS;QACnC;4CAAG,EAAE;IAEL,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0CAAE;gBAAC,4EAAmC;YAC5D,uIAAA,CAAA,iBAAc,CAAC,KAAK,CAAC;QACvB;yCAAG,EAAE;IAEL,OAAO;QAAE;QAAU;IAAM;AAC3B;GAVgB;AAaT,SAAS;;IACd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAEzE,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YAC5B,kBAAkB,SAAS,aAAa;QAC1C;oDAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE;YAC/B,IAAI,kBAAkB,eAAe,KAAK,EAAE;gBAC1C,eAAe,KAAK;YACtB;QACF;uDAAG;QAAC;KAAe;IAEnB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YAC9B,MAAM,oBAAoB,uIAAA,CAAA,qBAAkB,CAAC,oBAAoB,CAAC;YAClE,IAAI,kBAAkB,MAAM,GAAG,GAAG;gBAChC,iBAAiB,CAAC,EAAE,CAAC,KAAK;YAC5B;QACF;qDAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;IA1BgB;AA6BT,SAAS;QAAa,WAAA,iEAAoB;;IAC/C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAe;IACzC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAuB;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,YAAY,aAAa,OAAO,EAAE;gBACpC,MAAM,UAAU,uIAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC,aAAa,OAAO;gBACjE,WAAW,OAAO,GAAG,WAAW;YAClC;YAEA;0CAAO;oBACL,IAAI,WAAW,OAAO,EAAE;wBACtB,WAAW,OAAO;wBAClB,WAAW,OAAO,GAAG;oBACvB;gBACF;;QACF;iCAAG;QAAC;KAAS;IAEb,OAAO;AACT;IAnBgB;AAsBT,SAAS;QAAsB,WAAA,iEAQlC,CAAC;;IACH,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAe;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,MAAM,UAAU,WAAW,OAAO;YAClC,IAAI,CAAC,SAAS;YAEd,MAAM;iEAAgB,CAAC;oBACrB,OAAQ,EAAE,GAAG;wBACX,KAAK;gCACH;6BAAA,qBAAA,SAAS,QAAQ,cAAjB,yCAAA,wBAAA;4BACA;wBACF,KAAK;gCACH;6BAAA,oBAAA,SAAS,OAAO,cAAhB,wCAAA,uBAAA;4BACA;wBACF,KAAK;gCAEH;4BADA,EAAE,cAAc;6BAChB,sBAAA,SAAS,SAAS,cAAlB,0CAAA,yBAAA;4BACA;wBACF,KAAK;gCAEH;4BADA,EAAE,cAAc;6BAChB,wBAAA,SAAS,WAAW,cAApB,4CAAA,2BAAA;4BACA;wBACF,KAAK;gCACH;6BAAA,wBAAA,SAAS,WAAW,cAApB,4CAAA,2BAAA;4BACA;wBACF,KAAK;gCACH;6BAAA,yBAAA,SAAS,YAAY,cAArB,6CAAA,4BAAA;4BACA;wBACF,KAAK;gCACH;6BAAA,kBAAA,SAAS,KAAK,cAAd,sCAAA,qBAAA,UAAiB;4BACjB;oBACJ;gBACF;;YAEA,QAAQ,gBAAgB,CAAC,WAAW;YACpC;mDAAO,IAAM,QAAQ,mBAAmB,CAAC,WAAW;;QACtD;0CAAG;QAAC;KAAS;IAEb,OAAO;AACT;IAhDgB;AAmDT,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC,OAAoB;YAChD,OAAO,uIAAA,CAAA,oBAAiB,CAAC,aAAa,CAAC,OAAO;QAChD;qDAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC,OAAoB;YACnD,uIAAA,CAAA,oBAAiB,CAAC,gBAAgB,CAAC,OAAO;QAC5C;wDAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC,OAAoB;YAClD,OAAO,uIAAA,CAAA,oBAAiB,CAAC,eAAe,CAAC,OAAO;QAClD;uDAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;IACF;AACF;IAlBgB;AAqBT,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,SAAC;gBAAiB,4EAAmC;YAChF,uIAAA,CAAA,eAAY,CAAC,cAAc,CAAC,SAAS;QACvC;gDAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC,SAAsB;YAC3D,OAAO,uIAAA,CAAA,eAAY,CAAC,iBAAiB,CAAC,SAAS;QACjD;yDAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC,SAAsB;YAC1D,OAAO,uIAAA,CAAA,eAAY,CAAC,gBAAgB,CAAC,SAAS;QAChD;wDAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;IACF;AACF;IAlBgB;AAqBT,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgE;IACnG,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE;YAC3B,aAAa;YAEb,2BAA2B;YAC3B,MAAM,IAAI;iEAAQ,CAAA,UAAW,WAAW,SAAS;;YAEjD,IAAI;gBACF,MAAM,aAAa,uIAAA,CAAA,sBAAmB,CAAC,cAAc;gBACrD,UAAU;YACZ,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;YACjD,SAAU;gBACR,aAAa;YACf;QACF;wDAAG,EAAE;IAEL,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE;YAC3B,IAAI,CAAC,QAAQ,OAAO;YAEpB,MAAM,sBAAsB,OAAO,aAAa,CAAC,MAAM;iEAAC,CAAA,OAAQ,KAAK,MAAM;gEAAE,MAAM;YACnF,MAAM,qBAAqB,OAAO,aAAa,CAAC,MAAM;YACtD,MAAM,qBAAqB,qBAAqB,IAAI,AAAC,sBAAsB,qBAAsB,MAAM;YAEvG,MAAM,iBAAiB,OAAO,kBAAkB,CAAC,MAAM,CAAC,MAAM;YAC9D,MAAM,gBAAgB,KAAK,GAAG,CAAC,GAAG,MAAO,iBAAiB;YAE1D,MAAM,YAAY,OAAO,UAAU,GAAG,IAAI,MAAM;YAEhD,0BAA0B;YAC1B,IAAI,eAAe;YACnB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,gBAAgB,CAAC,MAAM,EAAE,IAAK;gBACvD,MAAM,UAAU,OAAO,gBAAgB,CAAC,EAAE;gBAC1C,MAAM,WAAW,OAAO,gBAAgB,CAAC,IAAI,EAAE;gBAC/C,IAAI,QAAQ,KAAK,GAAG,SAAS,KAAK,GAAG,GAAG;oBACtC,gBAAgB,IAAG,sCAAsC;gBAC3D;YACF;YAEA,MAAM,eAAe,CAAC,qBAAqB,gBAAgB,YAAY,YAAY,IAAI;YAEvF,OAAO;gBACL,SAAS,KAAK,KAAK,CAAC;gBACpB,eAAe,KAAK,KAAK,CAAC;gBAC1B,UAAU,KAAK,KAAK,CAAC;gBACrB,MAAM,KAAK,KAAK,CAAC;gBACjB,UAAU,KAAK,KAAK,CAAC;YACvB;QACF;wDAAG;QAAC;KAAO;IAEX,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;IA3DgB;AA8DT,SAAS;;IACd,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;;YAEA,MAAM,aAAa,OAAO,UAAU,CAAC;YACrC,wBAAwB,WAAW,OAAO;YAE1C,MAAM;2DAAe,CAAC;oBACpB,wBAAwB,EAAE,OAAO;gBACnC;;YAEA,WAAW,gBAAgB,CAAC,UAAU;YACtC;8CAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;;QACxD;qCAAG,EAAE;IAEL,OAAO;AACT;IAlBgB;AAqBT,SAAS;;IACd,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;;YAEA,MAAM,aAAa,OAAO,UAAU,CAAC;YACrC,uBAAuB,WAAW,OAAO;YAEzC,MAAM;0DAAe,CAAC;oBACpB,uBAAuB,EAAE,OAAO;gBAClC;;YAEA,WAAW,gBAAgB,CAAC,UAAU;YACtC;6CAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;;QACxD;oCAAG,EAAE;IAEL,OAAO;AACT;IAlBgB;AAqBT,SAAS;;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAEjE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;;YAEA,MAAM,aAAa,OAAO,UAAU,CAAC;YACrC,eAAe,WAAW,OAAO,GAAG,SAAS;YAE7C,MAAM;yDAAe,CAAC;oBACpB,eAAe,EAAE,OAAO,GAAG,SAAS;gBACtC;;YAEA,WAAW,gBAAgB,CAAC,UAAU;YACtC;4CAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;;QACxD;mCAAG,EAAE;IAEL,OAAO;AACT;IAlBgB;AAqBT,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAC5B,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,GAAG;IAChD,MAAM,EAAE,UAAU,oBAAoB,EAAE,GAAG;IAC3C,MAAM,uBAAuB;IAC7B,MAAM,sBAAsB;IAC5B,MAAM,cAAc;IAEpB,OAAO;QACL,gBAAgB;QAChB;QACA;QACA;QAEA,mBAAmB;QACnB;QACA;QACA;QAEA,mBAAmB;QACnB;QACA;QACA;QAEA,YAAY;QACZ,YAAY,uIAAA,CAAA,eAAY,CAAC,UAAU;IACrC;AACF;KA3BgB;;QACc;QACoB;QACL;QACd;QACD;QACR", "debugId": null}}, {"offset": {"line": 2084, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/form-feedback.tsx"], "sourcesContent": ["/**\n * Form Feedback Components\n * Real-time form validation, error handling, and user guidance system\n */\n\n'use client'\n\nimport React, { useState, useEffect, useRef } from 'react'\nimport { cn } from '@/lib/utils'\nimport { CheckCircle, AlertCircle, Info, X, Loader2 } from 'lucide-react'\nimport { useAccessibility } from '@/hooks/use-accessibility'\n\n// Feedback types\nexport type FeedbackType = 'success' | 'error' | 'warning' | 'info' | 'loading'\n\ninterface FeedbackMessage {\n  id: string\n  type: FeedbackType\n  message: string\n  field?: string\n  timestamp: number\n  duration?: number\n  persistent?: boolean\n}\n\n// Form feedback context\ninterface FormFeedbackContextType {\n  messages: FeedbackMessage[]\n  addMessage: (message: Omit<FeedbackMessage, 'id' | 'timestamp'>) => void\n  removeMessage: (id: string) => void\n  clearMessages: (field?: string) => void\n  clearAllMessages: () => void\n}\n\nconst FormFeedbackContext = React.createContext<FormFeedbackContextType | null>(null)\n\n// Form feedback provider\nexport function FormFeedbackProvider({ children }: { children: React.ReactNode }) {\n  const [messages, setMessages] = useState<FeedbackMessage[]>([])\n  const { announce } = useAccessibility()\n\n  const addMessage = (message: Omit<FeedbackMessage, 'id' | 'timestamp'>) => {\n    const id = `feedback-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`\n    const newMessage: FeedbackMessage = {\n      ...message,\n      id,\n      timestamp: Date.now(),\n      duration: message.duration || (message.type === 'error' ? 0 : 5000) // Errors persist, others auto-dismiss\n    }\n\n    setMessages(prev => [...prev, newMessage])\n\n    // Announce to screen readers\n    const priority = message.type === 'error' ? 'assertive' : 'polite'\n    announce(message.message, priority)\n\n    // Auto-remove non-persistent messages\n    if (newMessage.duration && newMessage.duration > 0) {\n      setTimeout(() => {\n        setMessages(prev => prev.filter(m => m.id !== id))\n      }, newMessage.duration)\n    }\n  }\n\n  const removeMessage = (id: string) => {\n    setMessages(prev => prev.filter(m => m.id !== id))\n  }\n\n  const clearMessages = (field?: string) => {\n    if (field) {\n      setMessages(prev => prev.filter(m => m.field !== field))\n    } else {\n      setMessages([])\n    }\n  }\n\n  const clearAllMessages = () => {\n    setMessages([])\n  }\n\n  return (\n    <FormFeedbackContext.Provider value={{\n      messages,\n      addMessage,\n      removeMessage,\n      clearMessages,\n      clearAllMessages\n    }}>\n      {children}\n    </FormFeedbackContext.Provider>\n  )\n}\n\n// Hook to use form feedback\nexport function useFormFeedback() {\n  const context = React.useContext(FormFeedbackContext)\n  if (!context) {\n    throw new Error('useFormFeedback must be used within FormFeedbackProvider')\n  }\n  return context\n}\n\n// Individual feedback message component\nexport function FeedbackMessage({ \n  message, \n  onDismiss,\n  className \n}: { \n  message: FeedbackMessage\n  onDismiss?: () => void\n  className?: string \n}) {\n  const getIcon = () => {\n    switch (message.type) {\n      case 'success':\n        return <CheckCircle className=\"h-4 w-4 text-green-600\" />\n      case 'error':\n        return <AlertCircle className=\"h-4 w-4 text-red-600\" />\n      case 'warning':\n        return <AlertCircle className=\"h-4 w-4 text-yellow-600\" />\n      case 'info':\n        return <Info className=\"h-4 w-4 text-blue-600\" />\n      case 'loading':\n        return <Loader2 className=\"h-4 w-4 text-gray-600 animate-spin\" />\n      default:\n        return null\n    }\n  }\n\n  const getStyles = () => {\n    switch (message.type) {\n      case 'success':\n        return 'bg-green-50 border-green-200 text-green-800'\n      case 'error':\n        return 'bg-red-50 border-red-200 text-red-800'\n      case 'warning':\n        return 'bg-yellow-50 border-yellow-200 text-yellow-800'\n      case 'info':\n        return 'bg-blue-50 border-blue-200 text-blue-800'\n      case 'loading':\n        return 'bg-gray-50 border-gray-200 text-gray-800'\n      default:\n        return 'bg-gray-50 border-gray-200 text-gray-800'\n    }\n  }\n\n  return (\n    <div\n      className={cn(\n        'flex items-start gap-2 p-3 rounded-md border text-sm',\n        getStyles(),\n        className\n      )}\n      role=\"alert\"\n      aria-live={message.type === 'error' ? 'assertive' : 'polite'}\n    >\n      {getIcon()}\n      <div className=\"flex-1 min-w-0\">\n        <p className=\"break-words\">{message.message}</p>\n      </div>\n      {onDismiss && (\n        <button\n          onClick={onDismiss}\n          className=\"flex-shrink-0 p-1 hover:bg-black/5 rounded\"\n          aria-label=\"Mesajı kapat\"\n        >\n          <X className=\"h-3 w-3\" />\n        </button>\n      )}\n    </div>\n  )\n}\n\n// Field-specific feedback component\nexport function FieldFeedback({ \n  field, \n  className \n}: { \n  field: string\n  className?: string \n}) {\n  const { messages, removeMessage } = useFormFeedback()\n  const fieldMessages = messages.filter(m => m.field === field)\n\n  if (fieldMessages.length === 0) return null\n\n  return (\n    <div className={cn('space-y-2 mt-1', className)}>\n      {fieldMessages.map(message => (\n        <FeedbackMessage\n          key={message.id}\n          message={message}\n          onDismiss={() => removeMessage(message.id)}\n        />\n      ))}\n    </div>\n  )\n}\n\n// Global feedback container\nexport function GlobalFeedback({ \n  className,\n  position = 'top-right' \n}: { \n  className?: string\n  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center'\n}) {\n  const { messages, removeMessage } = useFormFeedback()\n  const globalMessages = messages.filter(m => !m.field)\n\n  const getPositionStyles = () => {\n    switch (position) {\n      case 'top-right':\n        return 'fixed top-4 right-4 z-50'\n      case 'top-left':\n        return 'fixed top-4 left-4 z-50'\n      case 'bottom-right':\n        return 'fixed bottom-4 right-4 z-50'\n      case 'bottom-left':\n        return 'fixed bottom-4 left-4 z-50'\n      case 'top-center':\n        return 'fixed top-4 left-1/2 transform -translate-x-1/2 z-50'\n      default:\n        return 'fixed top-4 right-4 z-50'\n    }\n  }\n\n  if (globalMessages.length === 0) return null\n\n  return (\n    <div className={cn(getPositionStyles(), 'w-96 max-w-[calc(100vw-2rem)]', className)}>\n      <div className=\"space-y-2\">\n        {globalMessages.map(message => (\n          <FeedbackMessage\n            key={message.id}\n            message={message}\n            onDismiss={() => removeMessage(message.id)}\n            className=\"shadow-lg\"\n          />\n        ))}\n      </div>\n    </div>\n  )\n}\n\n// Form validation hook with feedback integration\nexport function useFormValidation<T extends Record<string, any>>(\n  initialValues: T,\n  validationRules: Record<keyof T, (value: any) => string | null>\n) {\n  const [values, setValues] = useState<T>(initialValues)\n  const [errors, setErrors] = useState<Partial<Record<keyof T, string>>>({})\n  const [touched, setTouched] = useState<Partial<Record<keyof T, boolean>>>({})\n  const [isValidating, setIsValidating] = useState(false)\n  const { addMessage, clearMessages } = useFormFeedback()\n\n  const validateField = (field: keyof T, value: any) => {\n    const rule = validationRules[field]\n    if (!rule) return null\n\n    const error = rule(value)\n    return error\n  }\n\n  const validateAllFields = () => {\n    const newErrors: Partial<Record<keyof T, string>> = {}\n    let hasErrors = false\n\n    Object.keys(values).forEach(key => {\n      const field = key as keyof T\n      const error = validateField(field, values[field])\n      if (error) {\n        newErrors[field] = error\n        hasErrors = true\n      }\n    })\n\n    setErrors(newErrors)\n    return !hasErrors\n  }\n\n  const setValue = (field: keyof T, value: any) => {\n    setValues(prev => ({ ...prev, [field]: value }))\n    \n    // Clear previous field messages\n    clearMessages(field as string)\n    \n    // Validate if field has been touched\n    if (touched[field]) {\n      const error = validateField(field, value)\n      if (error) {\n        setErrors(prev => ({ ...prev, [field]: error }))\n        addMessage({\n          type: 'error',\n          message: error,\n          field: field as string\n        })\n      } else {\n        setErrors(prev => {\n          const newErrors = { ...prev }\n          delete newErrors[field]\n          return newErrors\n        })\n        addMessage({\n          type: 'success',\n          message: 'Geçerli',\n          field: field as string,\n          duration: 2000\n        })\n      }\n    }\n  }\n\n  const markFieldTouched = (field: keyof T) => {\n    setTouched(prev => ({ ...prev, [field]: true }))\n  }\n\n  const handleSubmit = async (onSubmit: (values: T) => Promise<void> | void) => {\n    setIsValidating(true)\n    \n    // Mark all fields as touched\n    const allTouched = Object.keys(values).reduce((acc, key) => {\n      acc[key as keyof T] = true\n      return acc\n    }, {} as Partial<Record<keyof T, boolean>>)\n    setTouched(allTouched)\n\n    // Validate all fields\n    const isValid = validateAllFields()\n    \n    if (!isValid) {\n      addMessage({\n        type: 'error',\n        message: 'Lütfen form hatalarını düzeltin',\n        duration: 5000\n      })\n      setIsValidating(false)\n      return\n    }\n\n    try {\n      await onSubmit(values)\n      addMessage({\n        type: 'success',\n        message: 'Form başarıyla gönderildi',\n        duration: 3000\n      })\n    } catch (error) {\n      addMessage({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Bir hata oluştu',\n        duration: 0 // Persist error messages\n      })\n    } finally {\n      setIsValidating(false)\n    }\n  }\n\n  const reset = () => {\n    setValues(initialValues)\n    setErrors({})\n    setTouched({})\n    clearMessages()\n  }\n\n  return {\n    values,\n    errors,\n    touched,\n    isValidating,\n    setValue,\n    setTouched: markFieldTouched,\n    validateField,\n    validateAllFields,\n    handleSubmit,\n    reset,\n    isValid: Object.keys(errors).length === 0\n  }\n}\n\n// Progress indicator for multi-step forms\nexport function FormProgress({ \n  currentStep, \n  totalSteps, \n  stepLabels,\n  className \n}: {\n  currentStep: number\n  totalSteps: number\n  stepLabels?: string[]\n  className?: string\n}) {\n  const progress = (currentStep / totalSteps) * 100\n\n  return (\n    <div className={cn('w-full', className)}>\n      <div className=\"flex justify-between text-sm text-gray-600 mb-2\">\n        <span>Adım {currentStep} / {totalSteps}</span>\n        <span>{Math.round(progress)}% tamamlandı</span>\n      </div>\n      \n      <div className=\"w-full bg-gray-200 rounded-full h-2 mb-4\">\n        <div \n          className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n          style={{ width: `${progress}%` }}\n          role=\"progressbar\"\n          aria-valuenow={currentStep}\n          aria-valuemin={1}\n          aria-valuemax={totalSteps}\n          aria-label={`Form ilerlemesi: ${currentStep} / ${totalSteps}`}\n        />\n      </div>\n\n      {stepLabels && (\n        <div className=\"flex justify-between\">\n          {stepLabels.map((label, index) => (\n            <div\n              key={index}\n              className={cn(\n                'text-xs text-center flex-1',\n                index + 1 <= currentStep ? 'text-blue-600 font-medium' : 'text-gray-400'\n              )}\n            >\n              {label}\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;AAID;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AALA;;;;;AA6BA,MAAM,oCAAsB,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAiC;AAGzE,SAAS,qBAAqB,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAC9D,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD;IAEpC,MAAM,aAAa,CAAC;QAClB,MAAM,KAAK,AAAC,YAAyB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QAC1E,MAAM,aAA8B;YAClC,GAAG,OAAO;YACV;YACA,WAAW,KAAK,GAAG;YACnB,UAAU,QAAQ,QAAQ,IAAI,CAAC,QAAQ,IAAI,KAAK,UAAU,IAAI,IAAI,EAAE,sCAAsC;QAC5G;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAW;QAEzC,6BAA6B;QAC7B,MAAM,WAAW,QAAQ,IAAI,KAAK,UAAU,cAAc;QAC1D,SAAS,QAAQ,OAAO,EAAE;QAE1B,sCAAsC;QACtC,IAAI,WAAW,QAAQ,IAAI,WAAW,QAAQ,GAAG,GAAG;YAClD,WAAW;gBACT,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAChD,GAAG,WAAW,QAAQ;QACxB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAChD;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,OAAO;YACT,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;QACnD,OAAO;YACL,YAAY,EAAE;QAChB;IACF;IAEA,MAAM,mBAAmB;QACvB,YAAY,EAAE;IAChB;IAEA,qBACE,6LAAC,oBAAoB,QAAQ;QAAC,OAAO;YACnC;YACA;YACA;YACA;YACA;QACF;kBACG;;;;;;AAGP;GAtDgB;;QAEO,uIAAA,CAAA,mBAAgB;;;KAFvB;AAyDT,SAAS;;IACd,MAAM,UAAU,6JAAA,CAAA,UAAK,CAAC,UAAU,CAAC;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB;AAST,SAAS,gBAAgB,KAQ/B;QAR+B,EAC9B,OAAO,EACP,SAAS,EACT,SAAS,EAKV,GAR+B;IAS9B,MAAM,UAAU;QACd,OAAQ,QAAQ,IAAI;YAClB,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,oNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B;gBACE,OAAO;QACX;IACF;IAEA,MAAM,YAAY;QAChB,OAAQ,QAAQ,IAAI;YAClB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA,aACA;QAEF,MAAK;QACL,aAAW,QAAQ,IAAI,KAAK,UAAU,cAAc;;YAEnD;0BACD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAe,QAAQ,OAAO;;;;;;;;;;;YAE5C,2BACC,6LAAC;gBACC,SAAS;gBACT,WAAU;gBACV,cAAW;0BAEX,cAAA,6LAAC,+LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAKvB;MApEgB;AAuET,SAAS,cAAc,KAM7B;QAN6B,EAC5B,KAAK,EACL,SAAS,EAIV,GAN6B;;IAO5B,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG;IACpC,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;IAEvD,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;IAEvC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;kBAClC,cAAc,GAAG,CAAC,CAAA,wBACjB,6LAAC;gBAEC,SAAS;gBACT,WAAW,IAAM,cAAc,QAAQ,EAAE;eAFpC,QAAQ,EAAE;;;;;;;;;;AAOzB;IAvBgB;;QAOsB;;;MAPtB;AA0BT,SAAS,eAAe,KAM9B;QAN8B,EAC7B,SAAS,EACT,WAAW,WAAW,EAIvB,GAN8B;;IAO7B,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG;IACpC,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,KAAK;IAEpD,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,eAAe,MAAM,KAAK,GAAG,OAAO;IAExC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qBAAqB,iCAAiC;kBACvE,cAAA,6LAAC;YAAI,WAAU;sBACZ,eAAe,GAAG,CAAC,CAAA,wBAClB,6LAAC;oBAEC,SAAS;oBACT,WAAW,IAAM,cAAc,QAAQ,EAAE;oBACzC,WAAU;mBAHL,QAAQ,EAAE;;;;;;;;;;;;;;;AAS3B;IA3CgB;;QAOsB;;;MAPtB;AA8CT,SAAS,kBACd,aAAgB,EAChB,eAA+D;;IAE/D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAK;IACxC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoC,CAAC;IACxE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqC,CAAC;IAC3E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEtC,MAAM,gBAAgB,CAAC,OAAgB;QACrC,MAAM,OAAO,eAAe,CAAC,MAAM;QACnC,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,QAAQ,KAAK;QACnB,OAAO;IACT;IAEA,MAAM,oBAAoB;QACxB,MAAM,YAA8C,CAAC;QACrD,IAAI,YAAY;QAEhB,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;YAC1B,MAAM,QAAQ;YACd,MAAM,QAAQ,cAAc,OAAO,MAAM,CAAC,MAAM;YAChD,IAAI,OAAO;gBACT,SAAS,CAAC,MAAM,GAAG;gBACnB,YAAY;YACd;QACF;QAEA,UAAU;QACV,OAAO,CAAC;IACV;IAEA,MAAM,WAAW,CAAC,OAAgB;QAChC,UAAU,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAE9C,gCAAgC;QAChC,cAAc;QAEd,qCAAqC;QACrC,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,MAAM,QAAQ,cAAc,OAAO;YACnC,IAAI,OAAO;gBACT,UAAU,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,CAAC,MAAM,EAAE;oBAAM,CAAC;gBAC9C,WAAW;oBACT,MAAM;oBACN,SAAS;oBACT,OAAO;gBACT;YACF,OAAO;gBACL,UAAU,CAAA;oBACR,MAAM,YAAY;wBAAE,GAAG,IAAI;oBAAC;oBAC5B,OAAO,SAAS,CAAC,MAAM;oBACvB,OAAO;gBACT;gBACA,WAAW;oBACT,MAAM;oBACN,SAAS;oBACT,OAAO;oBACP,UAAU;gBACZ;YACF;QACF;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAK,CAAC;IAChD;IAEA,MAAM,eAAe,OAAO;QAC1B,gBAAgB;QAEhB,6BAA6B;QAC7B,MAAM,aAAa,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,CAAC,KAAK;YAClD,GAAG,CAAC,IAAe,GAAG;YACtB,OAAO;QACT,GAAG,CAAC;QACJ,WAAW;QAEX,sBAAsB;QACtB,MAAM,UAAU;QAEhB,IAAI,CAAC,SAAS;YACZ,WAAW;gBACT,MAAM;gBACN,SAAS;gBACT,UAAU;YACZ;YACA,gBAAgB;YAChB;QACF;QAEA,IAAI;YACF,MAAM,SAAS;YACf,WAAW;gBACT,MAAM;gBACN,SAAS;gBACT,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,WAAW;gBACT,MAAM;gBACN,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,UAAU,EAAE,yBAAyB;YACvC;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,QAAQ;QACZ,UAAU;QACV,UAAU,CAAC;QACX,WAAW,CAAC;QACZ;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA,YAAY;QACZ;QACA;QACA;QACA;QACA,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;IAC1C;AACF;IApIgB;;QAQwB;;;AA+HjC,SAAS,aAAa,KAU5B;QAV4B,EAC3B,WAAW,EACX,UAAU,EACV,UAAU,EACV,SAAS,EAMV,GAV4B;IAW3B,MAAM,WAAW,AAAC,cAAc,aAAc;IAE9C,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;0BAC3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;4BAAK;4BAAM;4BAAY;4BAAI;;;;;;;kCAC5B,6LAAC;;4BAAM,KAAK,KAAK,CAAC;4BAAU;;;;;;;;;;;;;0BAG9B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,OAAO,AAAC,GAAW,OAAT,UAAS;oBAAG;oBAC/B,MAAK;oBACL,iBAAe;oBACf,iBAAe;oBACf,iBAAe;oBACf,cAAY,AAAC,oBAAoC,OAAjB,aAAY,OAAgB,OAAX;;;;;;;;;;;YAIpD,4BACC,6LAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,OAAO,sBACtB,6LAAC;wBAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8BACA,QAAQ,KAAK,cAAc,8BAA8B;kCAG1D;uBANI;;;;;;;;;;;;;;;;AAanB;MAjDgB", "debugId": null}}, {"offset": {"line": 2600, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/performance-monitor-wrapper.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect } from 'react'\n\n/**\n * Performance Monitor Wrapper - Client Component\n * \n * Bu component Next.js 15 App Router uyumluluğu için ayrı bir Client Component\n * olarak oluşturulmuştur. Layout.tsx Server Component içinde hook kullanımını\n * önlemek için lazy loading ile performance monitor'ü yükler.\n * \n * @security Client-side only execution\n * @performance Lazy loading ile bundle size optimizasyonu\n * @accessibility Performance monitoring kullanıcı deneyimini etkilemez\n */\nexport function PerformanceMonitorWrapper() {\n  const [Component, setComponent] = useState<React.ComponentType | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n\n  useEffect(() => {\n    // Performance monitor'ü sadece client-side'da yükle\n    let isMounted = true\n\n    const loadPerformanceMonitor = async () => {\n      try {\n        // Development ortamında performance monitoring aktif\n        if (process.env.NODE_ENV === 'development') {\n          const module = await import('@/components/ui/performance-monitor')\n          \n          if (isMounted) {\n            setComponent(() => module.default)\n          }\n        }\n      } catch (error) {\n        // Performance monitor yüklenemezse sessizce devam et\n        console.warn('Performance monitor could not be loaded:', error)\n        \n        if (isMounted) {\n          setComponent(() => null)\n        }\n      } finally {\n        if (isMounted) {\n          setIsLoading(false)\n        }\n      }\n    }\n\n    loadPerformanceMonitor()\n\n    // Cleanup function\n    return () => {\n      isMounted = false\n    }\n  }, [])\n\n  // Loading state'inde hiçbir şey render etme\n  if (isLoading) {\n    return null\n  }\n\n  // Component yüklendiyse render et, yoksa null döndür\n  return Component ? <Component /> : null\n}\n\nexport default PerformanceMonitorWrapper\n"], "names": [], "mappings": ";;;;AA0BY;;AAxBZ;;;AAFA;;AAeO,SAAS;;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B;IACvE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+CAAE;YACR,oDAAoD;YACpD,IAAI,YAAY;YAEhB,MAAM;8EAAyB;oBAC7B,IAAI;wBACF,qDAAqD;wBACrD,wCAA4C;4BAC1C,MAAM,SAAS;4BAEf,IAAI,WAAW;gCACb;kGAAa,IAAM,OAAO,OAAO;;4BACnC;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,qDAAqD;wBACrD,QAAQ,IAAI,CAAC,4CAA4C;wBAEzD,IAAI,WAAW;4BACb;8FAAa,IAAM;;wBACrB;oBACF,SAAU;wBACR,IAAI,WAAW;4BACb,aAAa;wBACf;oBACF;gBACF;;YAEA;YAEA,mBAAmB;YACnB;uDAAO;oBACL,YAAY;gBACd;;QACF;8CAAG,EAAE;IAEL,4CAA4C;IAC5C,IAAI,WAAW;QACb,OAAO;IACT;IAEA,qDAAqD;IACrD,OAAO,0BAAY,6LAAC;;;;eAAe;AACrC;GA/CgB;KAAA;uCAiDD", "debugId": null}}, {"offset": {"line": 2680, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/hydration-safe.ts"], "sourcesContent": ["'use client'\n\nimport React, { useEffect, useState } from 'react'\n\n/**\n * Hook to prevent hydration mismatches by ensuring client-only rendering\n * for components that have different server/client behavior\n */\nexport function useHydrationSafe() {\n  const [isHydrated, setIsHydrated] = useState(false)\n\n  useEffect(() => {\n    setIsHydrated(true)\n  }, [])\n\n  return isHydrated\n}\n\n/**\n * Component wrapper that prevents hydration mismatches\n * by only rendering children after hydration is complete\n */\nexport function HydrationSafe({ \n  children, \n  fallback = null \n}: { \n  children: React.ReactNode\n  fallback?: React.ReactNode \n}) {\n  const isHydrated = useHydrationSafe()\n\n  if (!isHydrated) {\n    return fallback as React.ReactElement\n  }\n\n  return children as React.ReactElement\n}\n\n/**\n * Suppress hydration warnings for specific components\n * Use sparingly and only when hydration differences are intentional\n */\nexport function suppressHydrationWarning(element: React.ReactElement): React.ReactElement {\n  // TypeScript doesn't recognize suppressHydrationWarning, so we use any\n  const props = element.props || {}\n  return React.cloneElement(element, {\n    ...props,\n    suppressHydrationWarning: true\n  } as any)\n}\n\n/**\n * Safe localStorage access that won't cause hydration issues\n */\nexport function useLocalStorage<T>(key: string, defaultValue: T) {\n  const [value, setValue] = useState<T>(defaultValue)\n  const [isLoaded, setIsLoaded] = useState(false)\n\n  useEffect(() => {\n    try {\n      const item = localStorage.getItem(key)\n      if (item !== null) {\n        setValue(JSON.parse(item))\n      }\n    } catch (error) {\n      console.warn(`Error reading localStorage key \"${key}\":`, error)\n    } finally {\n      setIsLoaded(true)\n    }\n  }, [key])\n\n  const setStoredValue = (newValue: T) => {\n    try {\n      setValue(newValue)\n      localStorage.setItem(key, JSON.stringify(newValue))\n    } catch (error) {\n      console.warn(`Error setting localStorage key \"${key}\":`, error)\n    }\n  }\n\n  return [value, setStoredValue, isLoaded] as const\n}\n\n/**\n * Console error suppression for development hydration warnings\n * Only suppresses known hydration warnings, not real errors\n */\nexport function suppressHydrationConsoleErrors() {\n  if (typeof window === 'undefined' || process.env.NODE_ENV !== 'development') {\n    return\n  }\n\n  const originalError = console.error\n  const originalWarn = console.warn\n\n  console.error = (...args) => {\n    const message = args[0]\n    \n    // Suppress known hydration warnings\n    if (typeof message === 'string') {\n      const hydrationWarnings = [\n        'Warning: Text content did not match',\n        'Warning: Expected server HTML to contain',\n        'Warning: Did not expect server HTML to contain',\n        'Hydration failed because the initial UI does not match',\n        'There was an error while hydrating',\n        'emitPendingHydrationWarnings'\n      ]\n      \n      if (hydrationWarnings.some(warning => message.includes(warning))) {\n        return // Suppress hydration warnings\n      }\n    }\n    \n    // Allow all other errors through\n    originalError.apply(console, args)\n  }\n\n  console.warn = (...args) => {\n    const message = args[0]\n    \n    // Suppress known hydration warnings\n    if (typeof message === 'string') {\n      const hydrationWarnings = [\n        'Warning: Text content did not match',\n        'Warning: Expected server HTML to contain',\n        'Warning: Did not expect server HTML to contain'\n      ]\n      \n      if (hydrationWarnings.some(warning => message.includes(warning))) {\n        return // Suppress hydration warnings\n      }\n    }\n    \n    // Allow all other warnings through\n    originalWarn.apply(console, args)\n  }\n\n  // Restore original console methods on page unload\n  window.addEventListener('beforeunload', () => {\n    console.error = originalError\n    console.warn = originalWarn\n  })\n}\n\n/**\n * Client-side only component wrapper\n * Prevents any server-side rendering\n */\nexport function ClientOnly({ \n  children, \n  fallback = null \n}: { \n  children: React.ReactNode\n  fallback?: React.ReactNode \n}) {\n  const [hasMounted, setHasMounted] = useState(false)\n\n  useEffect(() => {\n    setHasMounted(true)\n  }, [])\n\n  if (!hasMounted) {\n    return fallback as React.ReactElement\n  }\n\n  return children as React.ReactElement\n}\n"], "names": [], "mappings": ";;;;;;;;AAwFuC;AAtFvC;;AAFA;;AAQO,SAAS;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,cAAc;QAChB;qCAAG,EAAE;IAEL,OAAO;AACT;GARgB;AAcT,SAAS,cAAc,KAM7B;QAN6B,EAC5B,QAAQ,EACR,WAAW,IAAI,EAIhB,GAN6B;;IAO5B,MAAM,aAAa;IAEnB,IAAI,CAAC,YAAY;QACf,OAAO;IACT;IAEA,OAAO;AACT;IAdgB;;QAOK;;;KAPL;AAoBT,SAAS,yBAAyB,OAA2B;IAClE,uEAAuE;IACvE,MAAM,QAAQ,QAAQ,KAAK,IAAI,CAAC;IAChC,qBAAO,6JAAA,CAAA,UAAK,CAAC,YAAY,CAAC,SAAS;QACjC,GAAG,KAAK;QACR,0BAA0B;IAC5B;AACF;AAKO,SAAS,gBAAmB,GAAW,EAAE,YAAe;;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAK;IACtC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI;gBACF,MAAM,OAAO,aAAa,OAAO,CAAC;gBAClC,IAAI,SAAS,MAAM;oBACjB,SAAS,KAAK,KAAK,CAAC;gBACtB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,AAAC,mCAAsC,OAAJ,KAAI,OAAK;YAC3D,SAAU;gBACR,YAAY;YACd;QACF;oCAAG;QAAC;KAAI;IAER,MAAM,iBAAiB,CAAC;QACtB,IAAI;YACF,SAAS;YACT,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,AAAC,mCAAsC,OAAJ,KAAI,OAAK;QAC3D;IACF;IAEA,OAAO;QAAC;QAAO;QAAgB;KAAS;AAC1C;IA3BgB;AAiCT,SAAS;IACd;;IAIA,MAAM,gBAAgB,QAAQ,KAAK;IACnC,MAAM,eAAe,QAAQ,IAAI;IAEjC,QAAQ,KAAK,GAAG;yCAAI;YAAA;;QAClB,MAAM,UAAU,IAAI,CAAC,EAAE;QAEvB,oCAAoC;QACpC,IAAI,OAAO,YAAY,UAAU;YAC/B,MAAM,oBAAoB;gBACxB;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,IAAI,kBAAkB,IAAI,CAAC,CAAA,UAAW,QAAQ,QAAQ,CAAC,WAAW;gBAChE,QAAO,8BAA8B;YACvC;QACF;QAEA,iCAAiC;QACjC,cAAc,KAAK,CAAC,SAAS;IAC/B;IAEA,QAAQ,IAAI,GAAG;yCAAI;YAAA;;QACjB,MAAM,UAAU,IAAI,CAAC,EAAE;QAEvB,oCAAoC;QACpC,IAAI,OAAO,YAAY,UAAU;YAC/B,MAAM,oBAAoB;gBACxB;gBACA;gBACA;aACD;YAED,IAAI,kBAAkB,IAAI,CAAC,CAAA,UAAW,QAAQ,QAAQ,CAAC,WAAW;gBAChE,QAAO,8BAA8B;YACvC;QACF;QAEA,mCAAmC;QACnC,aAAa,KAAK,CAAC,SAAS;IAC9B;IAEA,kDAAkD;IAClD,OAAO,gBAAgB,CAAC,gBAAgB;QACtC,QAAQ,KAAK,GAAG;QAChB,QAAQ,IAAI,GAAG;IACjB;AACF;AAMO,SAAS,WAAW,KAM1B;QAN0B,EACzB,QAAQ,EACR,WAAW,IAAI,EAIhB,GAN0B;;IAOzB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,cAAc;QAChB;+BAAG,EAAE;IAEL,IAAI,CAAC,YAAY;QACf,OAAO;IACT;IAEA,OAAO;AACT;IAlBgB;MAAA", "debugId": null}}]}
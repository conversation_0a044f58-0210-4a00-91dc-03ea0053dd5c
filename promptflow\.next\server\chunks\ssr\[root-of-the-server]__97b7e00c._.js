module.exports = {

"[next]/internal/font/google/inter_8fcc3bcb.module.css [app-rsc] (css module)": ((__turbopack_context__) => {

__turbopack_context__.v({
  "className": "inter_8fcc3bcb-module__7PpM1G__className",
  "variable": "inter_8fcc3bcb-module__7PpM1G__variable",
});
}),
"[next]/internal/font/google/inter_8fcc3bcb.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_8fcc3bcb$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__ = __turbopack_context__.i("[next]/internal/font/google/inter_8fcc3bcb.module.css [app-rsc] (css module)");
;
const fontData = {
    className: __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_8fcc3bcb$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].className,
    style: {
        fontFamily: "'Inter', 'Inter Fallback'",
        fontStyle: "normal"
    }
};
if (__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_8fcc3bcb$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable != null) {
    fontData.variable = __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_8fcc3bcb$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable;
}
const __TURBOPACK__default__export__ = fontData;
}),
"[project]/src/providers/query-provider.tsx [app-rsc] (client reference proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "QueryProvider": ()=>QueryProvider
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const QueryProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/providers/query-provider.tsx <module evaluation>", "QueryProvider");
}),
"[project]/src/providers/query-provider.tsx [app-rsc] (client reference proxy)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "QueryProvider": ()=>QueryProvider
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const QueryProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/providers/query-provider.tsx", "QueryProvider");
}),
"[project]/src/providers/query-provider.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$query$2d$provider$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/providers/query-provider.tsx [app-rsc] (client reference proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$query$2d$provider$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__ = __turbopack_context__.i("[project]/src/providers/query-provider.tsx [app-rsc] (client reference proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$query$2d$provider$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__);
}),
"[project]/src/lib/env-security.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * Environment Variables Security Checker
 * Ensures sensitive data is not exposed to client-side
 */ // Sensitive environment variables that should NEVER be exposed to client
__turbopack_context__.s({
    "checkEnvironmentSecurity": ()=>checkEnvironmentSecurity,
    "getSafeClientEnv": ()=>getSafeClientEnv,
    "performRuntimeSecurityCheck": ()=>performRuntimeSecurityCheck,
    "sanitizeEnvForLogging": ()=>sanitizeEnvForLogging,
    "validateEnvVar": ()=>validateEnvVar
});
const SENSITIVE_ENV_VARS = [
    'SUPABASE_SERVICE_ROLE_KEY',
    'SUPABASE_JWT_SECRET',
    'DATABASE_URL',
    'NEXTAUTH_SECRET',
    'OPENAI_API_KEY',
    'STRIPE_SECRET_KEY',
    'WEBHOOK_SECRET',
    'PRIVATE_KEY',
    'SECRET_KEY',
    'API_SECRET',
    'ADMIN_PASSWORD',
    'DB_PASSWORD',
    'ENCRYPTION_KEY'
];
// Client-safe environment variables (must start with NEXT_PUBLIC_)
const ALLOWED_CLIENT_ENV_VARS = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'NEXT_PUBLIC_APP_URL',
    'NEXT_PUBLIC_ENVIRONMENT',
    'NEXT_PUBLIC_ANALYTICS_ID',
    'NEXT_PUBLIC_SENTRY_DSN'
];
function checkEnvironmentSecurity() {
    const violations = [];
    const warnings = [];
    const recommendations = [];
    // Check if we're in client-side environment
    const isClientSide = "undefined" !== 'undefined';
    if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
    ;
    else {
        // Server-side checks
        console.log('🔍 [ENV_SECURITY] Running server-side environment security check...');
        // Check for missing sensitive variables that should be present
        const requiredServerVars = [
            'NEXT_PUBLIC_SUPABASE_URL',
            'NEXT_PUBLIC_SUPABASE_ANON_KEY'
        ];
        requiredServerVars.forEach((varName)=>{
            if (!process.env[varName]) {
                violations.push(`Required server variable ${varName} is missing`);
            }
        });
        // Check for weak or default values
        if (("TURBOPACK compile-time value", "https://iqehopwgrczylqliajww.supabase.co")?.includes('localhost') && ("TURBOPACK compile-time value", "development") === 'production') //TURBOPACK unreachable
        ;
        // Check for development keys in production
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
    }
    // General recommendations
    if ("TURBOPACK compile-time truthy", 1) {
        recommendations.push('Use .env.local for local development secrets');
        recommendations.push('Never commit .env files to version control');
        recommendations.push('Use different keys for development and production');
    }
    recommendations.push('Regularly rotate API keys and secrets');
    recommendations.push('Use environment-specific configurations');
    recommendations.push('Monitor for exposed secrets in client bundles');
    const isSecure = violations.length === 0;
    return {
        isSecure,
        violations,
        warnings,
        recommendations
    };
}
function validateEnvVar(name, value, options = {}) {
    const errors = [];
    const warnings = [];
    // Check if required
    if (options.required && !value) {
        errors.push(`Environment variable ${name} is required but not set`);
        return {
            isValid: false,
            errors,
            warnings
        };
    }
    if (!value) {
        return {
            isValid: true,
            errors,
            warnings
        };
    }
    // Check client safety
    if (options.clientSafe && !name.startsWith('NEXT_PUBLIC_')) {
        errors.push(`Client-safe variable ${name} must start with NEXT_PUBLIC_`);
    }
    if (!options.clientSafe && name.startsWith('NEXT_PUBLIC_')) {
        warnings.push(`Variable ${name} starts with NEXT_PUBLIC_ but is marked as not client-safe`);
    }
    // Check pattern
    if (options.pattern && !options.pattern.test(value)) {
        errors.push(`Environment variable ${name} does not match required pattern`);
    }
    // Check length
    if (options.minLength && value.length < options.minLength) {
        errors.push(`Environment variable ${name} is too short (minimum ${options.minLength} characters)`);
    }
    if (options.maxLength && value.length > options.maxLength) {
        errors.push(`Environment variable ${name} is too long (maximum ${options.maxLength} characters)`);
    }
    // Check for common weak values
    const weakValues = [
        'test',
        'dev',
        'development',
        'localhost',
        'example',
        'changeme',
        '123456'
    ];
    if (weakValues.some((weak)=>value.toLowerCase().includes(weak)) && ("TURBOPACK compile-time value", "development") === 'production') //TURBOPACK unreachable
    ;
    return {
        isValid: errors.length === 0,
        errors,
        warnings
    };
}
function getSafeClientEnv() {
    const safeEnv = {};
    ALLOWED_CLIENT_ENV_VARS.forEach((varName)=>{
        if (process.env[varName]) {
            safeEnv[varName] = process.env[varName];
        }
    });
    return safeEnv;
}
function sanitizeEnvForLogging(name, value) {
    // Never log sensitive values
    if (SENSITIVE_ENV_VARS.includes(name)) {
        return '[REDACTED]';
    }
    // For API keys and tokens, show only first and last few characters
    if (name.toLowerCase().includes('key') || name.toLowerCase().includes('token') || name.toLowerCase().includes('secret')) {
        if (value.length > 8) {
            return `${value.slice(0, 4)}...${value.slice(-4)}`;
        }
        return '[REDACTED]';
    }
    // For URLs, hide sensitive parts
    if (name.toLowerCase().includes('url') && value.includes('://')) {
        try {
            const url = new URL(value);
            return `${url.protocol}//${url.hostname}${url.pathname ? url.pathname : ''}`;
        } catch  {
            return value;
        }
    }
    return value;
}
function performRuntimeSecurityCheck() {
    const result = checkEnvironmentSecurity();
    if (!result.isSecure) {
        console.error('🚨 [ENV_SECURITY] CRITICAL SECURITY VIOLATIONS DETECTED:');
        result.violations.forEach((violation)=>{
            console.error(`  ❌ ${violation}`);
        });
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
    }
    if (result.warnings.length > 0) {
        console.warn('⚠️ [ENV_SECURITY] Security warnings:');
        result.warnings.forEach((warning)=>{
            console.warn(`  ⚠️ ${warning}`);
        });
    }
    if (result.recommendations.length > 0 && ("TURBOPACK compile-time value", "development") === 'development') {
        console.info('💡 [ENV_SECURITY] Security recommendations:');
        result.recommendations.forEach((rec)=>{
            console.info(`  💡 ${rec}`);
        });
    }
    console.log('✅ [ENV_SECURITY] Environment security check completed');
}
// Auto-run security check in development
if ("TURBOPACK compile-time truthy", 1) {
    performRuntimeSecurityCheck();
}
}),
"[project]/src/components/ui/sonner.tsx [app-rsc] (client reference proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "Toaster": ()=>Toaster
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const Toaster = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/sonner.tsx <module evaluation>", "Toaster");
}),
"[project]/src/components/ui/sonner.tsx [app-rsc] (client reference proxy)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "Toaster": ()=>Toaster
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const Toaster = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/sonner.tsx", "Toaster");
}),
"[project]/src/components/ui/sonner.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$sonner$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/ui/sonner.tsx [app-rsc] (client reference proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$sonner$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__ = __turbopack_context__.i("[project]/src/components/ui/sonner.tsx [app-rsc] (client reference proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$sonner$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__);
}),
"[project]/src/components/service-worker-registration.tsx [app-rsc] (client reference proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ServiceWorkerRegistration": ()=>ServiceWorkerRegistration,
    "useServiceWorker": ()=>useServiceWorker
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const ServiceWorkerRegistration = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ServiceWorkerRegistration() from the server but ServiceWorkerRegistration is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/service-worker-registration.tsx <module evaluation>", "ServiceWorkerRegistration");
const useServiceWorker = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useServiceWorker() from the server but useServiceWorker is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/service-worker-registration.tsx <module evaluation>", "useServiceWorker");
}),
"[project]/src/components/service-worker-registration.tsx [app-rsc] (client reference proxy)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ServiceWorkerRegistration": ()=>ServiceWorkerRegistration,
    "useServiceWorker": ()=>useServiceWorker
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const ServiceWorkerRegistration = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ServiceWorkerRegistration() from the server but ServiceWorkerRegistration is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/service-worker-registration.tsx", "ServiceWorkerRegistration");
const useServiceWorker = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useServiceWorker() from the server but useServiceWorker is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/service-worker-registration.tsx", "useServiceWorker");
}),
"[project]/src/components/service-worker-registration.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$service$2d$worker$2d$registration$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/service-worker-registration.tsx [app-rsc] (client reference proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$service$2d$worker$2d$registration$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__ = __turbopack_context__.i("[project]/src/components/service-worker-registration.tsx [app-rsc] (client reference proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$service$2d$worker$2d$registration$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__);
}),
"[project]/src/lib/seo-utils.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * SEO Utilities
 * Comprehensive SEO optimization tools and meta tag management
 */ __turbopack_context__.s({
    "BASE_SEO": ()=>BASE_SEO,
    "PAGE_SEO": ()=>PAGE_SEO,
    "SEOGenerator": ()=>SEOGenerator,
    "SEOPerformance": ()=>SEOPerformance,
    "SitemapGenerator": ()=>SitemapGenerator,
    "StructuredData": ()=>StructuredData
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
;
const BASE_SEO = {
    siteName: 'PromptFlow',
    siteUrl: process.env.NEXT_PUBLIC_SITE_URL || 'https://promptbir.com',
    defaultTitle: 'PromptFlow - AI Destekli Prompt Yönetim Platformu',
    defaultDescription: 'AI destekli geliştirme süreçlerini optimize eden minimalist prompt yönetim platformu. Projelerinizi organize edin, prompt\'larınızı yönetin ve AI ile daha verimli çalışın.',
    defaultKeywords: [
        'prompt yönetimi',
        'AI araçları',
        'yapay zeka',
        'geliştirici araçları',
        'prompt engineering',
        'AI destekli geliştirme',
        'kod optimizasyonu',
        'proje yönetimi'
    ],
    author: 'PromptFlow Team',
    language: 'tr',
    locale: 'tr_TR',
    twitterHandle: '@promptflow',
    facebookAppId: '',
    themeColor: '#3b82f6',
    backgroundColor: '#ffffff'
};
class SEOGenerator {
    static generateMetadata(options = {}) {
        const { title, description = BASE_SEO.defaultDescription, keywords = BASE_SEO.defaultKeywords, image, url, type = 'website', publishedTime, modifiedTime, author = BASE_SEO.author, noIndex = false, noFollow = false } = options;
        const fullTitle = title ? `${title} | ${BASE_SEO.siteName}` : BASE_SEO.defaultTitle;
        const fullUrl = url ? `${BASE_SEO.siteUrl}${url}` : BASE_SEO.siteUrl;
        const imageUrl = image ? image.startsWith('http') ? image : `${BASE_SEO.siteUrl}${image}` : `${BASE_SEO.siteUrl}/og-image.png`;
        const robots = [
            noIndex ? 'noindex' : 'index',
            noFollow ? 'nofollow' : 'follow'
        ].join(', ');
        return {
            title: fullTitle,
            description,
            keywords: keywords.join(', '),
            authors: [
                {
                    name: author
                }
            ],
            creator: author,
            publisher: BASE_SEO.siteName,
            robots,
            // Open Graph
            openGraph: {
                title: fullTitle,
                description,
                url: fullUrl,
                siteName: BASE_SEO.siteName,
                images: [
                    {
                        url: imageUrl,
                        width: 1200,
                        height: 630,
                        alt: title || BASE_SEO.defaultTitle
                    }
                ],
                locale: BASE_SEO.locale,
                type: type === 'article' ? 'article' : 'website',
                ...publishedTime && {
                    publishedTime
                },
                ...modifiedTime && {
                    modifiedTime
                }
            },
            // Twitter
            twitter: {
                card: 'summary_large_image',
                title: fullTitle,
                description,
                images: [
                    imageUrl
                ],
                creator: BASE_SEO.twitterHandle,
                site: BASE_SEO.twitterHandle
            },
            // Additional meta tags
            other: {
                'theme-color': BASE_SEO.themeColor,
                'msapplication-TileColor': BASE_SEO.themeColor,
                'apple-mobile-web-app-capable': 'yes',
                'apple-mobile-web-app-status-bar-style': 'default',
                'format-detection': 'telephone=no'
            }
        };
    }
    // Generate structured data (JSON-LD)
    static generateStructuredData(options) {
        const { type, data } = options;
        const baseStructure = {
            '@context': 'https://schema.org',
            '@type': type,
            ...data
        };
        // Add common properties based on type
        switch(type){
            case 'WebSite':
                return {
                    ...baseStructure,
                    name: data.name || BASE_SEO.siteName,
                    url: data.url || BASE_SEO.siteUrl,
                    description: data.description || BASE_SEO.defaultDescription,
                    inLanguage: BASE_SEO.language,
                    potentialAction: {
                        '@type': 'SearchAction',
                        target: `${BASE_SEO.siteUrl}/search?q={search_term_string}`,
                        'query-input': 'required name=search_term_string'
                    }
                };
            case 'WebApplication':
                return {
                    ...baseStructure,
                    name: data.name || BASE_SEO.siteName,
                    url: data.url || BASE_SEO.siteUrl,
                    description: data.description || BASE_SEO.defaultDescription,
                    applicationCategory: 'DeveloperApplication',
                    operatingSystem: 'Web Browser',
                    offers: {
                        '@type': 'Offer',
                        price: '0',
                        priceCurrency: 'USD'
                    }
                };
            case 'Article':
                return {
                    ...baseStructure,
                    headline: data.headline,
                    description: data.description,
                    author: {
                        '@type': 'Person',
                        name: data.author || BASE_SEO.author
                    },
                    publisher: {
                        '@type': 'Organization',
                        name: BASE_SEO.siteName,
                        logo: {
                            '@type': 'ImageObject',
                            url: `${BASE_SEO.siteUrl}/logo.png`
                        }
                    },
                    datePublished: data.datePublished,
                    dateModified: data.dateModified || data.datePublished,
                    mainEntityOfPage: {
                        '@type': 'WebPage',
                        '@id': data.url
                    }
                };
            case 'Organization':
                return {
                    ...baseStructure,
                    name: data.name || BASE_SEO.siteName,
                    url: data.url || BASE_SEO.siteUrl,
                    description: data.description || BASE_SEO.defaultDescription,
                    logo: `${BASE_SEO.siteUrl}/logo.png`,
                    sameAs: data.sameAs || [],
                    contactPoint: {
                        '@type': 'ContactPoint',
                        contactType: 'customer service',
                        availableLanguage: [
                            'Turkish',
                            'English'
                        ]
                    }
                };
            default:
                return baseStructure;
        }
    }
    // Generate breadcrumb structured data
    static generateBreadcrumbs(items) {
        return {
            '@context': 'https://schema.org',
            '@type': 'BreadcrumbList',
            itemListElement: items.map((item, index)=>({
                    '@type': 'ListItem',
                    position: index + 1,
                    name: item.name,
                    item: `${BASE_SEO.siteUrl}${item.url}`
                }))
        };
    }
    // Generate FAQ structured data
    static generateFAQ(faqs) {
        return {
            '@context': 'https://schema.org',
            '@type': 'FAQPage',
            mainEntity: faqs.map((faq)=>({
                    '@type': 'Question',
                    name: faq.question,
                    acceptedAnswer: {
                        '@type': 'Answer',
                        text: faq.answer
                    }
                }))
        };
    }
}
function StructuredData({ data }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("script", {
        type: "application/ld+json",
        dangerouslySetInnerHTML: {
            __html: JSON.stringify(data)
        }
    }, void 0, false, {
        fileName: "[project]/src/lib/seo-utils.tsx",
        lineNumber: 263,
        columnNumber: 5
    }, this);
}
const PAGE_SEO = {
    home: {
        title: 'Ana Sayfa',
        description: 'AI destekli prompt yönetim platformu ile projelerinizi organize edin ve geliştirme sürecinizi optimize edin.',
        keywords: [
            'ana sayfa',
            'prompt yönetimi',
            'AI araçları',
            'geliştirici platformu'
        ]
    },
    dashboard: {
        title: 'Dashboard',
        description: 'Prompt projelerinizi yönetin, organize edin ve AI destekli geliştirme sürecinizi optimize edin.',
        keywords: [
            'dashboard',
            'proje yönetimi',
            'prompt organizasyonu'
        ],
        noIndex: true // Private area
    },
    profile: {
        title: 'Profil',
        description: 'Kullanıcı profili ve hesap ayarları. Plan bilgilerinizi görüntüleyin ve hesabınızı yönetin.',
        keywords: [
            'profil',
            'hesap ayarları',
            'kullanıcı bilgileri'
        ],
        noIndex: true // Private area
    },
    auth: {
        title: 'Giriş Yap',
        description: 'PromptFlow hesabınıza giriş yapın veya yeni hesap oluşturun. AI destekli prompt yönetimi başlasın.',
        keywords: [
            'giriş',
            'kayıt',
            'hesap oluştur',
            'login'
        ]
    },
    templates: {
        title: 'Şablon Galerisi',
        description: 'Hazır prompt şablonları ile hızlı başlayın. Kategorilere göre düzenlenmiş profesyonel prompt koleksiyonu.',
        keywords: [
            'şablonlar',
            'prompt şablonları',
            'hazır promptlar',
            'galeri'
        ]
    }
};
class SitemapGenerator {
    static generateSitemap(pages) {
        const urls = pages.map((page)=>({
                url: `${BASE_SEO.siteUrl}${page.url}`,
                lastModified: page.lastModified || new Date(),
                changeFrequency: page.changeFrequency || 'weekly',
                priority: page.priority || 0.5
            }));
        return urls;
    }
    static generateRobotsTxt(options = {}) {
        const { allowAll = true, disallowPaths = [
            '/api/',
            '/admin/',
            '/_next/'
        ], sitemapUrl = `${BASE_SEO.siteUrl}/sitemap.xml` } = options;
        let robotsTxt = 'User-agent: *\n';
        if (allowAll) {
            robotsTxt += 'Allow: /\n';
        }
        disallowPaths.forEach((path)=>{
            robotsTxt += `Disallow: ${path}\n`;
        });
        robotsTxt += `\nSitemap: ${sitemapUrl}\n`;
        return robotsTxt;
    }
}
class SEOPerformance {
    static preloadCriticalResources() {
        if ("TURBOPACK compile-time truthy", 1) return;
        //TURBOPACK unreachable
        ;
        // Preload critical fonts
        const fontLinks = undefined;
        // Preload critical images
        const imageLinks = undefined;
    }
    static optimizeImages() {
        if ("TURBOPACK compile-time truthy", 1) return;
        //TURBOPACK unreachable
        ;
        // Add loading="lazy" to images below the fold
        const images = undefined;
    }
    static addStructuredDataToHead(data) {
        if ("TURBOPACK compile-time truthy", 1) return;
        //TURBOPACK unreachable
        ;
        const script = undefined;
    }
}
}),
"[project]/src/components/error-boundary.tsx [app-rsc] (client reference proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ErrorBoundary": ()=>ErrorBoundary,
    "useErrorHandler": ()=>useErrorHandler,
    "withErrorBoundary": ()=>withErrorBoundary
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const ErrorBoundary = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ErrorBoundary() from the server but ErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/error-boundary.tsx <module evaluation>", "ErrorBoundary");
const useErrorHandler = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useErrorHandler() from the server but useErrorHandler is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/error-boundary.tsx <module evaluation>", "useErrorHandler");
const withErrorBoundary = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call withErrorBoundary() from the server but withErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/error-boundary.tsx <module evaluation>", "withErrorBoundary");
}),
"[project]/src/components/error-boundary.tsx [app-rsc] (client reference proxy)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ErrorBoundary": ()=>ErrorBoundary,
    "useErrorHandler": ()=>useErrorHandler,
    "withErrorBoundary": ()=>withErrorBoundary
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const ErrorBoundary = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ErrorBoundary() from the server but ErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/error-boundary.tsx", "ErrorBoundary");
const useErrorHandler = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useErrorHandler() from the server but useErrorHandler is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/error-boundary.tsx", "useErrorHandler");
const withErrorBoundary = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call withErrorBoundary() from the server but withErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/error-boundary.tsx", "withErrorBoundary");
}),
"[project]/src/components/error-boundary.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$error$2d$boundary$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/error-boundary.tsx [app-rsc] (client reference proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$error$2d$boundary$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__ = __turbopack_context__.i("[project]/src/components/error-boundary.tsx [app-rsc] (client reference proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$error$2d$boundary$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__);
}),
"[project]/src/components/ui/form-feedback.tsx [app-rsc] (client reference proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "FeedbackMessage": ()=>FeedbackMessage,
    "FieldFeedback": ()=>FieldFeedback,
    "FormFeedbackProvider": ()=>FormFeedbackProvider,
    "FormProgress": ()=>FormProgress,
    "GlobalFeedback": ()=>GlobalFeedback,
    "useFormFeedback": ()=>useFormFeedback,
    "useFormValidation": ()=>useFormValidation
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const FeedbackMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call FeedbackMessage() from the server but FeedbackMessage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/form-feedback.tsx <module evaluation>", "FeedbackMessage");
const FieldFeedback = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call FieldFeedback() from the server but FieldFeedback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/form-feedback.tsx <module evaluation>", "FieldFeedback");
const FormFeedbackProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call FormFeedbackProvider() from the server but FormFeedbackProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/form-feedback.tsx <module evaluation>", "FormFeedbackProvider");
const FormProgress = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call FormProgress() from the server but FormProgress is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/form-feedback.tsx <module evaluation>", "FormProgress");
const GlobalFeedback = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call GlobalFeedback() from the server but GlobalFeedback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/form-feedback.tsx <module evaluation>", "GlobalFeedback");
const useFormFeedback = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useFormFeedback() from the server but useFormFeedback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/form-feedback.tsx <module evaluation>", "useFormFeedback");
const useFormValidation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useFormValidation() from the server but useFormValidation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/form-feedback.tsx <module evaluation>", "useFormValidation");
}),
"[project]/src/components/ui/form-feedback.tsx [app-rsc] (client reference proxy)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "FeedbackMessage": ()=>FeedbackMessage,
    "FieldFeedback": ()=>FieldFeedback,
    "FormFeedbackProvider": ()=>FormFeedbackProvider,
    "FormProgress": ()=>FormProgress,
    "GlobalFeedback": ()=>GlobalFeedback,
    "useFormFeedback": ()=>useFormFeedback,
    "useFormValidation": ()=>useFormValidation
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const FeedbackMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call FeedbackMessage() from the server but FeedbackMessage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/form-feedback.tsx", "FeedbackMessage");
const FieldFeedback = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call FieldFeedback() from the server but FieldFeedback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/form-feedback.tsx", "FieldFeedback");
const FormFeedbackProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call FormFeedbackProvider() from the server but FormFeedbackProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/form-feedback.tsx", "FormFeedbackProvider");
const FormProgress = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call FormProgress() from the server but FormProgress is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/form-feedback.tsx", "FormProgress");
const GlobalFeedback = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call GlobalFeedback() from the server but GlobalFeedback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/form-feedback.tsx", "GlobalFeedback");
const useFormFeedback = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useFormFeedback() from the server but useFormFeedback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/form-feedback.tsx", "useFormFeedback");
const useFormValidation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useFormValidation() from the server but useFormValidation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/form-feedback.tsx", "useFormValidation");
}),
"[project]/src/components/ui/form-feedback.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2d$feedback$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/ui/form-feedback.tsx [app-rsc] (client reference proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2d$feedback$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__ = __turbopack_context__.i("[project]/src/components/ui/form-feedback.tsx [app-rsc] (client reference proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2d$feedback$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__);
}),
"[project]/src/components/performance-monitor-wrapper.tsx [app-rsc] (client reference proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "PerformanceMonitorWrapper": ()=>PerformanceMonitorWrapper,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const PerformanceMonitorWrapper = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call PerformanceMonitorWrapper() from the server but PerformanceMonitorWrapper is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/performance-monitor-wrapper.tsx <module evaluation>", "PerformanceMonitorWrapper");
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/components/performance-monitor-wrapper.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/performance-monitor-wrapper.tsx <module evaluation>", "default");
}),
"[project]/src/components/performance-monitor-wrapper.tsx [app-rsc] (client reference proxy)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "PerformanceMonitorWrapper": ()=>PerformanceMonitorWrapper,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const PerformanceMonitorWrapper = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call PerformanceMonitorWrapper() from the server but PerformanceMonitorWrapper is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/performance-monitor-wrapper.tsx", "PerformanceMonitorWrapper");
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/components/performance-monitor-wrapper.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/performance-monitor-wrapper.tsx", "default");
}),
"[project]/src/components/performance-monitor-wrapper.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$performance$2d$monitor$2d$wrapper$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/performance-monitor-wrapper.tsx [app-rsc] (client reference proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$performance$2d$monitor$2d$wrapper$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__ = __turbopack_context__.i("[project]/src/components/performance-monitor-wrapper.tsx [app-rsc] (client reference proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$performance$2d$monitor$2d$wrapper$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__);
}),
"[project]/src/lib/hydration-safe.ts [app-rsc] (client reference proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ClientOnly": ()=>ClientOnly,
    "HydrationSafe": ()=>HydrationSafe,
    "suppressHydrationConsoleErrors": ()=>suppressHydrationConsoleErrors,
    "suppressHydrationWarning": ()=>suppressHydrationWarning,
    "useHydrationSafe": ()=>useHydrationSafe,
    "useLocalStorage": ()=>useLocalStorage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const ClientOnly = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ClientOnly() from the server but ClientOnly is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/lib/hydration-safe.ts <module evaluation>", "ClientOnly");
const HydrationSafe = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call HydrationSafe() from the server but HydrationSafe is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/lib/hydration-safe.ts <module evaluation>", "HydrationSafe");
const suppressHydrationConsoleErrors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call suppressHydrationConsoleErrors() from the server but suppressHydrationConsoleErrors is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/lib/hydration-safe.ts <module evaluation>", "suppressHydrationConsoleErrors");
const suppressHydrationWarning = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call suppressHydrationWarning() from the server but suppressHydrationWarning is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/lib/hydration-safe.ts <module evaluation>", "suppressHydrationWarning");
const useHydrationSafe = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useHydrationSafe() from the server but useHydrationSafe is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/lib/hydration-safe.ts <module evaluation>", "useHydrationSafe");
const useLocalStorage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useLocalStorage() from the server but useLocalStorage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/lib/hydration-safe.ts <module evaluation>", "useLocalStorage");
}),
"[project]/src/lib/hydration-safe.ts [app-rsc] (client reference proxy)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ClientOnly": ()=>ClientOnly,
    "HydrationSafe": ()=>HydrationSafe,
    "suppressHydrationConsoleErrors": ()=>suppressHydrationConsoleErrors,
    "suppressHydrationWarning": ()=>suppressHydrationWarning,
    "useHydrationSafe": ()=>useHydrationSafe,
    "useLocalStorage": ()=>useLocalStorage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const ClientOnly = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ClientOnly() from the server but ClientOnly is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/lib/hydration-safe.ts", "ClientOnly");
const HydrationSafe = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call HydrationSafe() from the server but HydrationSafe is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/lib/hydration-safe.ts", "HydrationSafe");
const suppressHydrationConsoleErrors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call suppressHydrationConsoleErrors() from the server but suppressHydrationConsoleErrors is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/lib/hydration-safe.ts", "suppressHydrationConsoleErrors");
const suppressHydrationWarning = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call suppressHydrationWarning() from the server but suppressHydrationWarning is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/lib/hydration-safe.ts", "suppressHydrationWarning");
const useHydrationSafe = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useHydrationSafe() from the server but useHydrationSafe is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/lib/hydration-safe.ts", "useHydrationSafe");
const useLocalStorage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useLocalStorage() from the server but useLocalStorage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/lib/hydration-safe.ts", "useLocalStorage");
}),
"[project]/src/lib/hydration-safe.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$hydration$2d$safe$2e$ts__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/hydration-safe.ts [app-rsc] (client reference proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$hydration$2d$safe$2e$ts__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__ = __turbopack_context__.i("[project]/src/lib/hydration-safe.ts [app-rsc] (client reference proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$hydration$2d$safe$2e$ts__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__);
}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>RootLayout,
    "metadata": ()=>metadata,
    "viewport": ()=>viewport
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_8fcc3bcb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[next]/internal/font/google/inter_8fcc3bcb.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$query$2d$provider$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/providers/query-provider.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$env$2d$security$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/env-security.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$sonner$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/sonner.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$service$2d$worker$2d$registration$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/service-worker-registration.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$seo$2d$utils$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/seo-utils.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$error$2d$boundary$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/error-boundary.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2d$feedback$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form-feedback.tsx [app-rsc] (ecmascript)");
// Performance monitor - Next.js 15 uyumlu Client Component import
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$performance$2d$monitor$2d$wrapper$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/performance-monitor-wrapper.tsx [app-rsc] (ecmascript)");
// Hydration safe utilities
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$hydration$2d$safe$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/hydration-safe.ts [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
const metadata = {
    ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$seo$2d$utils$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SEOGenerator"].generateMetadata({
        title: "PromptFlow - AI Destekli Prompt Yönetim Platformu",
        description: "AI destekli geliştirme süreçlerini optimize eden minimalist prompt yönetim platformu. Projelerinizi organize edin, prompt'larınızı yönetin ve AI ile daha verimli çalışın.",
        keywords: [
            "prompt yönetimi",
            "AI araçları",
            "yapay zeka",
            "geliştirici araçları",
            "prompt engineering",
            "AI destekli geliştirme",
            "kod optimizasyonu",
            "proje yönetimi",
            "ChatGPT prompts",
            "AI productivity"
        ],
        type: 'webapp'
    }),
    icons: {
        icon: [
            {
                url: '/favicon-16x16.png',
                sizes: '16x16',
                type: 'image/png'
            },
            {
                url: '/favicon-32x32.png',
                sizes: '32x32',
                type: 'image/png'
            },
            {
                url: '/favicon.ico',
                sizes: 'any'
            }
        ],
        shortcut: '/favicon.ico',
        apple: '/apple-touch-icon.png'
    },
    manifest: '/site.webmanifest',
    robots: {
        index: true,
        follow: true,
        googleBot: {
            index: true,
            follow: true,
            'max-video-preview': -1,
            'max-image-preview': 'large',
            'max-snippet': -1
        }
    },
    openGraph: {
        type: "website",
        locale: "tr_TR",
        url: "https://promptbir.com",
        siteName: "Promptbir",
        title: "Promptbir - AI Prompt Yönetim Platformu",
        description: "Yapay zeka prompt'larınızı profesyonelce yönetin. Takımınızla paylaşın, organize edin ve verimliliğinizi artırın. 10,000+ geliştirici tarafından güvenilir.",
        images: [
            {
                url: "/og-image.png",
                width: 1200,
                height: 630,
                alt: "Promptbir - AI Prompt Yönetim Platformu"
            }
        ]
    },
    twitter: {
        card: "summary_large_image",
        site: "@promptbir",
        creator: "@promptbir",
        title: "Promptbir - AI Prompt Yönetim Platformu",
        description: "Yapay zeka prompt'larınızı profesyonelce yönetin. Takımınızla paylaşın, organize edin ve verimliliğinizi artırın.",
        images: [
            "/twitter-image.png"
        ]
    },
    verification: {
        google: "your-google-verification-code"
    },
    alternates: {
        canonical: "https://promptbir.com",
        languages: {
            'tr-TR': 'https://promptbir.com',
            'en-US': 'https://promptbir.com/en'
        }
    },
    category: "Technology",
    metadataBase: new URL('https://promptbir.com')
};
const viewport = {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 1,
    userScalable: false,
    themeColor: [
        {
            media: '(prefers-color-scheme: light)',
            color: '#ffffff'
        },
        {
            media: '(prefers-color-scheme: dark)',
            color: '#000000'
        }
    ]
};
function RootLayout({ children }) {
    // Run security check on server-side
    if ("TURBOPACK compile-time truthy", 1) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$env$2d$security$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["performRuntimeSecurityCheck"])();
    }
    // Suppress hydration console errors in development (client-side only)
    if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
    ;
    // Generate structured data for the website
    const websiteStructuredData = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$seo$2d$utils$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SEOGenerator"].generateStructuredData({
        type: 'WebApplication',
        data: {
            name: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$seo$2d$utils$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BASE_SEO"].siteName,
            description: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$seo$2d$utils$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BASE_SEO"].defaultDescription,
            url: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$seo$2d$utils$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BASE_SEO"].siteUrl,
            applicationCategory: 'DeveloperApplication',
            operatingSystem: 'Web Browser',
            offers: {
                '@type': 'Offer',
                price: '0',
                priceCurrency: 'USD'
            },
            author: {
                '@type': 'Organization',
                name: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$seo$2d$utils$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BASE_SEO"].siteName
            }
        }
    });
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("html", {
        lang: "tr",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("head", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("link", {
                        rel: "dns-prefetch",
                        href: "//fonts.googleapis.com"
                    }, void 0, false, {
                        fileName: "[project]/src/app/layout.tsx",
                        lineNumber: 153,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("link", {
                        rel: "dns-prefetch",
                        href: "//fonts.gstatic.com"
                    }, void 0, false, {
                        fileName: "[project]/src/app/layout.tsx",
                        lineNumber: 154,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$seo$2d$utils$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["StructuredData"], {
                        data: websiteStructuredData
                    }, void 0, false, {
                        fileName: "[project]/src/app/layout.tsx",
                        lineNumber: 157,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                        name: "theme-color",
                        content: "#3b82f6"
                    }, void 0, false, {
                        fileName: "[project]/src/app/layout.tsx",
                        lineNumber: 162,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                        name: "mobile-web-app-capable",
                        content: "yes"
                    }, void 0, false, {
                        fileName: "[project]/src/app/layout.tsx",
                        lineNumber: 163,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                        name: "apple-mobile-web-app-capable",
                        content: "yes"
                    }, void 0, false, {
                        fileName: "[project]/src/app/layout.tsx",
                        lineNumber: 164,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                        name: "apple-mobile-web-app-status-bar-style",
                        content: "default"
                    }, void 0, false, {
                        fileName: "[project]/src/app/layout.tsx",
                        lineNumber: 165,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                        name: "format-detection",
                        content: "telephone=no"
                    }, void 0, false, {
                        fileName: "[project]/src/app/layout.tsx",
                        lineNumber: 166,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                        name: "viewport",
                        content: "width=device-width, initial-scale=1, viewport-fit=cover"
                    }, void 0, false, {
                        fileName: "[project]/src/app/layout.tsx",
                        lineNumber: 169,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/layout.tsx",
                lineNumber: 151,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("body", {
                className: `${__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_8fcc3bcb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].variable} font-sans antialiased`,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$error$2d$boundary$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ErrorBoundary"], {
                    level: "critical",
                    showDetails: ("TURBOPACK compile-time value", "development") === 'development',
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$query$2d$provider$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["QueryProvider"], {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2d$feedback$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["FormFeedbackProvider"], {
                            children: [
                                children,
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$sonner$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Toaster"], {}, void 0, false, {
                                    fileName: "[project]/src/app/layout.tsx",
                                    lineNumber: 178,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$hydration$2d$safe$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["HydrationSafe"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$service$2d$worker$2d$registration$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ServiceWorkerRegistration"], {}, void 0, false, {
                                            fileName: "[project]/src/app/layout.tsx",
                                            lineNumber: 180,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$performance$2d$monitor$2d$wrapper$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PerformanceMonitorWrapper"], {}, void 0, false, {
                                            fileName: "[project]/src/app/layout.tsx",
                                            lineNumber: 181,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/layout.tsx",
                                    lineNumber: 179,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/layout.tsx",
                            lineNumber: 176,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/layout.tsx",
                        lineNumber: 175,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/layout.tsx",
                    lineNumber: 174,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/layout.tsx",
                lineNumber: 171,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/layout.tsx",
        lineNumber: 150,
        columnNumber: 5
    }, this);
}
}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-rsc] (ecmascript)").vendored['react-rsc'].ReactJsxDevRuntime; //# sourceMappingURL=react-jsx-dev-runtime.js.map
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__97b7e00c._.js.map
{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/hooks/use-contexts.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useQuery, useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { supabaseBrowser as supabase } from \"@/lib/supabase-browser\";\r\nimport { Context, ContextCategory } from \"@/components/context-gallery\";\r\n\r\n// Context Categories Hooks\r\nexport function useContextCategories() {\r\n  return useQuery({\r\n    queryKey: [\"context-categories\"],\r\n    queryFn: async () => {\r\n      const { data, error } = await supabase\r\n        .from(\"context_categories\")\r\n        .select(\"*\")\r\n        .eq(\"is_active\", true)\r\n        .order(\"sort_order\", { ascending: true });\r\n\r\n      if (error) throw error;\r\n      return data as ContextCategory[];\r\n    },\r\n    staleTime: 5 * 60 * 1000, // 5 dakika\r\n  });\r\n}\r\n\r\n// Get all contexts with filters\r\nexport function useContexts(filters?: {\r\n  category_id?: string;\r\n  is_public?: boolean;\r\n  is_template?: boolean;\r\n  is_featured?: boolean;\r\n  search?: string;\r\n  author_id?: string;\r\n}) {\r\n  return useQuery({\r\n    queryKey: [\"contexts\", filters],\r\n    queryFn: async () => {\r\n      let query = supabase\r\n        .from(\"contexts\")\r\n        .select(`\r\n          *,\r\n          category:context_categories(*)\r\n        `)\r\n        .eq(\"status\", \"active\");\r\n\r\n      // Apply filters\r\n      if (filters?.category_id) {\r\n        query = query.eq(\"category_id\", filters.category_id);\r\n      }\r\n      \r\n      if (filters?.is_public !== undefined) {\r\n        query = query.eq(\"is_public\", filters.is_public);\r\n      }\r\n      \r\n      if (filters?.is_template !== undefined) {\r\n        query = query.eq(\"is_template\", filters.is_template);\r\n      }\r\n      \r\n      if (filters?.is_featured !== undefined) {\r\n        query = query.eq(\"is_featured\", filters.is_featured);\r\n      }\r\n\r\n      if (filters?.author_id) {\r\n        query = query.eq(\"author_id\", filters.author_id);\r\n      }\r\n\r\n      if (filters?.search) {\r\n        query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%,tags.cs.{${filters.search}}`);\r\n      }\r\n\r\n      // Order by featured first, then by usage count\r\n      query = query.order(\"is_featured\", { ascending: false })\r\n                   .order(\"usage_count\", { ascending: false });\r\n\r\n      const { data, error } = await query;\r\n\r\n      if (error) throw error;\r\n\r\n      // Transform data to match Context interface\r\n      return data.map((item) => ({\r\n        id: item.id,\r\n        title: item.title,\r\n        description: item.description,\r\n        content: item.content,\r\n        category: item.category,\r\n        author_id: item.author_id,\r\n        author_name: 'Kullanıcı', // Şimdilik static, sonra profiles tablosu eklenebilir\r\n        is_public: item.is_public,\r\n        is_featured: item.is_featured,\r\n        is_template: item.is_template,\r\n        tags: item.tags || [],\r\n        usage_count: item.usage_count,\r\n        like_count: item.like_count,\r\n        view_count: item.view_count,\r\n        approval_status: item.approval_status || 'approved',\r\n        approved_by: item.approved_by,\r\n        approved_at: item.approved_at,\r\n        approval_notes: item.approval_notes,\r\n        created_at: item.created_at,\r\n        updated_at: item.updated_at,\r\n      })) as Context[];\r\n    },\r\n    staleTime: 2 * 60 * 1000, // 2 dakika\r\n  });\r\n}\r\n\r\n// Get single context\r\nexport function useContext(id: string) {\r\n  return useQuery({\r\n    queryKey: [\"context\", id],\r\n    queryFn: async () => {\r\n      const { data, error } = await supabase\r\n        .from(\"contexts\")\r\n        .select(`\r\n          *,\r\n          category:context_categories(*)\r\n        `)\r\n        .eq(\"id\", id)\r\n        .single();\r\n\r\n      if (error) throw error;\r\n\r\n      return {\r\n        id: data.id,\r\n        title: data.title,\r\n        description: data.description,\r\n        content: data.content,\r\n        category: data.category,\r\n        author_id: data.author_id,\r\n        author_name: 'Kullanıcı', // Şimdilik static, sonra profiles tablosu eklenebilir\r\n        is_public: data.is_public,\r\n        is_featured: data.is_featured,\r\n        is_template: data.is_template,\r\n        tags: data.tags || [],\r\n        usage_count: data.usage_count,\r\n        like_count: data.like_count,\r\n        view_count: data.view_count,\r\n        created_at: data.created_at,\r\n        updated_at: data.updated_at,\r\n      } as Context;\r\n    },\r\n    enabled: !!id,\r\n  });\r\n}\r\n\r\n// Create context\r\nexport function useCreateContext() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: async (newContext: {\r\n      title: string;\r\n      description?: string;\r\n      content: string;\r\n      category_id: string;\r\n      is_public?: boolean;\r\n      is_template?: boolean;\r\n      tags?: string[];\r\n    }) => {\r\n      const { data: user } = await supabase.auth.getUser();\r\n      if (!user.user) throw new Error(\"Kullanıcı girişi gerekli\");\r\n\r\n      const { data, error } = await supabase\r\n        .from(\"contexts\")\r\n        .insert({\r\n          ...newContext,\r\n          author_id: user.user.id,\r\n        })\r\n        .select()\r\n        .single();\r\n\r\n      if (error) throw error;\r\n      return data;\r\n    },\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"contexts\"] });\r\n    },\r\n  });\r\n}\r\n\r\n// Update context\r\nexport function useUpdateContext() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: async ({\r\n      id,\r\n      updates\r\n    }: {\r\n      id: string;\r\n      updates: {\r\n        title?: string;\r\n        description?: string;\r\n        content?: string;\r\n        category_id?: string;\r\n        is_public?: boolean;\r\n        is_template?: boolean;\r\n        is_featured?: boolean;\r\n        tags?: string[];\r\n        status?: 'active' | 'inactive' | 'archived';\r\n      }\r\n    }) => {\r\n      const { data: user } = await supabase.auth.getUser();\r\n      if (!user.user) throw new Error(\"Kullanıcı girişi gerekli\");\r\n\r\n      const { data, error } = await supabase\r\n        .from(\"contexts\")\r\n        .update({\r\n          ...updates,\r\n          updated_at: new Date().toISOString(),\r\n        })\r\n        .eq(\"id\", id)\r\n        .eq(\"author_id\", user.user.id) // Sadece kendi context'lerini güncelleyebilir\r\n        .select()\r\n        .single();\r\n\r\n      if (error) throw error;\r\n      return data;\r\n    },\r\n    onSuccess: (data, variables) => {\r\n      queryClient.invalidateQueries({ queryKey: [\"contexts\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"context\", variables.id] });\r\n    },\r\n  });\r\n}\r\n\r\n// Delete context\r\nexport function useDeleteContext() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: async (id: string) => {\r\n      const { data: user } = await supabase.auth.getUser();\r\n      if (!user.user) throw new Error(\"Kullanıcı girişi gerekli\");\r\n\r\n      const { error } = await supabase\r\n        .from(\"contexts\")\r\n        .delete()\r\n        .eq(\"id\", id)\r\n        .eq(\"author_id\", user.user.id); // Sadece kendi context'lerini silebilir\r\n\r\n      if (error) throw error;\r\n    },\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"contexts\"] });\r\n    },\r\n  });\r\n}\r\n\r\n// Approve/Reject context (Admin only)\r\nexport function useApproveContext() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: async ({\r\n      contextId,\r\n      status,\r\n      notes\r\n    }: {\r\n      contextId: string;\r\n      status: 'approved' | 'rejected';\r\n      notes?: string\r\n    }) => {\r\n      const { error } = await supabase.rpc('approve_context', {\r\n        context_id_param: contextId,\r\n        approval_status_param: status,\r\n        approval_notes_param: notes\r\n      });\r\n\r\n      if (error) throw error;\r\n    },\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"contexts\"] });\r\n    },\r\n  });\r\n}\r\n\r\n// Use context (increment usage count)\r\nexport function useUseContext() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: async ({ \r\n      contextId, \r\n      projectId \r\n    }: { \r\n      contextId: string; \r\n      projectId?: string; \r\n    }) => {\r\n      const { data: user } = await supabase.auth.getUser();\r\n      if (!user.user) throw new Error(\"Kullanıcı girişi gerekli\");\r\n\r\n      const { error } = await supabase.rpc(\"increment_context_usage\", {\r\n        context_id_param: contextId,\r\n        user_id_param: user.user.id,\r\n        project_id_param: projectId || null,\r\n      });\r\n\r\n      if (error) throw error;\r\n    },\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"contexts\"] });\r\n    },\r\n  });\r\n}\r\n\r\n// Toggle like\r\nexport function useToggleContextLike() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: async (contextId: string) => {\r\n      const { data: user } = await supabase.auth.getUser();\r\n      if (!user.user) throw new Error(\"Kullanıcı girişi gerekli\");\r\n\r\n      const { data, error } = await supabase.rpc(\"toggle_context_like\", {\r\n        context_id_param: contextId,\r\n        user_id_param: user.user.id,\r\n      });\r\n\r\n      if (error) throw error;\r\n      return data; // Returns true if liked, false if unliked\r\n    },\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"contexts\"] });\r\n    },\r\n  });\r\n}\r\n\r\n// Get user's liked contexts\r\nexport function useUserLikedContexts() {\r\n  return useQuery({\r\n    queryKey: [\"user-liked-contexts\"],\r\n    queryFn: async () => {\r\n      const { data: user } = await supabase.auth.getUser();\r\n      if (!user.user) throw new Error(\"Kullanıcı girişi gerekli\");\r\n\r\n      const { data, error } = await supabase\r\n        .from(\"context_likes\")\r\n        .select(\"context_id\")\r\n        .eq(\"user_id\", user.user.id);\r\n\r\n      if (error) throw error;\r\n      return data.map(item => item.context_id);\r\n    },\r\n  });\r\n}\r\n\r\n// Get context statistics\r\nexport function useContextStats() {\r\n  return useQuery({\r\n    queryKey: [\"context-stats\"],\r\n    queryFn: async () => {\r\n      const { data, error } = await supabase\r\n        .from(\"contexts\")\r\n        .select(\"is_public, is_template, is_featured, status\")\r\n        .eq(\"status\", \"active\");\r\n\r\n      if (error) throw error;\r\n\r\n      const stats = {\r\n        total: data.length,\r\n        public: data.filter(c => c.is_public).length,\r\n        private: data.filter(c => !c.is_public).length,\r\n        templates: data.filter(c => c.is_template).length,\r\n        featured: data.filter(c => c.is_featured).length,\r\n      };\r\n\r\n      return stats;\r\n    },\r\n    staleTime: 5 * 60 * 1000, // 5 dakika\r\n  });\r\n} "], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAAA;AAAA;AACA;AAHA;;;AAOO,SAAS;IACd,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAqB;QAChC,SAAS;YACP,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,sBACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,MAChB,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAK;YAEzC,IAAI,OAAO,MAAM;YACjB,OAAO;QACT;QACA,WAAW,IAAI,KAAK;IACtB;AACF;AAGO,SAAS,YAAY,OAO3B;IACC,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAY;SAAQ;QAC/B,SAAS;YACP,IAAI,QAAQ,iIAAA,CAAA,kBAAQ,CACjB,IAAI,CAAC,YACL,MAAM,CAAC,CAAC;;;QAGT,CAAC,EACA,EAAE,CAAC,UAAU;YAEhB,gBAAgB;YAChB,IAAI,SAAS,aAAa;gBACxB,QAAQ,MAAM,EAAE,CAAC,eAAe,QAAQ,WAAW;YACrD;YAEA,IAAI,SAAS,cAAc,WAAW;gBACpC,QAAQ,MAAM,EAAE,CAAC,aAAa,QAAQ,SAAS;YACjD;YAEA,IAAI,SAAS,gBAAgB,WAAW;gBACtC,QAAQ,MAAM,EAAE,CAAC,eAAe,QAAQ,WAAW;YACrD;YAEA,IAAI,SAAS,gBAAgB,WAAW;gBACtC,QAAQ,MAAM,EAAE,CAAC,eAAe,QAAQ,WAAW;YACrD;YAEA,IAAI,SAAS,WAAW;gBACtB,QAAQ,MAAM,EAAE,CAAC,aAAa,QAAQ,SAAS;YACjD;YAEA,IAAI,SAAS,QAAQ;gBACnB,QAAQ,MAAM,EAAE,CAAC,CAAC,aAAa,EAAE,QAAQ,MAAM,CAAC,qBAAqB,EAAE,QAAQ,MAAM,CAAC,WAAW,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC;YACtH;YAEA,+CAA+C;YAC/C,QAAQ,MAAM,KAAK,CAAC,eAAe;gBAAE,WAAW;YAAM,GACxC,KAAK,CAAC,eAAe;gBAAE,WAAW;YAAM;YAEtD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;YAE9B,IAAI,OAAO,MAAM;YAEjB,4CAA4C;YAC5C,OAAO,KAAK,GAAG,CAAC,CAAC,OAAS,CAAC;oBACzB,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,aAAa,KAAK,WAAW;oBAC7B,SAAS,KAAK,OAAO;oBACrB,UAAU,KAAK,QAAQ;oBACvB,WAAW,KAAK,SAAS;oBACzB,aAAa;oBACb,WAAW,KAAK,SAAS;oBACzB,aAAa,KAAK,WAAW;oBAC7B,aAAa,KAAK,WAAW;oBAC7B,MAAM,KAAK,IAAI,IAAI,EAAE;oBACrB,aAAa,KAAK,WAAW;oBAC7B,YAAY,KAAK,UAAU;oBAC3B,YAAY,KAAK,UAAU;oBAC3B,iBAAiB,KAAK,eAAe,IAAI;oBACzC,aAAa,KAAK,WAAW;oBAC7B,aAAa,KAAK,WAAW;oBAC7B,gBAAgB,KAAK,cAAc;oBACnC,YAAY,KAAK,UAAU;oBAC3B,YAAY,KAAK,UAAU;gBAC7B,CAAC;QACH;QACA,WAAW,IAAI,KAAK;IACtB;AACF;AAGO,SAAS,WAAW,EAAU;IACnC,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;SAAG;QACzB,SAAS;YACP,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,CAAC;;;QAGT,CAAC,EACA,EAAE,CAAC,MAAM,IACT,MAAM;YAET,IAAI,OAAO,MAAM;YAEjB,OAAO;gBACL,IAAI,KAAK,EAAE;gBACX,OAAO,KAAK,KAAK;gBACjB,aAAa,KAAK,WAAW;gBAC7B,SAAS,KAAK,OAAO;gBACrB,UAAU,KAAK,QAAQ;gBACvB,WAAW,KAAK,SAAS;gBACzB,aAAa;gBACb,WAAW,KAAK,SAAS;gBACzB,aAAa,KAAK,WAAW;gBAC7B,aAAa,KAAK,WAAW;gBAC7B,MAAM,KAAK,IAAI,IAAI,EAAE;gBACrB,aAAa,KAAK,WAAW;gBAC7B,YAAY,KAAK,UAAU;gBAC3B,YAAY,KAAK,UAAU;gBAC3B,YAAY,KAAK,UAAU;gBAC3B,YAAY,KAAK,UAAU;YAC7B;QACF;QACA,SAAS,CAAC,CAAC;IACb;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO;YASjB,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;YAClD,IAAI,CAAC,KAAK,IAAI,EAAE,MAAM,IAAI,MAAM;YAEhC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC;gBACN,GAAG,UAAU;gBACb,WAAW,KAAK,IAAI,CAAC,EAAE;YACzB,GACC,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YACjB,OAAO;QACT;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;QACzD;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EACjB,EAAE,EACF,OAAO,EAcR;YACC,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;YAClD,IAAI,CAAC,KAAK,IAAI,EAAE,MAAM,IAAI,MAAM;YAEhC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC;gBACN,GAAG,OAAO;gBACV,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,EAAE,CAAC,MAAM,IACT,EAAE,CAAC,aAAa,KAAK,IAAI,CAAC,EAAE,EAAE,8CAA8C;aAC5E,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YACjB,OAAO;QACT;QACA,WAAW,CAAC,MAAM;YAChB,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACvD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAW,UAAU,EAAE;iBAAC;YAAC;QACtE;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO;YACjB,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;YAClD,IAAI,CAAC,KAAK,IAAI,EAAE,MAAM,IAAI,MAAM;YAEhC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAC7B,IAAI,CAAC,YACL,MAAM,GACN,EAAE,CAAC,MAAM,IACT,EAAE,CAAC,aAAa,KAAK,IAAI,CAAC,EAAE,GAAG,wCAAwC;YAE1E,IAAI,OAAO,MAAM;QACnB;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;QACzD;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EACjB,SAAS,EACT,MAAM,EACN,KAAK,EAKN;YACC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,mBAAmB;gBACtD,kBAAkB;gBAClB,uBAAuB;gBACvB,sBAAsB;YACxB;YAEA,IAAI,OAAO,MAAM;QACnB;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;QACzD;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EACjB,SAAS,EACT,SAAS,EAIV;YACC,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;YAClD,IAAI,CAAC,KAAK,IAAI,EAAE,MAAM,IAAI,MAAM;YAEhC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,2BAA2B;gBAC9D,kBAAkB;gBAClB,eAAe,KAAK,IAAI,CAAC,EAAE;gBAC3B,kBAAkB,aAAa;YACjC;YAEA,IAAI,OAAO,MAAM;QACnB;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;QACzD;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO;YACjB,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;YAClD,IAAI,CAAC,KAAK,IAAI,EAAE,MAAM,IAAI,MAAM;YAEhC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,uBAAuB;gBAChE,kBAAkB;gBAClB,eAAe,KAAK,IAAI,CAAC,EAAE;YAC7B;YAEA,IAAI,OAAO,MAAM;YACjB,OAAO,MAAM,0CAA0C;QACzD;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;QACzD;IACF;AACF;AAGO,SAAS;IACd,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAsB;QACjC,SAAS;YACP,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;YAClD,IAAI,CAAC,KAAK,IAAI,EAAE,MAAM,IAAI,MAAM;YAEhC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,iBACL,MAAM,CAAC,cACP,EAAE,CAAC,WAAW,KAAK,IAAI,CAAC,EAAE;YAE7B,IAAI,OAAO,MAAM;YACjB,OAAO,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,UAAU;QACzC;IACF;AACF;AAGO,SAAS;IACd,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAgB;QAC3B,SAAS;YACP,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,+CACP,EAAE,CAAC,UAAU;YAEhB,IAAI,OAAO,MAAM;YAEjB,MAAM,QAAQ;gBACZ,OAAO,KAAK,MAAM;gBAClB,QAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM;gBAC5C,SAAS,KAAK,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,SAAS,EAAE,MAAM;gBAC9C,WAAW,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,MAAM;gBACjD,UAAU,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,MAAM;YAClD;YAEA,OAAO;QACT;QACA,WAAW,IAAI,KAAK;IACtB;AACF", "debugId": null}}, {"offset": {"line": 556, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/hooks/use-prompts.ts"], "sourcesContent": ["'use client'\r\n\r\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\r\nimport { Database } from '@/lib/supabase'\r\nimport { canCreatePrompt, updateUsageStats, PLAN_LIMIT_MESSAGES } from '@/lib/plan-limits'\r\n// Advanced validation will be integrated in future updates\r\n// import { checkRateLimit, RateLimitError } from '@/lib/rate-limiter'\r\n// import { AdvancedValidator, COMMON_VALIDATION_RULES } from '@/lib/advanced-validation'\r\n\r\ntype Prompt = Database['public']['Tables']['prompts']['Row']\r\ntype PromptInsert = Database['public']['Tables']['prompts']['Insert']\r\ntype PromptUpdate = Database['public']['Tables']['prompts']['Update']\r\n\r\n// Proje prompt'larını getir\r\nexport function usePrompts(projectId: string | null) {\r\n  return useQuery({\r\n    queryKey: ['prompts', projectId],\r\n    queryFn: async (): Promise<Prompt[]> => {\r\n      if (!projectId) {\r\n        console.log(`📝 [USE_PROMPTS] No project ID provided`)\r\n        return []\r\n      }\r\n\r\n      console.log(`📝 [USE_PROMPTS] Fetching prompts for project:`, projectId)\r\n\r\n      try {\r\n        // Check if we have a session first\r\n        const { data: { session }, error: sessionError } = await supabase.auth.getSession()\r\n        console.log(`📝 [USE_PROMPTS] Session check:`, {\r\n          hasSession: !!session,\r\n          sessionError: sessionError?.message,\r\n          userId: session?.user?.id\r\n        })\r\n\r\n        const { data, error } = await supabase\r\n          .from('prompts')\r\n          .select('*')\r\n          .eq('project_id', projectId)\r\n          .order('order_index', { ascending: true })\r\n\r\n        if (error) {\r\n          console.error(`❌ [USE_PROMPTS] Error fetching prompts:`, error)\r\n          throw new Error(error.message)\r\n        }\r\n\r\n        console.log(`✅ [USE_PROMPTS] Prompts fetched:`, data?.length || 0, 'prompts')\r\n        return data || []\r\n      } catch (err) {\r\n        console.error(`💥 [USE_PROMPTS] Exception:`, err)\r\n        throw err\r\n      }\r\n    },\r\n    enabled: !!projectId,\r\n  })\r\n}\r\n\r\n// Prompt oluştur (Optimized with Optimistic Updates)\r\nexport function useCreatePrompt() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async (prompt: Omit<PromptInsert, 'user_id'>): Promise<Prompt> => {\r\n      // Plan limiti kontrol et\r\n      const limitCheck = await canCreatePrompt(prompt.project_id)\r\n      if (!limitCheck.allowed) {\r\n        throw new Error(limitCheck.reason || PLAN_LIMIT_MESSAGES.PROMPT_LIMIT_REACHED)\r\n      }\r\n\r\n      const { data: { user }, error: userError } = await supabase.auth.getUser()\r\n\r\n      if (userError || !user) {\r\n        throw new Error('Kullanıcı oturumu bulunamadı')\r\n      }\r\n\r\n      // Task code otomatik oluştur\r\n      const taskCode = prompt.task_code || `task-${prompt.order_index}`\r\n\r\n      const { data, error } = await supabase\r\n        .from('prompts')\r\n        .insert({\r\n          ...prompt,\r\n          user_id: user.id,\r\n          task_code: taskCode,\r\n          tags: prompt.tags || [],\r\n          is_favorite: prompt.is_favorite || false,\r\n          usage_count: prompt.usage_count || 0,\r\n        })\r\n        .select()\r\n        .single()\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n\r\n      return data\r\n    },\r\n    onMutate: async (newPrompt) => {\r\n      // Cancel outgoing refetches\r\n      await queryClient.cancelQueries({ queryKey: ['prompts', newPrompt.project_id] })\r\n\r\n      // Snapshot the previous value\r\n      const previousPrompts = queryClient.getQueryData(['prompts', newPrompt.project_id])\r\n\r\n      // Get current user for optimistic update\r\n      const { data: { user } } = await supabase.auth.getUser()\r\n      if (!user) return { previousPrompts }\r\n\r\n      // Get current prompts to calculate proper order_index\r\n      const currentPrompts = queryClient.getQueryData(['prompts', newPrompt.project_id]) as any[] || []\r\n      const maxOrderIndex = currentPrompts.length > 0\r\n        ? Math.max(...currentPrompts.map(p => p.order_index || 0))\r\n        : 0\r\n      const nextOrderIndex = maxOrderIndex + 1\r\n\r\n      // Create optimistic prompt with unique ID and proper order\r\n      const optimisticId = `temp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`\r\n      const optimisticPrompt = {\r\n        id: optimisticId,\r\n        ...newPrompt,\r\n        user_id: user.id,\r\n        order_index: nextOrderIndex,\r\n        task_code: newPrompt.task_code || `task-${nextOrderIndex}`,\r\n        tags: newPrompt.tags || [],\r\n        is_favorite: newPrompt.is_favorite || false,\r\n        usage_count: 0,\r\n        created_at: new Date().toISOString(),\r\n        updated_at: new Date().toISOString(),\r\n      }\r\n\r\n      // Optimistically update the cache\r\n      queryClient.setQueryData(['prompts', newPrompt.project_id], (old: any) => {\r\n        if (!Array.isArray(old)) return [optimisticPrompt]\r\n        // Add to beginning for immediate visibility\r\n        return [optimisticPrompt, ...old]\r\n      })\r\n\r\n      return { previousPrompts, optimisticPrompt }\r\n    },\r\n    onError: (err, newPrompt, context) => {\r\n      // Rollback optimistic update on error\r\n      if (context?.previousPrompts) {\r\n        queryClient.setQueryData(['prompts', newPrompt.project_id], context.previousPrompts)\r\n      }\r\n    },\r\n    onSuccess: async (data, variables, context) => {\r\n      // Replace optimistic prompt with real data and ensure no duplicates\r\n      queryClient.setQueryData(['prompts', data.project_id], (old: any) => {\r\n        if (!Array.isArray(old)) return [data]\r\n\r\n        // Remove optimistic prompt and any potential duplicates\r\n        const filteredOld = old.filter((prompt: any) =>\r\n          prompt.id !== context?.optimisticPrompt?.id && prompt.id !== data.id\r\n        )\r\n\r\n        // Add real data at the beginning (newest first)\r\n        return [data, ...filteredOld]\r\n      })\r\n\r\n      // Batch invalidate related queries (non-blocking)\r\n      setTimeout(() => {\r\n        queryClient.invalidateQueries({ queryKey: ['popular-hashtags', data.project_id] })\r\n        queryClient.invalidateQueries({ queryKey: ['all-hashtags', data.project_id] })\r\n        queryClient.invalidateQueries({ queryKey: ['popular-categories', data.project_id] })\r\n        queryClient.invalidateQueries({ queryKey: ['all-categories', data.project_id] })\r\n\r\n        // Update usage stats in background\r\n        updateUsageStats().then(() => {\r\n          queryClient.invalidateQueries({ queryKey: ['user-limits'] })\r\n          queryClient.invalidateQueries({ queryKey: ['usage-stats'] })\r\n        }).catch(error => {\r\n          console.warn('Kullanım istatistikleri güncellenemedi:', error)\r\n        })\r\n      }, 100)\r\n    },\r\n  })\r\n}\r\n\r\n// Prompt güncelle\r\nexport function useUpdatePrompt() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async ({ id, ...updates }: PromptUpdate & { id: string }): Promise<Prompt> => {\r\n      const { data, error } = await supabase\r\n        .from('prompts')\r\n        .update({\r\n          ...updates,\r\n          updated_at: new Date().toISOString(),\r\n        })\r\n        .eq('id', id)\r\n        .select()\r\n        .single()\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n\r\n      return data\r\n    },\r\n    onSuccess: (data) => {\r\n      queryClient.invalidateQueries({ queryKey: ['prompts', data.project_id] })\r\n      // Invalidate hashtag and category queries if tags or category were updated\r\n      queryClient.invalidateQueries({ queryKey: ['popular-hashtags', data.project_id] })\r\n      queryClient.invalidateQueries({ queryKey: ['all-hashtags', data.project_id] })\r\n      queryClient.invalidateQueries({ queryKey: ['popular-categories', data.project_id] })\r\n      queryClient.invalidateQueries({ queryKey: ['all-categories', data.project_id] })\r\n    },\r\n  })\r\n}\r\n\r\n// Prompt sil\r\nexport function useDeletePrompt() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async ({ id }: { id: string; projectId: string }): Promise<void> => {\r\n      const { error } = await supabase\r\n        .from('prompts')\r\n        .delete()\r\n        .eq('id', id)\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n    },\r\n    onSuccess: (_, { projectId }) => {\r\n      queryClient.invalidateQueries({ queryKey: ['prompts', projectId] })\r\n    },\r\n  })\r\n}\r\n\r\n// Prompt'u kullanıldı olarak işaretle (optimistic update ile)\r\nexport function useMarkPromptAsUsed() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async (promptId: string): Promise<Prompt> => {\r\n      const { data, error } = await supabase\r\n        .from('prompts')\r\n        .update({ is_used: true })\r\n        .eq('id', promptId)\r\n        .select()\r\n        .single()\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n\r\n      return data\r\n    },\r\n    onMutate: async (promptId) => {\r\n      // Optimistic update\r\n      const queryKey = ['prompts']\r\n      await queryClient.cancelQueries({ queryKey })\r\n\r\n      const previousPrompts = queryClient.getQueriesData({ queryKey })\r\n\r\n      queryClient.setQueriesData({ queryKey }, (old: unknown) => {\r\n        if (!old || !Array.isArray(old)) return old\r\n        return old.map((prompt: Prompt) =>\r\n          prompt.id === promptId ? { ...prompt, is_used: true } : prompt\r\n        )\r\n      })\r\n\r\n      return { previousPrompts }\r\n    },\r\n    onError: (err, _promptId, context) => {\r\n      // Hata durumunda geri al\r\n      if (context?.previousPrompts) {\r\n        context.previousPrompts.forEach(([queryKey, data]) => {\r\n          queryClient.setQueryData(queryKey, data)\r\n        })\r\n      }\r\n    },\r\n    onSettled: (data) => {\r\n      // Her durumda cache'i yenile\r\n      if (data) {\r\n        queryClient.invalidateQueries({ queryKey: ['prompts', data.project_id] })\r\n      }\r\n    },\r\n  })\r\n}\r\n\r\n// Proje prompt'larını sıfırla\r\nexport function useResetPrompts() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async (projectId: string): Promise<void> => {\r\n      const { error } = await supabase\r\n        .from('prompts')\r\n        .update({ is_used: false })\r\n        .eq('project_id', projectId)\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n    },\r\n    onSuccess: (_, projectId) => {\r\n      queryClient.invalidateQueries({ queryKey: ['prompts', projectId] })\r\n    },\r\n  })\r\n}\r\n\r\n// Toplu prompt güncelleme (optimistic update ile)\r\nexport function useBulkUpdatePrompts() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async (updates: Array<{ id: string; order_index?: number; task_code?: string; is_used?: boolean }>): Promise<Prompt[]> => {\r\n      // Try to use RPC function for order updates, fallback to individual updates for other fields\r\n      const orderUpdates = updates.filter(u => u.order_index !== undefined || u.task_code !== undefined)\r\n      const otherUpdates = updates.filter(u => u.order_index === undefined && u.task_code === undefined)\r\n\r\n      const results: Prompt[] = []\r\n\r\n      // Use RPC function for order/task_code updates (more efficient)\r\n      if (orderUpdates.length > 0) {\r\n        try {\r\n          const { data: rpcData, error: rpcError } = await supabase.rpc('bulk_update_prompts_order', {\r\n            prompt_updates: orderUpdates\r\n          })\r\n\r\n          if (rpcError) {\r\n            console.warn('RPC function failed, falling back to individual updates:', rpcError)\r\n            // Fallback to individual updates\r\n            const fallbackPromises = orderUpdates.map(async (update) => {\r\n              const { data, error } = await supabase\r\n                .from('prompts')\r\n                .update({\r\n                  ...update,\r\n                  updated_at: new Date().toISOString(),\r\n                })\r\n                .eq('id', update.id)\r\n                .select()\r\n                .single()\r\n\r\n              if (error) {\r\n                throw new Error(`Failed to update prompt ${update.id}: ${error.message}`)\r\n              }\r\n              return data\r\n            })\r\n\r\n            const fallbackResults = await Promise.all(fallbackPromises)\r\n            results.push(...fallbackResults)\r\n          } else if (rpcData) {\r\n            // Get full prompt data for RPC results\r\n            const rpcIds = rpcData.map((r: { id: string }) => r.id)\r\n            const { data: fullPrompts, error: selectError } = await supabase\r\n              .from('prompts')\r\n              .select('*')\r\n              .in('id', rpcIds)\r\n\r\n            if (selectError) {\r\n              throw new Error(`Failed to fetch updated prompts: ${selectError.message}`)\r\n            }\r\n\r\n            results.push(...(fullPrompts || []))\r\n          }\r\n        } catch (error) {\r\n          console.error('Bulk update error:', error)\r\n          throw error\r\n        }\r\n      }\r\n\r\n      // Handle other updates (is_used, etc.) with individual calls\r\n      if (otherUpdates.length > 0) {\r\n        const updatePromises = otherUpdates.map(async (update) => {\r\n          const { data, error } = await supabase\r\n            .from('prompts')\r\n            .update({\r\n              ...update,\r\n              updated_at: new Date().toISOString(),\r\n            })\r\n            .eq('id', update.id)\r\n            .select()\r\n            .single()\r\n\r\n          if (error) {\r\n            throw new Error(`Failed to update prompt ${update.id}: ${error.message}`)\r\n          }\r\n\r\n          return data\r\n        })\r\n\r\n        const updatedPrompts = await Promise.all(updatePromises)\r\n        results.push(...updatedPrompts)\r\n      }\r\n\r\n      return results\r\n    },\r\n    onMutate: async (updates) => {\r\n      // Optimistic update\r\n      const queryKey = ['prompts']\r\n      await queryClient.cancelQueries({ queryKey })\r\n\r\n      const previousPrompts = queryClient.getQueriesData({ queryKey })\r\n\r\n      queryClient.setQueriesData({ queryKey }, (old: unknown) => {\r\n        if (!old || !Array.isArray(old)) return old\r\n\r\n        return old.map((prompt: Prompt) => {\r\n          const update = updates.find(u => u.id === prompt.id)\r\n          return update ? { ...prompt, ...update } : prompt\r\n        })\r\n      })\r\n\r\n      return { previousPrompts }\r\n    },\r\n    onError: (err, _updates, context) => {\r\n      // Hata durumunda geri al\r\n      if (context?.previousPrompts) {\r\n        context.previousPrompts.forEach(([queryKey, data]) => {\r\n          queryClient.setQueryData(queryKey, data)\r\n        })\r\n      }\r\n    },\r\n    onSettled: (data) => {\r\n      // Her durumda cache'i yenile\r\n      if (data && data.length > 0) {\r\n        queryClient.invalidateQueries({ queryKey: ['prompts', data[0].project_id] })\r\n      }\r\n    },\r\n  })\r\n}"], "names": [], "mappings": ";;;;;;;;;AAEA;AAAA;AAAA;AACA;AAEA;AALA;;;;AAeO,SAAS,WAAW,SAAwB;IACjD,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;SAAU;QAChC,SAAS;YACP,IAAI,CAAC,WAAW;gBACd,QAAQ,GAAG,CAAC,CAAC,uCAAuC,CAAC;gBACrD,OAAO,EAAE;YACX;YAEA,QAAQ,GAAG,CAAC,CAAC,8CAA8C,CAAC,EAAE;YAE9D,IAAI;gBACF,mCAAmC;gBACnC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,UAAU;gBACjF,QAAQ,GAAG,CAAC,CAAC,+BAA+B,CAAC,EAAE;oBAC7C,YAAY,CAAC,CAAC;oBACd,cAAc,cAAc;oBAC5B,QAAQ,SAAS,MAAM;gBACzB;gBAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,cAAc,WACjB,KAAK,CAAC,eAAe;oBAAE,WAAW;gBAAK;gBAE1C,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,CAAC,uCAAuC,CAAC,EAAE;oBACzD,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,QAAQ,GAAG,CAAC,CAAC,gCAAgC,CAAC,EAAE,MAAM,UAAU,GAAG;gBACnE,OAAO,QAAQ,EAAE;YACnB,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,CAAC,2BAA2B,CAAC,EAAE;gBAC7C,MAAM;YACR;QACF;QACA,SAAS,CAAC,CAAC;IACb;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO;YACjB,yBAAyB;YACzB,MAAM,aAAa,MAAM,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,UAAU;YAC1D,IAAI,CAAC,WAAW,OAAO,EAAE;gBACvB,MAAM,IAAI,MAAM,WAAW,MAAM,IAAI,4HAAA,CAAA,sBAAmB,CAAC,oBAAoB;YAC/E;YAEA,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;YAExE,IAAI,aAAa,CAAC,MAAM;gBACtB,MAAM,IAAI,MAAM;YAClB;YAEA,6BAA6B;YAC7B,MAAM,WAAW,OAAO,SAAS,IAAI,CAAC,KAAK,EAAE,OAAO,WAAW,EAAE;YAEjE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC;gBACN,GAAG,MAAM;gBACT,SAAS,KAAK,EAAE;gBAChB,WAAW;gBACX,MAAM,OAAO,IAAI,IAAI,EAAE;gBACvB,aAAa,OAAO,WAAW,IAAI;gBACnC,aAAa,OAAO,WAAW,IAAI;YACrC,GACC,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,OAAO;QACT;QACA,UAAU,OAAO;YACf,4BAA4B;YAC5B,MAAM,YAAY,aAAa,CAAC;gBAAE,UAAU;oBAAC;oBAAW,UAAU,UAAU;iBAAC;YAAC;YAE9E,8BAA8B;YAC9B,MAAM,kBAAkB,YAAY,YAAY,CAAC;gBAAC;gBAAW,UAAU,UAAU;aAAC;YAElF,yCAAyC;YACzC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,MAAM,OAAO;gBAAE;YAAgB;YAEpC,sDAAsD;YACtD,MAAM,iBAAiB,YAAY,YAAY,CAAC;gBAAC;gBAAW,UAAU,UAAU;aAAC,KAAc,EAAE;YACjG,MAAM,gBAAgB,eAAe,MAAM,GAAG,IAC1C,KAAK,GAAG,IAAI,eAAe,GAAG,CAAC,CAAA,IAAK,EAAE,WAAW,IAAI,MACrD;YACJ,MAAM,iBAAiB,gBAAgB;YAEvC,2DAA2D;YAC3D,MAAM,eAAe,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YACpF,MAAM,mBAAmB;gBACvB,IAAI;gBACJ,GAAG,SAAS;gBACZ,SAAS,KAAK,EAAE;gBAChB,aAAa;gBACb,WAAW,UAAU,SAAS,IAAI,CAAC,KAAK,EAAE,gBAAgB;gBAC1D,MAAM,UAAU,IAAI,IAAI,EAAE;gBAC1B,aAAa,UAAU,WAAW,IAAI;gBACtC,aAAa;gBACb,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,kCAAkC;YAClC,YAAY,YAAY,CAAC;gBAAC;gBAAW,UAAU,UAAU;aAAC,EAAE,CAAC;gBAC3D,IAAI,CAAC,MAAM,OAAO,CAAC,MAAM,OAAO;oBAAC;iBAAiB;gBAClD,4CAA4C;gBAC5C,OAAO;oBAAC;uBAAqB;iBAAI;YACnC;YAEA,OAAO;gBAAE;gBAAiB;YAAiB;QAC7C;QACA,SAAS,CAAC,KAAK,WAAW;YACxB,sCAAsC;YACtC,IAAI,SAAS,iBAAiB;gBAC5B,YAAY,YAAY,CAAC;oBAAC;oBAAW,UAAU,UAAU;iBAAC,EAAE,QAAQ,eAAe;YACrF;QACF;QACA,WAAW,OAAO,MAAM,WAAW;YACjC,oEAAoE;YACpE,YAAY,YAAY,CAAC;gBAAC;gBAAW,KAAK,UAAU;aAAC,EAAE,CAAC;gBACtD,IAAI,CAAC,MAAM,OAAO,CAAC,MAAM,OAAO;oBAAC;iBAAK;gBAEtC,wDAAwD;gBACxD,MAAM,cAAc,IAAI,MAAM,CAAC,CAAC,SAC9B,OAAO,EAAE,KAAK,SAAS,kBAAkB,MAAM,OAAO,EAAE,KAAK,KAAK,EAAE;gBAGtE,gDAAgD;gBAChD,OAAO;oBAAC;uBAAS;iBAAY;YAC/B;YAEA,kDAAkD;YAClD,WAAW;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAoB,KAAK,UAAU;qBAAC;gBAAC;gBAChF,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAgB,KAAK,UAAU;qBAAC;gBAAC;gBAC5E,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAsB,KAAK,UAAU;qBAAC;gBAAC;gBAClF,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAkB,KAAK,UAAU;qBAAC;gBAAC;gBAE9E,mCAAmC;gBACnC,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD,IAAI,IAAI,CAAC;oBACtB,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;yBAAc;oBAAC;oBAC1D,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;yBAAc;oBAAC;gBAC5D,GAAG,KAAK,CAAC,CAAA;oBACP,QAAQ,IAAI,CAAC,2CAA2C;gBAC1D;YACF,GAAG;QACL;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EAAE,EAAE,EAAE,GAAG,SAAwC;YAClE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC;gBACN,GAAG,OAAO;gBACV,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,OAAO;QACT;QACA,WAAW,CAAC;YACV,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAW,KAAK,UAAU;iBAAC;YAAC;YACvE,2EAA2E;YAC3E,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAoB,KAAK,UAAU;iBAAC;YAAC;YAChF,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAgB,KAAK,UAAU;iBAAC;YAAC;YAC5E,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAsB,KAAK,UAAU;iBAAC;YAAC;YAClF,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAkB,KAAK,UAAU;iBAAC;YAAC;QAChF;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EAAE,EAAE,EAAqC;YAC1D,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAC7B,IAAI,CAAC,WACL,MAAM,GACN,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;QACF;QACA,WAAW,CAAC,GAAG,EAAE,SAAS,EAAE;YAC1B,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAW;iBAAU;YAAC;QACnE;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO;YACjB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC;gBAAE,SAAS;YAAK,GACvB,EAAE,CAAC,MAAM,UACT,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,OAAO;QACT;QACA,UAAU,OAAO;YACf,oBAAoB;YACpB,MAAM,WAAW;gBAAC;aAAU;YAC5B,MAAM,YAAY,aAAa,CAAC;gBAAE;YAAS;YAE3C,MAAM,kBAAkB,YAAY,cAAc,CAAC;gBAAE;YAAS;YAE9D,YAAY,cAAc,CAAC;gBAAE;YAAS,GAAG,CAAC;gBACxC,IAAI,CAAC,OAAO,CAAC,MAAM,OAAO,CAAC,MAAM,OAAO;gBACxC,OAAO,IAAI,GAAG,CAAC,CAAC,SACd,OAAO,EAAE,KAAK,WAAW;wBAAE,GAAG,MAAM;wBAAE,SAAS;oBAAK,IAAI;YAE5D;YAEA,OAAO;gBAAE;YAAgB;QAC3B;QACA,SAAS,CAAC,KAAK,WAAW;YACxB,yBAAyB;YACzB,IAAI,SAAS,iBAAiB;gBAC5B,QAAQ,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,KAAK;oBAC/C,YAAY,YAAY,CAAC,UAAU;gBACrC;YACF;QACF;QACA,WAAW,CAAC;YACV,6BAA6B;YAC7B,IAAI,MAAM;gBACR,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAW,KAAK,UAAU;qBAAC;gBAAC;YACzE;QACF;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO;YACjB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAC7B,IAAI,CAAC,WACL,MAAM,CAAC;gBAAE,SAAS;YAAM,GACxB,EAAE,CAAC,cAAc;YAEpB,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;QACF;QACA,WAAW,CAAC,GAAG;YACb,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAW;iBAAU;YAAC;QACnE;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO;YACjB,6FAA6F;YAC7F,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK,aAAa,EAAE,SAAS,KAAK;YACxF,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK,aAAa,EAAE,SAAS,KAAK;YAExF,MAAM,UAAoB,EAAE;YAE5B,gEAAgE;YAChE,IAAI,aAAa,MAAM,GAAG,GAAG;gBAC3B,IAAI;oBACF,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,QAAQ,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,6BAA6B;wBACzF,gBAAgB;oBAClB;oBAEA,IAAI,UAAU;wBACZ,QAAQ,IAAI,CAAC,4DAA4D;wBACzE,iCAAiC;wBACjC,MAAM,mBAAmB,aAAa,GAAG,CAAC,OAAO;4BAC/C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC;gCACN,GAAG,MAAM;gCACT,YAAY,IAAI,OAAO,WAAW;4BACpC,GACC,EAAE,CAAC,MAAM,OAAO,EAAE,EAClB,MAAM,GACN,MAAM;4BAET,IAAI,OAAO;gCACT,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,OAAO,EAAE,CAAC,EAAE,EAAE,MAAM,OAAO,EAAE;4BAC1E;4BACA,OAAO;wBACT;wBAEA,MAAM,kBAAkB,MAAM,QAAQ,GAAG,CAAC;wBAC1C,QAAQ,IAAI,IAAI;oBAClB,OAAO,IAAI,SAAS;wBAClB,uCAAuC;wBACvC,MAAM,SAAS,QAAQ,GAAG,CAAC,CAAC,IAAsB,EAAE,EAAE;wBACtD,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAC7D,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM;wBAEZ,IAAI,aAAa;4BACf,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,YAAY,OAAO,EAAE;wBAC3E;wBAEA,QAAQ,IAAI,IAAK,eAAe,EAAE;oBACpC;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,sBAAsB;oBACpC,MAAM;gBACR;YACF;YAEA,6DAA6D;YAC7D,IAAI,aAAa,MAAM,GAAG,GAAG;gBAC3B,MAAM,iBAAiB,aAAa,GAAG,CAAC,OAAO;oBAC7C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC;wBACN,GAAG,MAAM;wBACT,YAAY,IAAI,OAAO,WAAW;oBACpC,GACC,EAAE,CAAC,MAAM,OAAO,EAAE,EAClB,MAAM,GACN,MAAM;oBAET,IAAI,OAAO;wBACT,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,OAAO,EAAE,CAAC,EAAE,EAAE,MAAM,OAAO,EAAE;oBAC1E;oBAEA,OAAO;gBACT;gBAEA,MAAM,iBAAiB,MAAM,QAAQ,GAAG,CAAC;gBACzC,QAAQ,IAAI,IAAI;YAClB;YAEA,OAAO;QACT;QACA,UAAU,OAAO;YACf,oBAAoB;YACpB,MAAM,WAAW;gBAAC;aAAU;YAC5B,MAAM,YAAY,aAAa,CAAC;gBAAE;YAAS;YAE3C,MAAM,kBAAkB,YAAY,cAAc,CAAC;gBAAE;YAAS;YAE9D,YAAY,cAAc,CAAC;gBAAE;YAAS,GAAG,CAAC;gBACxC,IAAI,CAAC,OAAO,CAAC,MAAM,OAAO,CAAC,MAAM,OAAO;gBAExC,OAAO,IAAI,GAAG,CAAC,CAAC;oBACd,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,EAAE;oBACnD,OAAO,SAAS;wBAAE,GAAG,MAAM;wBAAE,GAAG,MAAM;oBAAC,IAAI;gBAC7C;YACF;YAEA,OAAO;gBAAE;YAAgB;QAC3B;QACA,SAAS,CAAC,KAAK,UAAU;YACvB,yBAAyB;YACzB,IAAI,SAAS,iBAAiB;gBAC5B,QAAQ,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,KAAK;oBAC/C,YAAY,YAAY,CAAC,UAAU;gBACrC;YACF;QACF;QACA,WAAW,CAAC;YACV,6BAA6B;YAC7B,IAAI,QAAQ,KAAK,MAAM,GAAG,GAAG;gBAC3B,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAW,IAAI,CAAC,EAAE,CAAC,UAAU;qBAAC;gBAAC;YAC5E;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 1025, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/hooks/use-context-to-prompt.ts"], "sourcesContent": ["'use client'\n\nimport { useMutation, useQueryClient } from '@tanstack/react-query'\nimport { useCreatePrompt } from './use-prompts'\nimport { useProjects } from './use-projects'\nimport { Context } from '@/components/context-gallery'\nimport { toast } from 'sonner'\n\ninterface ContextToPromptOptions {\n  /** Target project ID. If not provided, uses active project */\n  projectId?: string\n  /** Custom title for the prompt. If not provided, uses context title */\n  customTitle?: string\n  /** Additional tags to add to the prompt */\n  additionalTags?: string[]\n  /** Whether to mark as favorite */\n  markAsFavorite?: boolean\n  /** Custom category for the prompt */\n  customCategory?: string\n}\n\ninterface ContextToPromptResult {\n  success: boolean\n  promptId?: string\n  error?: string\n}\n\n/**\n * Hook for converting Context Gallery contexts to prompts in current project\n * Handles validation, limits checking, and proper data transformation\n */\nexport function useContextToPrompt() {\n  const queryClient = useQueryClient()\n  const createPromptMutation = useCreatePrompt()\n  const { data: projects } = useProjects()\n\n  return useMutation({\n    mutationFn: async ({\n      context,\n      options = {}\n    }: {\n      context: Context\n      options?: ContextToPromptOptions\n    }): Promise<ContextToPromptResult> => {\n      try {\n        // 1. Determine target project\n        const targetProjectId = options.projectId || getActiveProjectId(projects || [])\n        \n        if (!targetProjectId) {\n          throw new Error('Lütfen önce bir proje seçin')\n        }\n\n        // 2. Prepare prompt data\n        const promptData = {\n          project_id: targetProjectId,\n          prompt_text: context.content,\n          title: options.customTitle || context.title || 'Context Gallery Prompt',\n          category: options.customCategory || extractCategoryFromContext(context),\n          tags: combineTagsFromContext(context, options.additionalTags),\n          order_index: await getNextOrderIndex(targetProjectId),\n          is_used: false,\n          is_favorite: options.markAsFavorite || false\n        }\n\n        // 3. Create prompt using existing hook\n        const newPrompt = await createPromptMutation.mutateAsync(promptData)\n\n        // 4. Track context usage\n        await trackContextUsage(context.id, targetProjectId)\n\n        // 5. Update UI state\n        queryClient.invalidateQueries({ queryKey: ['prompts', targetProjectId] })\n        queryClient.invalidateQueries({ queryKey: ['projects'] })\n\n        return {\n          success: true,\n          promptId: newPrompt.id\n        }\n\n      } catch (error) {\n        console.error('Context to prompt conversion error:', error)\n        \n        const errorMessage = error instanceof Error \n          ? error.message \n          : 'Context prompt olarak eklenirken hata oluştu'\n\n        return {\n          success: false,\n          error: errorMessage\n        }\n      }\n    },\n    onSuccess: (result) => {\n      if (result.success) {\n        toast.success('Context başarıyla projeye eklendi!')\n      } else {\n        toast.error(result.error || 'Bir hata oluştu')\n      }\n    },\n    onError: (error) => {\n      console.error('Context to prompt mutation error:', error)\n      toast.error('Context eklenirken beklenmeyen bir hata oluştu')\n    }\n  })\n}\n\n/**\n * Simplified hook for quick context addition to active project\n */\nexport function useAddContextToProject() {\n  const contextToPromptMutation = useContextToPrompt()\n\n  return {\n    addContext: async (context: Context, customTitle?: string) => {\n      return contextToPromptMutation.mutateAsync({\n        context,\n        options: { customTitle }\n      })\n    },\n    isLoading: contextToPromptMutation.isPending,\n    error: contextToPromptMutation.error\n  }\n}\n\n/**\n * Hook for batch adding multiple contexts to project\n */\nexport function useBatchAddContexts() {\n  const contextToPromptMutation = useContextToPrompt()\n  // const queryClient = useQueryClient() // Unused for now\n\n  return useMutation({\n    mutationFn: async ({\n      contexts,\n      projectId,\n      options = {}\n    }: {\n      contexts: Context[]\n      projectId?: string\n      options?: Omit<ContextToPromptOptions, 'projectId'>\n    }) => {\n      const results = []\n      \n      for (const context of contexts) {\n        try {\n          const result = await contextToPromptMutation.mutateAsync({\n            context,\n            options: { ...options, projectId }\n          })\n          results.push({ context: context.id, result })\n        } catch (error) {\n          results.push({ \n            context: context.id, \n            result: { success: false, error: error instanceof Error ? error.message : 'Unknown error' }\n          })\n        }\n      }\n\n      return results\n    },\n    onSuccess: (results) => {\n      const successCount = results.filter(r => r.result.success).length\n      const totalCount = results.length\n      \n      if (successCount === totalCount) {\n        toast.success(`${successCount} context başarıyla eklendi!`)\n      } else {\n        toast.warning(`${successCount}/${totalCount} context eklendi. Bazı hatalar oluştu.`)\n      }\n    }\n  })\n}\n\n// Helper functions\ninterface ProjectLike {\n  id: string\n}\n\nfunction getActiveProjectId(projects: unknown[]): string | null {\n  if (!projects || projects.length === 0) return null\n\n  // Try to get from localStorage or URL params\n  const stored = localStorage.getItem('activeProjectId')\n  if (stored && projects.find((p: unknown) => (p as ProjectLike)?.id === stored)) {\n    return stored\n  }\n\n  // Fallback to first project\n  return (projects[0] as ProjectLike)?.id || null\n}\n\nfunction extractCategoryFromContext(context: Context): string | undefined {\n  // Extract category from context metadata or tags\n  if (context.category) return context.category.name || context.category.toString()\n  if (context.tags && context.tags.length > 0) {\n    // Use first tag as category if it looks like a category\n    const firstTag = context.tags[0]\n    if (firstTag.startsWith('/')) return firstTag\n  }\n  return undefined\n}\n\nfunction combineTagsFromContext(context: Context, additionalTags?: string[]): string[] {\n  const contextTags = context.tags || []\n  const additional = additionalTags || []\n  \n  // Combine and deduplicate tags\n  const allTags = [...contextTags, ...additional]\n  return Array.from(new Set(allTags)).filter(tag => tag.trim().length > 0)\n}\n\nasync function getNextOrderIndex(_projectId: string): Promise<number> {\n  // This would typically query the database to get the next order index\n  // For now, return a timestamp-based index\n  return Date.now()\n}\n\nasync function trackContextUsage(contextId: string, projectId: string): Promise<void> {\n  try {\n    // Track context usage in analytics\n    // This could be implemented with Supabase or analytics service\n    console.log(`Context ${contextId} used in project ${projectId}`)\n  } catch (error) {\n    console.warn('Failed to track context usage:', error)\n    // Don't throw error for analytics failure\n  }\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AACA;AACA;AAEA;AANA;;;;;AA+BO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,uBAAuB,CAAA,GAAA,8HAAA,CAAA,kBAAe,AAAD;IAC3C,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD;IAErC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EACjB,OAAO,EACP,UAAU,CAAC,CAAC,EAIb;YACC,IAAI;gBACF,8BAA8B;gBAC9B,MAAM,kBAAkB,QAAQ,SAAS,IAAI,mBAAmB,YAAY,EAAE;gBAE9E,IAAI,CAAC,iBAAiB;oBACpB,MAAM,IAAI,MAAM;gBAClB;gBAEA,yBAAyB;gBACzB,MAAM,aAAa;oBACjB,YAAY;oBACZ,aAAa,QAAQ,OAAO;oBAC5B,OAAO,QAAQ,WAAW,IAAI,QAAQ,KAAK,IAAI;oBAC/C,UAAU,QAAQ,cAAc,IAAI,2BAA2B;oBAC/D,MAAM,uBAAuB,SAAS,QAAQ,cAAc;oBAC5D,aAAa,MAAM,kBAAkB;oBACrC,SAAS;oBACT,aAAa,QAAQ,cAAc,IAAI;gBACzC;gBAEA,uCAAuC;gBACvC,MAAM,YAAY,MAAM,qBAAqB,WAAW,CAAC;gBAEzD,yBAAyB;gBACzB,MAAM,kBAAkB,QAAQ,EAAE,EAAE;gBAEpC,qBAAqB;gBACrB,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAW;qBAAgB;gBAAC;gBACvE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBAEvD,OAAO;oBACL,SAAS;oBACT,UAAU,UAAU,EAAE;gBACxB;YAEF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uCAAuC;gBAErD,MAAM,eAAe,iBAAiB,QAClC,MAAM,OAAO,GACb;gBAEJ,OAAO;oBACL,SAAS;oBACT,OAAO;gBACT;YACF;QACF;QACA,WAAW,CAAC;YACV,IAAI,OAAO,OAAO,EAAE;gBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;YAC9B;QACF;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,qCAAqC;YACnD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;AACF;AAKO,SAAS;IACd,MAAM,0BAA0B;IAEhC,OAAO;QACL,YAAY,OAAO,SAAkB;YACnC,OAAO,wBAAwB,WAAW,CAAC;gBACzC;gBACA,SAAS;oBAAE;gBAAY;YACzB;QACF;QACA,WAAW,wBAAwB,SAAS;QAC5C,OAAO,wBAAwB,KAAK;IACtC;AACF;AAKO,SAAS;IACd,MAAM,0BAA0B;IAChC,yDAAyD;IAEzD,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EACjB,QAAQ,EACR,SAAS,EACT,UAAU,CAAC,CAAC,EAKb;YACC,MAAM,UAAU,EAAE;YAElB,KAAK,MAAM,WAAW,SAAU;gBAC9B,IAAI;oBACF,MAAM,SAAS,MAAM,wBAAwB,WAAW,CAAC;wBACvD;wBACA,SAAS;4BAAE,GAAG,OAAO;4BAAE;wBAAU;oBACnC;oBACA,QAAQ,IAAI,CAAC;wBAAE,SAAS,QAAQ,EAAE;wBAAE;oBAAO;gBAC7C,EAAE,OAAO,OAAO;oBACd,QAAQ,IAAI,CAAC;wBACX,SAAS,QAAQ,EAAE;wBACnB,QAAQ;4BAAE,SAAS;4BAAO,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAAgB;oBAC5F;gBACF;YACF;YAEA,OAAO;QACT;QACA,WAAW,CAAC;YACV,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM;YACjE,MAAM,aAAa,QAAQ,MAAM;YAEjC,IAAI,iBAAiB,YAAY;gBAC/B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,aAAa,2BAA2B,CAAC;YAC5D,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,aAAa,CAAC,EAAE,WAAW,sCAAsC,CAAC;YACrF;QACF;IACF;AACF;AAOA,SAAS,mBAAmB,QAAmB;IAC7C,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG,OAAO;IAE/C,6CAA6C;IAC7C,MAAM,SAAS,aAAa,OAAO,CAAC;IACpC,IAAI,UAAU,SAAS,IAAI,CAAC,CAAC,IAAe,AAAC,GAAmB,OAAO,SAAS;QAC9E,OAAO;IACT;IAEA,4BAA4B;IAC5B,OAAO,AAAC,QAAQ,CAAC,EAAE,EAAkB,MAAM;AAC7C;AAEA,SAAS,2BAA2B,OAAgB;IAClD,iDAAiD;IACjD,IAAI,QAAQ,QAAQ,EAAE,OAAO,QAAQ,QAAQ,CAAC,IAAI,IAAI,QAAQ,QAAQ,CAAC,QAAQ;IAC/E,IAAI,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,GAAG;QAC3C,wDAAwD;QACxD,MAAM,WAAW,QAAQ,IAAI,CAAC,EAAE;QAChC,IAAI,SAAS,UAAU,CAAC,MAAM,OAAO;IACvC;IACA,OAAO;AACT;AAEA,SAAS,uBAAuB,OAAgB,EAAE,cAAyB;IACzE,MAAM,cAAc,QAAQ,IAAI,IAAI,EAAE;IACtC,MAAM,aAAa,kBAAkB,EAAE;IAEvC,+BAA+B;IAC/B,MAAM,UAAU;WAAI;WAAgB;KAAW;IAC/C,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,UAAU,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,GAAG,MAAM,GAAG;AACxE;AAEA,eAAe,kBAAkB,UAAkB;IACjD,sEAAsE;IACtE,0CAA0C;IAC1C,OAAO,KAAK,GAAG;AACjB;AAEA,eAAe,kBAAkB,SAAiB,EAAE,SAAiB;IACnE,IAAI;QACF,mCAAmC;QACnC,+DAA+D;QAC/D,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,UAAU,iBAAiB,EAAE,WAAW;IACjE,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,kCAAkC;IAC/C,0CAA0C;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 1211, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/context-creation-modal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Badge } from '@/components/ui/badge'\nimport { \n  Dialog, \n  DialogContent, \n  DialogDescription, \n  DialogHeader, \n  DialogTitle \n} from '@/components/ui/dialog'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { \n  Plus, \n  X, \n  Loader2, \n  AlertCircle, \n  Globe, \n  Lock, \n  Tag,\n  FileText\n} from 'lucide-react'\nimport { useContextCategories, useCreateContext } from '@/hooks/use-contexts'\nimport { useAddContextToProject } from '@/hooks/use-context-to-prompt'\nimport { useProjects } from '@/hooks/use-projects'\nimport { toast } from 'sonner'\n\ninterface ContextCreationModalProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  onSuccess?: () => void\n  /** Show option to add directly to current project */\n  showAddToProject?: boolean\n}\n\nexport function ContextCreationModal({\n  open,\n  onOpenChange,\n  onSuccess,\n  showAddToProject = true\n}: ContextCreationModalProps) {\n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    content: '',\n    category_id: '',\n    is_public: false,\n    is_template: false,\n    tags: [] as string[]\n  })\n  const [newTag, setNewTag] = useState('')\n  const [errors, setErrors] = useState<Record<string, string>>({})\n  const [addToProject, setAddToProject] = useState(false)\n\n  const { data: categories = [], isLoading: categoriesLoading, error: categoriesError } = useContextCategories()\n  const { data: projects = [] } = useProjects()\n  const createContextMutation = useCreateContext()\n  const { addContext: addContextToProject, isLoading: isAddingToProject } = useAddContextToProject()\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {}\n\n    if (!formData.title.trim()) {\n      newErrors.title = 'Başlık gereklidir'\n    }\n\n    if (!formData.content.trim()) {\n      newErrors.content = 'İçerik gereklidir'\n    }\n\n    if (!formData.category_id) {\n      newErrors.category_id = 'Kategori seçimi gereklidir'\n    }\n\n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n\n    if (!validateForm()) {\n      toast.error('Lütfen tüm gerekli alanları doldurun')\n      return\n    }\n\n    try {\n      // Create context first\n      const newContext = await createContextMutation.mutateAsync({\n        title: formData.title.trim(),\n        description: formData.description.trim() || undefined,\n        content: formData.content.trim(),\n        category_id: formData.category_id,\n        is_public: formData.is_public,\n        is_template: formData.is_template,\n        tags: formData.tags\n      })\n\n      // If \"Add to Project\" is checked, add to current project\n      if (addToProject && showAddToProject) {\n        try {\n          await addContextToProject(newContext, formData.title.trim())\n          toast.success('Context oluşturuldu ve projeye eklendi!')\n        } catch (projectError) {\n          console.error('Failed to add to project:', projectError)\n          toast.warning('Context oluşturuldu ancak projeye eklenirken hata oluştu')\n        }\n      } else {\n        toast.success('Context başarıyla oluşturuldu!')\n      }\n\n      // Reset form\n      setFormData({\n        title: '',\n        description: '',\n        content: '',\n        category_id: '',\n        is_public: false,\n        is_template: false,\n        tags: []\n      })\n      setErrors({})\n      setAddToProject(false)\n\n      onSuccess?.()\n      onOpenChange(false)\n    } catch (error) {\n      console.error('Context creation error:', error)\n      toast.error('Context oluşturulurken bir hata oluştu')\n    }\n  }\n\n  const addTag = () => {\n    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {\n      setFormData(prev => ({\n        ...prev,\n        tags: [...prev.tags, newTag.trim()]\n      }))\n      setNewTag('')\n    }\n  }\n\n  const removeTag = (tagToRemove: string) => {\n    setFormData(prev => ({\n      ...prev,\n      tags: prev.tags.filter(tag => tag !== tagToRemove)\n    }))\n  }\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter') {\n      e.preventDefault()\n      addTag()\n    }\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-2xl max-h-[90vh] overflow-hidden flex flex-col z-[60]\">\n        <DialogHeader className=\"flex-shrink-0\">\n          <DialogTitle className=\"flex items-center gap-2\">\n            <FileText className=\"h-5 w-5\" />\n            Yeni Context Oluştur\n          </DialogTitle>\n          <DialogDescription>\n            Yeni bir context oluşturun. Herkese açık contextler admin onayı gerektirir.\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"flex-1 overflow-y-auto\">\n          <form onSubmit={handleSubmit} className=\"space-y-6 p-1\">\n            {/* Title */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"title\">\n                Başlık <span className=\"text-red-500\">*</span>\n              </Label>\n              <Input\n                id=\"title\"\n                placeholder=\"Context başlığını girin...\"\n                value={formData.title}\n                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}\n                className={errors.title ? 'border-red-500' : ''}\n              />\n              {errors.title && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-3 w-3\" />\n                  {errors.title}\n                </p>\n              )}\n            </div>\n\n            {/* Description */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"description\">Açıklama</Label>\n              <Input\n                id=\"description\"\n                placeholder=\"Context açıklaması (isteğe bağlı)...\"\n                value={formData.description}\n                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n              />\n            </div>\n\n            {/* Category */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"category\">\n                Kategori <span className=\"text-red-500\">*</span>\n              </Label>\n              <Select\n                value={formData.category_id}\n                onValueChange={(value) => setFormData(prev => ({ ...prev, category_id: value }))}\n              >\n                <SelectTrigger className={errors.category_id ? 'border-red-500' : ''}>\n                  <SelectValue placeholder=\"Kategori seçin...\" />\n                </SelectTrigger>\n                <SelectContent className=\"z-[70]\">\n                  {categoriesLoading ? (\n                    <SelectItem value=\"loading\" disabled>\n                      Kategoriler yükleniyor...\n                    </SelectItem>\n                  ) : categoriesError ? (\n                    <SelectItem value=\"error\" disabled>\n                      Kategori yükleme hatası: {categoriesError.message}\n                    </SelectItem>\n                  ) : categories.length === 0 ? (\n                    <SelectItem value=\"empty\" disabled>\n                      Kategori bulunamadı\n                    </SelectItem>\n                  ) : (\n                    categories.map((category) => (\n                      <SelectItem key={category.id} value={category.id}>\n                        <span className=\"flex items-center gap-2\">\n                          <span>{category.icon || '📁'}</span>\n                          {category.name}\n                        </span>\n                      </SelectItem>\n                    ))\n                  )}\n                </SelectContent>\n              </Select>\n              {errors.category_id && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-3 w-3\" />\n                  {errors.category_id}\n                </p>\n              )}\n            </div>\n\n            {/* Content */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"content\">\n                İçerik <span className=\"text-red-500\">*</span>\n              </Label>\n              <Textarea\n                id=\"content\"\n                placeholder=\"Context içeriğini girin...\"\n                value={formData.content}\n                onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}\n                className={`min-h-[120px] resize-none ${errors.content ? 'border-red-500' : ''}`}\n              />\n              {errors.content && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-3 w-3\" />\n                  {errors.content}\n                </p>\n              )}\n            </div>\n\n            {/* Tags */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"tags\">Etiketler</Label>\n              <div className=\"flex gap-2\">\n                <Input\n                  id=\"tags\"\n                  placeholder=\"Etiket ekle...\"\n                  value={newTag}\n                  onChange={(e) => setNewTag(e.target.value)}\n                  onKeyPress={handleKeyPress}\n                  className=\"flex-1\"\n                />\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={addTag}\n                  disabled={!newTag.trim()}\n                >\n                  <Plus className=\"h-4 w-4\" />\n                </Button>\n              </div>\n              {formData.tags.length > 0 && (\n                <div className=\"flex flex-wrap gap-2 mt-2\">\n                  {formData.tags.map((tag) => (\n                    <Badge key={tag} variant=\"secondary\" className=\"flex items-center gap-1\">\n                      <Tag className=\"h-3 w-3\" />\n                      {tag}\n                      <button\n                        type=\"button\"\n                        onClick={() => removeTag(tag)}\n                        className=\"ml-1 hover:text-red-500\"\n                      >\n                        <X className=\"h-3 w-3\" />\n                      </button>\n                    </Badge>\n                  ))}\n                </div>\n              )}\n            </div>\n\n            {/* Visibility Options */}\n            <div className=\"space-y-3\">\n              <Label>Görünürlük Ayarları</Label>\n              <div className=\"space-y-2\">\n                <label className=\"flex items-center gap-3 cursor-pointer\">\n                  <input\n                    type=\"radio\"\n                    name=\"visibility\"\n                    checked={!formData.is_public}\n                    onChange={() => setFormData(prev => ({ ...prev, is_public: false }))}\n                    className=\"w-4 h-4\"\n                  />\n                  <div className=\"flex items-center gap-2\">\n                    <Lock className=\"h-4 w-4 text-gray-500\" />\n                    <div className=\"flex flex-col\">\n                      <span className=\"text-sm\">Özel</span>\n                      <span className=\"text-xs text-gray-500\">Sadece ben görebilirim</span>\n                    </div>\n                  </div>\n                </label>\n                <label className=\"flex items-center gap-3 cursor-pointer\">\n                  <input\n                    type=\"radio\"\n                    name=\"visibility\"\n                    checked={formData.is_public}\n                    onChange={() => setFormData(prev => ({ ...prev, is_public: true }))}\n                    className=\"w-4 h-4\"\n                  />\n                  <div className=\"flex items-center gap-2\">\n                    <Globe className=\"h-4 w-4 text-blue-500\" />\n                    <div className=\"flex flex-col\">\n                      <span className=\"text-sm\">Herkese Açık</span>\n                      <span className=\"text-xs text-gray-500\">Tüm kullanıcılar görebilir</span>\n                    </div>\n                  </div>\n                </label>\n              </div>\n            </div>\n\n            {/* Template Option */}\n            <div className=\"space-y-2\">\n              <label className=\"flex items-center gap-3 cursor-pointer\">\n                <input\n                  type=\"checkbox\"\n                  checked={formData.is_template}\n                  onChange={(e) => setFormData(prev => ({ ...prev, is_template: e.target.checked }))}\n                  className=\"w-4 h-4\"\n                />\n                <span className=\"text-sm\">Bu contexti şablon olarak işaretle</span>\n              </label>\n            </div>\n\n            {/* Add to Project Option */}\n            {showAddToProject && projects.length > 0 && (\n              <div className=\"space-y-2 p-3 bg-blue-50 rounded-lg border border-blue-200\">\n                <label className=\"flex items-center gap-3 cursor-pointer\">\n                  <input\n                    type=\"checkbox\"\n                    checked={addToProject}\n                    onChange={(e) => setAddToProject(e.target.checked)}\n                    className=\"w-4 h-4 text-blue-600\"\n                  />\n                  <div className=\"flex items-center gap-2\">\n                    <FileText className=\"h-4 w-4 text-blue-600\" />\n                    <div className=\"flex flex-col\">\n                      <span className=\"text-sm font-medium text-blue-900\">Mevcut projeye ekle</span>\n                      <span className=\"text-xs text-blue-700\">Context oluşturulduktan sonra aktif projeye prompt olarak eklenecek</span>\n                    </div>\n                  </div>\n                </label>\n              </div>\n            )}\n\n            {/* Submit Buttons */}\n            <div className=\"flex gap-3 pt-4\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => onOpenChange(false)}\n                className=\"flex-1\"\n              >\n                İptal\n              </Button>\n              <Button\n                type=\"submit\"\n                disabled={createContextMutation.isPending || isAddingToProject}\n                className=\"flex-1\"\n              >\n                {(createContextMutation.isPending || isAddingToProject) && (\n                  <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                )}\n                {addToProject ? 'Oluştur ve Ekle' : 'Oluştur'}\n              </Button>\n            </div>\n          </form>\n        </div>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AACA;AAnCA;;;;;;;;;;;;;;;AA6CO,SAAS,qBAAqB,EACnC,IAAI,EACJ,YAAY,EACZ,SAAS,EACT,mBAAmB,IAAI,EACG;IAC1B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,aAAa;QACb,SAAS;QACT,aAAa;QACb,WAAW;QACX,aAAa;QACb,MAAM,EAAE;IACV;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,EAAE,MAAM,aAAa,EAAE,EAAE,WAAW,iBAAiB,EAAE,OAAO,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,uBAAoB,AAAD;IAC3G,MAAM,EAAE,MAAM,WAAW,EAAE,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD;IAC1C,MAAM,wBAAwB,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD;IAC7C,MAAM,EAAE,YAAY,mBAAmB,EAAE,WAAW,iBAAiB,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,yBAAsB,AAAD;IAE/F,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,UAAU,OAAO,GAAG;QACtB;QAEA,IAAI,CAAC,SAAS,WAAW,EAAE;YACzB,UAAU,WAAW,GAAG;QAC1B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,uBAAuB;YACvB,MAAM,aAAa,MAAM,sBAAsB,WAAW,CAAC;gBACzD,OAAO,SAAS,KAAK,CAAC,IAAI;gBAC1B,aAAa,SAAS,WAAW,CAAC,IAAI,MAAM;gBAC5C,SAAS,SAAS,OAAO,CAAC,IAAI;gBAC9B,aAAa,SAAS,WAAW;gBACjC,WAAW,SAAS,SAAS;gBAC7B,aAAa,SAAS,WAAW;gBACjC,MAAM,SAAS,IAAI;YACrB;YAEA,yDAAyD;YACzD,IAAI,gBAAgB,kBAAkB;gBACpC,IAAI;oBACF,MAAM,oBAAoB,YAAY,SAAS,KAAK,CAAC,IAAI;oBACzD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB,EAAE,OAAO,cAAc;oBACrB,QAAQ,KAAK,CAAC,6BAA6B;oBAC3C,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB;YACF,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;YAEA,aAAa;YACb,YAAY;gBACV,OAAO;gBACP,aAAa;gBACb,SAAS;gBACT,aAAa;gBACb,WAAW;gBACX,aAAa;gBACb,MAAM,EAAE;YACV;YACA,UAAU,CAAC;YACX,gBAAgB;YAEhB;YACA,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,SAAS;QACb,IAAI,OAAO,IAAI,MAAM,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,KAAK;YAC3D,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,MAAM;2BAAI,KAAK,IAAI;wBAAE,OAAO,IAAI;qBAAG;gBACrC,CAAC;YACD,UAAU;QACZ;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,QAAQ;YACxC,CAAC;IACH;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGlC,8OAAC,kIAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAEtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;;4CAAQ;0DACd,8OAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAExC,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAY;wCACZ,OAAO,SAAS,KAAK;wCACrB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCACxE,WAAW,OAAO,KAAK,GAAG,mBAAmB;;;;;;oCAE9C,OAAO,KAAK,kBACX,8OAAC;wCAAE,WAAU;;0DACX,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,KAAK;;;;;;;;;;;;;0CAMnB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAc;;;;;;kDAC7B,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAY;wCACZ,OAAO,SAAS,WAAW;wCAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;;;;;;;;;;;;0CAKlF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;;4CAAW;0DACf,8OAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAE1C,8OAAC,kIAAA,CAAA,SAAM;wCACL,OAAO,SAAS,WAAW;wCAC3B,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,aAAa;gDAAM,CAAC;;0DAE9E,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAW,OAAO,WAAW,GAAG,mBAAmB;0DAChE,cAAA,8OAAC,kIAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACtB,kCACC,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;oDAAU,QAAQ;8DAAC;;;;;2DAGnC,gCACF,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;oDAAQ,QAAQ;;wDAAC;wDACP,gBAAgB,OAAO;;;;;;2DAEjD,WAAW,MAAM,KAAK,kBACxB,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;oDAAQ,QAAQ;8DAAC;;;;;2DAInC,WAAW,GAAG,CAAC,CAAC,yBACd,8OAAC,kIAAA,CAAA,aAAU;wDAAmB,OAAO,SAAS,EAAE;kEAC9C,cAAA,8OAAC;4DAAK,WAAU;;8EACd,8OAAC;8EAAM,SAAS,IAAI,IAAI;;;;;;gEACvB,SAAS,IAAI;;;;;;;uDAHD,SAAS,EAAE;;;;;;;;;;;;;;;;oCAUnC,OAAO,WAAW,kBACjB,8OAAC;wCAAE,WAAU;;0DACX,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,WAAW;;;;;;;;;;;;;0CAMzB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;;4CAAU;0DAChB,8OAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAExC,8OAAC,oIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,aAAY;wCACZ,OAAO,SAAS,OAAO;wCACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC1E,WAAW,CAAC,0BAA0B,EAAE,OAAO,OAAO,GAAG,mBAAmB,IAAI;;;;;;oCAEjF,OAAO,OAAO,kBACb,8OAAC;wCAAE,WAAU;;0DACX,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,OAAO;;;;;;;;;;;;;0CAMrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAO;;;;;;kDACtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gDACzC,YAAY;gDACZ,WAAU;;;;;;0DAEZ,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC,OAAO,IAAI;0DAEtB,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;oCAGnB,SAAS,IAAI,CAAC,MAAM,GAAG,mBACtB,8OAAC;wCAAI,WAAU;kDACZ,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,oBAClB,8OAAC,iIAAA,CAAA,QAAK;gDAAW,SAAQ;gDAAY,WAAU;;kEAC7C,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDACd;kEACD,8OAAC;wDACC,MAAK;wDACL,SAAS,IAAM,UAAU;wDACzB,WAAU;kEAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4DAAC,WAAU;;;;;;;;;;;;+CARL;;;;;;;;;;;;;;;;0CAiBpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,SAAS,CAAC,SAAS,SAAS;wDAC5B,UAAU,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,WAAW;gEAAM,CAAC;wDAClE,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAU;;;;;;kFAC1B,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;0DAI9C,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,SAAS,SAAS,SAAS;wDAC3B,UAAU,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,WAAW;gEAAK,CAAC;wDACjE,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAU;;;;;;kFAC1B,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQlD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CACC,MAAK;4CACL,SAAS,SAAS,WAAW;4CAC7B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,aAAa,EAAE,MAAM,CAAC,OAAO;oDAAC,CAAC;4CAChF,WAAU;;;;;;sDAEZ,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;4BAK7B,oBAAoB,SAAS,MAAM,GAAG,mBACrC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,OAAO;4CACjD,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAoC;;;;;;sEACpD,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQlD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,SAAS,IAAM,aAAa;wCAC5B,WAAU;kDACX;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,UAAU,sBAAsB,SAAS,IAAI;wCAC7C,WAAU;;4CAET,CAAC,sBAAsB,SAAS,IAAI,iBAAiB,mBACpD,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAEpB,eAAe,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpD", "debugId": null}}]}
"use strict";exports.id=8066,exports.ids=[5107,8066],exports.modules={8066:(a,b,c)=>{c.r(b),c.d(b,{PromptWorkspace:()=>df,default:()=>dg});var d,e,f,g,h,i,j,k,l,m,n=c(60687),o=c(43210),p=c.n(o),q=c(29523),r=c(44493),s=c(34729),t=c(89667),u=c(24711),v=c(96834),w=c(70569),x=c(98599),y=c(11273),z=c(65551),A=c(83721),B=c(18853),C=c(14163),D="Switch",[E,F]=(0,y.A)(D),[G,H]=E(D),I=o.forwardRef((a,b)=>{let{__scopeSwitch:c,name:d,checked:e,defaultChecked:f,required:g,disabled:h,value:i="on",onCheckedChange:j,form:k,...l}=a,[m,p]=o.useState(null),q=(0,x.s)(b,a=>p(a)),r=o.useRef(!1),s=!m||k||!!m.closest("form"),[t,u]=(0,z.i)({prop:e,defaultProp:f??!1,onChange:j,caller:D});return(0,n.jsxs)(G,{scope:c,checked:t,disabled:h,children:[(0,n.jsx)(C.sG.button,{type:"button",role:"switch","aria-checked":t,"aria-required":g,"data-state":M(t),"data-disabled":h?"":void 0,disabled:h,value:i,...l,ref:q,onClick:(0,w.m)(a.onClick,a=>{u(a=>!a),s&&(r.current=a.isPropagationStopped(),r.current||a.stopPropagation())})}),s&&(0,n.jsx)(L,{control:m,bubbles:!r.current,name:d,value:i,checked:t,required:g,disabled:h,form:k,style:{transform:"translateX(-100%)"}})]})});I.displayName=D;var J="SwitchThumb",K=o.forwardRef((a,b)=>{let{__scopeSwitch:c,...d}=a,e=H(J,c);return(0,n.jsx)(C.sG.span,{"data-state":M(e.checked),"data-disabled":e.disabled?"":void 0,...d,ref:b})});K.displayName=J;var L=o.forwardRef(({__scopeSwitch:a,control:b,checked:c,bubbles:d=!0,...e},f)=>{let g=o.useRef(null),h=(0,x.s)(g,f),i=(0,A.Z)(c),j=(0,B.X)(b);return o.useEffect(()=>{let a=g.current;if(!a)return;let b=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(i!==c&&b){let e=new Event("click",{bubbles:d});b.call(a,c),a.dispatchEvent(e)}},[i,c,d]),(0,n.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:c,...e,tabIndex:-1,ref:h,style:{...e.style,...j,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function M(a){return a?"checked":"unchecked"}L.displayName="SwitchBubbleInput";var N=c(4780);let O=o.forwardRef(({className:a,...b},c)=>(0,n.jsx)(I,{className:(0,N.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",a),...b,ref:c,children:(0,n.jsx)(K,{className:(0,N.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));O.displayName=I.displayName;var P=c(3589),Q=c(78272),R=c(62688);let S=(0,R.A)("grip-vertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]]),T=(0,R.A)("square-check-big",[["path",{d:"M21 10.656V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12.344",key:"2acyp4"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),U=(0,R.A)("square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]);var V=c(5336),W=c(65822),X=c(13964),Y=c(11860),Z=c(90131),$=c(70615),_=c(82570);let aa=(0,R.A)("hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]);var ab=c(10022),ac=c(12941),ad=c(84027),ae=c(99270),af=c(96474);let ag=(0,R.A)("mouse-pointer",[["path",{d:"M12.586 12.586 19 19",key:"ea5xo7"}],["path",{d:"M3.688 3.037a.497.497 0 0 0-.651.651l6.5 15.999a.501.501 0 0 0 .947-.062l1.569-6.083a2 2 0 0 1 1.448-1.479l6.124-1.579a.5.5 0 0 0 .063-.947z",key:"277e5u"}]]),ah=(0,R.A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]),ai=(0,R.A)("undo",[["path",{d:"M3 7v6h6",key:"1v2h90"}],["path",{d:"M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13",key:"1r6uu6"}]]);var aj=c(28555),ak=c(33295),al=c(97421),am=c(51423),an=c(8266);function ao(a,b=20){return(0,am.I)({queryKey:["popular-hashtags",a,b],queryFn:async()=>{if(!a)return[];let{data:c,error:d}=await an.L.from("prompts").select("tags").eq("project_id",a);if(d)throw Error(d.message);let e={};return c?.forEach(a=>{a.tags&&Array.isArray(a.tags)&&a.tags.forEach(a=>{if("string"==typeof a&&a.trim()){let b=a.startsWith("#")?a.toLowerCase():`#${a.toLowerCase()}`;e[b]=(e[b]||0)+1}})}),Object.entries(e).map(([a,b])=>({hashtag:a,count:b})).sort((a,b)=>b.count-a.count).slice(0,b)},enabled:!!a})}function ap(a,b=10){return(0,am.I)({queryKey:["popular-categories",a,b],queryFn:async()=>{if(!a)return[];let{data:c,error:d}=await an.L.from("prompts").select("category").eq("project_id",a).not("category","is",null);if(d)throw Error(d.message);let e={};return c?.forEach(a=>{if(a.category){let b=a.category.toLowerCase();e[b]=(e[b]||0)+1}}),Object.entries(e).map(([a,b])=>({category:a,count:b})).sort((a,b)=>b.count-a.count).slice(0,b)},enabled:!!a})}var aq=c(34248),ar=c(66432),as=c(52581);function at(a){return a.replace(/^#/,"").trim().toLowerCase()}function au(a){let b=a.trim().toLowerCase();return b.startsWith("/")||(b="/"+b),(b=b.replace(/\/+/g,"/")).length>1&&b.endsWith("/")&&(b=b.slice(0,-1)),b}function av(a){let b=function(a){let b=a.match(/#[\w\u00C0-\u017F]+/g);return b?b.map(a=>a.toLowerCase()):[]}(a),c=function(a){let b=a.match(/\/[\w\u00C0-\u017F\/]+/g);return b?b.map(a=>a.toLowerCase()):[]}(a);return{hashtags:b.map(at),folderPaths:c.map(au),originalText:a}}function aw(a,b){return[...new Set([...a,...b].filter(a=>a.trim().length>0))]}function ax({hashtags:a,onHashtagsChange:b,suggestions:c=[],placeholder:d="Etiket ekleyin... (\xf6rn: #frontend, #api)",className:e,maxTags:f=10,disabled:g=!1}){let[h,i]=(0,o.useState)(""),[j,k]=(0,o.useState)(!1),[l,m]=(0,o.useState)(-1),p=(0,o.useRef)(null),r=(0,o.useRef)(null),s=(function(a,b,c=5){let d=at(a);return d?b.filter(a=>a.toLowerCase().includes(d)).slice(0,c):[]})(h,c,5).filter(b=>!a.includes(b)),u=c=>{let d=at(c);if(!d||!function(a){let b=at(a);return/^[\w\u00C0-\u017F]+$/.test(b)&&b.length>0}(d))return;let e=function(a){let b=at(a);return b?`#${b}`:""}(d);a.includes(e)||a.length>=f||(b([...a,e]),i(""),k(!1),m(-1))},w=c=>{b(a.filter((a,b)=>b!==c))};return(0,n.jsxs)("div",{className:(0,N.cn)("space-y-2",e),children:[a.length>0&&(0,n.jsx)("div",{className:"flex flex-wrap gap-1",children:a.map((a,b)=>(0,n.jsxs)(v.E,{variant:"secondary",className:"flex items-center gap-1 text-xs bg-blue-100 text-blue-800 hover:bg-blue-200",children:[(0,n.jsx)(aa,{className:"w-3 h-3"}),a.replace("#",""),!g&&(0,n.jsx)(q.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-0 w-3 h-3 hover:bg-transparent",onClick:()=>w(b),children:(0,n.jsx)(Y.A,{className:"w-2 h-2"})})]},b))}),(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsxs)("div",{className:"relative flex-1",children:[(0,n.jsx)(aa,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),(0,n.jsx)(t.p,{ref:p,type:"text",value:h,onChange:a=>{let b=a.target.value;i(b),k(b.length>0),m(-1)},onKeyDown:b=>{"Enter"===b.key?(b.preventDefault(),l>=0&&s[l]?u(s[l]):h.trim()&&u(h.trim())):"ArrowDown"===b.key?(b.preventDefault(),m(a=>a<s.length-1?a+1:a)):"ArrowUp"===b.key?(b.preventDefault(),m(a=>a>0?a-1:-1)):"Escape"===b.key?(k(!1),m(-1)):"Backspace"===b.key&&!h&&a.length>0&&w(a.length-1)},onFocus:()=>k(h.length>0),placeholder:a.length>=f?`Maksimum ${f} etiket`:d,disabled:g||a.length>=f,className:"pl-10"})]}),h.trim()&&(0,n.jsx)(q.$,{type:"button",size:"sm",onClick:()=>u(h.trim()),disabled:g||a.length>=f,className:"shrink-0",children:(0,n.jsx)(af.A,{className:"w-4 h-4"})})]}),j&&s.length>0&&(0,n.jsx)("div",{ref:r,className:"absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-40 overflow-y-auto",children:s.map((a,b)=>(0,n.jsxs)("button",{type:"button",className:(0,N.cn)("w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center gap-2",l===b&&"bg-blue-50"),onClick:()=>{u(a),p.current?.focus()},children:[(0,n.jsx)(aa,{className:"w-3 h-3 text-gray-400"}),a.replace("#","")]},a))})]}),(0,n.jsxs)("div",{className:"text-xs text-gray-500",children:[a.length,"/",f," etiket • Eklemek i\xe7in Enter'a basın • # \xf6neki kullanın"]})]})}var ay=c(14952),az=c(32192);let aA=(0,R.A)("folder-open",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]]);function aB({category:a,onCategoryChange:b,suggestions:c=[],placeholder:d="Klas\xf6r se\xe7in... (\xf6rn: /frontend, /admin/users)",className:e,disabled:f=!1}){let[g,h]=(0,o.useState)(""),[i,j]=(0,o.useState)(!1),[k,l]=(0,o.useState)(-1),[m,p]=(0,o.useState)(!1),r=(0,o.useRef)(null),s=(0,o.useRef)(null),u=function(a,b,c=5){let d=a.toLowerCase();return d?b.filter(a=>a.toLowerCase().includes(d)).slice(0,c):[]}(g,c,8),v=a?function(a){if(!a||"/"===a)return["/"];let b=a.split("/").filter(a=>a.length>0),c=["/"],d="";return b.forEach(a=>{d+="/"+a,c.push(d)}),c}(a):[],w=a=>{let c=au(a);c&&function(a){let b=au(a);return/^\/[\w\u00C0-\u017F\/]*$/.test(b)}(c)&&(b("/"===c?null:c),h(""),j(!1),l(-1),p(!1))},x=()=>{b(null),p(!1)};return(0,n.jsxs)("div",{className:(0,N.cn)("space-y-2",e),children:[a&&!m&&(0,n.jsxs)("div",{className:"flex items-center gap-1 p-2 bg-gray-50 rounded-md border",children:[(0,n.jsx)("div",{className:"flex items-center gap-1 flex-1 min-w-0",children:v.map((a,c)=>(0,n.jsxs)("div",{className:"flex items-center gap-1",children:[c>0&&(0,n.jsx)(ay.A,{className:"w-3 h-3 text-gray-400"}),(0,n.jsx)(q.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-1 text-xs hover:bg-gray-200",onClick:()=>{"/"===a?x():b(a)},children:0===c?(0,n.jsx)(az.A,{className:"w-3 h-3"}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(_.A,{className:"w-3 h-3"}),(0,n.jsx)("span",{className:"ml-1",children:function(a){if(!a||"/"===a)return"Root";let b=a.lastIndexOf("/");return a.substring(b+1)||"Root"}(a)})]})})]},a))}),!f&&(0,n.jsxs)("div",{className:"flex items-center gap-1",children:[(0,n.jsx)(q.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-1",onClick:()=>{p(!0),h(a||""),setTimeout(()=>r.current?.focus(),0)},children:"Edit"}),(0,n.jsx)(q.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-1",onClick:x,children:"Clear"})]})]}),(!a||m)&&(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsxs)("div",{className:"relative flex-1",children:[(0,n.jsx)(_.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),(0,n.jsx)(t.p,{ref:r,type:"text",value:g,onChange:a=>{let b=a.target.value;h(b),j(b.length>0),l(-1)},onKeyDown:a=>{"Enter"===a.key?(a.preventDefault(),k>=0&&u[k]?w(u[k]):g.trim()&&w(g.trim())):"ArrowDown"===a.key?(a.preventDefault(),l(a=>a<u.length-1?a+1:a)):"ArrowUp"===a.key?(a.preventDefault(),l(a=>a>0?a-1:-1)):"Escape"===a.key&&(j(!1),l(-1),p(!1),h(""))},onFocus:()=>j(g.length>0),placeholder:d,disabled:f,className:"pl-10"})]}),g.trim()&&(0,n.jsx)(q.$,{type:"button",size:"sm",onClick:()=>w(g.trim()),disabled:f,className:"shrink-0",children:(0,n.jsx)(af.A,{className:"w-4 h-4"})})]}),i&&u.length>0&&(0,n.jsx)("div",{ref:s,className:"absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-40 overflow-y-auto",children:u.map((a,b)=>(0,n.jsxs)("button",{type:"button",className:(0,N.cn)("w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center gap-2",k===b&&"bg-blue-50"),onClick:()=>{w(a),r.current?.focus()},children:[(0,n.jsx)(aA,{className:"w-3 h-3 text-gray-400"}),a]},a))})]}),(0,n.jsx)("div",{className:"text-xs text-gray-500",children:"Klas\xf6rler i\xe7in eğik \xe7izgi kullanın (\xf6rn: /frontend/components)"})]})}var aC=c(35950),aD=c(25541),aE=c(46657),aF=c(12597),aG=c(13861);let aH=(0,R.A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),aI=(0,R.A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);function aJ({projectId:a,className:b}){let[c,d]=(0,o.useState)(!1),{data:e=[]}=(0,ak.F$)(a),{data:f=[]}=ao(a,10),{data:g=[]}=ap(a,5),h=e.length,i=e.filter(a=>a.category||a.tags&&a.tags.length>0).length,j=h-i,k=h>0?i/h*100:0,l=e.filter(a=>a.tags&&a.tags.length>0).length,m=e.filter(a=>a.category).length;return a?(0,n.jsxs)("div",{className:b,children:[(0,n.jsx)(q.$,{type:"button",variant:"outline",size:"sm",onClick:()=>d(!c),className:"mb-4",children:c?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(aF.A,{className:"w-4 h-4 mr-2"}),"Analitikleri Gizle"]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(aG.A,{className:"w-4 h-4 mr-2"}),"Analitikleri G\xf6ster"]})}),c&&(0,n.jsxs)("div",{className:"space-y-3 w-full overflow-hidden",children:[(0,n.jsxs)(r.Zp,{className:"w-full bg-white shadow-sm border border-gray-200",children:[(0,n.jsx)(r.aR,{className:"pb-2 px-3 pt-3",children:(0,n.jsxs)(r.ZB,{className:"text-xs font-medium flex items-center gap-1.5",children:[(0,n.jsx)(aH,{className:"w-3 h-3 shrink-0"}),(0,n.jsx)("span",{className:"truncate",children:"Genel Bakış"})]})}),(0,n.jsxs)(r.Wu,{className:"space-y-3 px-3 pb-3",children:[(0,n.jsxs)("div",{className:"space-y-1",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[(0,n.jsx)("span",{className:"text-xs text-gray-600 truncate",children:"Kategorizasyon"}),(0,n.jsxs)("span",{className:"text-xs font-medium shrink-0",children:[k.toFixed(0),"%"]})]}),(0,n.jsx)(aE.k,{value:k,className:"h-1.5 w-full"}),(0,n.jsxs)("div",{className:"text-xs text-gray-500 leading-tight",children:[i,"/",h," kategorili"]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,n.jsxs)("div",{className:"text-center p-2 bg-blue-50 rounded",children:[(0,n.jsx)("div",{className:"text-sm font-semibold text-blue-700",children:l}),(0,n.jsx)("div",{className:"text-xs text-blue-600",children:"Etiket"})]}),(0,n.jsxs)("div",{className:"text-center p-2 bg-green-50 rounded",children:[(0,n.jsx)("div",{className:"text-sm font-semibold text-green-700",children:m}),(0,n.jsx)("div",{className:"text-xs text-green-600",children:"Klas\xf6r"})]})]}),j>0&&(0,n.jsx)("div",{className:"p-2 bg-orange-50 border border-orange-200 rounded",children:(0,n.jsxs)("div",{className:"flex items-start gap-1.5 text-orange-700",children:[(0,n.jsx)(aI,{className:"w-3 h-3 shrink-0 mt-0.5"}),(0,n.jsxs)("div",{className:"min-w-0",children:[(0,n.jsxs)("div",{className:"text-xs font-medium leading-tight",children:[j," kategorisiz"]}),(0,n.jsx)("div",{className:"text-xs text-orange-600 leading-tight",children:"Etiket/klas\xf6r ekleyin"})]})]})})]})]}),f.length>0&&(0,n.jsxs)(r.Zp,{className:"w-full bg-white shadow-sm border border-gray-200",children:[(0,n.jsx)(r.aR,{className:"pb-2 px-3 pt-3",children:(0,n.jsxs)(r.ZB,{className:"text-xs font-medium flex items-center gap-1.5",children:[(0,n.jsx)(aa,{className:"w-3 h-3 shrink-0"}),(0,n.jsx)("span",{className:"truncate",children:"Pop\xfcler Etiketler"})]})}),(0,n.jsx)(r.Wu,{className:"px-3 pb-3",children:(0,n.jsx)("div",{className:"space-y-1.5",children:f.slice(0,3).map(({hashtag:a,count:b},c)=>{let d=h>0?b/h*100:0;return(0,n.jsxs)("div",{className:"flex items-center justify-between gap-2",children:[(0,n.jsxs)("div",{className:"flex items-center gap-1.5 min-w-0 flex-1",children:[(0,n.jsxs)("span",{className:"text-xs text-gray-500 w-3 shrink-0",children:["#",c+1]}),(0,n.jsx)(v.E,{variant:"secondary",className:"text-xs px-1.5 py-0.5 truncate max-w-[80px]",children:a.replace("#","")})]}),(0,n.jsxs)("div",{className:"flex items-center gap-1.5 shrink-0",children:[(0,n.jsx)("div",{className:"w-8 h-1 bg-gray-200 rounded-full overflow-hidden",children:(0,n.jsx)("div",{className:"h-full bg-blue-500 rounded-full",style:{width:`${Math.max(d,10)}%`}})}),(0,n.jsx)("span",{className:"text-xs text-gray-600 w-4 text-right",children:b})]})]},a)})})})]}),g.length>0&&(0,n.jsxs)(r.Zp,{className:"w-full bg-white shadow-sm border border-gray-200",children:[(0,n.jsx)(r.aR,{className:"pb-2 px-3 pt-3",children:(0,n.jsxs)(r.ZB,{className:"text-xs font-medium flex items-center gap-1.5",children:[(0,n.jsx)(_.A,{className:"w-3 h-3 shrink-0"}),(0,n.jsx)("span",{className:"truncate",children:"Pop\xfcler Klas\xf6rler"})]})}),(0,n.jsx)(r.Wu,{className:"px-3 pb-3",children:(0,n.jsx)("div",{className:"space-y-1.5",children:g.slice(0,3).map(({category:a,count:b},c)=>{let d=h>0?b/h*100:0;return(0,n.jsxs)("div",{className:"flex items-center justify-between gap-2",children:[(0,n.jsxs)("div",{className:"flex items-center gap-1.5 min-w-0 flex-1",children:[(0,n.jsxs)("span",{className:"text-xs text-gray-500 w-3 shrink-0",children:["#",c+1]}),(0,n.jsxs)(v.E,{variant:"outline",className:"text-xs px-1.5 py-0.5 flex items-center gap-1 truncate max-w-[80px]",children:[(0,n.jsx)(_.A,{className:"w-2.5 h-2.5 shrink-0"}),(0,n.jsx)("span",{className:"truncate",children:a})]})]}),(0,n.jsxs)("div",{className:"flex items-center gap-1.5 shrink-0",children:[(0,n.jsx)("div",{className:"w-8 h-1 bg-gray-200 rounded-full overflow-hidden",children:(0,n.jsx)("div",{className:"h-full bg-green-500 rounded-full",style:{width:`${Math.max(d,10)}%`}})}),(0,n.jsx)("span",{className:"text-xs text-gray-600 w-4 text-right",children:b})]})]},a)})})})]}),h>0&&(0,n.jsxs)(r.Zp,{className:"w-full bg-white shadow-sm border border-gray-200",children:[(0,n.jsx)(r.aR,{className:"pb-2 px-3 pt-3",children:(0,n.jsxs)(r.ZB,{className:"text-xs font-medium flex items-center gap-1.5",children:[(0,n.jsx)(aD.A,{className:"w-3 h-3 shrink-0"}),(0,n.jsx)("span",{className:"truncate",children:"\xd6neriler"})]})}),(0,n.jsx)(r.Wu,{className:"px-3 pb-3",children:(0,n.jsxs)("div",{className:"space-y-1.5 text-xs text-gray-600",children:[k<50&&(0,n.jsxs)("div",{className:"flex items-start gap-1.5",children:[(0,n.jsx)("span",{className:"text-orange-500 shrink-0",children:"•"}),(0,n.jsx)("span",{className:"leading-tight",children:"Etiket ekleyin"})]}),0===m&&(0,n.jsxs)("div",{className:"flex items-start gap-1.5",children:[(0,n.jsx)("span",{className:"text-blue-500 shrink-0",children:"•"}),(0,n.jsx)("span",{className:"leading-tight",children:"Klas\xf6r kullanın"})]}),f.length>10&&(0,n.jsxs)("div",{className:"flex items-start gap-1.5",children:[(0,n.jsx)("span",{className:"text-green-500 shrink-0",children:"•"}),(0,n.jsx)("span",{className:"leading-tight",children:"İyi etiket kullanımı!"})]}),h>20&&k>80&&(0,n.jsxs)("div",{className:"flex items-start gap-1.5",children:[(0,n.jsx)("span",{className:"text-green-500 shrink-0",children:"•"}),(0,n.jsx)("span",{className:"leading-tight",children:"M\xfckemmel organizasyon!"})]})]})})]})]})]}):null}function aK({projectId:a,onHashtagClick:b,onCategoryClick:c,selectedHashtags:d=[],selectedCategory:e=null,className:f}){let[g,h]=(0,o.useState)(""),[i,j]=(0,o.useState)(!0),[k,l]=(0,o.useState)(!0),{data:m=[],isLoading:p}=ao(a),{data:r=[],isLoading:s}=ap(a),w=m.filter(({hashtag:a})=>a.toLowerCase().includes(g.toLowerCase())),x=r.filter(({category:a})=>a.toLowerCase().includes(g.toLowerCase()));return(0,n.jsxs)("div",{className:(0,N.cn)("w-64 bg-white border-l border-gray-200 flex flex-col relative z-50",f),children:[(0,n.jsxs)("div",{className:"p-4 border-b border-gray-200",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,n.jsx)(aD.A,{className:"w-4 h-4 text-blue-600"}),(0,n.jsx)("h3",{className:"font-medium text-sm",children:"AI Tags"})]}),(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)(ae.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),(0,n.jsx)(t.p,{type:"text",placeholder:"Etiket ve klas\xf6r ara...",value:g,onChange:a=>h(a.target.value),className:"pl-10 text-sm"}),g&&(0,n.jsx)(q.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0",onClick:()=>{h("")},children:(0,n.jsx)(Y.A,{className:"w-3 h-3"})})]})]}),(0,n.jsx)(u.F,{className:"flex-1 overflow-hidden",children:(0,n.jsxs)("div",{className:"p-4 space-y-4 max-w-full pb-24 sm:pb-20 lg:pb-24",children:[i&&(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(_.A,{className:"w-3 h-3 text-gray-500"}),(0,n.jsx)("span",{className:"text-xs font-medium text-gray-700 uppercase tracking-wide",children:"Klas\xf6rler"})]}),(0,n.jsx)(q.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-0 text-xs",onClick:()=>j(!i),children:i?"Gizle":"G\xf6ster"})]}),s?(0,n.jsx)("div",{className:"text-xs text-gray-500",children:"Klas\xf6rler y\xfckleniyor..."}):x.length>0?(0,n.jsx)("div",{className:"space-y-1",children:x.map(({category:a,count:b})=>(0,n.jsxs)(q.$,{type:"button",variant:"ghost",size:"sm",className:(0,N.cn)("w-full justify-between h-auto p-2 text-xs hover:bg-gray-100",e===a&&"bg-blue-50 text-blue-700"),onClick:()=>{c?.(a)},children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 min-w-0",children:[(0,n.jsx)(_.A,{className:"w-3 h-3 shrink-0"}),(0,n.jsx)("span",{className:"truncate",children:a})]}),(0,n.jsx)(v.E,{variant:"secondary",className:"text-xs",children:b})]},a))}):(0,n.jsx)("div",{className:"text-xs text-gray-500",children:g?"Eşleşen klas\xf6r bulunamadı":"Hen\xfcz klas\xf6r yok"})]}),i&&k&&(0,n.jsx)(aC.w,{}),k&&(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(aa,{className:"w-3 h-3 text-gray-500"}),(0,n.jsx)("span",{className:"text-xs font-medium text-gray-700 uppercase tracking-wide",children:"Pop\xfcler Etiketler"})]}),(0,n.jsx)(q.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-0 text-xs",onClick:()=>l(!k),children:k?"Gizle":"G\xf6ster"})]}),p?(0,n.jsx)("div",{className:"text-xs text-gray-500",children:"Etiketler y\xfckleniyor..."}):w.length>0?(0,n.jsx)("div",{className:"space-y-1",children:w.map(({hashtag:a,count:c})=>(0,n.jsxs)(q.$,{type:"button",variant:"ghost",size:"sm",className:(0,N.cn)("w-full justify-between h-auto p-2 text-xs hover:bg-gray-100",d.includes(a)&&"bg-blue-50 text-blue-700"),onClick:()=>{b?.(a)},children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 min-w-0",children:[(0,n.jsx)(aa,{className:"w-3 h-3 shrink-0"}),(0,n.jsx)("span",{className:"truncate",children:a.replace("#","")})]}),(0,n.jsx)(v.E,{variant:"secondary",className:"text-xs",children:c})]},a))}):(0,n.jsx)("div",{className:"text-xs text-gray-500",children:g?"Eşleşen etiket bulunamadı":"Hen\xfcz etiket yok"})]}),a&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(aC.w,{}),(0,n.jsx)("div",{className:"w-full overflow-hidden",children:(0,n.jsx)(aJ,{projectId:a,className:"w-full max-w-none"})})]})]})}),(0,n.jsx)("div",{className:"p-4 border-t border-gray-200",children:(0,n.jsx)("div",{className:"text-xs text-gray-500 text-center",children:"Filtrelemek i\xe7in tıklayın • Sayılar kullanım sayısını g\xf6sterir"})})]})}let aL=function(a){var b=typeof a;return null!=a&&("object"==b||"function"==b)};var aM="object"==typeof global&&global&&global.Object===Object&&global,aN="object"==typeof self&&self&&self.Object===Object&&self,aO=aM||aN||Function("return this")();let aP=function(){return aO.Date.now()};var aQ=/\s/;let aR=function(a){for(var b=a.length;b--&&aQ.test(a.charAt(b)););return b};var aS=/^\s+/,aT=aO.Symbol,aU=Object.prototype,aV=aU.hasOwnProperty,aW=aU.toString,aX=aT?aT.toStringTag:void 0;let aY=function(a){var b=aV.call(a,aX),c=a[aX];try{a[aX]=void 0;var d=!0}catch(a){}var e=aW.call(a);return d&&(b?a[aX]=c:delete a[aX]),e};var aZ=Object.prototype.toString,a$=aT?aT.toStringTag:void 0;let a_=function(a){return null==a?void 0===a?"[object Undefined]":"[object Null]":a$&&a$ in Object(a)?aY(a):aZ.call(a)},a0=function(a){return"symbol"==typeof a||null!=a&&"object"==typeof a&&"[object Symbol]"==a_(a)};var a1=0/0,a2=/^[-+]0x[0-9a-f]+$/i,a3=/^0b[01]+$/i,a4=/^0o[0-7]+$/i,a5=parseInt;let a6=function(a){if("number"==typeof a)return a;if(a0(a))return a1;if(aL(a)){var b,c="function"==typeof a.valueOf?a.valueOf():a;a=aL(c)?c+"":c}if("string"!=typeof a)return 0===a?a:+a;a=(b=a)?b.slice(0,aR(b)+1).replace(aS,""):b;var d=a3.test(a);return d||a4.test(a)?a5(a.slice(2),d?2:8):a2.test(a)?a1:+a};var a7=Math.max,a8=Math.min;let a9=function(a,b,c){var d,e,f,g,h,i,j=0,k=!1,l=!1,m=!0;if("function"!=typeof a)throw TypeError("Expected a function");function n(b){var c=d,f=e;return d=e=void 0,j=b,g=a.apply(f,c)}function o(a){var c=a-i,d=a-j;return void 0===i||c>=b||c<0||l&&d>=f}function p(){var a,c,d,e=aP();if(o(e))return q(e);h=setTimeout(p,(a=e-i,c=e-j,d=b-a,l?a8(d,f-c):d))}function q(a){return(h=void 0,m&&d)?n(a):(d=e=void 0,g)}function r(){var a,c=aP(),f=o(c);if(d=arguments,e=this,i=c,f){if(void 0===h)return j=a=i,h=setTimeout(p,b),k?n(a):g;if(l)return clearTimeout(h),h=setTimeout(p,b),n(i)}return void 0===h&&(h=setTimeout(p,b)),g}return b=a6(b)||0,aL(c)&&(k=!!c.leading,f=(l="maxWait"in c)?a7(a6(c.maxWait)||0,b):f,m="trailing"in c?!!c.trailing:m),r.cancel=function(){void 0!==h&&clearTimeout(h),j=0,d=i=e=h=void 0},r.flush=function(){return void 0===h?g:q(aP())},r},ba=function({value:a,onChange:b,onKeyDown:c,placeholder:d,className:e,suggestions:f={hashtags:[],folders:[]},disabled:g=!1,enableDynamicHeight:h=!0,heightConfig:i={}}){let[j,k]=(0,o.useState)(!1),[l,m]=(0,o.useState)([]),[p,q]=(0,o.useState)(-1),[r,s]=(0,o.useState)(null),[t,u]=(0,o.useState)(-1),[,v]=(0,o.useState)(""),w=(0,o.useRef)(null),x=(0,o.useRef)(null),{heightStyle:y}=function(a,b,c){let{heightStyle:d,maxHeight:e,minHeight:f}=function(a={}){let{minHeight:b=44,maxHeightFraction:c=.33,baseHeight:d=56,enableTransitions:e=!0}=a,[f,g]=(0,o.useState)(0),[h,i]=(0,o.useState)(200),j=(0,o.useCallback)(()=>{},[c,b]),k={minHeight:`${b}px`,maxHeight:`${h}px`,height:"auto",resize:"none",...e&&{transition:"max-height 0.2s ease-out, min-height 0.2s ease-out"}};return{maxHeight:h,minHeight:b,heightStyle:k,recalculateHeight:j,viewportHeight:f}}(c),[g,h]=(0,o.useState)(!1);return{heightStyle:{...d,overflow:"hidden"},maxHeight:e,minHeight:f,isExpanding:g}}(0,0,h?i:void 0),z=(0,o.useCallback)((a,b)=>{if(0===b)return null;for(let c=b-1;c>=0;c--){let d=a[c];if("#"===d||"/"===d){let e=c>0?a[c-1]:" ";if(" "===e||"\n"===e||0===c){let e=a.slice(c+1,b);if(!e.includes(" ")&&!e.includes("\n"))return{char:d,position:c,searchTerm:e}}}if(" "===d||"\n"===d)break}return null},[]),A=(0,o.useCallback)((a,b)=>{let c="hashtag"===b?f?.hashtags||[]:f?.folders||[];return c&&0!==c.length?c.filter(b=>!!b&&"string"==typeof b&&b.replace(/^[#/]/,"").toLowerCase().includes(a.toLowerCase())).slice(0,8).map((a,c)=>({id:`${b}-${c}`,value:a,type:b,usage_count:Math.floor(100*Math.random())})).sort((a,b)=>(b.usage_count||0)-(a.usage_count||0)):[]},[f]),B=(0,o.useCallback)(c=>{if(-1===t)return;let d=a.slice(0,t),e=a.slice(w.current?.selectionStart||0),f="hashtag"===c.type?"#":"/",g=c.value.startsWith(f)?c.value:`${f}${c.value}`;b(d+g+" "+e),setTimeout(()=>{if(w.current){let a=d.length+g.length+1;w.current.setSelectionRange(a,a),w.current.focus()}},0),k(!1),q(-1)},[a,t,b]),C=(0,o.useMemo)(()=>a9((a,b)=>{try{let c=z(a,b);if(c){s(c.char),u(c.position),v(c.searchTerm);let a="#"===c.char?"hashtag":"folder",b=A(c.searchTerm,a);m(b||[]),k(b&&b.length>0),q(-1)}else k(!1),s(null),u(-1),v(""),m([]),q(-1)}catch(a){console.error("Error in trigger detection:",a),k(!1),s(null),u(-1),v(""),m([]),q(-1)}},100),[z,A]),D=(0,o.useCallback)(a=>{b(a),w.current&&C(a,w.current.selectionStart||0)},[b,C]),E=(0,o.useCallback)(a=>{if(j&&l.length>0)switch(a.key){case"ArrowDown":a.preventDefault(),q(a=>a<l.length-1?a+1:0);break;case"ArrowUp":a.preventDefault(),q(a=>a>0?a-1:l.length-1);break;case"Enter":case"Tab":p>=0&&(a.preventDefault(),B(l[p]));break;case"Escape":a.preventDefault(),k(!1),q(-1)}c?.(a)},[j,l,p,c,B]);return(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("textarea",{ref:w,value:a||"",onChange:a=>D(a.target.value),onKeyDown:E,placeholder:d,className:`${e} ${h?"dynamic-textarea":""}`,disabled:g,style:h?y:{height:"auto"}}),j&&l&&l.length>0&&(0,n.jsxs)("div",{ref:x,className:"absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto",children:[(0,n.jsxs)("div",{className:"p-2 text-xs text-gray-500 border-b border-gray-100",children:["#"===r?"Hashtags":"Folders"," - Use ↑↓ to navigate, Enter to select"]}),l.map((a,b)=>a&&a.id&&a.value?(0,n.jsxs)("div",{className:`flex items-center gap-2 px-3 py-2 cursor-pointer transition-colors ${b===p?"bg-blue-50 text-blue-700":"hover:bg-gray-50"}`,onClick:()=>B(a),children:["hashtag"===a.type?(0,n.jsx)(aa,{className:"h-4 w-4 text-blue-500"}):(0,n.jsx)(_.A,{className:"h-4 w-4 text-orange-500"}),(0,n.jsx)("span",{className:"flex-1 text-sm",children:a.value}),a.usage_count&&(0,n.jsxs)("span",{className:"text-xs text-gray-400",children:[a.usage_count," uses"]})]},a.id):null)]})]})};function bb(){return(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 animate-pulse",children:[1,2,3,4,5,6].map(a=>(0,n.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,n.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mb-3"}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("div",{className:"h-3 bg-gray-200 rounded w-full"}),(0,n.jsx)("div",{className:"h-3 bg-gray-200 rounded w-5/6"}),(0,n.jsx)("div",{className:"h-3 bg-gray-200 rounded w-4/6"})]}),(0,n.jsxs)("div",{className:"flex gap-2 mt-4",children:[(0,n.jsx)("div",{className:"h-5 bg-blue-100 rounded-full w-16"}),(0,n.jsx)("div",{className:"h-5 bg-purple-100 rounded-full w-20"})]})]},a))})}var bc=c(81620);let bd=(0,R.A)("link-2",[["path",{d:"M9 17H7A5 5 0 0 1 7 7h2",key:"8i5ue5"}],["path",{d:"M15 7h2a5 5 0 1 1 0 10h-2",key:"1b9ql8"}],["line",{x1:"8",x2:"16",y1:"12",y2:"12",key:"1jonct"}]]);var be=c(11437),bf=c(64021),bg=c(63503),bh=c(80013),bi=c(77781);function bj({promptId:a,promptTitle:b,promptText:c,taskCode:d,className:e,variant:f="ghost",size:g="sm"}){let[h,i]=(0,o.useState)(!1),[j,k]=(0,o.useState)(b||d||""),[l,m]=(0,o.useState)(""),[p,r]=(0,o.useState)(!0),[u,v]=(0,o.useState)(""),[w,x]=(0,o.useState)(""),[y,z]=(0,o.useState)(!1),A=(0,bi.Xm)(),{data:B}=(0,bi.X1)(),C=B?.find(b=>b.prompt_id===a),D=async()=>{try{await A.mutateAsync({prompt_id:a,title:j.trim()||void 0,description:l.trim()||void 0,is_public:p,password:u.trim()||void 0,expires_at:w||void 0}),i(!1),E()}catch(a){console.error("Share error:",a)}},E=()=>{k(b||d||""),m(""),r(!0),v(""),x(""),z(!1)},F=async()=>{if(C){let a=`${window.location.origin}/share/${C.share_token}`;try{await navigator.clipboard.writeText(a),as.oR.success("Link panoya kopyalandı!")}catch(a){as.oR.error("Link kopyalanamadı")}}},G=(a,b=100)=>a.length<=b?a:a.substring(0,b)+"...";return(0,n.jsxs)(bg.lG,{open:h,onOpenChange:i,children:[(0,n.jsx)(bg.zM,{asChild:!0,children:(0,n.jsx)(q.$,{variant:f,size:g,className:e,onClick:()=>i(!0),children:(0,n.jsx)(bc.A,{className:"h-4 w-4"})})}),(0,n.jsxs)(bg.Cf,{className:"sm:max-w-md",children:[(0,n.jsxs)(bg.c7,{children:[(0,n.jsxs)(bg.L3,{className:"flex items-center gap-2",children:[(0,n.jsx)(bc.A,{className:"h-5 w-5"}),"Prompt Paylaş"]}),(0,n.jsx)(bg.rr,{children:"Bu prompt'u başkalarıyla paylaşmak i\xe7in bir link oluşturun"})]}),C?(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"p-4 bg-green-50 border border-green-200 rounded-lg",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,n.jsx)(bd,{className:"h-4 w-4 text-green-600"}),(0,n.jsx)("span",{className:"text-sm font-medium text-green-800",children:"Bu prompt zaten paylaşılmış"})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(t.p,{value:`${window.location.origin}/share/${C.share_token}`,readOnly:!0,className:"text-xs bg-white"}),(0,n.jsx)(q.$,{size:"sm",onClick:F,className:"shrink-0",children:(0,n.jsx)($.A,{className:"h-4 w-4"})})]}),(0,n.jsxs)("div",{className:"mt-2 flex items-center gap-4 text-xs text-green-700",children:[(0,n.jsxs)("span",{className:"flex items-center gap-1",children:[(0,n.jsx)(aG.A,{className:"h-3 w-3"}),C.view_count," g\xf6r\xfcnt\xfclenme"]}),(0,n.jsx)("span",{children:new Date(C.created_at).toLocaleDateString("tr-TR")})]})]}),(0,n.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,n.jsx)("h4",{className:"text-sm font-medium mb-2",children:"Prompt \xd6nizleme"}),(0,n.jsx)("p",{className:"text-xs text-gray-600 font-mono",children:G(c)})]})]}):(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,n.jsx)("h4",{className:"text-sm font-medium mb-2",children:"Prompt \xd6nizleme"}),(0,n.jsx)("p",{className:"text-xs text-gray-600 font-mono",children:G(c)})]}),(0,n.jsx)(aC.w,{}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(bh.J,{htmlFor:"share-title",children:"Başlık (Opsiyonel)"}),(0,n.jsx)(t.p,{id:"share-title",value:j,onChange:a=>k(a.target.value),placeholder:"Paylaşım i\xe7in \xf6zel başlık",className:"mt-1"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(bh.J,{htmlFor:"share-description",children:"A\xe7ıklama (Opsiyonel)"}),(0,n.jsx)(s.T,{id:"share-description",value:l,onChange:a=>m(a.target.value),placeholder:"Bu prompt hakkında kısa a\xe7ıklama",className:"mt-1 resize-none",rows:2})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[p?(0,n.jsx)(be.A,{className:"h-4 w-4 text-green-600"}):(0,n.jsx)(bf.A,{className:"h-4 w-4 text-orange-600"}),(0,n.jsx)(bh.J,{htmlFor:"is-public",children:"Herkese A\xe7ık"})]}),(0,n.jsx)(O,{id:"is-public",checked:p,onCheckedChange:r})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(bh.J,{htmlFor:"password",children:"Şifre Koruması (Opsiyonel)"}),(0,n.jsxs)("div",{className:"flex gap-2 mt-1",children:[(0,n.jsx)(t.p,{id:"password",type:y?"text":"password",value:u,onChange:a=>v(a.target.value),placeholder:"Şifre belirleyin"}),(0,n.jsx)(q.$,{type:"button",variant:"outline",size:"icon",onClick:()=>z(!y),children:y?(0,n.jsx)(aF.A,{className:"h-4 w-4"}):(0,n.jsx)(aG.A,{className:"h-4 w-4"})})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(bh.J,{htmlFor:"expires-at",children:"Son Kullanma Tarihi (Opsiyonel)"}),(0,n.jsx)(t.p,{id:"expires-at",type:"datetime-local",value:w,onChange:a=>x(a.target.value),className:"mt-1",min:new Date().toISOString().slice(0,16)})]})]}),(0,n.jsx)(aC.w,{}),(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsx)(q.$,{onClick:D,disabled:A.isPending,className:"flex-1",children:A.isPending?"Oluşturuluyor...":(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(bc.A,{className:"h-4 w-4 mr-2"}),"Paylaşım Linki Oluştur"]})}),(0,n.jsx)(q.$,{variant:"outline",onClick:()=>i(!1),children:"İptal"})]})]})]})]})}var bk=c(51215);let bl="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function bm(a){let b=Object.prototype.toString.call(a);return"[object Window]"===b||"[object global]"===b}function bn(a){return"nodeType"in a}function bo(a){var b,c;return a?bm(a)?a:bn(a)&&null!=(b=null==(c=a.ownerDocument)?void 0:c.defaultView)?b:window:window}function bp(a){let{Document:b}=bo(a);return a instanceof b}function bq(a){return!bm(a)&&a instanceof bo(a).HTMLElement}function br(a){return a instanceof bo(a).SVGElement}function bs(a){return a?bm(a)?a.document:bn(a)?bp(a)?a:bq(a)||br(a)?a.ownerDocument:document:document:document}let bt=bl?o.useLayoutEffect:o.useEffect;function bu(a){let b=(0,o.useRef)(a);return bt(()=>{b.current=a}),(0,o.useCallback)(function(){for(var a=arguments.length,c=Array(a),d=0;d<a;d++)c[d]=arguments[d];return null==b.current?void 0:b.current(...c)},[])}function bv(a,b){void 0===b&&(b=[a]);let c=(0,o.useRef)(a);return bt(()=>{c.current!==a&&(c.current=a)},b),c}function bw(a,b){let c=(0,o.useRef)();return(0,o.useMemo)(()=>{let b=a(c.current);return c.current=b,b},[...b])}function bx(a){let b=bu(a),c=(0,o.useRef)(null),d=(0,o.useCallback)(a=>{a!==c.current&&(null==b||b(a,c.current)),c.current=a},[]);return[c,d]}function by(a){let b=(0,o.useRef)();return(0,o.useEffect)(()=>{b.current=a},[a]),b.current}let bz={};function bA(a,b){return(0,o.useMemo)(()=>{if(b)return b;let c=null==bz[a]?0:bz[a]+1;return bz[a]=c,a+"-"+c},[a,b])}function bB(a){return function(b){for(var c=arguments.length,d=Array(c>1?c-1:0),e=1;e<c;e++)d[e-1]=arguments[e];return d.reduce((b,c)=>{for(let[d,e]of Object.entries(c)){let c=b[d];null!=c&&(b[d]=c+a*e)}return b},{...b})}}let bC=bB(1),bD=bB(-1);function bE(a){if(!a)return!1;let{KeyboardEvent:b}=bo(a.target);return b&&a instanceof b}function bF(a){if(function(a){if(!a)return!1;let{TouchEvent:b}=bo(a.target);return b&&a instanceof b}(a)){if(a.touches&&a.touches.length){let{clientX:b,clientY:c}=a.touches[0];return{x:b,y:c}}else if(a.changedTouches&&a.changedTouches.length){let{clientX:b,clientY:c}=a.changedTouches[0];return{x:b,y:c}}}return"clientX"in a&&"clientY"in a?{x:a.clientX,y:a.clientY}:null}let bG=Object.freeze({Translate:{toString(a){if(!a)return;let{x:b,y:c}=a;return"translate3d("+(b?Math.round(b):0)+"px, "+(c?Math.round(c):0)+"px, 0)"}},Scale:{toString(a){if(!a)return;let{scaleX:b,scaleY:c}=a;return"scaleX("+b+") scaleY("+c+")"}},Transform:{toString(a){if(a)return[bG.Translate.toString(a),bG.Scale.toString(a)].join(" ")}},Transition:{toString(a){let{property:b,duration:c,easing:d}=a;return b+" "+c+"ms "+d}}}),bH="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]",bI={display:"none"};function bJ(a){let{id:b,value:c}=a;return p().createElement("div",{id:b,style:bI},c)}function bK(a){let{id:b,announcement:c,ariaLiveType:d="assertive"}=a;return p().createElement("div",{id:b,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":d,"aria-atomic":!0},c)}let bL=(0,o.createContext)(null),bM={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},bN={onDragStart(a){let{active:b}=a;return"Picked up draggable item "+b.id+"."},onDragOver(a){let{active:b,over:c}=a;return c?"Draggable item "+b.id+" was moved over droppable area "+c.id+".":"Draggable item "+b.id+" is no longer over a droppable area."},onDragEnd(a){let{active:b,over:c}=a;return c?"Draggable item "+b.id+" was dropped over droppable area "+c.id:"Draggable item "+b.id+" was dropped."},onDragCancel(a){let{active:b}=a;return"Dragging was cancelled. Draggable item "+b.id+" was dropped."}};function bO(a){let{announcements:b=bN,container:c,hiddenTextDescribedById:d,screenReaderInstructions:e=bM}=a,{announce:f,announcement:g}=function(){let[a,b]=(0,o.useState)("");return{announce:(0,o.useCallback)(a=>{null!=a&&b(a)},[]),announcement:a}}(),h=bA("DndLiveRegion"),[i,j]=(0,o.useState)(!1);(0,o.useEffect)(()=>{j(!0)},[]);var k=(0,o.useMemo)(()=>({onDragStart(a){let{active:c}=a;f(b.onDragStart({active:c}))},onDragMove(a){let{active:c,over:d}=a;b.onDragMove&&f(b.onDragMove({active:c,over:d}))},onDragOver(a){let{active:c,over:d}=a;f(b.onDragOver({active:c,over:d}))},onDragEnd(a){let{active:c,over:d}=a;f(b.onDragEnd({active:c,over:d}))},onDragCancel(a){let{active:c,over:d}=a;f(b.onDragCancel({active:c,over:d}))}}),[f,b]);let l=(0,o.useContext)(bL);if((0,o.useEffect)(()=>{if(!l)throw Error("useDndMonitor must be used within a children of <DndContext>");return l(k)},[k,l]),!i)return null;let m=p().createElement(p().Fragment,null,p().createElement(bJ,{id:d,value:e.draggable}),p().createElement(bK,{id:h,announcement:g}));return c?(0,bk.createPortal)(m,c):m}function bP(){}function bQ(a,b){return(0,o.useMemo)(()=>({sensor:a,options:null!=b?b:{}}),[a,b])}!function(a){a.DragStart="dragStart",a.DragMove="dragMove",a.DragEnd="dragEnd",a.DragCancel="dragCancel",a.DragOver="dragOver",a.RegisterDroppable="registerDroppable",a.SetDroppableDisabled="setDroppableDisabled",a.UnregisterDroppable="unregisterDroppable"}(d||(d={}));let bR=Object.freeze({x:0,y:0});function bS(a,b){return Math.sqrt(Math.pow(a.x-b.x,2)+Math.pow(a.y-b.y,2))}function bT(a,b){let{data:{value:c}}=a,{data:{value:d}}=b;return c-d}function bU(a,b){let{data:{value:c}}=a,{data:{value:d}}=b;return d-c}function bV(a){let{left:b,top:c,height:d,width:e}=a;return[{x:b,y:c},{x:b+e,y:c},{x:b,y:c+d},{x:b+e,y:c+d}]}function bW(a,b){if(!a||0===a.length)return null;let[c]=a;return b?c[b]:c}function bX(a,b,c){return void 0===b&&(b=a.left),void 0===c&&(c=a.top),{x:b+.5*a.width,y:c+.5*a.height}}let bY=a=>{let{collisionRect:b,droppableRects:c,droppableContainers:d}=a,e=bX(b,b.left,b.top),f=[];for(let a of d){let{id:b}=a,d=c.get(b);if(d){let c=bS(bX(d),e);f.push({id:b,data:{droppableContainer:a,value:c}})}}return f.sort(bT)},bZ=a=>{let{collisionRect:b,droppableRects:c,droppableContainers:d}=a,e=[];for(let a of d){let{id:d}=a,f=c.get(d);if(f){let c=function(a,b){let c=Math.max(b.top,a.top),d=Math.max(b.left,a.left),e=Math.min(b.left+b.width,a.left+a.width),f=Math.min(b.top+b.height,a.top+a.height);if(d<e&&c<f){let g=b.width*b.height,h=a.width*a.height,i=(e-d)*(f-c);return Number((i/(g+h-i)).toFixed(4))}return 0}(f,b);c>0&&e.push({id:d,data:{droppableContainer:a,value:c}})}}return e.sort(bU)};function b$(a,b){return a&&b?{x:a.left-b.left,y:a.top-b.top}:bR}let b_=function(a){return function(b){for(var c=arguments.length,d=Array(c>1?c-1:0),e=1;e<c;e++)d[e-1]=arguments[e];return d.reduce((b,c)=>({...b,top:b.top+a*c.y,bottom:b.bottom+a*c.y,left:b.left+a*c.x,right:b.right+a*c.x}),{...b})}}(1),b0={ignoreTransform:!1};function b1(a,b){void 0===b&&(b=b0);let c=a.getBoundingClientRect();if(b.ignoreTransform){let{transform:b,transformOrigin:d}=bo(a).getComputedStyle(a);b&&(c=function(a,b,c){let d=function(a){if(a.startsWith("matrix3d(")){let b=a.slice(9,-1).split(/, /);return{x:+b[12],y:+b[13],scaleX:+b[0],scaleY:+b[5]}}if(a.startsWith("matrix(")){let b=a.slice(7,-1).split(/, /);return{x:+b[4],y:+b[5],scaleX:+b[0],scaleY:+b[3]}}return null}(b);if(!d)return a;let{scaleX:e,scaleY:f,x:g,y:h}=d,i=a.left-g-(1-e)*parseFloat(c),j=a.top-h-(1-f)*parseFloat(c.slice(c.indexOf(" ")+1)),k=e?a.width/e:a.width,l=f?a.height/f:a.height;return{width:k,height:l,top:j,right:i+k,bottom:j+l,left:i}}(c,b,d))}let{top:d,left:e,width:f,height:g,bottom:h,right:i}=c;return{top:d,left:e,width:f,height:g,bottom:h,right:i}}function b2(a){return b1(a,{ignoreTransform:!0})}function b3(a,b){let c=[];return a?function d(e){var f;if(null!=b&&c.length>=b||!e)return c;if(bp(e)&&null!=e.scrollingElement&&!c.includes(e.scrollingElement))return c.push(e.scrollingElement),c;if(!bq(e)||br(e)||c.includes(e))return c;let g=bo(a).getComputedStyle(e);return(e!==a&&function(a,b){void 0===b&&(b=bo(a).getComputedStyle(a));let c=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(a=>{let d=b[a];return"string"==typeof d&&c.test(d)})}(e,g)&&c.push(e),void 0===(f=g)&&(f=bo(e).getComputedStyle(e)),"fixed"===f.position)?c:d(e.parentNode)}(a):c}function b4(a){let[b]=b3(a,1);return null!=b?b:null}function b5(a){return bl&&a?bm(a)?a:bn(a)?bp(a)||a===bs(a).scrollingElement?window:bq(a)?a:null:null:null}function b6(a){return bm(a)?a.scrollX:a.scrollLeft}function b7(a){return bm(a)?a.scrollY:a.scrollTop}function b8(a){return{x:b6(a),y:b7(a)}}function b9(a){return!!bl&&!!a&&a===document.scrollingElement}function ca(a){let b={x:0,y:0},c=b9(a)?{height:window.innerHeight,width:window.innerWidth}:{height:a.clientHeight,width:a.clientWidth},d={x:a.scrollWidth-c.width,y:a.scrollHeight-c.height},e=a.scrollTop<=b.y,f=a.scrollLeft<=b.x;return{isTop:e,isLeft:f,isBottom:a.scrollTop>=d.y,isRight:a.scrollLeft>=d.x,maxScroll:d,minScroll:b}}!function(a){a[a.Forward=1]="Forward",a[a.Backward=-1]="Backward"}(e||(e={}));let cb={x:.2,y:.2};function cc(a){return a.reduce((a,b)=>bC(a,b8(b)),bR)}let cd=[["x",["left","right"],function(a){return a.reduce((a,b)=>a+b6(b),0)}],["y",["top","bottom"],function(a){return a.reduce((a,b)=>a+b7(b),0)}]];class ce{constructor(a,b){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;let c=b3(b),d=cc(c);for(let[b,e,f]of(this.rect={...a},this.width=a.width,this.height=a.height,cd))for(let a of e)Object.defineProperty(this,a,{get:()=>{let e=f(c),g=d[b]-e;return this.rect[a]+g},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class cf{constructor(a){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(a=>{var b;return null==(b=this.target)?void 0:b.removeEventListener(...a)})},this.target=a}add(a,b,c){var d;null==(d=this.target)||d.addEventListener(a,b,c),this.listeners.push([a,b,c])}}function cg(a,b){let c=Math.abs(a.x),d=Math.abs(a.y);return"number"==typeof b?Math.sqrt(c**2+d**2)>b:"x"in b&&"y"in b?c>b.x&&d>b.y:"x"in b?c>b.x:"y"in b&&d>b.y}function ch(a){a.preventDefault()}function ci(a){a.stopPropagation()}!function(a){a.Click="click",a.DragStart="dragstart",a.Keydown="keydown",a.ContextMenu="contextmenu",a.Resize="resize",a.SelectionChange="selectionchange",a.VisibilityChange="visibilitychange"}(f||(f={})),function(a){a.Space="Space",a.Down="ArrowDown",a.Right="ArrowRight",a.Left="ArrowLeft",a.Up="ArrowUp",a.Esc="Escape",a.Enter="Enter",a.Tab="Tab"}(g||(g={}));let cj={start:[g.Space,g.Enter],cancel:[g.Esc],end:[g.Space,g.Enter,g.Tab]},ck=(a,b)=>{let{currentCoordinates:c}=b;switch(a.code){case g.Right:return{...c,x:c.x+25};case g.Left:return{...c,x:c.x-25};case g.Down:return{...c,y:c.y+25};case g.Up:return{...c,y:c.y-25}}};class cl{constructor(a){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=a;let{event:{target:b}}=a;this.props=a,this.listeners=new cf(bs(b)),this.windowListeners=new cf(bo(b)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(f.Resize,this.handleCancel),this.windowListeners.add(f.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(f.Keydown,this.handleKeyDown))}handleStart(){let{activeNode:a,onStart:b}=this.props,c=a.node.current;c&&function(a,b){if(void 0===b&&(b=b1),!a)return;let{top:c,left:d,bottom:e,right:f}=b(a);b4(a)&&(e<=0||f<=0||c>=window.innerHeight||d>=window.innerWidth)&&a.scrollIntoView({block:"center",inline:"center"})}(c),b(bR)}handleKeyDown(a){if(bE(a)){let{active:b,context:c,options:d}=this.props,{keyboardCodes:e=cj,coordinateGetter:f=ck,scrollBehavior:h="smooth"}=d,{code:i}=a;if(e.end.includes(i))return void this.handleEnd(a);if(e.cancel.includes(i))return void this.handleCancel(a);let{collisionRect:j}=c.current,k=j?{x:j.left,y:j.top}:bR;this.referenceCoordinates||(this.referenceCoordinates=k);let l=f(a,{active:b,context:c.current,currentCoordinates:k});if(l){let b=bD(l,k),d={x:0,y:0},{scrollableAncestors:e}=c.current;for(let c of e){let e=a.code,{isTop:f,isRight:i,isLeft:j,isBottom:k,maxScroll:m,minScroll:n}=ca(c),o=function(a){if(a===document.scrollingElement){let{innerWidth:a,innerHeight:b}=window;return{top:0,left:0,right:a,bottom:b,width:a,height:b}}let{top:b,left:c,right:d,bottom:e}=a.getBoundingClientRect();return{top:b,left:c,right:d,bottom:e,width:a.clientWidth,height:a.clientHeight}}(c),p={x:Math.min(e===g.Right?o.right-o.width/2:o.right,Math.max(e===g.Right?o.left:o.left+o.width/2,l.x)),y:Math.min(e===g.Down?o.bottom-o.height/2:o.bottom,Math.max(e===g.Down?o.top:o.top+o.height/2,l.y))},q=e===g.Right&&!i||e===g.Left&&!j,r=e===g.Down&&!k||e===g.Up&&!f;if(q&&p.x!==l.x){let a=c.scrollLeft+b.x,f=e===g.Right&&a<=m.x||e===g.Left&&a>=n.x;if(f&&!b.y)return void c.scrollTo({left:a,behavior:h});f?d.x=c.scrollLeft-a:d.x=e===g.Right?c.scrollLeft-m.x:c.scrollLeft-n.x,d.x&&c.scrollBy({left:-d.x,behavior:h});break}if(r&&p.y!==l.y){let a=c.scrollTop+b.y,f=e===g.Down&&a<=m.y||e===g.Up&&a>=n.y;if(f&&!b.x)return void c.scrollTo({top:a,behavior:h});f?d.y=c.scrollTop-a:d.y=e===g.Down?c.scrollTop-m.y:c.scrollTop-n.y,d.y&&c.scrollBy({top:-d.y,behavior:h});break}}this.handleMove(a,bC(bD(l,this.referenceCoordinates),d))}}}handleMove(a,b){let{onMove:c}=this.props;a.preventDefault(),c(b)}handleEnd(a){let{onEnd:b}=this.props;a.preventDefault(),this.detach(),b()}handleCancel(a){let{onCancel:b}=this.props;a.preventDefault(),this.detach(),b()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function cm(a){return!!(a&&"distance"in a)}function cn(a){return!!(a&&"delay"in a)}cl.activators=[{eventName:"onKeyDown",handler:(a,b,c)=>{let{keyboardCodes:d=cj,onActivation:e}=b,{active:f}=c,{code:g}=a.nativeEvent;if(d.start.includes(g)){let b=f.activatorNode.current;return(!b||a.target===b)&&(a.preventDefault(),null==e||e({event:a.nativeEvent}),!0)}return!1}}];class co{constructor(a,b,c){var d;void 0===c&&(c=function(a){let{EventTarget:b}=bo(a);return a instanceof b?a:bs(a)}(a.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=a,this.events=b;let{event:e}=a,{target:f}=e;this.props=a,this.events=b,this.document=bs(f),this.documentListeners=new cf(this.document),this.listeners=new cf(c),this.windowListeners=new cf(bo(f)),this.initialCoordinates=null!=(d=bF(e))?d:bR,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){let{events:a,props:{options:{activationConstraint:b,bypassActivationConstraint:c}}}=this;if(this.listeners.add(a.move.name,this.handleMove,{passive:!1}),this.listeners.add(a.end.name,this.handleEnd),a.cancel&&this.listeners.add(a.cancel.name,this.handleCancel),this.windowListeners.add(f.Resize,this.handleCancel),this.windowListeners.add(f.DragStart,ch),this.windowListeners.add(f.VisibilityChange,this.handleCancel),this.windowListeners.add(f.ContextMenu,ch),this.documentListeners.add(f.Keydown,this.handleKeydown),b){if(null!=c&&c({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(cn(b)){this.timeoutId=setTimeout(this.handleStart,b.delay),this.handlePending(b);return}if(cm(b))return void this.handlePending(b)}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(a,b){let{active:c,onPending:d}=this.props;d(c,a,this.initialCoordinates,b)}handleStart(){let{initialCoordinates:a}=this,{onStart:b}=this.props;a&&(this.activated=!0,this.documentListeners.add(f.Click,ci,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(f.SelectionChange,this.removeTextSelection),b(a))}handleMove(a){var b;let{activated:c,initialCoordinates:d,props:e}=this,{onMove:f,options:{activationConstraint:g}}=e;if(!d)return;let h=null!=(b=bF(a))?b:bR,i=bD(d,h);if(!c&&g){if(cm(g)){if(null!=g.tolerance&&cg(i,g.tolerance))return this.handleCancel();if(cg(i,g.distance))return this.handleStart()}return cn(g)&&cg(i,g.tolerance)?this.handleCancel():void this.handlePending(g,i)}a.cancelable&&a.preventDefault(),f(h)}handleEnd(){let{onAbort:a,onEnd:b}=this.props;this.detach(),this.activated||a(this.props.active),b()}handleCancel(){let{onAbort:a,onCancel:b}=this.props;this.detach(),this.activated||a(this.props.active),b()}handleKeydown(a){a.code===g.Esc&&this.handleCancel()}removeTextSelection(){var a;null==(a=this.document.getSelection())||a.removeAllRanges()}}let cp={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class cq extends co{constructor(a){let{event:b}=a;super(a,cp,bs(b.target))}}cq.activators=[{eventName:"onPointerDown",handler:(a,b)=>{let{nativeEvent:c}=a,{onActivation:d}=b;return!!c.isPrimary&&0===c.button&&(null==d||d({event:c}),!0)}}];let cr={move:{name:"mousemove"},end:{name:"mouseup"}};!function(a){a[a.RightClick=2]="RightClick"}(h||(h={}));class cs extends co{constructor(a){super(a,cr,bs(a.event.target))}}cs.activators=[{eventName:"onMouseDown",handler:(a,b)=>{let{nativeEvent:c}=a,{onActivation:d}=b;return c.button!==h.RightClick&&(null==d||d({event:c}),!0)}}];let ct={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class cu extends co{constructor(a){super(a,ct)}static setup(){return window.addEventListener(ct.move.name,a,{capture:!1,passive:!1}),function(){window.removeEventListener(ct.move.name,a)};function a(){}}}cu.activators=[{eventName:"onTouchStart",handler:(a,b)=>{let{nativeEvent:c}=a,{onActivation:d}=b,{touches:e}=c;return!(e.length>1)&&(null==d||d({event:c}),!0)}}],function(a){a[a.Pointer=0]="Pointer",a[a.DraggableRect=1]="DraggableRect"}(i||(i={})),function(a){a[a.TreeOrder=0]="TreeOrder",a[a.ReversedTreeOrder=1]="ReversedTreeOrder"}(j||(j={}));let cv={x:{[e.Backward]:!1,[e.Forward]:!1},y:{[e.Backward]:!1,[e.Forward]:!1}};!function(a){a[a.Always=0]="Always",a[a.BeforeDragging=1]="BeforeDragging",a[a.WhileDragging=2]="WhileDragging"}(k||(k={})),(l||(l={})).Optimized="optimized";let cw=new Map;function cx(a,b){return bw(c=>a?c||("function"==typeof b?b(a):a):null,[b,a])}function cy(a){let{callback:b,disabled:c}=a,d=bu(b),e=(0,o.useMemo)(()=>{if(c||"undefined"==typeof window||void 0===window.ResizeObserver)return;let{ResizeObserver:a}=window;return new a(d)},[c]);return(0,o.useEffect)(()=>()=>null==e?void 0:e.disconnect(),[e]),e}function cz(a){return new ce(b1(a),a)}function cA(a,b,c){void 0===b&&(b=cz);let[d,e]=(0,o.useState)(null);function f(){e(d=>{if(!a)return null;if(!1===a.isConnected){var e;return null!=(e=null!=d?d:c)?e:null}let f=b(a);return JSON.stringify(d)===JSON.stringify(f)?d:f})}let g=function(a){let{callback:b,disabled:c}=a,d=bu(b),e=(0,o.useMemo)(()=>{if(c||"undefined"==typeof window||void 0===window.MutationObserver)return;let{MutationObserver:a}=window;return new a(d)},[d,c]);return(0,o.useEffect)(()=>()=>null==e?void 0:e.disconnect(),[e]),e}({callback(b){if(a)for(let c of b){let{type:b,target:d}=c;if("childList"===b&&d instanceof HTMLElement&&d.contains(a)){f();break}}}}),h=cy({callback:f});return bt(()=>{f(),a?(null==h||h.observe(a),null==g||g.observe(document.body,{childList:!0,subtree:!0})):(null==h||h.disconnect(),null==g||g.disconnect())},[a]),d}let cB=[];function cC(a,b){void 0===b&&(b=[]);let c=(0,o.useRef)(null);return(0,o.useEffect)(()=>{c.current=null},b),(0,o.useEffect)(()=>{let b=a!==bR;b&&!c.current&&(c.current=a),!b&&c.current&&(c.current=null)},[a]),c.current?bD(a,c.current):bR}function cD(a){return(0,o.useMemo)(()=>a?function(a){let b=a.innerWidth,c=a.innerHeight;return{top:0,left:0,right:b,bottom:c,width:b,height:c}}(a):null,[a])}let cE=[],cF=[{sensor:cq,options:{}},{sensor:cl,options:{}}],cG={current:{}},cH={draggable:{measure:b2},droppable:{measure:b2,strategy:k.WhileDragging,frequency:l.Optimized},dragOverlay:{measure:b1}};class cI extends Map{get(a){var b;return null!=a&&null!=(b=super.get(a))?b:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(a=>{let{disabled:b}=a;return!b})}getNodeFor(a){var b,c;return null!=(b=null==(c=this.get(a))?void 0:c.node.current)?b:void 0}}let cJ={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new cI,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:bP},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:cH,measureDroppableContainers:bP,windowRect:null,measuringScheduled:!1},cK={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:bP,draggableNodes:new Map,over:null,measureDroppableContainers:bP},cL=(0,o.createContext)(cK),cM=(0,o.createContext)(cJ);function cN(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new cI}}}function cO(a,b){switch(b.type){case d.DragStart:return{...a,draggable:{...a.draggable,initialCoordinates:b.initialCoordinates,active:b.active}};case d.DragMove:if(null==a.draggable.active)return a;return{...a,draggable:{...a.draggable,translate:{x:b.coordinates.x-a.draggable.initialCoordinates.x,y:b.coordinates.y-a.draggable.initialCoordinates.y}}};case d.DragEnd:case d.DragCancel:return{...a,draggable:{...a.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case d.RegisterDroppable:{let{element:c}=b,{id:d}=c,e=new cI(a.droppable.containers);return e.set(d,c),{...a,droppable:{...a.droppable,containers:e}}}case d.SetDroppableDisabled:{let{id:c,key:d,disabled:e}=b,f=a.droppable.containers.get(c);if(!f||d!==f.key)return a;let g=new cI(a.droppable.containers);return g.set(c,{...f,disabled:e}),{...a,droppable:{...a.droppable,containers:g}}}case d.UnregisterDroppable:{let{id:c,key:d}=b,e=a.droppable.containers.get(c);if(!e||d!==e.key)return a;let f=new cI(a.droppable.containers);return f.delete(c),{...a,droppable:{...a.droppable,containers:f}}}default:return a}}function cP(a){let{disabled:b}=a,{active:c,activatorEvent:d,draggableNodes:e}=(0,o.useContext)(cL),f=by(d),g=by(null==c?void 0:c.id);return(0,o.useEffect)(()=>{if(!b&&!d&&f&&null!=g){if(!bE(f)||document.activeElement===f.target)return;let a=e.get(g);if(!a)return;let{activatorNode:b,node:c}=a;(b.current||c.current)&&requestAnimationFrame(()=>{for(let a of[b.current,c.current]){if(!a)continue;let b=a.matches(bH)?a:a.querySelector(bH);if(b){b.focus();break}}})}},[d,b,e,g,f]),null}let cQ=(0,o.createContext)({...bR,scaleX:1,scaleY:1});!function(a){a[a.Uninitialized=0]="Uninitialized",a[a.Initializing=1]="Initializing",a[a.Initialized=2]="Initialized"}(m||(m={}));let cR=(0,o.memo)(function(a){var b,c,f,g,h,l;let{id:n,accessibility:q,autoScroll:r=!0,children:s,sensors:t=cF,collisionDetection:u=bZ,measuring:v,modifiers:w,...x}=a,[y,z]=(0,o.useReducer)(cO,void 0,cN),[A,B]=function(){let[a]=(0,o.useState)(()=>new Set),b=(0,o.useCallback)(b=>(a.add(b),()=>a.delete(b)),[a]);return[(0,o.useCallback)(b=>{let{type:c,event:d}=b;a.forEach(a=>{var b;return null==(b=a[c])?void 0:b.call(a,d)})},[a]),b]}(),[C,D]=(0,o.useState)(m.Uninitialized),E=C===m.Initialized,{draggable:{active:F,nodes:G,translate:H},droppable:{containers:I}}=y,J=null!=F?G.get(F):null,K=(0,o.useRef)({initial:null,translated:null}),L=(0,o.useMemo)(()=>{var a;return null!=F?{id:F,data:null!=(a=null==J?void 0:J.data)?a:cG,rect:K}:null},[F,J]),M=(0,o.useRef)(null),[N,O]=(0,o.useState)(null),[P,Q]=(0,o.useState)(null),R=bv(x,Object.values(x)),S=bA("DndDescribedBy",n),T=(0,o.useMemo)(()=>I.getEnabled(),[I]),U=(0,o.useMemo)(()=>({draggable:{...cH.draggable,...null==v?void 0:v.draggable},droppable:{...cH.droppable,...null==v?void 0:v.droppable},dragOverlay:{...cH.dragOverlay,...null==v?void 0:v.dragOverlay}}),[null==v?void 0:v.draggable,null==v?void 0:v.droppable,null==v?void 0:v.dragOverlay]),{droppableRects:V,measureDroppableContainers:W,measuringScheduled:X}=function(a,b){let{dragging:c,dependencies:d,config:e}=b,[f,g]=(0,o.useState)(null),{frequency:h,measure:i,strategy:j}=e,l=(0,o.useRef)(a),m=function(){switch(j){case k.Always:return!1;case k.BeforeDragging:return c;default:return!c}}(),n=bv(m),p=(0,o.useCallback)(function(a){void 0===a&&(a=[]),n.current||g(b=>null===b?a:b.concat(a.filter(a=>!b.includes(a))))},[n]),q=(0,o.useRef)(null),r=bw(b=>{if(m&&!c)return cw;if(!b||b===cw||l.current!==a||null!=f){let b=new Map;for(let c of a){if(!c)continue;if(f&&f.length>0&&!f.includes(c.id)&&c.rect.current){b.set(c.id,c.rect.current);continue}let a=c.node.current,d=a?new ce(i(a),a):null;c.rect.current=d,d&&b.set(c.id,d)}return b}return b},[a,f,c,m,i]);return(0,o.useEffect)(()=>{l.current=a},[a]),(0,o.useEffect)(()=>{m||p()},[c,m]),(0,o.useEffect)(()=>{f&&f.length>0&&g(null)},[JSON.stringify(f)]),(0,o.useEffect)(()=>{m||"number"!=typeof h||null!==q.current||(q.current=setTimeout(()=>{p(),q.current=null},h))},[h,m,p,...d]),{droppableRects:r,measureDroppableContainers:p,measuringScheduled:null!=f}}(T,{dragging:E,dependencies:[H.x,H.y],config:U.droppable}),Y=function(a,b){let c=null!=b?a.get(b):void 0,d=c?c.node.current:null;return bw(a=>{var c;return null==b?null:null!=(c=null!=d?d:a)?c:null},[d,b])}(G,F),Z=(0,o.useMemo)(()=>P?bF(P):null,[P]),$=function(){let a=(null==N?void 0:N.autoScrollEnabled)===!1,b="object"==typeof r?!1===r.enabled:!1===r,c=E&&!a&&!b;return"object"==typeof r?{...r,enabled:c}:{enabled:c}}(),_=cx(Y,U.draggable.measure);!function(a){let{activeNode:b,measure:c,initialRect:d,config:e=!0}=a,f=(0,o.useRef)(!1),{x:g,y:h}="boolean"==typeof e?{x:e,y:e}:e;bt(()=>{if(!g&&!h||!b){f.current=!1;return}if(f.current||!d)return;let a=null==b?void 0:b.node.current;if(!a||!1===a.isConnected)return;let e=b$(c(a),d);if(g||(e.x=0),h||(e.y=0),f.current=!0,Math.abs(e.x)>0||Math.abs(e.y)>0){let b=b4(a);b&&b.scrollBy({top:e.y,left:e.x})}},[b,g,h,d,c])}({activeNode:null!=F?G.get(F):null,config:$.layoutShiftCompensation,initialRect:_,measure:U.draggable.measure});let aa=cA(Y,U.draggable.measure,_),ab=cA(Y?Y.parentElement:null),ac=(0,o.useRef)({activatorEvent:null,active:null,activeNode:Y,collisionRect:null,collisions:null,droppableRects:V,draggableNodes:G,draggingNode:null,draggingNodeRect:null,droppableContainers:I,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),ad=I.getNodeFor(null==(b=ac.current.over)?void 0:b.id),ae=function(a){let{measure:b}=a,[c,d]=(0,o.useState)(null),e=cy({callback:(0,o.useCallback)(a=>{for(let{target:c}of a)if(bq(c)){d(a=>{let d=b(c);return a?{...a,width:d.width,height:d.height}:d});break}},[b])}),[f,g]=bx((0,o.useCallback)(a=>{let c=function(a){if(!a)return null;if(a.children.length>1)return a;let b=a.children[0];return bq(b)?b:a}(a);null==e||e.disconnect(),c&&(null==e||e.observe(c)),d(c?b(c):null)},[b,e]));return(0,o.useMemo)(()=>({nodeRef:f,rect:c,setRef:g}),[c,f,g])}({measure:U.dragOverlay.measure}),af=null!=(c=ae.nodeRef.current)?c:Y,ag=E?null!=(f=ae.rect)?f:aa:null,ah=!!(ae.nodeRef.current&&ae.rect),ai=function(a){let b=cx(a);return b$(a,b)}(ah?null:aa),aj=cD(af?bo(af):null),ak=function(a){let b=(0,o.useRef)(a),c=bw(c=>a?c&&c!==cB&&a&&b.current&&a.parentNode===b.current.parentNode?c:b3(a):cB,[a]);return(0,o.useEffect)(()=>{b.current=a},[a]),c}(E?null!=ad?ad:Y:null),al=function(a,b){void 0===b&&(b=b1);let[c]=a,d=cD(c?bo(c):null),[e,f]=(0,o.useState)(cE);function g(){f(()=>a.length?a.map(a=>b9(a)?d:new ce(b(a),a)):cE)}let h=cy({callback:g});return bt(()=>{null==h||h.disconnect(),g(),a.forEach(a=>null==h?void 0:h.observe(a))},[a]),e}(ak),am=function(a,b){let{transform:c,...d}=b;return null!=a&&a.length?a.reduce((a,b)=>b({transform:a,...d}),c):c}(w,{transform:{x:H.x-ai.x,y:H.y-ai.y,scaleX:1,scaleY:1},activatorEvent:P,active:L,activeNodeRect:aa,containerNodeRect:ab,draggingNodeRect:ag,over:ac.current.over,overlayNodeRect:ae.rect,scrollableAncestors:ak,scrollableAncestorRects:al,windowRect:aj}),an=Z?bC(Z,H):null,ao=function(a){let[b,c]=(0,o.useState)(null),d=(0,o.useRef)(a),e=(0,o.useCallback)(a=>{let b=b5(a.target);b&&c(a=>a?(a.set(b,b8(b)),new Map(a)):null)},[]);return(0,o.useEffect)(()=>{let b=d.current;if(a!==b){f(b);let g=a.map(a=>{let b=b5(a);return b?(b.addEventListener("scroll",e,{passive:!0}),[b,b8(b)]):null}).filter(a=>null!=a);c(g.length?new Map(g):null),d.current=a}return()=>{f(a),f(b)};function f(a){a.forEach(a=>{let b=b5(a);null==b||b.removeEventListener("scroll",e)})}},[e,a]),(0,o.useMemo)(()=>a.length?b?Array.from(b.values()).reduce((a,b)=>bC(a,b),bR):cc(a):bR,[a,b])}(ak),ap=cC(ao),aq=cC(ao,[aa]),ar=bC(am,ap),as=ag?b_(ag,am):null,at=L&&as?u({active:L,collisionRect:as,droppableRects:V,droppableContainers:T,pointerCoordinates:an}):null,au=bW(at,"id"),[av,aw]=(0,o.useState)(null),ax=(h=ah?am:bC(am,aq),l=null!=(g=null==av?void 0:av.rect)?g:null,{...h,scaleX:l&&aa?l.width/aa.width:1,scaleY:l&&aa?l.height/aa.height:1}),ay=(0,o.useRef)(null),az=(0,o.useCallback)((a,b)=>{let{sensor:c,options:e}=b;if(null==M.current)return;let f=G.get(M.current);if(!f)return;let g=a.nativeEvent,h=new c({active:M.current,activeNode:f,event:g,options:e,context:ac,onAbort(a){if(!G.get(a))return;let{onDragAbort:b}=R.current,c={id:a};null==b||b(c),A({type:"onDragAbort",event:c})},onPending(a,b,c,d){if(!G.get(a))return;let{onDragPending:e}=R.current,f={id:a,constraint:b,initialCoordinates:c,offset:d};null==e||e(f),A({type:"onDragPending",event:f})},onStart(a){let b=M.current;if(null==b)return;let c=G.get(b);if(!c)return;let{onDragStart:e}=R.current,f={activatorEvent:g,active:{id:b,data:c.data,rect:K}};(0,bk.unstable_batchedUpdates)(()=>{null==e||e(f),D(m.Initializing),z({type:d.DragStart,initialCoordinates:a,active:b}),A({type:"onDragStart",event:f}),O(ay.current),Q(g)})},onMove(a){z({type:d.DragMove,coordinates:a})},onEnd:i(d.DragEnd),onCancel:i(d.DragCancel)});function i(a){return async function(){let{active:b,collisions:c,over:e,scrollAdjustedTranslate:f}=ac.current,h=null;if(b&&f){let{cancelDrop:i}=R.current;h={activatorEvent:g,active:b,collisions:c,delta:f,over:e},a===d.DragEnd&&"function"==typeof i&&await Promise.resolve(i(h))&&(a=d.DragCancel)}M.current=null,(0,bk.unstable_batchedUpdates)(()=>{z({type:a}),D(m.Uninitialized),aw(null),O(null),Q(null),ay.current=null;let b=a===d.DragEnd?"onDragEnd":"onDragCancel";if(h){let a=R.current[b];null==a||a(h),A({type:b,event:h})}})}}ay.current=h},[G]),aA=(0,o.useCallback)((a,b)=>(c,d)=>{let e=c.nativeEvent,f=G.get(d);null!==M.current||!f||e.dndKit||e.defaultPrevented||!0===a(c,b.options,{active:f})&&(e.dndKit={capturedBy:b.sensor},M.current=d,az(c,b))},[G,az]),aB=(0,o.useMemo)(()=>t.reduce((a,b)=>{let{sensor:c}=b;return[...a,...c.activators.map(a=>({eventName:a.eventName,handler:aA(a.handler,b)}))]},[]),[t,aA]);(0,o.useEffect)(()=>{if(!bl)return;let a=t.map(a=>{let{sensor:b}=a;return null==b.setup?void 0:b.setup()});return()=>{for(let b of a)null==b||b()}},t.map(a=>{let{sensor:b}=a;return b})),bt(()=>{aa&&C===m.Initializing&&D(m.Initialized)},[aa,C]),(0,o.useEffect)(()=>{let{onDragMove:a}=R.current,{active:b,activatorEvent:c,collisions:d,over:e}=ac.current;if(!b||!c)return;let f={active:b,activatorEvent:c,collisions:d,delta:{x:ar.x,y:ar.y},over:e};(0,bk.unstable_batchedUpdates)(()=>{null==a||a(f),A({type:"onDragMove",event:f})})},[ar.x,ar.y]),(0,o.useEffect)(()=>{let{active:a,activatorEvent:b,collisions:c,droppableContainers:d,scrollAdjustedTranslate:e}=ac.current;if(!a||null==M.current||!b||!e)return;let{onDragOver:f}=R.current,g=d.get(au),h=g&&g.rect.current?{id:g.id,rect:g.rect.current,data:g.data,disabled:g.disabled}:null,i={active:a,activatorEvent:b,collisions:c,delta:{x:e.x,y:e.y},over:h};(0,bk.unstable_batchedUpdates)(()=>{aw(h),null==f||f(i),A({type:"onDragOver",event:i})})},[au]),bt(()=>{ac.current={activatorEvent:P,active:L,activeNode:Y,collisionRect:as,collisions:at,droppableRects:V,draggableNodes:G,draggingNode:af,draggingNodeRect:ag,droppableContainers:I,over:av,scrollableAncestors:ak,scrollAdjustedTranslate:ar},K.current={initial:ag,translated:as}},[L,Y,at,as,G,af,ag,V,I,av,ak,ar]),function(a){let{acceleration:b,activator:c=i.Pointer,canScroll:d,draggingRect:f,enabled:g,interval:h=5,order:k=j.TreeOrder,pointerCoordinates:l,scrollableAncestors:m,scrollableAncestorRects:n,delta:p,threshold:q}=a,r=function(a){let{delta:b,disabled:c}=a,d=by(b);return bw(a=>{if(c||!d||!a)return cv;let f={x:Math.sign(b.x-d.x),y:Math.sign(b.y-d.y)};return{x:{[e.Backward]:a.x[e.Backward]||-1===f.x,[e.Forward]:a.x[e.Forward]||1===f.x},y:{[e.Backward]:a.y[e.Backward]||-1===f.y,[e.Forward]:a.y[e.Forward]||1===f.y}}},[c,b,d])}({delta:p,disabled:!g}),[s,t]=function(){let a=(0,o.useRef)(null);return[(0,o.useCallback)((b,c)=>{a.current=setInterval(b,c)},[]),(0,o.useCallback)(()=>{null!==a.current&&(clearInterval(a.current),a.current=null)},[])]}(),u=(0,o.useRef)({x:0,y:0}),v=(0,o.useRef)({x:0,y:0}),w=(0,o.useMemo)(()=>{switch(c){case i.Pointer:return l?{top:l.y,bottom:l.y,left:l.x,right:l.x}:null;case i.DraggableRect:return f}},[c,f,l]),x=(0,o.useRef)(null),y=(0,o.useCallback)(()=>{let a=x.current;if(!a)return;let b=u.current.x*v.current.x,c=u.current.y*v.current.y;a.scrollBy(b,c)},[]),z=(0,o.useMemo)(()=>k===j.TreeOrder?[...m].reverse():m,[k,m]);(0,o.useEffect)(()=>{if(!g||!m.length||!w)return void t();for(let a of z){if((null==d?void 0:d(a))===!1)continue;let c=n[m.indexOf(a)];if(!c)continue;let{direction:f,speed:g}=function(a,b,c,d,f){let{top:g,left:h,right:i,bottom:j}=c;void 0===d&&(d=10),void 0===f&&(f=cb);let{isTop:k,isBottom:l,isLeft:m,isRight:n}=ca(a),o={x:0,y:0},p={x:0,y:0},q={height:b.height*f.y,width:b.width*f.x};return!k&&g<=b.top+q.height?(o.y=e.Backward,p.y=d*Math.abs((b.top+q.height-g)/q.height)):!l&&j>=b.bottom-q.height&&(o.y=e.Forward,p.y=d*Math.abs((b.bottom-q.height-j)/q.height)),!n&&i>=b.right-q.width?(o.x=e.Forward,p.x=d*Math.abs((b.right-q.width-i)/q.width)):!m&&h<=b.left+q.width&&(o.x=e.Backward,p.x=d*Math.abs((b.left+q.width-h)/q.width)),{direction:o,speed:p}}(a,c,w,b,q);for(let a of["x","y"])r[a][f[a]]||(g[a]=0,f[a]=0);if(g.x>0||g.y>0){t(),x.current=a,s(y,h),u.current=g,v.current=f;return}}u.current={x:0,y:0},v.current={x:0,y:0},t()},[b,y,d,t,g,h,JSON.stringify(w),JSON.stringify(r),s,m,z,n,JSON.stringify(q)])}({...$,delta:H,draggingRect:as,pointerCoordinates:an,scrollableAncestors:ak,scrollableAncestorRects:al});let aC=(0,o.useMemo)(()=>({active:L,activeNode:Y,activeNodeRect:aa,activatorEvent:P,collisions:at,containerNodeRect:ab,dragOverlay:ae,draggableNodes:G,droppableContainers:I,droppableRects:V,over:av,measureDroppableContainers:W,scrollableAncestors:ak,scrollableAncestorRects:al,measuringConfiguration:U,measuringScheduled:X,windowRect:aj}),[L,Y,aa,P,at,ab,ae,G,I,V,av,W,ak,al,U,X,aj]),aD=(0,o.useMemo)(()=>({activatorEvent:P,activators:aB,active:L,activeNodeRect:aa,ariaDescribedById:{draggable:S},dispatch:z,draggableNodes:G,over:av,measureDroppableContainers:W}),[P,aB,L,aa,z,S,G,av,W]);return p().createElement(bL.Provider,{value:B},p().createElement(cL.Provider,{value:aD},p().createElement(cM.Provider,{value:aC},p().createElement(cQ.Provider,{value:ax},s)),p().createElement(cP,{disabled:(null==q?void 0:q.restoreFocus)===!1})),p().createElement(bO,{...q,hiddenTextDescribedById:S}))}),cS=(0,o.createContext)(null),cT="button",cU={timeout:25};function cV(a,b,c){let d=a.slice();return d.splice(c<0?d.length+c:c,0,d.splice(b,1)[0]),d}function cW(a){return null!==a&&a>=0}let cX=a=>{let{rects:b,activeIndex:c,overIndex:d,index:e}=a,f=cV(b,d,c),g=b[e],h=f[e];return h&&g?{x:h.left-g.left,y:h.top-g.top,scaleX:h.width/g.width,scaleY:h.height/g.height}:null},cY={scaleX:1,scaleY:1},cZ=a=>{var b;let{activeIndex:c,activeNodeRect:d,index:e,rects:f,overIndex:g}=a,h=null!=(b=f[c])?b:d;if(!h)return null;if(e===c){let a=f[g];return a?{x:0,y:c<g?a.top+a.height-(h.top+h.height):a.top-h.top,...cY}:null}let i=function(a,b,c){let d=a[b],e=a[b-1],f=a[b+1];return d?c<b?e?d.top-(e.top+e.height):f?f.top-(d.top+d.height):0:f?f.top-(d.top+d.height):e?d.top-(e.top+e.height):0:0}(f,e,c);return e>c&&e<=g?{x:0,y:-h.height-i,...cY}:e<c&&e>=g?{x:0,y:h.height+i,...cY}:{x:0,y:0,...cY}},c$="Sortable",c_=p().createContext({activeIndex:-1,containerId:c$,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:cX,disabled:{draggable:!1,droppable:!1}});function c0(a){let{children:b,id:c,items:d,strategy:e=cX,disabled:f=!1}=a,{active:g,dragOverlay:h,droppableRects:i,over:j,measureDroppableContainers:k}=(0,o.useContext)(cM),l=bA(c$,c),m=null!==h.rect,n=(0,o.useMemo)(()=>d.map(a=>"object"==typeof a&&"id"in a?a.id:a),[d]),q=null!=g,r=g?n.indexOf(g.id):-1,s=j?n.indexOf(j.id):-1,t=(0,o.useRef)(n),u=!function(a,b){if(a===b)return!0;if(a.length!==b.length)return!1;for(let c=0;c<a.length;c++)if(a[c]!==b[c])return!1;return!0}(n,t.current),v=-1!==s&&-1===r||u,w="boolean"==typeof f?{draggable:f,droppable:f}:f;bt(()=>{u&&q&&k(n)},[u,n,q,k]),(0,o.useEffect)(()=>{t.current=n},[n]);let x=(0,o.useMemo)(()=>({activeIndex:r,containerId:l,disabled:w,disableTransforms:v,items:n,overIndex:s,useDragOverlay:m,sortedRects:n.reduce((a,b,c)=>{let d=i.get(b);return d&&(a[c]=d),a},Array(n.length)),strategy:e}),[r,l,w.draggable,w.droppable,v,n,s,i,m,e]);return p().createElement(c_.Provider,{value:x},b)}let c1=a=>{let{id:b,items:c,activeIndex:d,overIndex:e}=a;return cV(c,d,e).indexOf(b)},c2=a=>{let{containerId:b,isSorting:c,wasDragging:d,index:e,items:f,newIndex:g,previousItems:h,previousContainerId:i,transition:j}=a;return!!j&&!!d&&(h===f||e!==g)&&(!!c||g!==e&&b===i)},c3={duration:200,easing:"ease"},c4="transform",c5=bG.Transition.toString({property:c4,duration:0,easing:"linear"}),c6={roleDescription:"sortable"};function c7(a){if(!a)return!1;let b=a.data.current;return!!b&&"sortable"in b&&"object"==typeof b.sortable&&"containerId"in b.sortable&&"items"in b.sortable&&"index"in b.sortable}let c8=[g.Down,g.Right,g.Up,g.Left],c9=(a,b)=>{let{context:{active:c,collisionRect:d,droppableRects:e,droppableContainers:f,over:h,scrollableAncestors:i}}=b;if(c8.includes(a.code)){if(a.preventDefault(),!c||!d)return;let b=[];f.getEnabled().forEach(c=>{if(!c||null!=c&&c.disabled)return;let f=e.get(c.id);if(f)switch(a.code){case g.Down:d.top<f.top&&b.push(c);break;case g.Up:d.top>f.top&&b.push(c);break;case g.Left:d.left>f.left&&b.push(c);break;case g.Right:d.left<f.left&&b.push(c)}});let j=(a=>{let{collisionRect:b,droppableRects:c,droppableContainers:d}=a,e=bV(b),f=[];for(let a of d){let{id:b}=a,d=c.get(b);if(d){let c=bV(d),g=Number((e.reduce((a,b,d)=>a+bS(c[d],b),0)/4).toFixed(4));f.push({id:b,data:{droppableContainer:a,value:g}})}}return f.sort(bT)})({active:c,collisionRect:d,droppableRects:e,droppableContainers:b,pointerCoordinates:null}),k=bW(j,"id");if(k===(null==h?void 0:h.id)&&j.length>1&&(k=j[1].id),null!=k){let a=f.get(c.id),b=f.get(k),g=b?e.get(b.id):null,h=null==b?void 0:b.node.current;if(h&&g&&a&&b){let c=b3(h).some((a,b)=>i[b]!==a),e=da(a,b),f=function(a,b){return!!c7(a)&&!!c7(b)&&!!da(a,b)&&a.data.current.sortable.index<b.data.current.sortable.index}(a,b),j=c||!e?{x:0,y:0}:{x:f?d.width-g.width:0,y:f?d.height-g.height:0},k={x:g.left,y:g.top};return j.x&&j.y?k:bD(k,j)}}}};function da(a,b){return!!c7(a)&&!!c7(b)&&a.data.current.sortable.containerId===b.data.current.sortable.containerId}let db=(0,o.lazy)(()=>Promise.all([c.e(2762),c.e(4617),c.e(9450),c.e(6257),c.e(3785)]).then(c.bind(c,33785)).then(a=>({default:a.EnhancedContextGalleryModal}))),dc="Yapılan canlı testlerde Console log ekranında hatalar karşımıza \xe7ıkmıştır. Proje yapımıza uygun olarak d\xfczenleme yap. Consol logları şu şekildedir:",dd=(0,o.memo)(function({promptText:a,className:b,onDoubleClick:c,title:d}){let[e,f]=(0,o.useState)(!1);if(!a.startsWith(dc))return(0,n.jsx)("p",{className:b,onDoubleClick:c,title:d,children:a});let g=a.substring(dc.length).trim();return(0,n.jsxs)("div",{className:"space-y-2 w-full max-w-full overflow-hidden",children:[(0,n.jsx)("p",{className:b,onDoubleClick:c,title:d,children:dc}),(0,n.jsxs)("div",{className:"border-2 border-red-300 rounded-md bg-red-50 p-3 w-full max-w-full overflow-hidden box-border",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,n.jsx)("span",{className:"text-sm font-medium text-red-700 truncate",children:"Console Log İ\xe7eriği"}),(0,n.jsx)("button",{onClick:()=>f(!e),className:"text-red-600 hover:text-red-800 text-sm font-medium flex items-center gap-1 flex-shrink-0",children:e?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(P.A,{className:"h-4 w-4"}),"Daralt"]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(Q.A,{className:"h-4 w-4"}),"Genişlet"]})})]}),e?(0,n.jsx)("pre",{className:"text-sm text-red-800 whitespace-pre-wrap bg-white p-2 rounded border border-red-200 max-h-40 overflow-y-auto w-full max-w-full overflow-x-hidden break-words",children:g}):(0,n.jsx)("p",{className:"text-sm text-red-700 w-full max-w-full overflow-hidden break-words whitespace-pre-wrap",children:g.split("\n")[0].length>100?g.split("\n")[0].substring(0,100)+"...":g.split("\n")[0]})]})]})}),de=(0,o.memo)(function({prompt:a,isMultiSelectMode:b,selectedPrompts:c,editingPromptId:e,editingText:f,editTextareaRef:g,onSelectPrompt:h,onEditPrompt:i,onSaveEdit:j,onCancelEdit:k,onCopyPrompt:l,setEditingText:m,onHashtagFilter:p}){let{attributes:t,listeners:u,setNodeRef:w,transform:x,transition:y,isDragging:z}=function(a){var b,c,e,f;let{animateLayoutChanges:g=c2,attributes:h,disabled:i,data:j,getNewIndex:k=c1,id:l,strategy:m,resizeObserverConfig:n,transition:p=c3}=a,{items:q,containerId:r,activeIndex:s,disabled:t,disableTransforms:u,sortedRects:v,overIndex:w,useDragOverlay:x,strategy:y}=(0,o.useContext)(c_),z=(b=i,c=t,"boolean"==typeof b?{draggable:b,droppable:!1}:{draggable:null!=(e=null==b?void 0:b.draggable)?e:c.draggable,droppable:null!=(f=null==b?void 0:b.droppable)?f:c.droppable}),A=q.indexOf(l),B=(0,o.useMemo)(()=>({sortable:{containerId:r,index:A,items:q},...j}),[r,j,A,q]),C=(0,o.useMemo)(()=>q.slice(q.indexOf(l)),[q,l]),{rect:D,node:E,isOver:F,setNodeRef:G}=function(a){let{data:b,disabled:c=!1,id:e,resizeObserverConfig:f}=a,g=bA("Droppable"),{active:h,dispatch:i,over:j,measureDroppableContainers:k}=(0,o.useContext)(cL),l=(0,o.useRef)({disabled:c}),m=(0,o.useRef)(!1),n=(0,o.useRef)(null),p=(0,o.useRef)(null),{disabled:q,updateMeasurementsFor:r,timeout:s}={...cU,...f},t=bv(null!=r?r:e),u=cy({callback:(0,o.useCallback)(()=>{if(!m.current){m.current=!0;return}null!=p.current&&clearTimeout(p.current),p.current=setTimeout(()=>{k(Array.isArray(t.current)?t.current:[t.current]),p.current=null},s)},[s]),disabled:q||!h}),[v,w]=bx((0,o.useCallback)((a,b)=>{u&&(b&&(u.unobserve(b),m.current=!1),a&&u.observe(a))},[u])),x=bv(b);return(0,o.useEffect)(()=>{u&&v.current&&(u.disconnect(),m.current=!1,u.observe(v.current))},[v,u]),(0,o.useEffect)(()=>(i({type:d.RegisterDroppable,element:{id:e,key:g,disabled:c,node:v,rect:n,data:x}}),()=>i({type:d.UnregisterDroppable,key:g,id:e})),[e]),(0,o.useEffect)(()=>{c!==l.current.disabled&&(i({type:d.SetDroppableDisabled,id:e,key:g,disabled:c}),l.current.disabled=c)},[e,g,c,i]),{active:h,rect:n,isOver:(null==j?void 0:j.id)===e,node:v,over:j,setNodeRef:w}}({id:l,data:B,disabled:z.droppable,resizeObserverConfig:{updateMeasurementsFor:C,...n}}),{active:H,activatorEvent:I,activeNodeRect:J,attributes:K,setNodeRef:L,listeners:M,isDragging:N,over:O,setActivatorNodeRef:P,transform:Q}=function(a){let{id:b,data:c,disabled:d=!1,attributes:e}=a,f=bA("Draggable"),{activators:g,activatorEvent:h,active:i,activeNodeRect:j,ariaDescribedById:k,draggableNodes:l,over:m}=(0,o.useContext)(cL),{role:n=cT,roleDescription:p="draggable",tabIndex:q=0}=null!=e?e:{},r=(null==i?void 0:i.id)===b,s=(0,o.useContext)(r?cQ:cS),[t,u]=bx(),[v,w]=bx(),x=(0,o.useMemo)(()=>g.reduce((a,c)=>{let{eventName:d,handler:e}=c;return a[d]=a=>{e(a,b)},a},{}),[g,b]),y=bv(c);return bt(()=>(l.set(b,{id:b,key:f,node:t,activatorNode:v,data:y}),()=>{let a=l.get(b);a&&a.key===f&&l.delete(b)}),[l,b]),{active:i,activatorEvent:h,activeNodeRect:j,attributes:(0,o.useMemo)(()=>({role:n,tabIndex:q,"aria-disabled":d,"aria-pressed":!!r&&n===cT||void 0,"aria-roledescription":p,"aria-describedby":k.draggable}),[d,n,q,r,p,k.draggable]),isDragging:r,listeners:d?void 0:x,node:t,over:m,setNodeRef:u,setActivatorNodeRef:w,transform:s}}({id:l,data:B,attributes:{...c6,...h},disabled:z.draggable}),R=function(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];return(0,o.useMemo)(()=>a=>{b.forEach(b=>b(a))},b)}(G,L),S=!!H,T=S&&!u&&cW(s)&&cW(w),U=!x&&N,V=U&&T?Q:null,W=T?null!=V?V:(null!=m?m:y)({rects:v,activeNodeRect:J,activeIndex:s,overIndex:w,index:A}):null,X=cW(s)&&cW(w)?k({id:l,items:q,activeIndex:s,overIndex:w}):A,Y=null==H?void 0:H.id,Z=(0,o.useRef)({activeId:Y,items:q,newIndex:X,containerId:r}),$=q!==Z.current.items,_=g({active:H,containerId:r,isDragging:N,isSorting:S,id:l,index:A,items:q,newIndex:Z.current.newIndex,previousItems:Z.current.items,previousContainerId:Z.current.containerId,transition:p,wasDragging:null!=Z.current.activeId}),aa=function(a){let{disabled:b,index:c,node:d,rect:e}=a,[f,g]=(0,o.useState)(null),h=(0,o.useRef)(c);return bt(()=>{if(!b&&c!==h.current&&d.current){let a=e.current;if(a){let b=b1(d.current,{ignoreTransform:!0}),c={x:a.left-b.left,y:a.top-b.top,scaleX:a.width/b.width,scaleY:a.height/b.height};(c.x||c.y)&&g(c)}}c!==h.current&&(h.current=c)},[b,c,d,e]),(0,o.useEffect)(()=>{f&&g(null)},[f]),f}({disabled:!_,index:A,node:E,rect:D});return(0,o.useEffect)(()=>{S&&Z.current.newIndex!==X&&(Z.current.newIndex=X),r!==Z.current.containerId&&(Z.current.containerId=r),q!==Z.current.items&&(Z.current.items=q)},[S,X,r,q]),(0,o.useEffect)(()=>{if(Y===Z.current.activeId)return;if(null!=Y&&null==Z.current.activeId){Z.current.activeId=Y;return}let a=setTimeout(()=>{Z.current.activeId=Y},50);return()=>clearTimeout(a)},[Y]),{active:H,activeIndex:s,attributes:K,data:B,rect:D,index:A,newIndex:X,items:q,isOver:F,isSorting:S,isDragging:N,listeners:M,node:E,overIndex:w,over:O,setNodeRef:R,setActivatorNodeRef:P,setDroppableNodeRef:G,setDraggableNodeRef:L,transform:null!=aa?aa:W,transition:aa||$&&Z.current.newIndex===A?c5:(!U||bE(I))&&p&&(S||_)?bG.Transition.toString({...p,property:c4}):void 0}}({id:a.id}),A={transform:bG.Transform.toString(x),transition:y,opacity:z?.5:1};return(0,n.jsxs)(r.Zp,{ref:w,style:A,className:`transition-all duration-200 ${a.is_used?"bg-gray-50 border-gray-300":"bg-white border-gray-200 hover:border-blue-300 hover:shadow-md"} ${c.has(a.id)?"ring-2 ring-blue-500 border-blue-500":""}`,onClick:()=>b?h(a.id):void 0,children:[(0,n.jsxs)(r.aR,{className:"pb-3",children:[(0,n.jsxs)("div",{className:"block lg:hidden",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[!b&&(0,n.jsx)("div",{className:"flex items-center justify-center w-8 h-8 cursor-grab active:cursor-grabbing",...t,...u,children:(0,n.jsx)(S,{className:"h-5 w-5 text-gray-400"})}),b&&(0,n.jsx)("div",{className:"flex items-center justify-center w-8 h-8 cursor-pointer",onClick:b=>{b.stopPropagation(),h(a.id)},children:c.has(a.id)?(0,n.jsx)(T,{className:"h-6 w-6 text-blue-600"}):(0,n.jsx)(U,{className:"h-6 w-6 text-gray-400"})}),(0,n.jsx)("div",{className:`flex items-center justify-center w-10 h-10 rounded-full text-base font-medium ${a.is_used?"bg-green-100 text-green-700":"bg-blue-100 text-blue-700"}`,children:a.order_index}),(0,n.jsx)("div",{className:"flex flex-col",children:(0,n.jsx)("span",{className:`text-sm font-mono px-2 py-1 rounded ${a.is_used?"bg-gray-100 text-gray-500":"bg-blue-50 text-blue-600"}`,children:a.task_code||`task-${a.order_index}`})})]}),(0,n.jsx)("div",{className:"flex items-center gap-2",children:a.is_used?(0,n.jsx)(V.A,{className:"h-5 w-5 text-green-600"}):(0,n.jsx)(W.A,{className:"h-5 w-5 text-gray-400"})})]}),!b&&(0,n.jsx)("div",{className:"flex items-center gap-2 justify-end",children:e===a.id?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(q.$,{variant:"default",size:"sm",onClick:j,className:"bg-green-600 hover:bg-green-700 text-white border-green-600 hover:border-green-700 min-h-[44px] px-4",children:[(0,n.jsx)(X.A,{className:"h-4 w-4 mr-2"}),"Kaydet"]}),(0,n.jsxs)(q.$,{variant:"outline",size:"sm",onClick:k,className:"text-red-600 hover:text-red-700 min-h-[44px] px-4",children:[(0,n.jsx)(Y.A,{className:"h-4 w-4 mr-2"}),"İptal"]})]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(q.$,{variant:"outline",size:"sm",onClick:()=>i(a),className:"text-gray-600 hover:text-gray-700 min-h-[44px] px-4",children:[(0,n.jsx)(Z.A,{className:"h-4 w-4 mr-2"}),"D\xfczenle"]}),(0,n.jsx)(bj,{promptId:a.id,promptTitle:a.title||void 0,promptText:a.prompt_text,taskCode:a.task_code||void 0,variant:"outline",size:"sm",className:"min-h-[44px] px-4"}),(0,n.jsxs)(q.$,{variant:a.is_used?"secondary":"default",size:"sm",onClick:()=>l(a),className:`min-h-[44px] px-4 ${a.is_used?"opacity-60":""}`,children:[(0,n.jsx)($.A,{className:"h-4 w-4 mr-2"}),a.is_used?"Kopyalandı":"Kopyala"]})]})})]}),(0,n.jsxs)("div",{className:"hidden lg:flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[!b&&(0,n.jsx)("div",{className:"flex items-center justify-center w-6 h-6 cursor-grab active:cursor-grabbing",...t,...u,children:(0,n.jsx)(S,{className:"h-4 w-4 text-gray-400"})}),b&&(0,n.jsx)("div",{className:"flex items-center justify-center w-6 h-6 cursor-pointer",onClick:b=>{b.stopPropagation(),h(a.id)},children:c.has(a.id)?(0,n.jsx)(T,{className:"h-5 w-5 text-blue-600"}):(0,n.jsx)(U,{className:"h-5 w-5 text-gray-400"})}),(0,n.jsx)("div",{className:`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${a.is_used?"bg-green-100 text-green-700":"bg-blue-100 text-blue-700"}`,children:a.order_index}),(0,n.jsx)("div",{className:"flex flex-col",children:(0,n.jsx)("span",{className:`text-xs font-mono px-2 py-1 rounded ${a.is_used?"bg-gray-100 text-gray-500":"bg-blue-50 text-blue-600"}`,children:a.task_code||`task-${a.order_index}`})}),(0,n.jsx)("div",{className:"flex items-center gap-2",children:a.is_used?(0,n.jsx)(V.A,{className:"h-4 w-4 text-green-600"}):(0,n.jsx)(W.A,{className:"h-4 w-4 text-gray-400"})})]}),!b&&(0,n.jsx)("div",{className:"flex items-center gap-2",children:e===a.id?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(q.$,{variant:"default",size:"sm",onClick:j,className:"bg-green-600 hover:bg-green-700 text-white border-green-600 hover:border-green-700",children:[(0,n.jsx)(X.A,{className:"h-4 w-4 mr-2"}),"Kaydet"]}),(0,n.jsxs)(q.$,{variant:"outline",size:"sm",onClick:k,className:"text-red-600 hover:text-red-700",children:[(0,n.jsx)(Y.A,{className:"h-4 w-4 mr-2"}),"İptal"]})]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(q.$,{variant:"outline",size:"sm",onClick:()=>i(a),className:"text-gray-600 hover:text-gray-700",children:[(0,n.jsx)(Z.A,{className:"h-4 w-4 mr-2"}),"D\xfczenle"]}),(0,n.jsx)(bj,{promptId:a.id,promptTitle:a.title||void 0,promptText:a.prompt_text,taskCode:a.task_code||void 0,variant:"outline",size:"sm"}),(0,n.jsxs)(q.$,{variant:a.is_used?"secondary":"default",size:"sm",onClick:()=>l(a),className:a.is_used?"opacity-60":"",children:[(0,n.jsx)($.A,{className:"h-4 w-4 mr-2"}),a.is_used?"Kopyalandı":"Kopyala"]})]})})]})]}),(0,n.jsx)(r.Wu,{className:"pt-0",children:e===a.id?(0,n.jsx)(s.T,{ref:g,value:f,onChange:a=>m(a.target.value),className:"min-h-[60px] max-h-[200px] resize-none",style:{height:"auto"},onKeyDown:a=>{"Enter"===a.key&&a.ctrlKey?j():"Escape"===a.key&&k()},autoFocus:!0}):(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(dd,{promptText:a.prompt_text,className:`text-sm leading-relaxed cursor-pointer whitespace-pre-wrap break-words ${a.is_used?"text-gray-500":"text-gray-700"}`,onDoubleClick:()=>i(a),title:"D\xfczenlemek i\xe7in \xe7ift tıklayın"}),(a.tags&&a.tags.length>0||a.category)&&(0,n.jsxs)("div",{className:"flex flex-wrap items-center gap-1 pt-2 sm:gap-1.5",children:[a.category&&(0,n.jsxs)(v.E,{variant:"outline",className:"text-xs sm:text-xs bg-gray-50 text-gray-700 border-gray-300 hover:bg-gray-100 transition-colors cursor-pointer",onClick:a=>{a.preventDefault(),a.stopPropagation()},children:[(0,n.jsx)(_.A,{className:"w-3 h-3 mr-1 text-gray-500"}),a.category]}),a.tags&&Array.isArray(a.tags)&&a.tags.map((a,b)=>{let c="string"==typeof a?a:String(a);return c&&""!==c.trim()?(0,n.jsxs)(v.E,{variant:"secondary",className:"text-xs sm:text-xs bg-blue-50 text-blue-700 border-blue-200 cursor-pointer hover:bg-blue-100 transition-colors",onClick:a=>{a.preventDefault(),a.stopPropagation(),p(c)},children:[(0,n.jsx)(aa,{className:"w-3 h-3 mr-1 text-blue-500"}),c.replace("#","")]},b):null})]})]})})]})});function df({isContextGalleryOpen:a=!1,onToggleContextGallery:b}={}){let[c,d]=(0,o.useState)(""),[e,f]=(0,o.useState)(null),[g,h]=(0,o.useState)(""),[i,j]=(0,o.useState)(new Set),[k,l]=(0,o.useState)(!1),[m,p]=(0,o.useState)(""),[r,s]=(0,o.useState)([]),[w,x]=(0,o.useState)(null),[y,z]=(0,o.useState)(""),[A,B]=(0,o.useState)(!1),[C,D]=(0,o.useState)(!0),[E,F]=(0,o.useState)([]),[G,H]=(0,o.useState)(null),[I,J]=(0,o.useState)(!1),[K,L]=(0,o.useState)(1),M=(0,o.useRef)(null),{activeProjectId:N,isContextEnabled:R}=(0,aj.C)(),{data:S}=(0,aq.p$)(),T=function(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];return(0,o.useMemo)(()=>[...b].filter(a=>null!=a),[...b])}(bQ(cq),bQ(cl,{coordinateGetter:c9})),{data:U=[]}=(0,ak.F$)(N),{data:X}=(0,al.By)(N),{data:Z=[]}=(0,am.I)({queryKey:["all-hashtags",N],queryFn:async()=>{if(!N)return[];let{data:a,error:b}=await an.L.from("prompts").select("tags").eq("project_id",N);if(b)throw Error(b.message);let c=new Set;return a?.forEach(a=>{a.tags&&Array.isArray(a.tags)&&a.tags.forEach(a=>{if("string"==typeof a&&a.trim()){let b=a.startsWith("#")?a.toLowerCase():`#${a.toLowerCase()}`;c.add(b)}})}),Array.from(c).sort()},enabled:!!N}),{data:ao=[]}=(0,am.I)({queryKey:["all-categories",N],queryFn:async()=>{if(!N)return[];let{data:a,error:b}=await an.L.from("prompts").select("category").eq("project_id",N).not("category","is",null);if(b)throw Error(b.message);let c=new Set;return a?.forEach(a=>{a.category&&c.add(a.category.toLowerCase())}),Array.from(c).sort()},enabled:!!N}),ap=(0,ak.sW)(),at=(0,ak.$I)(),au=(0,ak.GQ)(),ay=(0,ak.Qu)(),az=U.filter(a=>{try{let b=""===m.trim()||a.prompt_text.toLowerCase().includes(m.toLowerCase())||a.title&&a.title.toLowerCase().includes(m.toLowerCase()),c=!G||a.category===G,d=0===E.length||a.tags&&Array.isArray(a.tags)&&E.some(b=>a.tags.some(a=>("string"==typeof a?a:String(a)).toLowerCase()===b.toLowerCase()));return b&&c&&d}catch(b){return console.error("Error filtering prompt:",b,a),!0}}),aA=async()=>{if(c.trim()&&N){if(S&&!S.can_create_prompt)return void as.oR.error(`Prompt oluşturma limitinize ulaştınız (${S.current_prompts}/${S.max_prompts_per_project}). Planınızı y\xfckseltin.`);let a=c;I&&(a=`Yapılan canlı testlerde Console log ekranında hatalar karşımıza \xe7ıkmıştır. Proje yapımıza uygun olarak d\xfczenleme yap. Consol logları şu şekildedir:

`+c);let b=a;d(""),z(""),s([]),x(null),B(!1);try{let{hashtags:a,folderPaths:c,originalText:d}=av(b),e=aw(r||[],a||[]),f=c&&c.length>0?c[0]:w,g=U.length>0?Math.max(...U.map(a=>a.order_index||0)):0;await ap.mutateAsync({project_id:N,prompt_text:d,title:y?.trim()||void 0,category:f||void 0,tags:e||[],order_index:g+1,is_used:!1}),as.oR.success("Prompt başarıyla eklendi!")}catch(a){console.error("Prompt ekleme hatası:",a),d(b),z(y),s(r),x(w),B(A),as.oR.error("Prompt eklenirken bir hata oluştu. L\xfctfen tekrar deneyin.")}}},aC=(0,o.useCallback)(a=>{try{let b="string"==typeof a?a:String(a);if(!b||""===b.trim())return void console.warn("Invalid hashtag provided to handleHashtagFilter:",a);E.includes(b)?F(E.filter(a=>a!==b)):F([...E,b])}catch(b){console.error("Error in handleHashtagFilter:",b,a)}},[E]),aD=(0,o.useCallback)(a=>{H(G===a?null:a)},[G]),aE=(0,o.useCallback)(async a=>{try{let b=R&&X?.context_text||"",c=a.task_code||`task-${a.order_index}`,d="";d=b?`${b}

${c}
${a.prompt_text}`:`${c}
${a.prompt_text}`,await navigator.clipboard.writeText(d),await ay.mutateAsync(a.id)}catch(a){console.error("Kopyalama hatası:",a)}},[R,X?.context_text,ay]),aF=async()=>{if(N)try{let a=U.filter(a=>a.is_used).sort((a,b)=>new Date(b.created_at).getTime()-new Date(a.created_at).getTime())[0];a&&await at.mutateAsync({id:a.id,is_used:!1})}catch(a){console.error("Geri alma hatası:",a)}},aG=(0,o.useCallback)(a=>{f(a.id),h(a.prompt_text)},[]),aH=async()=>{if(e&&g.trim())try{let{hashtags:a,folderPaths:b,originalText:c}=av(g),d=U.find(a=>a.id===e),i=d?.tags||[],j=d?.category,k=aw(i,a||[]),l=b&&b.length>0?b[0]:j;await at.mutateAsync({id:e,prompt_text:c,tags:k,category:l||void 0}),f(null),h("")}catch(a){console.error("Prompt g\xfcncelleme hatası:",a),alert("Prompt g\xfcncellenirken bir hata oluştu. L\xfctfen tekrar deneyin.")}},aI=(0,o.useCallback)(()=>{f(null),h("")},[]),aJ=(0,o.useCallback)(()=>{l(!k),j(new Set)},[k]),aL=(0,o.useCallback)(a=>{let b=new Set(i);b.has(a)?b.delete(a):b.add(a),j(b)},[i]),aM=async()=>{if(0!==i.size)try{let a=U.filter(a=>i.has(a.id)).sort((a,b)=>a.order_index-b.order_index),b=R&&X?.context_text||"",c=b?`${b}

`:"";for(let b of(a.forEach((b,d)=>{let e=b.task_code||`task-${b.order_index}`;c+=`${d+1}. ${e}
${b.prompt_text}`,d<a.length-1&&(c+="\n\n")}),await navigator.clipboard.writeText(c),i))await ay.mutateAsync(b);j(new Set),l(!1)}catch(a){console.error("\xc7oklu kopyalama hatası:",a)}},aN=()=>{let a=U.filter(a=>!a.is_used).sort((a,b)=>a.order_index-b.order_index);if(0===a.length)return void alert("Kopyalanmamış prompt bulunamadı!");let b=R&&X?.context_text||"",c=`# ${X?.name||"Prompt Listesi"}

`;b&&(c+=`## Context

${b}

`),c+=`## Kopyalanmamış Prompt'lar

`,a.forEach((a,b)=>{let d=a.task_code||`task-${a.order_index}`;c+=`### ${b+1}. ${d}

${a.prompt_text}

---

`});let d=new Blob([c],{type:"text/markdown"}),e=URL.createObjectURL(d),f=document.createElement("a");f.href=e,f.download=`${X?.name||"prompts"}-unused.md`,document.body.appendChild(f),f.click(),document.body.removeChild(f),URL.revokeObjectURL(e)},aO=(0,o.useCallback)(async a=>{let{active:b,over:c}=a;if(!c||b.id===c.id)return;let d=U.sort((a,b)=>{if(a.is_used!==b.is_used)return a.is_used?1:-1;if(!a.is_used&&!b.is_used)return a.order_index-b.order_index;if(a.is_used&&b.is_used){let c=a.last_used_at?new Date(a.last_used_at).getTime():0;return(b.last_used_at?new Date(b.last_used_at).getTime():0)-c}return 0}),e=d.findIndex(a=>a.id===b.id),f=d.findIndex(a=>a.id===c.id),g=cV(d,e,f),h=[];for(let a=0;a<g.length;a++){let b=g[a],c=a+1,d=`task-${c}`;(b.order_index!==c||b.task_code!==d)&&h.push({id:b.id,order_index:c,task_code:d})}if(h.length>0)try{await au.mutateAsync(h)}catch(a){console.error("Sıralama g\xfcncelleme hatası:",a)}},[U,au]);return N?(0,n.jsxs)("div",{className:"flex h-full",children:[(0,n.jsxs)("div",{className:"flex flex-col flex-1",children:[(0,n.jsxs)("div",{className:"p-3 sm:p-4 lg:p-6 border-b border-gray-200 bg-white",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-3 sm:mb-4",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 sm:gap-3",children:[(0,n.jsxs)("div",{className:"flex md:hidden gap-1 sm:gap-2",children:[(0,n.jsx)(q.$,{variant:"outline",size:"sm",className:"h-8 w-8 sm:h-10 sm:w-10 p-0 touch-manipulation",children:(0,n.jsx)(ac.A,{className:"h-4 w-4 sm:h-5 sm:w-5"})}),(0,n.jsx)(q.$,{variant:"outline",size:"sm",className:"h-8 w-8 sm:h-10 sm:w-10 p-0 touch-manipulation",children:(0,n.jsx)(ad.A,{className:"h-4 w-4 sm:h-5 sm:w-5"})})]}),(0,n.jsx)("h2",{className:"text-base sm:text-lg lg:text-xl font-semibold text-gray-900 truncate",children:X?.name||"Prompt Listesi"})]}),(0,n.jsxs)("div",{className:"flex items-center gap-1 sm:gap-2 overflow-x-auto",children:[(E.length>0||G)&&(0,n.jsxs)("div",{className:"flex items-center gap-1 flex-shrink-0",children:[E.slice(0,2).map(a=>(0,n.jsxs)(v.E,{variant:"outline",className:"text-xs whitespace-nowrap",children:[(0,n.jsx)(aa,{className:"w-3 h-3 mr-1"}),(0,n.jsx)("span",{className:"hidden sm:inline",children:a.replace("#","")}),(0,n.jsx)("span",{className:"sm:hidden",children:a.replace("#","").slice(0,3)}),(0,n.jsx)(q.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-0 w-3 h-3 ml-1 hover:bg-transparent touch-manipulation",onClick:b=>{b.preventDefault(),b.stopPropagation(),aC(a)},children:(0,n.jsx)(Y.A,{className:"w-2 h-2"})})]},a)),E.length>2&&(0,n.jsxs)(v.E,{variant:"outline",className:"text-xs",children:["+",E.length-2]}),G&&(0,n.jsxs)(v.E,{variant:"outline",className:"text-xs whitespace-nowrap",children:[(0,n.jsx)(_.A,{className:"w-3 h-3 mr-1"}),(0,n.jsx)("span",{className:"hidden sm:inline",children:G}),(0,n.jsx)("span",{className:"sm:hidden",children:G.slice(0,3)}),(0,n.jsx)(q.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-0 w-3 h-3 ml-1 hover:bg-transparent",onClick:()=>H(null),children:(0,n.jsx)(Y.A,{className:"w-2 h-2"})})]})]}),(0,n.jsxs)(q.$,{type:"button",variant:"outline",size:"sm",onClick:()=>D(!C),className:"hidden lg:flex",children:[(0,n.jsx)(aa,{className:"w-4 h-4 mr-1"}),"AI Tag ",C?"Sakla":"G\xf6ster"]}),(0,n.jsxs)(v.E,{variant:"secondary",className:"text-xs lg:text-sm",children:[m?az.filter(a=>!a.is_used).length:U.filter(a=>!a.is_used).length," / ",m?az.length:U.length]})]})]}),(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)(ae.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,n.jsx)("input",{type:"text",placeholder:"Prompt'larda ara...",value:m,onChange:a=>p(a.target.value),className:"w-full pl-10 pr-10 py-2.5 sm:py-2 border border-gray-300 rounded-lg text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50 hover:bg-white transition-colors touch-manipulation"}),m&&(0,n.jsx)("button",{onClick:()=>p(""),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 touch-manipulation p-1",children:(0,n.jsx)(Y.A,{className:"h-4 w-4"})})]})]}),(0,n.jsx)(u.F,{className:"flex-1 p-2 sm:p-3 lg:p-6 pb-32 sm:pb-28 lg:pb-24",children:(0,n.jsxs)("div",{className:"space-y-2 sm:space-y-3",children:[0===U.length?(0,n.jsxs)("div",{className:"text-center py-8 sm:py-12",children:[(0,n.jsx)(af.A,{className:"h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-3 sm:mb-4"}),(0,n.jsx)("h3",{className:"text-base sm:text-lg font-medium text-gray-900 mb-2",children:"Hen\xfcz Prompt Yok"}),(0,n.jsx)("p",{className:"text-sm sm:text-base text-gray-500 px-4",children:"Aşağıdaki alandan ilk promptunuzu ekleyin"})]}):0===az.length?(0,n.jsxs)("div",{className:"text-center py-8 sm:py-12",children:[(0,n.jsx)(ae.A,{className:"h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-3 sm:mb-4"}),(0,n.jsx)("h3",{className:"text-base sm:text-lg font-medium text-gray-900 mb-2",children:"Arama Sonucu Bulunamadı"}),(0,n.jsxs)("p",{className:"text-sm sm:text-base text-gray-500 px-4",children:['"',m,'" i\xe7in eşleşen prompt bulunamadı']}),(0,n.jsx)(q.$,{variant:"outline",className:"mt-3 sm:mt-4 touch-manipulation",onClick:()=>p(""),children:"Aramayı Temizle"})]}):(0,n.jsx)(cR,{sensors:T,collisionDetection:bY,onDragEnd:aO,children:(0,n.jsx)(c0,{items:az.map(a=>a.id),strategy:cZ,children:(()=>{let a=az.sort((a,b)=>{if(a.is_used!==b.is_used)return a.is_used?1:-1;if(!a.is_used&&!b.is_used)return a.order_index-b.order_index;if(a.is_used&&b.is_used){let c=a.last_used_at?new Date(a.last_used_at).getTime():0;return(b.last_used_at?new Date(b.last_used_at).getTime():0)-c}return 0}),b=m.trim().length>0,c=a;if(!b){let b=(K-1)*20;c=a.slice(b,b+20)}let d=c.filter(a=>!a.is_used),f=c.filter(a=>a.is_used),j=d.length>0,l=f.length>0;return a.length,(0,n.jsxs)(n.Fragment,{children:[d.map(a=>(0,n.jsx)(de,{prompt:a,isMultiSelectMode:k,selectedPrompts:i,editingPromptId:e,editingText:g,editTextareaRef:M,onSelectPrompt:aL,onEditPrompt:aG,onSaveEdit:aH,onCancelEdit:aI,onCopyPrompt:aE,setEditingText:h,onHashtagFilter:aC},a.id)),j&&l&&(0,n.jsxs)("div",{className:"relative my-8",children:[(0,n.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,n.jsx)("div",{className:"w-full border-t-2 border-green-300 shadow-md"})}),(0,n.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,n.jsxs)("span",{className:"bg-gradient-to-r from-green-50 to-green-100 px-6 py-2 text-green-700 font-semibold rounded-full border border-green-200 shadow-sm",children:[(0,n.jsx)(V.A,{className:"h-4 w-4 inline mr-2"}),"Kullanılan Promptlar"]})})]}),f.map(a=>(0,n.jsx)(de,{prompt:a,isMultiSelectMode:k,selectedPrompts:i,editingPromptId:e,editingText:g,editTextareaRef:M,onSelectPrompt:aL,onEditPrompt:aG,onSaveEdit:aH,onCancelEdit:aI,onCopyPrompt:aE,setEditingText:h,onHashtagFilter:aC},a.id))]})})()})}),(()=>{let a=az.sort((a,b)=>{if(a.is_used!==b.is_used)return a.is_used?1:-1;if(!a.is_used&&!b.is_used)return a.order_index-b.order_index;if(a.is_used&&b.is_used){let c=a.last_used_at?new Date(a.last_used_at).getTime():0;return(b.last_used_at?new Date(b.last_used_at).getTime():0)-c}return 0}),b=m.trim().length>0,c=a.length,d=Math.ceil(c/20);return!b&&d>1?(0,n.jsxs)("div",{className:"flex items-center justify-between px-4 py-3 border-t border-gray-200 bg-gray-50",children:[(0,n.jsxs)("div",{className:"text-sm text-gray-600",children:["Toplam ",c," prompt, sayfa ",K," / ",d]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(q.$,{variant:"outline",size:"sm",onClick:()=>L(Math.max(1,K-1)),disabled:1===K,children:"\xd6nceki"}),(0,n.jsx)("div",{className:"flex items-center gap-1",children:Array.from({length:Math.min(5,d)},(a,b)=>{let c;return c=d<=5||K<=3?b+1:K>=d-2?d-4+b:K-2+b,(0,n.jsx)(q.$,{variant:K===c?"default":"outline",size:"sm",onClick:()=>L(c),className:"w-8 h-8 p-0",children:c},c)})}),(0,n.jsx)(q.$,{variant:"outline",size:"sm",onClick:()=>L(Math.min(d,K+1)),disabled:K===d,children:"Sonraki"})]})]}):null})()]})}),(0,n.jsx)(o.Suspense,{fallback:a?(0,n.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,n.jsx)("div",{className:"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden",children:(0,n.jsx)(bb,{})})}):null,children:(0,n.jsx)(db,{open:a,onOpenChange:b||(()=>{}),onSelectContext:a=>{d(a.content),b?.()}})}),(0,n.jsx)("div",{className:"fixed bottom-0 left-0 right-0 bg-gradient-to-t from-white via-white to-white/95 border-t border-gray-200 shadow-xl safe-area-bottom z-[60] backdrop-blur-sm",children:(0,n.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,n.jsxs)("div",{className:"block lg:hidden",children:[(0,n.jsxs)("div",{className:"p-3 border-b border-gray-100 bg-gray-50/50",children:[(0,n.jsx)("div",{className:"flex justify-center mb-2",children:(0,n.jsxs)(q.$,{variant:k?"default":"outline",size:"sm",onClick:aJ,className:`min-h-[44px] px-3 transition-all duration-200 ${k?"text-white bg-blue-600 hover:bg-blue-700 shadow-md":"text-blue-600 hover:text-blue-700 border-blue-200 hover:border-blue-300 hover:bg-blue-50"}`,children:[(0,n.jsx)(ag,{className:"h-4 w-4 mr-1"}),(0,n.jsx)("span",{className:"text-xs font-medium",children:k?"Se\xe7im İptal":"\xc7oklu Se\xe7"})]})}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[k&&i.size>0&&(0,n.jsxs)(q.$,{variant:"default",size:"sm",onClick:aM,className:"text-white bg-green-600 hover:bg-green-700 min-h-[40px] px-2 shadow-md transition-all duration-200",children:[(0,n.jsx)($.A,{className:"h-4 w-4 mr-1"}),(0,n.jsxs)("span",{className:"text-xs font-medium",children:["Kopyala (",i.size,")"]})]}),(0,n.jsxs)(q.$,{variant:"outline",size:"sm",onClick:aN,className:"text-purple-600 hover:text-purple-700 border-purple-200 hover:border-purple-300 hover:bg-purple-50 min-h-[40px] px-2 transition-all duration-200",children:[(0,n.jsx)(ah,{className:"h-4 w-4 mr-1"}),(0,n.jsx)("span",{className:"text-xs font-medium",children:"Export"})]}),(0,n.jsxs)(q.$,{variant:"outline",size:"sm",onClick:aF,className:"text-orange-600 hover:text-orange-700 border-orange-200 hover:border-orange-300 hover:bg-orange-50 min-h-[40px] px-2 transition-all duration-200",children:[(0,n.jsx)(ai,{className:"h-4 w-4 mr-1"}),(0,n.jsx)("span",{className:"text-xs font-medium",children:"Geri Al"})]})]})]}),(0,n.jsxs)("div",{className:"p-2 sm:p-3 relative z-[60]",children:[(0,n.jsxs)("div",{className:"block sm:hidden space-y-2",children:[(0,n.jsx)("div",{className:"relative",children:(0,n.jsx)(ba,{value:c,onChange:d,onKeyDown:a=>{"Enter"!==a.key||a.shiftKey||(a.preventDefault(),aA())},placeholder:"Prompt yazın... (/ klas\xf6r, # etiket)",className:"resize-none text-base border-2 focus:border-blue-500 transition-colors duration-200 pr-16 w-full rounded-lg p-3 touch-manipulation",suggestions:{hashtags:Z||[],folders:ao||[]},enableDynamicHeight:!0,heightConfig:{minHeight:48,maxHeightFraction:.25,baseHeight:48}})}),(0,n.jsxs)("div",{className:"flex gap-1",children:[(0,n.jsxs)(q.$,{variant:"outline",size:"sm",onClick:aJ,className:`flex-1 min-h-[44px] transition-all duration-200 touch-manipulation ${k?"text-white bg-blue-600 hover:bg-blue-700":"text-blue-600 hover:text-blue-700 border-blue-200"}`,children:[(0,n.jsx)(ag,{className:"h-4 w-4 mr-1"}),(0,n.jsx)("span",{className:"text-xs",children:"Se\xe7"})]}),(0,n.jsxs)(q.$,{variant:"outline",size:"sm",onClick:aN,className:"flex-1 text-purple-600 hover:text-purple-700 border-purple-200 hover:border-purple-300 hover:bg-purple-50 min-h-[44px] touch-manipulation",children:[(0,n.jsx)(ah,{className:"h-4 w-4 mr-1"}),(0,n.jsx)("span",{className:"text-xs",children:"Export"})]}),(0,n.jsxs)(q.$,{variant:"outline",size:"sm",onClick:aF,className:"flex-1 text-orange-600 hover:text-orange-700 border-orange-200 hover:border-orange-300 hover:bg-orange-50 min-h-[44px] touch-manipulation",children:[(0,n.jsx)(ai,{className:"h-4 w-4 mr-1"}),(0,n.jsx)("span",{className:"text-xs",children:"Geri"})]}),(0,n.jsxs)(q.$,{onClick:aA,disabled:!c.trim(),size:"default",className:"flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white min-h-[44px] shadow-lg hover:shadow-xl transition-all duration-200 font-medium touch-manipulation",children:[(0,n.jsx)(af.A,{className:"h-4 w-4 mr-1"}),(0,n.jsx)("span",{className:"text-xs",children:"Ekle"})]})]})]}),(0,n.jsxs)("div",{className:"hidden sm:flex gap-2 items-end relative z-[60]",children:[(0,n.jsx)("div",{className:"flex-1 relative",children:(0,n.jsx)(ba,{value:c,onChange:d,onKeyDown:a=>{"Enter"!==a.key||a.shiftKey||(a.preventDefault(),aA())},placeholder:"Prompt metninizi buraya yazın... (/ i\xe7in klas\xf6r, # i\xe7in etiket)",className:"resize-none text-base border-2 focus:border-blue-500 transition-colors duration-200 pr-16 w-full rounded-lg p-3",suggestions:{hashtags:Z||[],folders:ao||[]},enableDynamicHeight:!0,heightConfig:{minHeight:44,maxHeightFraction:.33,baseHeight:44}})}),(0,n.jsxs)("div",{className:"flex gap-1",children:[(0,n.jsx)(q.$,{variant:"outline",size:"sm",onClick:aJ,className:`min-h-[44px] px-2 transition-all duration-200 ${k?"text-white bg-blue-600 hover:bg-blue-700":"text-blue-600 hover:text-blue-700 border-blue-200"}`,children:(0,n.jsx)(ag,{className:"h-4 w-4"})}),(0,n.jsx)(q.$,{variant:"outline",size:"sm",onClick:aN,className:"text-purple-600 hover:text-purple-700 border-purple-200 hover:border-purple-300 hover:bg-purple-50 min-h-[44px] px-2",children:(0,n.jsx)(ah,{className:"h-4 w-4"})}),(0,n.jsx)(q.$,{variant:"outline",size:"sm",onClick:aF,className:"text-orange-600 hover:text-orange-700 border-orange-200 hover:border-orange-300 hover:bg-orange-50 min-h-[44px] px-2",children:(0,n.jsx)(ai,{className:"h-4 w-4"})}),(0,n.jsxs)(q.$,{onClick:aA,disabled:!c.trim(),size:"default",className:"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white px-4 min-h-[44px] shrink-0 shadow-lg hover:shadow-xl transition-all duration-200 font-medium",children:[(0,n.jsx)(af.A,{className:"h-4 w-4 mr-1"}),"Ekle"]})]})]})]})]}),(0,n.jsx)("div",{className:"hidden lg:block p-4 bg-gradient-to-r from-gray-50 to-white border-t border-gray-100 relative z-[60]",children:(0,n.jsxs)("div",{className:"bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 p-4",children:[(0,n.jsxs)("div",{className:"flex gap-4 items-end",children:[(0,n.jsxs)("div",{className:"flex-1 relative",children:[(0,n.jsx)(ba,{value:c,onChange:d,onKeyDown:a=>{"Enter"!==a.key||a.shiftKey||(a.preventDefault(),aA())},placeholder:"Prompt metninizi buraya yazın... (/ i\xe7in klas\xf6r, # i\xe7in etiket)",className:"resize-none border-0 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg bg-gray-50 hover:bg-white transition-all duration-200 text-base pr-20 w-full p-3",suggestions:{hashtags:Z||[],folders:ao||[]},enableDynamicHeight:!0,heightConfig:{minHeight:56,maxHeightFraction:.33,baseHeight:56}}),A&&(0,n.jsxs)("div",{className:"space-y-4 pt-4 border-t border-gray-200 bg-gray-50 rounded-lg p-4 mt-3",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,n.jsx)("div",{className:"w-1 h-4 bg-purple-500 rounded-full"}),(0,n.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Kategorilendirme"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Başlık (İsteğe bağlı)"}),(0,n.jsx)(t.p,{type:"text",placeholder:"Bu prompt i\xe7in kısa bir başlık...",value:y,onChange:a=>z(a.target.value),className:"border-gray-300 focus:border-purple-500 focus:ring-purple-500"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Etiketler"}),N&&(0,n.jsx)(ax,{hashtags:r,onHashtagsChange:s,suggestions:Z||[],placeholder:"Etiket ekleyin... (\xf6rn: #frontend, #api)",maxTags:5})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Klas\xf6r/Kategori"}),N&&(0,n.jsx)(aB,{category:w,onCategoryChange:x,suggestions:ao||[],placeholder:"Klas\xf6r se\xe7in... (\xf6rn: /frontend, /admin)"})]})]})]}),(0,n.jsxs)("div",{className:"flex items-center justify-center gap-3 py-3 border-t border-gray-200 mt-4 pt-4",children:[(0,n.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Console Log Fix"}),(0,n.jsx)(O,{checked:I,onCheckedChange:J,className:"scale-90"})]}),S&&(0,n.jsx)(ar.LD,{type:"prompt",current:S.current_prompts,max:S.max_prompts_per_project,onUpgrade:()=>{}}),(0,n.jsxs)(q.$,{onClick:aA,disabled:!c.trim()||S&&!S.can_create_prompt,size:"lg",className:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:from-gray-300 disabled:to-gray-400 text-white px-8 py-3 min-h-[56px] shadow-lg hover:shadow-xl transition-all duration-200 font-semibold rounded-lg",children:[(0,n.jsx)(af.A,{className:"h-5 w-5 mr-2"}),"Prompt Ekle"]})]}),(0,n.jsx)("div",{className:"space-y-3",children:(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsxs)(q.$,{variant:k?"default":"outline",size:"sm",onClick:aJ,className:`transition-all duration-200 rounded-lg font-medium ${k?"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-md":"border-blue-200 text-blue-600 hover:text-blue-700 hover:border-blue-300 hover:bg-blue-50"}`,children:[(0,n.jsx)(ag,{className:"h-4 w-4 mr-2"}),"\xc7oklu Se\xe7"]}),k&&i.size>0&&(0,n.jsxs)(q.$,{variant:"default",size:"sm",onClick:aM,className:"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white shadow-md transition-all duration-200 rounded-lg font-medium",children:[(0,n.jsx)($.A,{className:"h-4 w-4 mr-2"}),"Kopyala (",i.size,")"]}),(0,n.jsxs)(q.$,{variant:"outline",size:"sm",onClick:aN,className:"border-purple-200 text-purple-600 hover:text-purple-700 hover:border-purple-300 hover:bg-purple-50 transition-all duration-200 rounded-lg font-medium",children:[(0,n.jsx)(ah,{className:"h-4 w-4 mr-2"}),"Export"]}),(0,n.jsxs)(q.$,{variant:"outline",size:"sm",onClick:aF,className:"border-orange-200 text-orange-600 hover:text-orange-700 hover:border-orange-300 hover:bg-orange-50 transition-all duration-200 rounded-lg font-medium",children:[(0,n.jsx)(ai,{className:"h-4 w-4 mr-2"}),"Geri Al"]}),!c.trim()&&(0,n.jsx)(q.$,{type:"button",variant:"outline",size:"sm",onClick:()=>B(!A),className:"border-gray-300 text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition-all duration-200",children:A?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(P.A,{className:"w-4 h-4 mr-2"}),"Kategorileri Gizle"]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(Q.A,{className:"w-4 h-4 mr-2"}),"Kategori Ekle"]})})]})})]})})]})})]}),C&&N&&(0,n.jsx)(aK,{projectId:N,onHashtagClick:aC,onCategoryClick:aD,selectedHashtags:E,selectedCategory:G,className:"hidden lg:flex"})]}):(0,n.jsxs)("div",{className:"flex flex-col h-full",children:[(0,n.jsx)("div",{className:"h-16 sm:h-20 lg:h-24"}),(0,n.jsx)("div",{className:"flex-1 flex items-center justify-center px-4",children:(0,n.jsx)("div",{className:"max-w-2xl w-full",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12",children:[(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)(W.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-6"}),(0,n.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Proje Se\xe7in"}),(0,n.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Başlamak i\xe7in sol panelden bir proje se\xe7in veya yeni bir proje oluşturun"})]}),(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)(ab.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-6"}),(0,n.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Context Alanı"}),(0,n.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Proje se\xe7tikten sonra sağ panelden context metninizi yazabilirsiniz"})]})]})})}),(0,n.jsx)("div",{className:"h-32 sm:h-40 lg:h-48"})]})}let dg=df},12597:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},70615:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},77781:(a,b,c)=>{c.d(b,{X1:()=>i,Xm:()=>j,v8:()=>k});var d=c(51423),e=c(8693),f=c(54050),g=c(8266),h=c(52581);function i(){return(0,d.I)({queryKey:["shared-prompts","user"],queryFn:async()=>{let{data:a}=await g.L.auth.getUser();if(!a.user)throw Error("Not authenticated");let{data:b,error:c}=await g.L.from("shared_prompts").select(`
          *,
          prompt:prompts(
            id,
            prompt_text,
            title,
            description,
            category,
            tags,
            task_code,
            created_at,
            project:projects(name)
          )
        `).eq("user_id",a.user.id).eq("is_active",!0).order("created_at",{ascending:!1});if(c)throw c;return b.map(a=>({...a,project_name:a.prompt?.project?.name||"Unknown Project"}))},staleTime:3e5})}function j(){let a=(0,e.jE)();return(0,f.n)({mutationFn:async a=>{let{data:b}=await g.L.auth.getUser();if(!b.user)throw Error("Not authenticated");let{data:c,error:d}=await g.L.from("prompts").select("id, user_id").eq("id",a.prompt_id).eq("user_id",b.user.id).single();if(d||!c)throw Error("Prompt not found or access denied");let e=function(){let a=new Uint8Array(16);return crypto.getRandomValues(a),Array.from(a,a=>a.toString(16).padStart(2,"0")).join("")}(),f=null;a.password&&(f=await l(a.password));let{data:h,error:i}=await g.L.from("shared_prompts").insert({prompt_id:a.prompt_id,user_id:b.user.id,share_token:e,title:a.title||null,description:a.description||null,is_public:a.is_public??!0,password_hash:f,expires_at:a.expires_at||null}).select().single();if(i)throw i;let j=`${window.location.origin}/share/${e}`;return{id:h.id,share_token:e,share_url:j,created_at:h.created_at}},onSuccess:b=>{a.invalidateQueries({queryKey:["shared-prompts"]}),h.oR.success("Paylaşım linki oluşturuldu!",{description:"Link panoya kopyalandı"}),navigator.clipboard.writeText(b.share_url).catch(console.error)},onError:a=>{console.error("Share creation error:",a),h.oR.error("Paylaşım oluşturulamadı",{description:a instanceof Error?a.message:"Bilinmeyen hata"})}})}function k(a,b){return(0,d.I)({queryKey:["shared-prompt",a,b],queryFn:async()=>{let{data:c,error:d}=await g.L.from("shared_prompts").select(`
          *,
          prompt:prompts(
            id,
            prompt_text,
            title,
            description,
            category,
            tags,
            task_code,
            created_at,
            project:projects(name)
          )
        `).eq("share_token",a).eq("is_active",!0).single();if(d)throw Error("Paylaşım bulunamadı veya s\xfcresi dolmuş");if(c.password_hash&&b){if(!await m(b,c.password_hash))throw Error("Şifre yanlış")}else if(c.password_hash&&!b)throw Error("Şifre gerekli");if(c.expires_at&&new Date(c.expires_at)<new Date)throw Error("Paylaşımın s\xfcresi dolmuş");return g.L.from("shared_prompts").update({view_count:c.view_count+1}).eq("id",c.id).then(()=>{fetch("/api/shared-prompts/record-view",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({share_token:a,viewer_ip:null,viewer_user_agent:navigator.userAgent,referrer:document.referrer||null,session_id:`session_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,viewed_at:new Date().toISOString()})}).catch(console.error)}),{...c,project_name:c.prompt?.project?.name||"Unknown Project",author_email:"Unknown Author"}},enabled:!!a,staleTime:0,retry:!1})}async function l(a){let b=new TextEncoder().encode(a);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",b)),a=>a.toString(16).padStart(2,"0")).join("")}async function m(a,b){return await l(a)===b}},81620:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])}};
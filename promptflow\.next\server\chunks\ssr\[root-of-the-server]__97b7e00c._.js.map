{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_8fcc3bcb.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_8fcc3bcb-module__7PpM1G__className\",\n  \"variable\": \"inter_8fcc3bcb-module__7PpM1G__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_8fcc3bcb.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-inter%22,%22display%22:%22swap%22,%22preload%22:true}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/providers/query-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const QueryProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/query-provider.tsx <module evaluation>\",\n    \"QueryProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,kEACA", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/providers/query-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const QueryProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/query-provider.tsx\",\n    \"QueryProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,8CACA", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/env-security.ts"], "sourcesContent": ["/**\n * Environment Variables Security Checker\n * Ensures sensitive data is not exposed to client-side\n */\n\n// Sensitive environment variables that should NEVER be exposed to client\nconst SENSITIVE_ENV_VARS = [\n  'SUPABASE_SERVICE_ROLE_KEY',\n  'SUPABASE_JWT_SECRET',\n  'DATABASE_URL',\n  'NEXTAUTH_SECRET',\n  'OPENAI_API_KEY',\n  'STRIPE_SECRET_KEY',\n  'WEBHOOK_SECRET',\n  'PRIVATE_KEY',\n  'SECRET_KEY',\n  'API_SECRET',\n  'ADMIN_PASSWORD',\n  'DB_PASSWORD',\n  'ENCRYPTION_KEY'\n] as const\n\n// Client-safe environment variables (must start with NEXT_PUBLIC_)\nconst ALLOWED_CLIENT_ENV_VARS = [\n  'NEXT_PUBLIC_SUPABASE_URL',\n  'NEXT_PUBLIC_SUPABASE_ANON_KEY',\n  'NEXT_PUBLIC_APP_URL',\n  'NEXT_PUBLIC_ENVIRONMENT',\n  'NEXT_PUBLIC_ANALYTICS_ID',\n  'NEXT_PUBLIC_SENTRY_DSN'\n] as const\n\ninterface SecurityCheckResult {\n  isSecure: boolean\n  violations: string[]\n  warnings: string[]\n  recommendations: string[]\n}\n\n/**\n * Check if environment variables are properly secured\n */\nexport function checkEnvironmentSecurity(): SecurityCheckResult {\n  const violations: string[] = []\n  const warnings: string[] = []\n  const recommendations: string[] = []\n\n  // Check if we're in client-side environment\n  const isClientSide = typeof window !== 'undefined'\n\n  if (isClientSide) {\n    // Client-side checks\n    console.log('🔍 [ENV_SECURITY] Running client-side environment security check...')\n\n    // Check for exposed sensitive variables\n    SENSITIVE_ENV_VARS.forEach(varName => {\n      if (process.env[varName]) {\n        violations.push(`CRITICAL: ${varName} is exposed to client-side code`)\n      }\n    })\n\n    // Check for non-NEXT_PUBLIC_ variables in client\n    Object.keys(process.env).forEach(key => {\n      if (!key.startsWith('NEXT_PUBLIC_') && \n          !key.startsWith('NODE_') && \n          !key.startsWith('npm_') &&\n          key !== '__NEXT_PRIVATE_PREBUNDLED_REACT') {\n        warnings.push(`Variable ${key} is available on client but doesn't follow NEXT_PUBLIC_ convention`)\n      }\n    })\n\n    // Check for required client variables\n    const requiredClientVars = ['NEXT_PUBLIC_SUPABASE_URL', 'NEXT_PUBLIC_SUPABASE_ANON_KEY']\n    requiredClientVars.forEach(varName => {\n      if (!process.env[varName]) {\n        violations.push(`Required client variable ${varName} is missing`)\n      }\n    })\n\n  } else {\n    // Server-side checks\n    console.log('🔍 [ENV_SECURITY] Running server-side environment security check...')\n\n    // Check for missing sensitive variables that should be present\n    const requiredServerVars = ['NEXT_PUBLIC_SUPABASE_URL', 'NEXT_PUBLIC_SUPABASE_ANON_KEY']\n    requiredServerVars.forEach(varName => {\n      if (!process.env[varName]) {\n        violations.push(`Required server variable ${varName} is missing`)\n      }\n    })\n\n    // Check for weak or default values\n    if (process.env.NEXT_PUBLIC_SUPABASE_URL?.includes('localhost') && \n        process.env.NODE_ENV === 'production') {\n      warnings.push('Using localhost Supabase URL in production environment')\n    }\n\n    // Check for development keys in production\n    if (process.env.NODE_ENV === 'production') {\n      if (process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.includes('test') ||\n          process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.includes('dev')) {\n        warnings.push('Potentially using development keys in production')\n      }\n    }\n  }\n\n  // General recommendations\n  if (process.env.NODE_ENV !== 'production') {\n    recommendations.push('Use .env.local for local development secrets')\n    recommendations.push('Never commit .env files to version control')\n    recommendations.push('Use different keys for development and production')\n  }\n\n  recommendations.push('Regularly rotate API keys and secrets')\n  recommendations.push('Use environment-specific configurations')\n  recommendations.push('Monitor for exposed secrets in client bundles')\n\n  const isSecure = violations.length === 0\n\n  return {\n    isSecure,\n    violations,\n    warnings,\n    recommendations\n  }\n}\n\n/**\n * Validate specific environment variable\n */\nexport function validateEnvVar(\n  name: string,\n  value: string | undefined,\n  options: {\n    required?: boolean\n    clientSafe?: boolean\n    pattern?: RegExp\n    minLength?: number\n    maxLength?: number\n  } = {}\n): {\n  isValid: boolean\n  errors: string[]\n  warnings: string[]\n} {\n  const errors: string[] = []\n  const warnings: string[] = []\n\n  // Check if required\n  if (options.required && !value) {\n    errors.push(`Environment variable ${name} is required but not set`)\n    return { isValid: false, errors, warnings }\n  }\n\n  if (!value) {\n    return { isValid: true, errors, warnings }\n  }\n\n  // Check client safety\n  if (options.clientSafe && !name.startsWith('NEXT_PUBLIC_')) {\n    errors.push(`Client-safe variable ${name} must start with NEXT_PUBLIC_`)\n  }\n\n  if (!options.clientSafe && name.startsWith('NEXT_PUBLIC_')) {\n    warnings.push(`Variable ${name} starts with NEXT_PUBLIC_ but is marked as not client-safe`)\n  }\n\n  // Check pattern\n  if (options.pattern && !options.pattern.test(value)) {\n    errors.push(`Environment variable ${name} does not match required pattern`)\n  }\n\n  // Check length\n  if (options.minLength && value.length < options.minLength) {\n    errors.push(`Environment variable ${name} is too short (minimum ${options.minLength} characters)`)\n  }\n\n  if (options.maxLength && value.length > options.maxLength) {\n    errors.push(`Environment variable ${name} is too long (maximum ${options.maxLength} characters)`)\n  }\n\n  // Check for common weak values\n  const weakValues = ['test', 'dev', 'development', 'localhost', 'example', 'changeme', '123456']\n  if (weakValues.some(weak => value.toLowerCase().includes(weak)) && \n      process.env.NODE_ENV === 'production') {\n    warnings.push(`Environment variable ${name} appears to contain weak or development values`)\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors,\n    warnings\n  }\n}\n\n/**\n * Get safe environment variables for client-side use\n */\nexport function getSafeClientEnv(): Record<string, string> {\n  const safeEnv: Record<string, string> = {}\n\n  ALLOWED_CLIENT_ENV_VARS.forEach(varName => {\n    if (process.env[varName]) {\n      safeEnv[varName] = process.env[varName]!\n    }\n  })\n\n  return safeEnv\n}\n\n/**\n * Sanitize environment variable for logging\n */\nexport function sanitizeEnvForLogging(name: string, value: string): string {\n  // Never log sensitive values\n  if (SENSITIVE_ENV_VARS.includes(name as typeof SENSITIVE_ENV_VARS[number])) {\n    return '[REDACTED]'\n  }\n\n  // For API keys and tokens, show only first and last few characters\n  if (name.toLowerCase().includes('key') || \n      name.toLowerCase().includes('token') || \n      name.toLowerCase().includes('secret')) {\n    if (value.length > 8) {\n      return `${value.slice(0, 4)}...${value.slice(-4)}`\n    }\n    return '[REDACTED]'\n  }\n\n  // For URLs, hide sensitive parts\n  if (name.toLowerCase().includes('url') && value.includes('://')) {\n    try {\n      const url = new URL(value)\n      return `${url.protocol}//${url.hostname}${url.pathname ? url.pathname : ''}`\n    } catch {\n      return value\n    }\n  }\n\n  return value\n}\n\n/**\n * Runtime security check - call this in app initialization\n */\nexport function performRuntimeSecurityCheck(): void {\n  const result = checkEnvironmentSecurity()\n\n  if (!result.isSecure) {\n    console.error('🚨 [ENV_SECURITY] CRITICAL SECURITY VIOLATIONS DETECTED:')\n    result.violations.forEach(violation => {\n      console.error(`  ❌ ${violation}`)\n    })\n    \n    if (process.env.NODE_ENV === 'production') {\n      throw new Error('Critical environment security violations detected in production')\n    }\n  }\n\n  if (result.warnings.length > 0) {\n    console.warn('⚠️ [ENV_SECURITY] Security warnings:')\n    result.warnings.forEach(warning => {\n      console.warn(`  ⚠️ ${warning}`)\n    })\n  }\n\n  if (result.recommendations.length > 0 && process.env.NODE_ENV === 'development') {\n    console.info('💡 [ENV_SECURITY] Security recommendations:')\n    result.recommendations.forEach(rec => {\n      console.info(`  💡 ${rec}`)\n    })\n  }\n\n  console.log('✅ [ENV_SECURITY] Environment security check completed')\n}\n\n// Auto-run security check in development\nif (process.env.NODE_ENV === 'development' && typeof window === 'undefined') {\n  performRuntimeSecurityCheck()\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,yEAAyE;;;;;;;;AACzE,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,mEAAmE;AACnE,MAAM,0BAA0B;IAC9B;IACA;IACA;IACA;IACA;IACA;CACD;AAYM,SAAS;IACd,MAAM,aAAuB,EAAE;IAC/B,MAAM,WAAqB,EAAE;IAC7B,MAAM,kBAA4B,EAAE;IAEpC,4CAA4C;IAC5C,MAAM,eAAe,gBAAkB;IAEvC;;SA6BO;QACL,qBAAqB;QACrB,QAAQ,GAAG,CAAC;QAEZ,+DAA+D;QAC/D,MAAM,qBAAqB;YAAC;YAA4B;SAAgC;QACxF,mBAAmB,OAAO,CAAC,CAAA;YACzB,IAAI,CAAC,QAAQ,GAAG,CAAC,QAAQ,EAAE;gBACzB,WAAW,IAAI,CAAC,CAAC,yBAAyB,EAAE,QAAQ,WAAW,CAAC;YAClE;QACF;QAEA,mCAAmC;QACnC,IAAI,8EAAsC,SAAS,gBAC/C,oDAAyB;;QAI7B,2CAA2C;QAC3C;;IAMF;IAEA,0BAA0B;IAC1B,wCAA2C;QACzC,gBAAgB,IAAI,CAAC;QACrB,gBAAgB,IAAI,CAAC;QACrB,gBAAgB,IAAI,CAAC;IACvB;IAEA,gBAAgB,IAAI,CAAC;IACrB,gBAAgB,IAAI,CAAC;IACrB,gBAAgB,IAAI,CAAC;IAErB,MAAM,WAAW,WAAW,MAAM,KAAK;IAEvC,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AAKO,SAAS,eACd,IAAY,EACZ,KAAyB,EACzB,UAMI,CAAC,CAAC;IAMN,MAAM,SAAmB,EAAE;IAC3B,MAAM,WAAqB,EAAE;IAE7B,oBAAoB;IACpB,IAAI,QAAQ,QAAQ,IAAI,CAAC,OAAO;QAC9B,OAAO,IAAI,CAAC,CAAC,qBAAqB,EAAE,KAAK,wBAAwB,CAAC;QAClE,OAAO;YAAE,SAAS;YAAO;YAAQ;QAAS;IAC5C;IAEA,IAAI,CAAC,OAAO;QACV,OAAO;YAAE,SAAS;YAAM;YAAQ;QAAS;IAC3C;IAEA,sBAAsB;IACtB,IAAI,QAAQ,UAAU,IAAI,CAAC,KAAK,UAAU,CAAC,iBAAiB;QAC1D,OAAO,IAAI,CAAC,CAAC,qBAAqB,EAAE,KAAK,6BAA6B,CAAC;IACzE;IAEA,IAAI,CAAC,QAAQ,UAAU,IAAI,KAAK,UAAU,CAAC,iBAAiB;QAC1D,SAAS,IAAI,CAAC,CAAC,SAAS,EAAE,KAAK,0DAA0D,CAAC;IAC5F;IAEA,gBAAgB;IAChB,IAAI,QAAQ,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,IAAI,CAAC,QAAQ;QACnD,OAAO,IAAI,CAAC,CAAC,qBAAqB,EAAE,KAAK,gCAAgC,CAAC;IAC5E;IAEA,eAAe;IACf,IAAI,QAAQ,SAAS,IAAI,MAAM,MAAM,GAAG,QAAQ,SAAS,EAAE;QACzD,OAAO,IAAI,CAAC,CAAC,qBAAqB,EAAE,KAAK,uBAAuB,EAAE,QAAQ,SAAS,CAAC,YAAY,CAAC;IACnG;IAEA,IAAI,QAAQ,SAAS,IAAI,MAAM,MAAM,GAAG,QAAQ,SAAS,EAAE;QACzD,OAAO,IAAI,CAAC,CAAC,qBAAqB,EAAE,KAAK,sBAAsB,EAAE,QAAQ,SAAS,CAAC,YAAY,CAAC;IAClG;IAEA,+BAA+B;IAC/B,MAAM,aAAa;QAAC;QAAQ;QAAO;QAAe;QAAa;QAAW;QAAY;KAAS;IAC/F,IAAI,WAAW,IAAI,CAAC,CAAA,OAAQ,MAAM,WAAW,GAAG,QAAQ,CAAC,UACrD,oDAAyB;;IAI7B,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;QACA;IACF;AACF;AAKO,SAAS;IACd,MAAM,UAAkC,CAAC;IAEzC,wBAAwB,OAAO,CAAC,CAAA;QAC9B,IAAI,QAAQ,GAAG,CAAC,QAAQ,EAAE;YACxB,OAAO,CAAC,QAAQ,GAAG,QAAQ,GAAG,CAAC,QAAQ;QACzC;IACF;IAEA,OAAO;AACT;AAKO,SAAS,sBAAsB,IAAY,EAAE,KAAa;IAC/D,6BAA6B;IAC7B,IAAI,mBAAmB,QAAQ,CAAC,OAA4C;QAC1E,OAAO;IACT;IAEA,mEAAmE;IACnE,IAAI,KAAK,WAAW,GAAG,QAAQ,CAAC,UAC5B,KAAK,WAAW,GAAG,QAAQ,CAAC,YAC5B,KAAK,WAAW,GAAG,QAAQ,CAAC,WAAW;QACzC,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,OAAO,GAAG,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM,KAAK,CAAC,CAAC,IAAI;QACpD;QACA,OAAO;IACT;IAEA,iCAAiC;IACjC,IAAI,KAAK,WAAW,GAAG,QAAQ,CAAC,UAAU,MAAM,QAAQ,CAAC,QAAQ;QAC/D,IAAI;YACF,MAAM,MAAM,IAAI,IAAI;YACpB,OAAO,GAAG,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,QAAQ,GAAG,IAAI,QAAQ,GAAG,IAAI,QAAQ,GAAG,IAAI;QAC9E,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAKO,SAAS;IACd,MAAM,SAAS;IAEf,IAAI,CAAC,OAAO,QAAQ,EAAE;QACpB,QAAQ,KAAK,CAAC;QACd,OAAO,UAAU,CAAC,OAAO,CAAC,CAAA;YACxB,QAAQ,KAAK,CAAC,CAAC,IAAI,EAAE,WAAW;QAClC;QAEA;;IAGF;IAEA,IAAI,OAAO,QAAQ,CAAC,MAAM,GAAG,GAAG;QAC9B,QAAQ,IAAI,CAAC;QACb,OAAO,QAAQ,CAAC,OAAO,CAAC,CAAA;YACtB,QAAQ,IAAI,CAAC,CAAC,KAAK,EAAE,SAAS;QAChC;IACF;IAEA,IAAI,OAAO,eAAe,CAAC,MAAM,GAAG,KAAK,oDAAyB,eAAe;QAC/E,QAAQ,IAAI,CAAC;QACb,OAAO,eAAe,CAAC,OAAO,CAAC,CAAA;YAC7B,QAAQ,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK;QAC5B;IACF;IAEA,QAAQ,GAAG,CAAC;AACd;AAEA,yCAAyC;AACzC,wCAA6E;IAC3E;AACF", "debugId": null}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/sonner.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sonner.tsx <module evaluation>\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8DACA", "debugId": null}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/sonner.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sonner.tsx\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0CACA", "debugId": null}}, {"offset": {"line": 287, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 295, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/service-worker-registration.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ServiceWorkerRegistration = registerClientReference(\n    function() { throw new Error(\"Attempted to call ServiceWorkerRegistration() from the server but ServiceWorkerRegistration is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/service-worker-registration.tsx <module evaluation>\",\n    \"ServiceWorkerRegistration\",\n);\nexport const useServiceWorker = registerClientReference(\n    function() { throw new Error(\"Attempted to call useServiceWorker() from the server but useServiceWorker is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/service-worker-registration.tsx <module evaluation>\",\n    \"useServiceWorker\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,4BAA4B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3D;IAAa,MAAM,IAAI,MAAM;AAAkQ,GAC/R,gFACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,gFACA", "debugId": null}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/service-worker-registration.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ServiceWorkerRegistration = registerClientReference(\n    function() { throw new Error(\"Attempted to call ServiceWorkerRegistration() from the server but ServiceWorkerRegistration is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/service-worker-registration.tsx\",\n    \"ServiceWorkerRegistration\",\n);\nexport const useServiceWorker = registerClientReference(\n    function() { throw new Error(\"Attempted to call useServiceWorker() from the server but useServiceWorker is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/service-worker-registration.tsx\",\n    \"useServiceWorker\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,4BAA4B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3D;IAAa,MAAM,IAAI,MAAM;AAAkQ,GAC/R,4DACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,4DACA", "debugId": null}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 335, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/seo-utils.tsx"], "sourcesContent": ["/**\n * SEO Utilities\n * Comprehensive SEO optimization tools and meta tag management\n */\n\nimport { Metadata } from 'next'\n\n// Base SEO configuration\nexport const BASE_SEO = {\n  siteName: 'PromptFlow',\n  siteUrl: process.env.NEXT_PUBLIC_SITE_URL || 'https://promptbir.com',\n  defaultTitle: 'PromptFlow - AI Destekli Prompt Yönetim Platformu',\n  defaultDescription: 'AI destekli geliştirme süreçlerini optimize eden minimalist prompt yönetim platformu. Projelerinizi organize edin, prompt\\'larınızı yönetin ve AI ile daha verimli çalışın.',\n  defaultKeywords: [\n    'prompt yönetimi',\n    'AI araçları',\n    'yapay zeka',\n    'geliştirici araçları',\n    'prompt engineering',\n    'AI destekli geliştirme',\n    'kod optimizasyonu',\n    'proje yönetimi'\n  ],\n  author: 'PromptFlow Team',\n  language: 'tr',\n  locale: 'tr_TR',\n  twitterHandle: '@promptflow',\n  facebookAppId: '',\n  themeColor: '#3b82f6',\n  backgroundColor: '#ffffff'\n} as const\n\n// Page types for structured data\nexport type PageType = \n  | 'website'\n  | 'article'\n  | 'profile'\n  | 'product'\n  | 'organization'\n  | 'webapp'\n\n// SEO metadata generator\nexport class SEOGenerator {\n  static generateMetadata(options: {\n    title?: string\n    description?: string\n    keywords?: string[]\n    image?: string\n    url?: string\n    type?: PageType\n    publishedTime?: string\n    modifiedTime?: string\n    author?: string\n    noIndex?: boolean\n    noFollow?: boolean\n  } = {}): Metadata {\n    const {\n      title,\n      description = BASE_SEO.defaultDescription,\n      keywords = BASE_SEO.defaultKeywords,\n      image,\n      url,\n      type = 'website',\n      publishedTime,\n      modifiedTime,\n      author = BASE_SEO.author,\n      noIndex = false,\n      noFollow = false\n    } = options\n\n    const fullTitle = title \n      ? `${title} | ${BASE_SEO.siteName}`\n      : BASE_SEO.defaultTitle\n\n    const fullUrl = url \n      ? `${BASE_SEO.siteUrl}${url}`\n      : BASE_SEO.siteUrl\n\n    const imageUrl = image \n      ? image.startsWith('http') \n        ? image \n        : `${BASE_SEO.siteUrl}${image}`\n      : `${BASE_SEO.siteUrl}/og-image.png`\n\n    const robots = [\n      noIndex ? 'noindex' : 'index',\n      noFollow ? 'nofollow' : 'follow'\n    ].join(', ')\n\n    return {\n      title: fullTitle,\n      description,\n      keywords: keywords.join(', '),\n      authors: [{ name: author }],\n      creator: author,\n      publisher: BASE_SEO.siteName,\n      robots,\n      \n      // Open Graph\n      openGraph: {\n        title: fullTitle,\n        description,\n        url: fullUrl,\n        siteName: BASE_SEO.siteName,\n        images: [\n          {\n            url: imageUrl,\n            width: 1200,\n            height: 630,\n            alt: title || BASE_SEO.defaultTitle\n          }\n        ],\n        locale: BASE_SEO.locale,\n        type: type === 'article' ? 'article' : 'website',\n        ...(publishedTime && { publishedTime }),\n        ...(modifiedTime && { modifiedTime })\n      },\n\n      // Twitter\n      twitter: {\n        card: 'summary_large_image',\n        title: fullTitle,\n        description,\n        images: [imageUrl],\n        creator: BASE_SEO.twitterHandle,\n        site: BASE_SEO.twitterHandle\n      },\n\n      // Additional meta tags\n      other: {\n        'theme-color': BASE_SEO.themeColor,\n        'msapplication-TileColor': BASE_SEO.themeColor,\n        'apple-mobile-web-app-capable': 'yes',\n        'apple-mobile-web-app-status-bar-style': 'default',\n        'format-detection': 'telephone=no'\n      }\n    }\n  }\n\n  // Generate structured data (JSON-LD)\n  static generateStructuredData(options: {\n    type: 'WebSite' | 'WebApplication' | 'Article' | 'Person' | 'Organization' | 'Product'\n    data: Record<string, any>\n  }) {\n    const { type, data } = options\n\n    const baseStructure = {\n      '@context': 'https://schema.org',\n      '@type': type,\n      ...data\n    }\n\n    // Add common properties based on type\n    switch (type) {\n      case 'WebSite':\n        return {\n          ...baseStructure,\n          name: data.name || BASE_SEO.siteName,\n          url: data.url || BASE_SEO.siteUrl,\n          description: data.description || BASE_SEO.defaultDescription,\n          inLanguage: BASE_SEO.language,\n          potentialAction: {\n            '@type': 'SearchAction',\n            target: `${BASE_SEO.siteUrl}/search?q={search_term_string}`,\n            'query-input': 'required name=search_term_string'\n          }\n        }\n\n      case 'WebApplication':\n        return {\n          ...baseStructure,\n          name: data.name || BASE_SEO.siteName,\n          url: data.url || BASE_SEO.siteUrl,\n          description: data.description || BASE_SEO.defaultDescription,\n          applicationCategory: 'DeveloperApplication',\n          operatingSystem: 'Web Browser',\n          offers: {\n            '@type': 'Offer',\n            price: '0',\n            priceCurrency: 'USD'\n          }\n        }\n\n      case 'Article':\n        return {\n          ...baseStructure,\n          headline: data.headline,\n          description: data.description,\n          author: {\n            '@type': 'Person',\n            name: data.author || BASE_SEO.author\n          },\n          publisher: {\n            '@type': 'Organization',\n            name: BASE_SEO.siteName,\n            logo: {\n              '@type': 'ImageObject',\n              url: `${BASE_SEO.siteUrl}/logo.png`\n            }\n          },\n          datePublished: data.datePublished,\n          dateModified: data.dateModified || data.datePublished,\n          mainEntityOfPage: {\n            '@type': 'WebPage',\n            '@id': data.url\n          }\n        }\n\n      case 'Organization':\n        return {\n          ...baseStructure,\n          name: data.name || BASE_SEO.siteName,\n          url: data.url || BASE_SEO.siteUrl,\n          description: data.description || BASE_SEO.defaultDescription,\n          logo: `${BASE_SEO.siteUrl}/logo.png`,\n          sameAs: data.sameAs || [],\n          contactPoint: {\n            '@type': 'ContactPoint',\n            contactType: 'customer service',\n            availableLanguage: ['Turkish', 'English']\n          }\n        }\n\n      default:\n        return baseStructure\n    }\n  }\n\n  // Generate breadcrumb structured data\n  static generateBreadcrumbs(items: Array<{ name: string; url: string }>) {\n    return {\n      '@context': 'https://schema.org',\n      '@type': 'BreadcrumbList',\n      itemListElement: items.map((item, index) => ({\n        '@type': 'ListItem',\n        position: index + 1,\n        name: item.name,\n        item: `${BASE_SEO.siteUrl}${item.url}`\n      }))\n    }\n  }\n\n  // Generate FAQ structured data\n  static generateFAQ(faqs: Array<{ question: string; answer: string }>) {\n    return {\n      '@context': 'https://schema.org',\n      '@type': 'FAQPage',\n      mainEntity: faqs.map(faq => ({\n        '@type': 'Question',\n        name: faq.question,\n        acceptedAnswer: {\n          '@type': 'Answer',\n          text: faq.answer\n        }\n      }))\n    }\n  }\n}\n\n// SEO component for injecting structured data\nexport function StructuredData({ data }: { data: Record<string, any> }) {\n  return (\n    <script\n      type=\"application/ld+json\"\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(data) }}\n    />\n  )\n}\n\n// Page-specific SEO configurations\nexport const PAGE_SEO = {\n  home: {\n    title: 'Ana Sayfa',\n    description: 'AI destekli prompt yönetim platformu ile projelerinizi organize edin ve geliştirme sürecinizi optimize edin.',\n    keywords: ['ana sayfa', 'prompt yönetimi', 'AI araçları', 'geliştirici platformu']\n  },\n  \n  dashboard: {\n    title: 'Dashboard',\n    description: 'Prompt projelerinizi yönetin, organize edin ve AI destekli geliştirme sürecinizi optimize edin.',\n    keywords: ['dashboard', 'proje yönetimi', 'prompt organizasyonu'],\n    noIndex: true // Private area\n  },\n  \n  profile: {\n    title: 'Profil',\n    description: 'Kullanıcı profili ve hesap ayarları. Plan bilgilerinizi görüntüleyin ve hesabınızı yönetin.',\n    keywords: ['profil', 'hesap ayarları', 'kullanıcı bilgileri'],\n    noIndex: true // Private area\n  },\n  \n  auth: {\n    title: 'Giriş Yap',\n    description: 'PromptFlow hesabınıza giriş yapın veya yeni hesap oluşturun. AI destekli prompt yönetimi başlasın.',\n    keywords: ['giriş', 'kayıt', 'hesap oluştur', 'login']\n  },\n  \n  templates: {\n    title: 'Şablon Galerisi',\n    description: 'Hazır prompt şablonları ile hızlı başlayın. Kategorilere göre düzenlenmiş profesyonel prompt koleksiyonu.',\n    keywords: ['şablonlar', 'prompt şablonları', 'hazır promptlar', 'galeri']\n  }\n} as const\n\n// Sitemap generation helper\nexport class SitemapGenerator {\n  static generateSitemap(pages: Array<{\n    url: string\n    lastModified?: Date\n    changeFrequency?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'\n    priority?: number\n  }>) {\n    const urls = pages.map(page => ({\n      url: `${BASE_SEO.siteUrl}${page.url}`,\n      lastModified: page.lastModified || new Date(),\n      changeFrequency: page.changeFrequency || 'weekly',\n      priority: page.priority || 0.5\n    }))\n\n    return urls\n  }\n\n  static generateRobotsTxt(options: {\n    allowAll?: boolean\n    disallowPaths?: string[]\n    sitemapUrl?: string\n  } = {}) {\n    const {\n      allowAll = true,\n      disallowPaths = ['/api/', '/admin/', '/_next/'],\n      sitemapUrl = `${BASE_SEO.siteUrl}/sitemap.xml`\n    } = options\n\n    let robotsTxt = 'User-agent: *\\n'\n    \n    if (allowAll) {\n      robotsTxt += 'Allow: /\\n'\n    }\n    \n    disallowPaths.forEach(path => {\n      robotsTxt += `Disallow: ${path}\\n`\n    })\n    \n    robotsTxt += `\\nSitemap: ${sitemapUrl}\\n`\n    \n    return robotsTxt\n  }\n}\n\n// Performance optimization for SEO\nexport class SEOPerformance {\n  static preloadCriticalResources() {\n    if (typeof window === 'undefined') return\n\n    // Preload critical fonts\n    const fontLinks = [\n      '/fonts/inter-var.woff2',\n      '/fonts/inter-var-italic.woff2'\n    ]\n\n    fontLinks.forEach(href => {\n      const link = document.createElement('link')\n      link.rel = 'preload'\n      link.as = 'font'\n      link.type = 'font/woff2'\n      link.crossOrigin = 'anonymous'\n      link.href = href\n      document.head.appendChild(link)\n    })\n\n    // Preload critical images\n    const imageLinks = [\n      '/logo.png',\n      '/og-image.png'\n    ]\n\n    imageLinks.forEach(href => {\n      const link = document.createElement('link')\n      link.rel = 'preload'\n      link.as = 'image'\n      link.href = href\n      document.head.appendChild(link)\n    })\n  }\n\n  static optimizeImages() {\n    if (typeof window === 'undefined') return\n\n    // Add loading=\"lazy\" to images below the fold\n    const images = document.querySelectorAll('img:not([loading])')\n    \n    images.forEach((img, index) => {\n      if (index > 2) { // First 3 images load eagerly\n        img.setAttribute('loading', 'lazy')\n      }\n    })\n  }\n\n  static addStructuredDataToHead(data: Record<string, any>) {\n    if (typeof window === 'undefined') return\n\n    const script = document.createElement('script')\n    script.type = 'application/ld+json'\n    script.textContent = JSON.stringify(data)\n    document.head.appendChild(script)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;AAKM,MAAM,WAAW;IACtB,UAAU;IACV,SAAS,QAAQ,GAAG,CAAC,oBAAoB,IAAI;IAC7C,cAAc;IACd,oBAAoB;IACpB,iBAAiB;QACf;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,eAAe;IACf,eAAe;IACf,YAAY;IACZ,iBAAiB;AACnB;AAYO,MAAM;IACX,OAAO,iBAAiB,UAYpB,CAAC,CAAC,EAAY;QAChB,MAAM,EACJ,KAAK,EACL,cAAc,SAAS,kBAAkB,EACzC,WAAW,SAAS,eAAe,EACnC,KAAK,EACL,GAAG,EACH,OAAO,SAAS,EAChB,aAAa,EACb,YAAY,EACZ,SAAS,SAAS,MAAM,EACxB,UAAU,KAAK,EACf,WAAW,KAAK,EACjB,GAAG;QAEJ,MAAM,YAAY,QACd,GAAG,MAAM,GAAG,EAAE,SAAS,QAAQ,EAAE,GACjC,SAAS,YAAY;QAEzB,MAAM,UAAU,MACZ,GAAG,SAAS,OAAO,GAAG,KAAK,GAC3B,SAAS,OAAO;QAEpB,MAAM,WAAW,QACb,MAAM,UAAU,CAAC,UACf,QACA,GAAG,SAAS,OAAO,GAAG,OAAO,GAC/B,GAAG,SAAS,OAAO,CAAC,aAAa,CAAC;QAEtC,MAAM,SAAS;YACb,UAAU,YAAY;YACtB,WAAW,aAAa;SACzB,CAAC,IAAI,CAAC;QAEP,OAAO;YACL,OAAO;YACP;YACA,UAAU,SAAS,IAAI,CAAC;YACxB,SAAS;gBAAC;oBAAE,MAAM;gBAAO;aAAE;YAC3B,SAAS;YACT,WAAW,SAAS,QAAQ;YAC5B;YAEA,aAAa;YACb,WAAW;gBACT,OAAO;gBACP;gBACA,KAAK;gBACL,UAAU,SAAS,QAAQ;gBAC3B,QAAQ;oBACN;wBACE,KAAK;wBACL,OAAO;wBACP,QAAQ;wBACR,KAAK,SAAS,SAAS,YAAY;oBACrC;iBACD;gBACD,QAAQ,SAAS,MAAM;gBACvB,MAAM,SAAS,YAAY,YAAY;gBACvC,GAAI,iBAAiB;oBAAE;gBAAc,CAAC;gBACtC,GAAI,gBAAgB;oBAAE;gBAAa,CAAC;YACtC;YAEA,UAAU;YACV,SAAS;gBACP,MAAM;gBACN,OAAO;gBACP;gBACA,QAAQ;oBAAC;iBAAS;gBAClB,SAAS,SAAS,aAAa;gBAC/B,MAAM,SAAS,aAAa;YAC9B;YAEA,uBAAuB;YACvB,OAAO;gBACL,eAAe,SAAS,UAAU;gBAClC,2BAA2B,SAAS,UAAU;gBAC9C,gCAAgC;gBAChC,yCAAyC;gBACzC,oBAAoB;YACtB;QACF;IACF;IAEA,qCAAqC;IACrC,OAAO,uBAAuB,OAG7B,EAAE;QACD,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG;QAEvB,MAAM,gBAAgB;YACpB,YAAY;YACZ,SAAS;YACT,GAAG,IAAI;QACT;QAEA,sCAAsC;QACtC,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,GAAG,aAAa;oBAChB,MAAM,KAAK,IAAI,IAAI,SAAS,QAAQ;oBACpC,KAAK,KAAK,GAAG,IAAI,SAAS,OAAO;oBACjC,aAAa,KAAK,WAAW,IAAI,SAAS,kBAAkB;oBAC5D,YAAY,SAAS,QAAQ;oBAC7B,iBAAiB;wBACf,SAAS;wBACT,QAAQ,GAAG,SAAS,OAAO,CAAC,8BAA8B,CAAC;wBAC3D,eAAe;oBACjB;gBACF;YAEF,KAAK;gBACH,OAAO;oBACL,GAAG,aAAa;oBAChB,MAAM,KAAK,IAAI,IAAI,SAAS,QAAQ;oBACpC,KAAK,KAAK,GAAG,IAAI,SAAS,OAAO;oBACjC,aAAa,KAAK,WAAW,IAAI,SAAS,kBAAkB;oBAC5D,qBAAqB;oBACrB,iBAAiB;oBACjB,QAAQ;wBACN,SAAS;wBACT,OAAO;wBACP,eAAe;oBACjB;gBACF;YAEF,KAAK;gBACH,OAAO;oBACL,GAAG,aAAa;oBAChB,UAAU,KAAK,QAAQ;oBACvB,aAAa,KAAK,WAAW;oBAC7B,QAAQ;wBACN,SAAS;wBACT,MAAM,KAAK,MAAM,IAAI,SAAS,MAAM;oBACtC;oBACA,WAAW;wBACT,SAAS;wBACT,MAAM,SAAS,QAAQ;wBACvB,MAAM;4BACJ,SAAS;4BACT,KAAK,GAAG,SAAS,OAAO,CAAC,SAAS,CAAC;wBACrC;oBACF;oBACA,eAAe,KAAK,aAAa;oBACjC,cAAc,KAAK,YAAY,IAAI,KAAK,aAAa;oBACrD,kBAAkB;wBAChB,SAAS;wBACT,OAAO,KAAK,GAAG;oBACjB;gBACF;YAEF,KAAK;gBACH,OAAO;oBACL,GAAG,aAAa;oBAChB,MAAM,KAAK,IAAI,IAAI,SAAS,QAAQ;oBACpC,KAAK,KAAK,GAAG,IAAI,SAAS,OAAO;oBACjC,aAAa,KAAK,WAAW,IAAI,SAAS,kBAAkB;oBAC5D,MAAM,GAAG,SAAS,OAAO,CAAC,SAAS,CAAC;oBACpC,QAAQ,KAAK,MAAM,IAAI,EAAE;oBACzB,cAAc;wBACZ,SAAS;wBACT,aAAa;wBACb,mBAAmB;4BAAC;4BAAW;yBAAU;oBAC3C;gBACF;YAEF;gBACE,OAAO;QACX;IACF;IAEA,sCAAsC;IACtC,OAAO,oBAAoB,KAA2C,EAAE;QACtE,OAAO;YACL,YAAY;YACZ,SAAS;YACT,iBAAiB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;oBAC3C,SAAS;oBACT,UAAU,QAAQ;oBAClB,MAAM,KAAK,IAAI;oBACf,MAAM,GAAG,SAAS,OAAO,GAAG,KAAK,GAAG,EAAE;gBACxC,CAAC;QACH;IACF;IAEA,+BAA+B;IAC/B,OAAO,YAAY,IAAiD,EAAE;QACpE,OAAO;YACL,YAAY;YACZ,SAAS;YACT,YAAY,KAAK,GAAG,CAAC,CAAA,MAAO,CAAC;oBAC3B,SAAS;oBACT,MAAM,IAAI,QAAQ;oBAClB,gBAAgB;wBACd,SAAS;wBACT,MAAM,IAAI,MAAM;oBAClB;gBACF,CAAC;QACH;IACF;AACF;AAGO,SAAS,eAAe,EAAE,IAAI,EAAiC;IACpE,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAM;;;;;;AAG9D;AAGO,MAAM,WAAW;IACtB,MAAM;QACJ,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAa;YAAmB;YAAe;SAAwB;IACpF;IAEA,WAAW;QACT,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAa;YAAkB;SAAuB;QACjE,SAAS,KAAK,eAAe;IAC/B;IAEA,SAAS;QACP,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAU;YAAkB;SAAsB;QAC7D,SAAS,KAAK,eAAe;IAC/B;IAEA,MAAM;QACJ,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAS;YAAS;YAAiB;SAAQ;IACxD;IAEA,WAAW;QACT,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAa;YAAqB;YAAmB;SAAS;IAC3E;AACF;AAGO,MAAM;IACX,OAAO,gBAAgB,KAKrB,EAAE;QACF,MAAM,OAAO,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAC9B,KAAK,GAAG,SAAS,OAAO,GAAG,KAAK,GAAG,EAAE;gBACrC,cAAc,KAAK,YAAY,IAAI,IAAI;gBACvC,iBAAiB,KAAK,eAAe,IAAI;gBACzC,UAAU,KAAK,QAAQ,IAAI;YAC7B,CAAC;QAED,OAAO;IACT;IAEA,OAAO,kBAAkB,UAIrB,CAAC,CAAC,EAAE;QACN,MAAM,EACJ,WAAW,IAAI,EACf,gBAAgB;YAAC;YAAS;YAAW;SAAU,EAC/C,aAAa,GAAG,SAAS,OAAO,CAAC,YAAY,CAAC,EAC/C,GAAG;QAEJ,IAAI,YAAY;QAEhB,IAAI,UAAU;YACZ,aAAa;QACf;QAEA,cAAc,OAAO,CAAC,CAAA;YACpB,aAAa,CAAC,UAAU,EAAE,KAAK,EAAE,CAAC;QACpC;QAEA,aAAa,CAAC,WAAW,EAAE,WAAW,EAAE,CAAC;QAEzC,OAAO;IACT;AACF;AAGO,MAAM;IACX,OAAO,2BAA2B;QAChC,wCAAmC;;;QAEnC,yBAAyB;QACzB,MAAM;QAeN,0BAA0B;QAC1B,MAAM;IAYR;IAEA,OAAO,iBAAiB;QACtB,wCAAmC;;;QAEnC,8CAA8C;QAC9C,MAAM;IAOR;IAEA,OAAO,wBAAwB,IAAyB,EAAE;QACxD,wCAAmC;;;QAEnC,MAAM;IAIR;AACF", "debugId": null}}, {"offset": {"line": 667, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/error-boundary.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ErrorBoundary = registerClientReference(\n    function() { throw new Error(\"Attempted to call ErrorBoundary() from the server but ErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/error-boundary.tsx <module evaluation>\",\n    \"ErrorBoundary\",\n);\nexport const useErrorHandler = registerClientReference(\n    function() { throw new Error(\"Attempted to call useErrorHandler() from the server but useErrorHandler is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/error-boundary.tsx <module evaluation>\",\n    \"useErrorHandler\",\n);\nexport const withErrorBoundary = registerClientReference(\n    function() { throw new Error(\"Attempted to call withErrorBoundary() from the server but withErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/error-boundary.tsx <module evaluation>\",\n    \"withErrorBoundary\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,mEACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,mEACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,mEACA", "debugId": null}}, {"offset": {"line": 687, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/error-boundary.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ErrorBoundary = registerClientReference(\n    function() { throw new Error(\"Attempted to call ErrorBoundary() from the server but ErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/error-boundary.tsx\",\n    \"ErrorBoundary\",\n);\nexport const useErrorHandler = registerClientReference(\n    function() { throw new Error(\"Attempted to call useErrorHandler() from the server but useErrorHandler is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/error-boundary.tsx\",\n    \"useErrorHandler\",\n);\nexport const withErrorBoundary = registerClientReference(\n    function() { throw new Error(\"Attempted to call withErrorBoundary() from the server but withErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/error-boundary.tsx\",\n    \"withErrorBoundary\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,+CACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,+CACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,+CACA", "debugId": null}}, {"offset": {"line": 707, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 715, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/form-feedback.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const FeedbackMessage = registerClientReference(\n    function() { throw new Error(\"Attempted to call FeedbackMessage() from the server but FeedbackMessage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/form-feedback.tsx <module evaluation>\",\n    \"FeedbackMessage\",\n);\nexport const FieldFeedback = registerClientReference(\n    function() { throw new Error(\"Attempted to call FieldFeedback() from the server but FieldFeedback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/form-feedback.tsx <module evaluation>\",\n    \"FieldFeedback\",\n);\nexport const FormFeedbackProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call FormFeedbackProvider() from the server but FormFeedbackProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/form-feedback.tsx <module evaluation>\",\n    \"FormFeedbackProvider\",\n);\nexport const FormProgress = registerClientReference(\n    function() { throw new Error(\"Attempted to call FormProgress() from the server but FormProgress is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/form-feedback.tsx <module evaluation>\",\n    \"FormProgress\",\n);\nexport const GlobalFeedback = registerClientReference(\n    function() { throw new Error(\"Attempted to call GlobalFeedback() from the server but GlobalFeedback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/form-feedback.tsx <module evaluation>\",\n    \"GlobalFeedback\",\n);\nexport const useFormFeedback = registerClientReference(\n    function() { throw new Error(\"Attempted to call useFormFeedback() from the server but useFormFeedback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/form-feedback.tsx <module evaluation>\",\n    \"useFormFeedback\",\n);\nexport const useFormValidation = registerClientReference(\n    function() { throw new Error(\"Attempted to call useFormValidation() from the server but useFormValidation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/form-feedback.tsx <module evaluation>\",\n    \"useFormValidation\",\n);\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,qEACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,qEACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,qEACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,qEACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,qEACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,qEACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,qEACA", "debugId": null}}, {"offset": {"line": 751, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/form-feedback.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const FeedbackMessage = registerClientReference(\n    function() { throw new Error(\"Attempted to call FeedbackMessage() from the server but FeedbackMessage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/form-feedback.tsx\",\n    \"FeedbackMessage\",\n);\nexport const FieldFeedback = registerClientReference(\n    function() { throw new Error(\"Attempted to call FieldFeedback() from the server but FieldFeedback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/form-feedback.tsx\",\n    \"FieldFeedback\",\n);\nexport const FormFeedbackProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call FormFeedbackProvider() from the server but FormFeedbackProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/form-feedback.tsx\",\n    \"FormFeedbackProvider\",\n);\nexport const FormProgress = registerClientReference(\n    function() { throw new Error(\"Attempted to call FormProgress() from the server but FormProgress is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/form-feedback.tsx\",\n    \"FormProgress\",\n);\nexport const GlobalFeedback = registerClientReference(\n    function() { throw new Error(\"Attempted to call GlobalFeedback() from the server but GlobalFeedback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/form-feedback.tsx\",\n    \"GlobalFeedback\",\n);\nexport const useFormFeedback = registerClientReference(\n    function() { throw new Error(\"Attempted to call useFormFeedback() from the server but useFormFeedback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/form-feedback.tsx\",\n    \"useFormFeedback\",\n);\nexport const useFormValidation = registerClientReference(\n    function() { throw new Error(\"Attempted to call useFormValidation() from the server but useFormValidation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/form-feedback.tsx\",\n    \"useFormValidation\",\n);\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,iDACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,iDACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,iDACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,iDACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,iDACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,iDACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,iDACA", "debugId": null}}, {"offset": {"line": 787, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 795, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/performance-monitor-wrapper.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const PerformanceMonitorWrapper = registerClientReference(\n    function() { throw new Error(\"Attempted to call PerformanceMonitorWrapper() from the server but PerformanceMonitorWrapper is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/performance-monitor-wrapper.tsx <module evaluation>\",\n    \"PerformanceMonitorWrapper\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/performance-monitor-wrapper.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/performance-monitor-wrapper.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,4BAA4B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3D;IAAa,MAAM,IAAI,MAAM;AAAkQ,GAC/R,gFACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkT,GAC/U,gFACA", "debugId": null}}, {"offset": {"line": 811, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/performance-monitor-wrapper.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const PerformanceMonitorWrapper = registerClientReference(\n    function() { throw new Error(\"Attempted to call PerformanceMonitorWrapper() from the server but PerformanceMonitorWrapper is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/performance-monitor-wrapper.tsx\",\n    \"PerformanceMonitorWrapper\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/performance-monitor-wrapper.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/performance-monitor-wrapper.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,4BAA4B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3D;IAAa,MAAM,IAAI,MAAM;AAAkQ,GAC/R,4DACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8R,GAC3T,4DACA", "debugId": null}}, {"offset": {"line": 827, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 835, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/hydration-safe.ts/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ClientOnly = registerClientReference(\n    function() { throw new Error(\"Attempted to call ClientOnly() from the server but ClientOnly is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/hydration-safe.ts <module evaluation>\",\n    \"ClientOnly\",\n);\nexport const HydrationSafe = registerClientReference(\n    function() { throw new Error(\"Attempted to call HydrationSafe() from the server but HydrationSafe is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/hydration-safe.ts <module evaluation>\",\n    \"HydrationSafe\",\n);\nexport const suppressHydrationConsoleErrors = registerClientReference(\n    function() { throw new Error(\"Attempted to call suppressHydrationConsoleErrors() from the server but suppressHydrationConsoleErrors is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/hydration-safe.ts <module evaluation>\",\n    \"suppressHydrationConsoleErrors\",\n);\nexport const suppressHydrationWarning = registerClientReference(\n    function() { throw new Error(\"Attempted to call suppressHydrationWarning() from the server but suppressHydrationWarning is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/hydration-safe.ts <module evaluation>\",\n    \"suppressHydrationWarning\",\n);\nexport const useHydrationSafe = registerClientReference(\n    function() { throw new Error(\"Attempted to call useHydrationSafe() from the server but useHydrationSafe is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/hydration-safe.ts <module evaluation>\",\n    \"useHydrationSafe\",\n);\nexport const useLocalStorage = registerClientReference(\n    function() { throw new Error(\"Attempted to call useLocalStorage() from the server but useLocalStorage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/hydration-safe.ts <module evaluation>\",\n    \"useLocalStorage\",\n);\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,2DACA;AAEG,MAAM,iCAAiC,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChE;IAAa,MAAM,IAAI,MAAM;AAA4Q,GACzS,2DACA;AAEG,MAAM,2BAA2B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1D;IAAa,MAAM,IAAI,MAAM;AAAgQ,GAC7R,2DACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,2DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,2DACA", "debugId": null}}, {"offset": {"line": 867, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/hydration-safe.ts/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ClientOnly = registerClientReference(\n    function() { throw new Error(\"Attempted to call ClientOnly() from the server but ClientOnly is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/hydration-safe.ts\",\n    \"ClientOnly\",\n);\nexport const HydrationSafe = registerClientReference(\n    function() { throw new Error(\"Attempted to call HydrationSafe() from the server but HydrationSafe is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/hydration-safe.ts\",\n    \"HydrationSafe\",\n);\nexport const suppressHydrationConsoleErrors = registerClientReference(\n    function() { throw new Error(\"Attempted to call suppressHydrationConsoleErrors() from the server but suppressHydrationConsoleErrors is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/hydration-safe.ts\",\n    \"suppressHydrationConsoleErrors\",\n);\nexport const suppressHydrationWarning = registerClientReference(\n    function() { throw new Error(\"Attempted to call suppressHydrationWarning() from the server but suppressHydrationWarning is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/hydration-safe.ts\",\n    \"suppressHydrationWarning\",\n);\nexport const useHydrationSafe = registerClientReference(\n    function() { throw new Error(\"Attempted to call useHydrationSafe() from the server but useHydrationSafe is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/hydration-safe.ts\",\n    \"useHydrationSafe\",\n);\nexport const useLocalStorage = registerClientReference(\n    function() { throw new Error(\"Attempted to call useLocalStorage() from the server but useLocalStorage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/hydration-safe.ts\",\n    \"useLocalStorage\",\n);\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,uCACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,uCACA;AAEG,MAAM,iCAAiC,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChE;IAAa,MAAM,IAAI,MAAM;AAA4Q,GACzS,uCACA;AAEG,MAAM,2BAA2B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1D;IAAa,MAAM,IAAI,MAAM;AAAgQ,GAC7R,uCACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,uCACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,uCACA", "debugId": null}}, {"offset": {"line": 899, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 907, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON>, Viewport } from \"next\";\nimport { Inter } from \"next/font/google\";\nimport React from \"react\";\nimport \"./globals.css\";\nimport { QueryProvider } from \"@/providers/query-provider\";\nimport { performRuntimeSecurityCheck } from \"@/lib/env-security\";\nimport { Toaster } from \"@/components/ui/sonner\";\nimport { ServiceWorkerRegistration } from \"@/components/service-worker-registration\";\nimport { SEOGenerator, StructuredData, BASE_SEO } from \"@/lib/seo-utils\";\nimport { ErrorBoundary } from \"@/components/error-boundary\";\nimport { FormFeedbackProvider } from \"@/components/ui/form-feedback\";\n// Performance monitor - Next.js 15 uyumlu Client Component import\nimport { PerformanceMonitorWrapper } from \"@/components/performance-monitor-wrapper\";\n// Hydration safe utilities\nimport { HydrationSafe, suppressHydrationConsoleErrors } from \"@/lib/hydration-safe\";\n\nconst inter = Inter({\n  subsets: [\"latin\"],\n  variable: \"--font-inter\",\n  display: \"swap\",\n  preload: true,\n});\n\n// Generate comprehensive SEO metadata\nexport const metadata: Metadata = {\n  ...SEOGenerator.generateMetadata({\n    title: \"PromptFlow - AI Destekli Prompt Yönetim Platformu\",\n    description: \"AI destekli geliştirme süreçlerini optimize eden minimalist prompt yönetim platformu. Projelerinizi organize edin, prompt'larınızı yönetin ve AI ile daha verimli çalışın.\",\n    keywords: [\n      \"prompt yönetimi\",\n      \"AI araçları\",\n      \"yapay zeka\",\n      \"geliştirici araçları\",\n      \"prompt engineering\",\n      \"AI destekli geliştirme\",\n      \"kod optimizasyonu\",\n      \"proje yönetimi\",\n      \"ChatGPT prompts\",\n      \"AI productivity\"\n    ],\n    type: 'webapp'\n  }),\n  icons: {\n    icon: [\n      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },\n      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },\n      { url: '/favicon.ico', sizes: 'any' }\n    ],\n    shortcut: '/favicon.ico',\n    apple: '/apple-touch-icon.png',\n  },\n  manifest: '/site.webmanifest',\n  robots: {\n    index: true,\n    follow: true,\n    googleBot: {\n      index: true,\n      follow: true,\n      'max-video-preview': -1,\n      'max-image-preview': 'large',\n      'max-snippet': -1,\n    },\n  },\n  openGraph: {\n    type: \"website\",\n    locale: \"tr_TR\",\n    url: \"https://promptbir.com\",\n    siteName: \"Promptbir\",\n    title: \"Promptbir - AI Prompt Yönetim Platformu\",\n    description: \"Yapay zeka prompt'larınızı profesyonelce yönetin. Takımınızla paylaşın, organize edin ve verimliliğinizi artırın. 10,000+ geliştirici tarafından güvenilir.\",\n    images: [\n      {\n        url: \"/og-image.png\",\n        width: 1200,\n        height: 630,\n        alt: \"Promptbir - AI Prompt Yönetim Platformu\",\n      },\n    ],\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    site: \"@promptbir\",\n    creator: \"@promptbir\",\n    title: \"Promptbir - AI Prompt Yönetim Platformu\",\n    description: \"Yapay zeka prompt'larınızı profesyonelce yönetin. Takımınızla paylaşın, organize edin ve verimliliğinizi artırın.\",\n    images: [\"/twitter-image.png\"],\n  },\n  verification: {\n    google: \"your-google-verification-code\",\n  },\n  alternates: {\n    canonical: \"https://promptbir.com\",\n    languages: {\n      'tr-TR': 'https://promptbir.com',\n      'en-US': 'https://promptbir.com/en',\n    },\n  },\n  category: \"Technology\",\n  metadataBase: new URL('https://promptbir.com'),\n};\n\nexport const viewport: Viewport = {\n  width: 'device-width',\n  initialScale: 1,\n  maximumScale: 1,\n  userScalable: false,\n  themeColor: [\n    { media: '(prefers-color-scheme: light)', color: '#ffffff' },\n    { media: '(prefers-color-scheme: dark)', color: '#000000' },\n  ],\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  // Run security check on server-side\n  if (typeof window === 'undefined') {\n    performRuntimeSecurityCheck();\n  }\n\n  // Suppress hydration console errors in development (client-side only)\n  if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {\n    suppressHydrationConsoleErrors();\n  }\n\n  // Generate structured data for the website\n  const websiteStructuredData = SEOGenerator.generateStructuredData({\n    type: 'WebApplication',\n    data: {\n      name: BASE_SEO.siteName,\n      description: BASE_SEO.defaultDescription,\n      url: BASE_SEO.siteUrl,\n      applicationCategory: 'DeveloperApplication',\n      operatingSystem: 'Web Browser',\n      offers: {\n        '@type': 'Offer',\n        price: '0',\n        priceCurrency: 'USD'\n      },\n      author: {\n        '@type': 'Organization',\n        name: BASE_SEO.siteName\n      }\n    }\n  })\n\n  return (\n    <html lang=\"tr\">\n      <head>\n        {/* DNS prefetch for external resources */}\n        <link rel=\"dns-prefetch\" href=\"//fonts.googleapis.com\" />\n        <link rel=\"dns-prefetch\" href=\"//fonts.gstatic.com\" />\n\n        {/* Structured Data */}\n        <StructuredData data={websiteStructuredData} />\n\n        {/* Critical resources are automatically preloaded by Next.js 15 */}\n\n        {/* Theme and app configuration */}\n        <meta name=\"theme-color\" content=\"#3b82f6\" />\n        <meta name=\"mobile-web-app-capable\" content=\"yes\" />\n        <meta name=\"apple-mobile-web-app-capable\" content=\"yes\" />\n        <meta name=\"apple-mobile-web-app-status-bar-style\" content=\"default\" />\n        <meta name=\"format-detection\" content=\"telephone=no\" />\n\n        {/* Viewport meta for responsive design */}\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, viewport-fit=cover\" />\n      </head>\n      <body\n        className={`${inter.variable} font-sans antialiased`}\n      >\n        <ErrorBoundary level=\"critical\" showDetails={process.env.NODE_ENV === 'development'}>\n          <QueryProvider>\n            <FormFeedbackProvider>\n              {children}\n              <Toaster />\n              <HydrationSafe>\n                <ServiceWorkerRegistration />\n                <PerformanceMonitorWrapper />\n              </HydrationSafe>\n            </FormFeedbackProvider>\n          </QueryProvider>\n        </ErrorBoundary>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kEAAkE;AAClE;AACA,2BAA2B;AAC3B;;;;;;;;;;;;;AAUO,MAAM,WAAqB;IAChC,GAAG,2HAAA,CAAA,eAAY,CAAC,gBAAgB,CAAC;QAC/B,OAAO;QACP,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,MAAM;IACR,EAAE;IACF,OAAO;QACL,MAAM;YACJ;gBAAE,KAAK;gBAAsB,OAAO;gBAAS,MAAM;YAAY;YAC/D;gBAAE,KAAK;gBAAsB,OAAO;gBAAS,MAAM;YAAY;YAC/D;gBAAE,KAAK;gBAAgB,OAAO;YAAM;SACrC;QACD,UAAU;QACV,OAAO;IACT;IACA,UAAU;IACV,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB,CAAC;YACtB,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;IACA,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK;QACL,UAAU;QACV,OAAO;QACP,aAAa;QACb,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;IACH;IACA,SAAS;QACP,MAAM;QACN,MAAM;QACN,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;SAAqB;IAChC;IACA,cAAc;QACZ,QAAQ;IACV;IACA,YAAY;QACV,WAAW;QACX,WAAW;YACT,SAAS;YACT,SAAS;QACX;IACF;IACA,UAAU;IACV,cAAc,IAAI,IAAI;AACxB;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,cAAc;IACd,cAAc;IACd,cAAc;IACd,YAAY;QACV;YAAE,OAAO;YAAiC,OAAO;QAAU;QAC3D;YAAE,OAAO;YAAgC,OAAO;QAAU;KAC3D;AACH;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,oCAAoC;IACpC,wCAAmC;QACjC,CAAA,GAAA,6HAAA,CAAA,8BAA2B,AAAD;IAC5B;IAEA,sEAAsE;IACtE;;IAIA,2CAA2C;IAC3C,MAAM,wBAAwB,2HAAA,CAAA,eAAY,CAAC,sBAAsB,CAAC;QAChE,MAAM;QACN,MAAM;YACJ,MAAM,2HAAA,CAAA,WAAQ,CAAC,QAAQ;YACvB,aAAa,2HAAA,CAAA,WAAQ,CAAC,kBAAkB;YACxC,KAAK,2HAAA,CAAA,WAAQ,CAAC,OAAO;YACrB,qBAAqB;YACrB,iBAAiB;YACjB,QAAQ;gBACN,SAAS;gBACT,OAAO;gBACP,eAAe;YACjB;YACA,QAAQ;gBACN,SAAS;gBACT,MAAM,2HAAA,CAAA,WAAQ,CAAC,QAAQ;YACzB;QACF;IACF;IAEA,qBACE,8OAAC;QAAK,MAAK;;0BACT,8OAAC;;kCAEC,8OAAC;wBAAK,KAAI;wBAAe,MAAK;;;;;;kCAC9B,8OAAC;wBAAK,KAAI;wBAAe,MAAK;;;;;;kCAG9B,8OAAC,2HAAA,CAAA,iBAAc;wBAAC,MAAM;;;;;;kCAKtB,8OAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;kCACjC,8OAAC;wBAAK,MAAK;wBAAyB,SAAQ;;;;;;kCAC5C,8OAAC;wBAAK,MAAK;wBAA+B,SAAQ;;;;;;kCAClD,8OAAC;wBAAK,MAAK;wBAAwC,SAAQ;;;;;;kCAC3D,8OAAC;wBAAK,MAAK;wBAAmB,SAAQ;;;;;;kCAGtC,8OAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;;;;;;;0BAEhC,8OAAC;gBACC,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,sBAAsB,CAAC;0BAEpD,cAAA,8OAAC,uIAAA,CAAA,gBAAa;oBAAC,OAAM;oBAAW,aAAa,oDAAyB;8BACpE,cAAA,8OAAC,sIAAA,CAAA,gBAAa;kCACZ,cAAA,8OAAC,4IAAA,CAAA,uBAAoB;;gCAClB;8CACD,8OAAC,kIAAA,CAAA,UAAO;;;;;8CACR,8OAAC,+HAAA,CAAA,gBAAa;;sDACZ,8OAAC,uJAAA,CAAA,4BAAyB;;;;;sDAC1B,8OAAC,uJAAA,CAAA,4BAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1C", "debugId": null}}, {"offset": {"line": 1218, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}
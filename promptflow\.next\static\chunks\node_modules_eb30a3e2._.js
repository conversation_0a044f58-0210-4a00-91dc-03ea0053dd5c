(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>_objectWithoutPropertiesLoose
});
function _objectWithoutPropertiesLoose(r, e) {
    if (null == r) return {};
    var t = {};
    for(var n in r)if (({}).hasOwnProperty.call(r, n)) {
        if (-1 !== e.indexOf(n)) continue;
        t[n] = r[n];
    }
    return t;
}
;
}),
"[project]/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>_objectWithoutProperties
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutPropertiesLoose$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js [app-client] (ecmascript)");
;
function _objectWithoutProperties(e, t) {
    if (null == e) return {};
    var o, r, i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutPropertiesLoose$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(e, t);
    if (Object.getOwnPropertySymbols) {
        var n = Object.getOwnPropertySymbols(e);
        for(r = 0; r < n.length; r++)o = n[r], -1 === t.indexOf(o) && ({}).propertyIsEnumerable.call(e, o) && (i[o] = e[o]);
    }
    return i;
}
;
}),
"[project]/node_modules/@babel/runtime/helpers/esm/arrayLikeToArray.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>_arrayLikeToArray
});
function _arrayLikeToArray(r, a) {
    (null == a || a > r.length) && (a = r.length);
    for(var e = 0, n = Array(a); e < a; e++)n[e] = r[e];
    return n;
}
;
}),
"[project]/node_modules/@babel/runtime/helpers/esm/arrayWithoutHoles.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>_arrayWithoutHoles
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$arrayLikeToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/arrayLikeToArray.js [app-client] (ecmascript)");
;
function _arrayWithoutHoles(r) {
    if (Array.isArray(r)) return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$arrayLikeToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(r);
}
;
}),
"[project]/node_modules/@babel/runtime/helpers/esm/iterableToArray.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>_iterableToArray
});
function _iterableToArray(r) {
    if ("undefined" != typeof Symbol && null != r[Symbol.iterator] || null != r["@@iterator"]) return Array.from(r);
}
;
}),
"[project]/node_modules/@babel/runtime/helpers/esm/unsupportedIterableToArray.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>_unsupportedIterableToArray
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$arrayLikeToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/arrayLikeToArray.js [app-client] (ecmascript)");
;
function _unsupportedIterableToArray(r, a) {
    if (r) {
        if ("string" == typeof r) return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$arrayLikeToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(r, a);
        var t = ({}).toString.call(r).slice(8, -1);
        return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$arrayLikeToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(r, a) : void 0;
    }
}
;
}),
"[project]/node_modules/@babel/runtime/helpers/esm/nonIterableSpread.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>_nonIterableSpread
});
function _nonIterableSpread() {
    throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
;
}),
"[project]/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>_toConsumableArray
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$arrayWithoutHoles$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/arrayWithoutHoles.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$iterableToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/iterableToArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$unsupportedIterableToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/unsupportedIterableToArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$nonIterableSpread$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/nonIterableSpread.js [app-client] (ecmascript)");
;
;
;
;
function _toConsumableArray(r) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$arrayWithoutHoles$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(r) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$iterableToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(r) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$unsupportedIterableToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(r) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$nonIterableSpread$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
}
;
}),
"[project]/node_modules/@babel/runtime/helpers/esm/typeof.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>_typeof
});
function _typeof(o) {
    "@babel/helpers - typeof";
    return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o) {
        return typeof o;
    } : function(o) {
        return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o;
    }, _typeof(o);
}
;
}),
"[project]/node_modules/@babel/runtime/helpers/esm/toPrimitive.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>toPrimitive
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/typeof.js [app-client] (ecmascript)");
;
function toPrimitive(t, r) {
    if ("object" != (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(t) || !t) return t;
    var e = t[Symbol.toPrimitive];
    if (void 0 !== e) {
        var i = e.call(t, r || "default");
        if ("object" != (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(i)) return i;
        throw new TypeError("@@toPrimitive must return a primitive value.");
    }
    return ("string" === r ? String : Number)(t);
}
;
}),
"[project]/node_modules/@babel/runtime/helpers/esm/toPropertyKey.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>toPropertyKey
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/typeof.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toPrimitive$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/toPrimitive.js [app-client] (ecmascript)");
;
;
function toPropertyKey(t) {
    var i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toPrimitive$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(t, "string");
    return "symbol" == (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(i) ? i : i + "";
}
;
}),
"[project]/node_modules/@babel/runtime/helpers/esm/defineProperty.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>_defineProperty
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toPropertyKey$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/toPropertyKey.js [app-client] (ecmascript)");
;
function _defineProperty(e, r, t) {
    return (r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toPropertyKey$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(r)) in e ? Object.defineProperty(e, r, {
        value: t,
        enumerable: !0,
        configurable: !0,
        writable: !0
    }) : e[r] = t, e;
}
;
}),
"[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>_extends
});
function _extends() {
    return _extends = ("TURBOPACK compile-time truthy", 1) ? Object.assign.bind() : "TURBOPACK unreachable", _extends.apply(null, arguments);
}
;
}),
"[project]/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>_asyncToGenerator
});
function asyncGeneratorStep(n, t, e, r, o, a, c) {
    try {
        var i = n[a](c), u = i.value;
    } catch (n) {
        return void e(n);
    }
    i.done ? t(u) : Promise.resolve(u).then(r, o);
}
function _asyncToGenerator(n) {
    return function() {
        var t = this, e = arguments;
        return new Promise(function(r, o) {
            var a = n.apply(t, e);
            function _next(n) {
                asyncGeneratorStep(a, r, o, _next, _throw, "next", n);
            }
            function _throw(n) {
                asyncGeneratorStep(a, r, o, _next, _throw, "throw", n);
            }
            _next(void 0);
        });
    };
}
;
}),
"[project]/node_modules/@babel/runtime/helpers/esm/classCallCheck.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>_classCallCheck
});
function _classCallCheck(a, n) {
    if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function");
}
;
}),
"[project]/node_modules/@babel/runtime/helpers/esm/createClass.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>_createClass
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toPropertyKey$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/toPropertyKey.js [app-client] (ecmascript)");
;
function _defineProperties(e, r) {
    for(var t = 0; t < r.length; t++){
        var o = r[t];
        o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toPropertyKey$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(o.key), o);
    }
}
function _createClass(e, r, t) {
    return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", {
        writable: !1
    }), e;
}
;
}),
"[project]/node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>_assertThisInitialized
});
function _assertThisInitialized(e) {
    if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
    return e;
}
;
}),
"[project]/node_modules/@babel/runtime/helpers/esm/possibleConstructorReturn.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>_possibleConstructorReturn
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/typeof.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$assertThisInitialized$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js [app-client] (ecmascript)");
;
;
function _possibleConstructorReturn(t, e) {
    if (e && ("object" == (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(e) || "function" == typeof e)) return e;
    if (void 0 !== e) throw new TypeError("Derived constructors may only return object or undefined");
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$assertThisInitialized$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(t);
}
;
}),
"[project]/node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>_getPrototypeOf
});
function _getPrototypeOf(t) {
    return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(t) {
        return t.__proto__ || Object.getPrototypeOf(t);
    }, _getPrototypeOf(t);
}
;
}),
"[project]/node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>_setPrototypeOf
});
function _setPrototypeOf(t, e) {
    return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(t, e) {
        return t.__proto__ = e, t;
    }, _setPrototypeOf(t, e);
}
;
}),
"[project]/node_modules/@babel/runtime/helpers/esm/inherits.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>_inherits
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$setPrototypeOf$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js [app-client] (ecmascript)");
;
function _inherits(t, e) {
    if ("function" != typeof e && null !== e) throw new TypeError("Super expression must either be null or a function");
    t.prototype = Object.create(e && e.prototype, {
        constructor: {
            value: t,
            writable: !0,
            configurable: !0
        }
    }), Object.defineProperty(t, "prototype", {
        writable: !1
    }), e && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$setPrototypeOf$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(t, e);
}
;
}),
"[project]/node_modules/format/format.js [app-client] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
//
// format - printf-like string formatting for JavaScript
// github.com/samsonjs/format
// @_sjs
//
// Copyright 2010 - 2013 Sami Samhuri <<EMAIL>>
//
// MIT License
// http://sjs.mit-license.org
//
;
(function() {
    //// Export the API
    var namespace;
    // CommonJS / Node module
    if ("TURBOPACK compile-time truthy", 1) {
        namespace = module.exports = format;
    } else //TURBOPACK unreachable
    ;
    namespace.format = format;
    namespace.vsprintf = vsprintf;
    if (typeof console !== 'undefined' && typeof console.log === 'function') {
        namespace.printf = printf;
    }
    function printf() {
        console.log(format.apply(null, arguments));
    }
    function vsprintf(fmt, replacements) {
        return format.apply(null, [
            fmt
        ].concat(replacements));
    }
    function format(fmt) {
        var argIndex = 1 // skip initial format argument
        , args = [].slice.call(arguments), i = 0, n = fmt.length, result = '', c, escaped = false, arg, tmp, leadingZero = false, precision, nextArg = function() {
            return args[argIndex++];
        }, slurpNumber = function() {
            var digits = '';
            while(/\d/.test(fmt[i])){
                digits += fmt[i++];
                c = fmt[i];
            }
            return digits.length > 0 ? parseInt(digits) : null;
        };
        for(; i < n; ++i){
            c = fmt[i];
            if (escaped) {
                escaped = false;
                if (c == '.') {
                    leadingZero = false;
                    c = fmt[++i];
                } else if (c == '0' && fmt[i + 1] == '.') {
                    leadingZero = true;
                    i += 2;
                    c = fmt[i];
                } else {
                    leadingZero = true;
                }
                precision = slurpNumber();
                switch(c){
                    case 'b':
                        result += parseInt(nextArg(), 10).toString(2);
                        break;
                    case 'c':
                        arg = nextArg();
                        if (typeof arg === 'string' || arg instanceof String) result += arg;
                        else result += String.fromCharCode(parseInt(arg, 10));
                        break;
                    case 'd':
                        result += parseInt(nextArg(), 10);
                        break;
                    case 'f':
                        tmp = String(parseFloat(nextArg()).toFixed(precision || 6));
                        result += leadingZero ? tmp : tmp.replace(/^0/, '');
                        break;
                    case 'j':
                        result += JSON.stringify(nextArg());
                        break;
                    case 'o':
                        result += '0' + parseInt(nextArg(), 10).toString(8);
                        break;
                    case 's':
                        result += nextArg();
                        break;
                    case 'x':
                        result += '0x' + parseInt(nextArg(), 10).toString(16);
                        break;
                    case 'X':
                        result += '0x' + parseInt(nextArg(), 10).toString(16).toUpperCase();
                        break;
                    default:
                        result += c;
                        break;
                }
            } else if (c === '%') {
                escaped = true;
            } else {
                result += c;
            }
        }
        return result;
    }
})();
}}),
"[project]/node_modules/fault/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
var formatter = __turbopack_context__.r("[project]/node_modules/format/format.js [app-client] (ecmascript)");
var fault = create(Error);
module.exports = fault;
fault.eval = create(EvalError);
fault.range = create(RangeError);
fault.reference = create(ReferenceError);
fault.syntax = create(SyntaxError);
fault.type = create(TypeError);
fault.uri = create(URIError);
fault.create = create;
// Create a new `EConstructor`, with the formatted `format` as a first argument.
function create(EConstructor) {
    FormattedError.displayName = EConstructor.displayName || EConstructor.name;
    return FormattedError;
    //TURBOPACK unreachable
    ;
    function FormattedError(format) {
        if (format) {
            format = formatter.apply(null, arguments);
        }
        return new EConstructor(format);
    }
}
}}),
"[project]/node_modules/lowlight/lib/core.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
var high = __turbopack_context__.r("[project]/node_modules/highlight.js/lib/core.js [app-client] (ecmascript)");
var fault = __turbopack_context__.r("[project]/node_modules/fault/index.js [app-client] (ecmascript)");
exports.highlight = highlight;
exports.highlightAuto = highlightAuto;
exports.registerLanguage = registerLanguage;
exports.listLanguages = listLanguages;
exports.registerAlias = registerAlias;
Emitter.prototype.addText = text;
Emitter.prototype.addKeyword = addKeyword;
Emitter.prototype.addSublanguage = addSublanguage;
Emitter.prototype.openNode = open;
Emitter.prototype.closeNode = close;
Emitter.prototype.closeAllNodes = noop;
Emitter.prototype.finalize = noop;
Emitter.prototype.toHTML = toHtmlNoop;
var defaultPrefix = 'hljs-';
// Highlighting `value` in the language `name`.
function highlight(name, value, options) {
    var before = high.configure({});
    var settings = options || {};
    var prefix = settings.prefix;
    var result;
    if (typeof name !== 'string') {
        throw fault('Expected `string` for name, got `%s`', name);
    }
    if (!high.getLanguage(name)) {
        throw fault('Unknown language: `%s` is not registered', name);
    }
    if (typeof value !== 'string') {
        throw fault('Expected `string` for value, got `%s`', value);
    }
    if (prefix === null || prefix === undefined) {
        prefix = defaultPrefix;
    }
    high.configure({
        __emitter: Emitter,
        classPrefix: prefix
    });
    result = high.highlight(value, {
        language: name,
        ignoreIllegals: true
    });
    high.configure(before || {});
    /* istanbul ignore if - Highlight.js seems to use this (currently) for broken
   * grammars, so let’s keep it in there just to be sure. */ if (result.errorRaised) {
        throw result.errorRaised;
    }
    return {
        relevance: result.relevance,
        language: result.language,
        value: result.emitter.rootNode.children
    };
}
function highlightAuto(value, options) {
    var settings = options || {};
    var subset = settings.subset || high.listLanguages();
    var prefix = settings.prefix;
    var length = subset.length;
    var index = -1;
    var result;
    var secondBest;
    var current;
    var name;
    if (prefix === null || prefix === undefined) {
        prefix = defaultPrefix;
    }
    if (typeof value !== 'string') {
        throw fault('Expected `string` for value, got `%s`', value);
    }
    secondBest = {
        relevance: 0,
        language: null,
        value: []
    };
    result = {
        relevance: 0,
        language: null,
        value: []
    };
    while(++index < length){
        name = subset[index];
        if (!high.getLanguage(name)) {
            continue;
        }
        current = highlight(name, value, options);
        current.language = name;
        if (current.relevance > secondBest.relevance) {
            secondBest = current;
        }
        if (current.relevance > result.relevance) {
            secondBest = result;
            result = current;
        }
    }
    if (secondBest.language) {
        result.secondBest = secondBest;
    }
    return result;
}
// Register a language.
function registerLanguage(name, syntax) {
    high.registerLanguage(name, syntax);
}
// Get a list of all registered languages.
function listLanguages() {
    return high.listLanguages();
}
// Register more aliases for an already registered language.
function registerAlias(name, alias) {
    var map = name;
    var key;
    if (alias) {
        map = {};
        map[name] = alias;
    }
    for(key in map){
        high.registerAliases(map[key], {
            languageName: key
        });
    }
}
function Emitter(options) {
    this.options = options;
    this.rootNode = {
        children: []
    };
    this.stack = [
        this.rootNode
    ];
}
function addKeyword(value, name) {
    this.openNode(name);
    this.addText(value);
    this.closeNode();
}
function addSublanguage(other, name) {
    var stack = this.stack;
    var current = stack[stack.length - 1];
    var results = other.rootNode.children;
    var node = name ? {
        type: 'element',
        tagName: 'span',
        properties: {
            className: [
                name
            ]
        },
        children: results
    } : results;
    current.children = current.children.concat(node);
}
function text(value) {
    var stack = this.stack;
    var current;
    var tail;
    if (value === '') return;
    current = stack[stack.length - 1];
    tail = current.children[current.children.length - 1];
    if (tail && tail.type === 'text') {
        tail.value += value;
    } else {
        current.children.push({
            type: 'text',
            value: value
        });
    }
}
function open(name) {
    var stack = this.stack;
    var className = this.options.classPrefix + name;
    var current = stack[stack.length - 1];
    var child = {
        type: 'element',
        tagName: 'span',
        properties: {
            className: [
                className
            ]
        },
        children: []
    };
    current.children.push(child);
    stack.push(child);
}
function close() {
    this.stack.pop();
}
function toHtmlNoop() {
    return '';
}
function noop() {}
}}),
"[project]/node_modules/lowlight/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
var low = __turbopack_context__.r("[project]/node_modules/lowlight/lib/core.js [app-client] (ecmascript)");
module.exports = low;
low.registerLanguage('1c', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/1c.js [app-client] (ecmascript)"));
low.registerLanguage('abnf', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/abnf.js [app-client] (ecmascript)"));
low.registerLanguage('accesslog', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/accesslog.js [app-client] (ecmascript)"));
low.registerLanguage('actionscript', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/actionscript.js [app-client] (ecmascript)"));
low.registerLanguage('ada', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/ada.js [app-client] (ecmascript)"));
low.registerLanguage('angelscript', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/angelscript.js [app-client] (ecmascript)"));
low.registerLanguage('apache', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/apache.js [app-client] (ecmascript)"));
low.registerLanguage('applescript', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/applescript.js [app-client] (ecmascript)"));
low.registerLanguage('arcade', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/arcade.js [app-client] (ecmascript)"));
low.registerLanguage('arduino', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/arduino.js [app-client] (ecmascript)"));
low.registerLanguage('armasm', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/armasm.js [app-client] (ecmascript)"));
low.registerLanguage('xml', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/xml.js [app-client] (ecmascript)"));
low.registerLanguage('asciidoc', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/asciidoc.js [app-client] (ecmascript)"));
low.registerLanguage('aspectj', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/aspectj.js [app-client] (ecmascript)"));
low.registerLanguage('autohotkey', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/autohotkey.js [app-client] (ecmascript)"));
low.registerLanguage('autoit', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/autoit.js [app-client] (ecmascript)"));
low.registerLanguage('avrasm', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/avrasm.js [app-client] (ecmascript)"));
low.registerLanguage('awk', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/awk.js [app-client] (ecmascript)"));
low.registerLanguage('axapta', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/axapta.js [app-client] (ecmascript)"));
low.registerLanguage('bash', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/bash.js [app-client] (ecmascript)"));
low.registerLanguage('basic', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/basic.js [app-client] (ecmascript)"));
low.registerLanguage('bnf', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/bnf.js [app-client] (ecmascript)"));
low.registerLanguage('brainfuck', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/brainfuck.js [app-client] (ecmascript)"));
low.registerLanguage('c-like', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/c-like.js [app-client] (ecmascript)"));
low.registerLanguage('c', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/c.js [app-client] (ecmascript)"));
low.registerLanguage('cal', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/cal.js [app-client] (ecmascript)"));
low.registerLanguage('capnproto', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/capnproto.js [app-client] (ecmascript)"));
low.registerLanguage('ceylon', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/ceylon.js [app-client] (ecmascript)"));
low.registerLanguage('clean', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/clean.js [app-client] (ecmascript)"));
low.registerLanguage('clojure', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/clojure.js [app-client] (ecmascript)"));
low.registerLanguage('clojure-repl', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/clojure-repl.js [app-client] (ecmascript)"));
low.registerLanguage('cmake', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/cmake.js [app-client] (ecmascript)"));
low.registerLanguage('coffeescript', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/coffeescript.js [app-client] (ecmascript)"));
low.registerLanguage('coq', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/coq.js [app-client] (ecmascript)"));
low.registerLanguage('cos', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/cos.js [app-client] (ecmascript)"));
low.registerLanguage('cpp', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/cpp.js [app-client] (ecmascript)"));
low.registerLanguage('crmsh', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/crmsh.js [app-client] (ecmascript)"));
low.registerLanguage('crystal', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/crystal.js [app-client] (ecmascript)"));
low.registerLanguage('csharp', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/csharp.js [app-client] (ecmascript)"));
low.registerLanguage('csp', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/csp.js [app-client] (ecmascript)"));
low.registerLanguage('css', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/css.js [app-client] (ecmascript)"));
low.registerLanguage('d', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/d.js [app-client] (ecmascript)"));
low.registerLanguage('markdown', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/markdown.js [app-client] (ecmascript)"));
low.registerLanguage('dart', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/dart.js [app-client] (ecmascript)"));
low.registerLanguage('delphi', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/delphi.js [app-client] (ecmascript)"));
low.registerLanguage('diff', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/diff.js [app-client] (ecmascript)"));
low.registerLanguage('django', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/django.js [app-client] (ecmascript)"));
low.registerLanguage('dns', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/dns.js [app-client] (ecmascript)"));
low.registerLanguage('dockerfile', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/dockerfile.js [app-client] (ecmascript)"));
low.registerLanguage('dos', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/dos.js [app-client] (ecmascript)"));
low.registerLanguage('dsconfig', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/dsconfig.js [app-client] (ecmascript)"));
low.registerLanguage('dts', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/dts.js [app-client] (ecmascript)"));
low.registerLanguage('dust', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/dust.js [app-client] (ecmascript)"));
low.registerLanguage('ebnf', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/ebnf.js [app-client] (ecmascript)"));
low.registerLanguage('elixir', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/elixir.js [app-client] (ecmascript)"));
low.registerLanguage('elm', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/elm.js [app-client] (ecmascript)"));
low.registerLanguage('ruby', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/ruby.js [app-client] (ecmascript)"));
low.registerLanguage('erb', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/erb.js [app-client] (ecmascript)"));
low.registerLanguage('erlang-repl', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/erlang-repl.js [app-client] (ecmascript)"));
low.registerLanguage('erlang', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/erlang.js [app-client] (ecmascript)"));
low.registerLanguage('excel', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/excel.js [app-client] (ecmascript)"));
low.registerLanguage('fix', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/fix.js [app-client] (ecmascript)"));
low.registerLanguage('flix', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/flix.js [app-client] (ecmascript)"));
low.registerLanguage('fortran', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/fortran.js [app-client] (ecmascript)"));
low.registerLanguage('fsharp', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/fsharp.js [app-client] (ecmascript)"));
low.registerLanguage('gams', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/gams.js [app-client] (ecmascript)"));
low.registerLanguage('gauss', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/gauss.js [app-client] (ecmascript)"));
low.registerLanguage('gcode', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/gcode.js [app-client] (ecmascript)"));
low.registerLanguage('gherkin', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/gherkin.js [app-client] (ecmascript)"));
low.registerLanguage('glsl', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/glsl.js [app-client] (ecmascript)"));
low.registerLanguage('gml', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/gml.js [app-client] (ecmascript)"));
low.registerLanguage('go', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/go.js [app-client] (ecmascript)"));
low.registerLanguage('golo', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/golo.js [app-client] (ecmascript)"));
low.registerLanguage('gradle', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/gradle.js [app-client] (ecmascript)"));
low.registerLanguage('groovy', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/groovy.js [app-client] (ecmascript)"));
low.registerLanguage('haml', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/haml.js [app-client] (ecmascript)"));
low.registerLanguage('handlebars', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/handlebars.js [app-client] (ecmascript)"));
low.registerLanguage('haskell', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/haskell.js [app-client] (ecmascript)"));
low.registerLanguage('haxe', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/haxe.js [app-client] (ecmascript)"));
low.registerLanguage('hsp', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/hsp.js [app-client] (ecmascript)"));
low.registerLanguage('htmlbars', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/htmlbars.js [app-client] (ecmascript)"));
low.registerLanguage('http', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/http.js [app-client] (ecmascript)"));
low.registerLanguage('hy', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/hy.js [app-client] (ecmascript)"));
low.registerLanguage('inform7', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/inform7.js [app-client] (ecmascript)"));
low.registerLanguage('ini', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/ini.js [app-client] (ecmascript)"));
low.registerLanguage('irpf90', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/irpf90.js [app-client] (ecmascript)"));
low.registerLanguage('isbl', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/isbl.js [app-client] (ecmascript)"));
low.registerLanguage('java', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/java.js [app-client] (ecmascript)"));
low.registerLanguage('javascript', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/javascript.js [app-client] (ecmascript)"));
low.registerLanguage('jboss-cli', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/jboss-cli.js [app-client] (ecmascript)"));
low.registerLanguage('json', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/json.js [app-client] (ecmascript)"));
low.registerLanguage('julia', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/julia.js [app-client] (ecmascript)"));
low.registerLanguage('julia-repl', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/julia-repl.js [app-client] (ecmascript)"));
low.registerLanguage('kotlin', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/kotlin.js [app-client] (ecmascript)"));
low.registerLanguage('lasso', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/lasso.js [app-client] (ecmascript)"));
low.registerLanguage('latex', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/latex.js [app-client] (ecmascript)"));
low.registerLanguage('ldif', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/ldif.js [app-client] (ecmascript)"));
low.registerLanguage('leaf', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/leaf.js [app-client] (ecmascript)"));
low.registerLanguage('less', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/less.js [app-client] (ecmascript)"));
low.registerLanguage('lisp', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/lisp.js [app-client] (ecmascript)"));
low.registerLanguage('livecodeserver', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/livecodeserver.js [app-client] (ecmascript)"));
low.registerLanguage('livescript', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/livescript.js [app-client] (ecmascript)"));
low.registerLanguage('llvm', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/llvm.js [app-client] (ecmascript)"));
low.registerLanguage('lsl', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/lsl.js [app-client] (ecmascript)"));
low.registerLanguage('lua', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/lua.js [app-client] (ecmascript)"));
low.registerLanguage('makefile', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/makefile.js [app-client] (ecmascript)"));
low.registerLanguage('mathematica', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/mathematica.js [app-client] (ecmascript)"));
low.registerLanguage('matlab', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/matlab.js [app-client] (ecmascript)"));
low.registerLanguage('maxima', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/maxima.js [app-client] (ecmascript)"));
low.registerLanguage('mel', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/mel.js [app-client] (ecmascript)"));
low.registerLanguage('mercury', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/mercury.js [app-client] (ecmascript)"));
low.registerLanguage('mipsasm', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/mipsasm.js [app-client] (ecmascript)"));
low.registerLanguage('mizar', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/mizar.js [app-client] (ecmascript)"));
low.registerLanguage('perl', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/perl.js [app-client] (ecmascript)"));
low.registerLanguage('mojolicious', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/mojolicious.js [app-client] (ecmascript)"));
low.registerLanguage('monkey', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/monkey.js [app-client] (ecmascript)"));
low.registerLanguage('moonscript', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/moonscript.js [app-client] (ecmascript)"));
low.registerLanguage('n1ql', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/n1ql.js [app-client] (ecmascript)"));
low.registerLanguage('nginx', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/nginx.js [app-client] (ecmascript)"));
low.registerLanguage('nim', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/nim.js [app-client] (ecmascript)"));
low.registerLanguage('nix', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/nix.js [app-client] (ecmascript)"));
low.registerLanguage('node-repl', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/node-repl.js [app-client] (ecmascript)"));
low.registerLanguage('nsis', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/nsis.js [app-client] (ecmascript)"));
low.registerLanguage('objectivec', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/objectivec.js [app-client] (ecmascript)"));
low.registerLanguage('ocaml', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/ocaml.js [app-client] (ecmascript)"));
low.registerLanguage('openscad', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/openscad.js [app-client] (ecmascript)"));
low.registerLanguage('oxygene', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/oxygene.js [app-client] (ecmascript)"));
low.registerLanguage('parser3', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/parser3.js [app-client] (ecmascript)"));
low.registerLanguage('pf', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/pf.js [app-client] (ecmascript)"));
low.registerLanguage('pgsql', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/pgsql.js [app-client] (ecmascript)"));
low.registerLanguage('php', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/php.js [app-client] (ecmascript)"));
low.registerLanguage('php-template', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/php-template.js [app-client] (ecmascript)"));
low.registerLanguage('plaintext', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/plaintext.js [app-client] (ecmascript)"));
low.registerLanguage('pony', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/pony.js [app-client] (ecmascript)"));
low.registerLanguage('powershell', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/powershell.js [app-client] (ecmascript)"));
low.registerLanguage('processing', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/processing.js [app-client] (ecmascript)"));
low.registerLanguage('profile', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/profile.js [app-client] (ecmascript)"));
low.registerLanguage('prolog', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/prolog.js [app-client] (ecmascript)"));
low.registerLanguage('properties', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/properties.js [app-client] (ecmascript)"));
low.registerLanguage('protobuf', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/protobuf.js [app-client] (ecmascript)"));
low.registerLanguage('puppet', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/puppet.js [app-client] (ecmascript)"));
low.registerLanguage('purebasic', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/purebasic.js [app-client] (ecmascript)"));
low.registerLanguage('python', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/python.js [app-client] (ecmascript)"));
low.registerLanguage('python-repl', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/python-repl.js [app-client] (ecmascript)"));
low.registerLanguage('q', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/q.js [app-client] (ecmascript)"));
low.registerLanguage('qml', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/qml.js [app-client] (ecmascript)"));
low.registerLanguage('r', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/r.js [app-client] (ecmascript)"));
low.registerLanguage('reasonml', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/reasonml.js [app-client] (ecmascript)"));
low.registerLanguage('rib', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/rib.js [app-client] (ecmascript)"));
low.registerLanguage('roboconf', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/roboconf.js [app-client] (ecmascript)"));
low.registerLanguage('routeros', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/routeros.js [app-client] (ecmascript)"));
low.registerLanguage('rsl', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/rsl.js [app-client] (ecmascript)"));
low.registerLanguage('ruleslanguage', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/ruleslanguage.js [app-client] (ecmascript)"));
low.registerLanguage('rust', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/rust.js [app-client] (ecmascript)"));
low.registerLanguage('sas', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/sas.js [app-client] (ecmascript)"));
low.registerLanguage('scala', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/scala.js [app-client] (ecmascript)"));
low.registerLanguage('scheme', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/scheme.js [app-client] (ecmascript)"));
low.registerLanguage('scilab', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/scilab.js [app-client] (ecmascript)"));
low.registerLanguage('scss', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/scss.js [app-client] (ecmascript)"));
low.registerLanguage('shell', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/shell.js [app-client] (ecmascript)"));
low.registerLanguage('smali', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/smali.js [app-client] (ecmascript)"));
low.registerLanguage('smalltalk', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/smalltalk.js [app-client] (ecmascript)"));
low.registerLanguage('sml', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/sml.js [app-client] (ecmascript)"));
low.registerLanguage('sqf', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/sqf.js [app-client] (ecmascript)"));
low.registerLanguage('sql_more', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/sql_more.js [app-client] (ecmascript)"));
low.registerLanguage('sql', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/sql.js [app-client] (ecmascript)"));
low.registerLanguage('stan', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/stan.js [app-client] (ecmascript)"));
low.registerLanguage('stata', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/stata.js [app-client] (ecmascript)"));
low.registerLanguage('step21', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/step21.js [app-client] (ecmascript)"));
low.registerLanguage('stylus', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/stylus.js [app-client] (ecmascript)"));
low.registerLanguage('subunit', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/subunit.js [app-client] (ecmascript)"));
low.registerLanguage('swift', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/swift.js [app-client] (ecmascript)"));
low.registerLanguage('taggerscript', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/taggerscript.js [app-client] (ecmascript)"));
low.registerLanguage('yaml', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/yaml.js [app-client] (ecmascript)"));
low.registerLanguage('tap', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/tap.js [app-client] (ecmascript)"));
low.registerLanguage('tcl', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/tcl.js [app-client] (ecmascript)"));
low.registerLanguage('thrift', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/thrift.js [app-client] (ecmascript)"));
low.registerLanguage('tp', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/tp.js [app-client] (ecmascript)"));
low.registerLanguage('twig', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/twig.js [app-client] (ecmascript)"));
low.registerLanguage('typescript', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/typescript.js [app-client] (ecmascript)"));
low.registerLanguage('vala', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/vala.js [app-client] (ecmascript)"));
low.registerLanguage('vbnet', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/vbnet.js [app-client] (ecmascript)"));
low.registerLanguage('vbscript', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/vbscript.js [app-client] (ecmascript)"));
low.registerLanguage('vbscript-html', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/vbscript-html.js [app-client] (ecmascript)"));
low.registerLanguage('verilog', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/verilog.js [app-client] (ecmascript)"));
low.registerLanguage('vhdl', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/vhdl.js [app-client] (ecmascript)"));
low.registerLanguage('vim', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/vim.js [app-client] (ecmascript)"));
low.registerLanguage('x86asm', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/x86asm.js [app-client] (ecmascript)"));
low.registerLanguage('xl', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/xl.js [app-client] (ecmascript)"));
low.registerLanguage('xquery', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/xquery.js [app-client] (ecmascript)"));
low.registerLanguage('zephir', __turbopack_context__.r("[project]/node_modules/highlight.js/lib/languages/zephir.js [app-client] (ecmascript)"));
}}),
"[project]/node_modules/xtend/immutable.js [app-client] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
module.exports = extend;
var hasOwnProperty = Object.prototype.hasOwnProperty;
function extend() {
    var target = {};
    for(var i = 0; i < arguments.length; i++){
        var source = arguments[i];
        for(var key in source){
            if (hasOwnProperty.call(source, key)) {
                target[key] = source[key];
            }
        }
    }
    return target;
}
}}),
"[project]/node_modules/property-information/lib/util/schema.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
module.exports = Schema;
var proto = Schema.prototype;
proto.space = null;
proto.normal = {};
proto.property = {};
function Schema(property, normal, space) {
    this.property = property;
    this.normal = normal;
    if (space) {
        this.space = space;
    }
}
}}),
"[project]/node_modules/property-information/lib/util/merge.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
var xtend = __turbopack_context__.r("[project]/node_modules/xtend/immutable.js [app-client] (ecmascript)");
var Schema = __turbopack_context__.r("[project]/node_modules/property-information/lib/util/schema.js [app-client] (ecmascript)");
module.exports = merge;
function merge(definitions) {
    var length = definitions.length;
    var property = [];
    var normal = [];
    var index = -1;
    var info;
    var space;
    while(++index < length){
        info = definitions[index];
        property.push(info.property);
        normal.push(info.normal);
        space = info.space;
    }
    return new Schema(xtend.apply(null, property), xtend.apply(null, normal), space);
}
}}),
"[project]/node_modules/property-information/normalize.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
module.exports = normalize;
function normalize(value) {
    return value.toLowerCase();
}
}}),
"[project]/node_modules/property-information/lib/util/info.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
module.exports = Info;
var proto = Info.prototype;
proto.space = null;
proto.attribute = null;
proto.property = null;
proto.boolean = false;
proto.booleanish = false;
proto.overloadedBoolean = false;
proto.number = false;
proto.commaSeparated = false;
proto.spaceSeparated = false;
proto.commaOrSpaceSeparated = false;
proto.mustUseProperty = false;
proto.defined = false;
function Info(property, attribute) {
    this.property = property;
    this.attribute = attribute;
}
}}),
"[project]/node_modules/property-information/lib/util/types.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
var powers = 0;
exports.boolean = increment();
exports.booleanish = increment();
exports.overloadedBoolean = increment();
exports.number = increment();
exports.spaceSeparated = increment();
exports.commaSeparated = increment();
exports.commaOrSpaceSeparated = increment();
function increment() {
    return Math.pow(2, ++powers);
}
}}),
"[project]/node_modules/property-information/lib/util/defined-info.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
var Info = __turbopack_context__.r("[project]/node_modules/property-information/lib/util/info.js [app-client] (ecmascript)");
var types = __turbopack_context__.r("[project]/node_modules/property-information/lib/util/types.js [app-client] (ecmascript)");
module.exports = DefinedInfo;
DefinedInfo.prototype = new Info();
DefinedInfo.prototype.defined = true;
var checks = [
    'boolean',
    'booleanish',
    'overloadedBoolean',
    'number',
    'commaSeparated',
    'spaceSeparated',
    'commaOrSpaceSeparated'
];
var checksLength = checks.length;
function DefinedInfo(property, attribute, mask, space) {
    var index = -1;
    var check;
    mark(this, 'space', space);
    Info.call(this, property, attribute);
    while(++index < checksLength){
        check = checks[index];
        mark(this, check, (mask & types[check]) === types[check]);
    }
}
function mark(values, key, value) {
    if (value) {
        values[key] = value;
    }
}
}}),
"[project]/node_modules/property-information/lib/util/create.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
var normalize = __turbopack_context__.r("[project]/node_modules/property-information/normalize.js [app-client] (ecmascript)");
var Schema = __turbopack_context__.r("[project]/node_modules/property-information/lib/util/schema.js [app-client] (ecmascript)");
var DefinedInfo = __turbopack_context__.r("[project]/node_modules/property-information/lib/util/defined-info.js [app-client] (ecmascript)");
module.exports = create;
function create(definition) {
    var space = definition.space;
    var mustUseProperty = definition.mustUseProperty || [];
    var attributes = definition.attributes || {};
    var props = definition.properties;
    var transform = definition.transform;
    var property = {};
    var normal = {};
    var prop;
    var info;
    for(prop in props){
        info = new DefinedInfo(prop, transform(attributes, prop), props[prop], space);
        if (mustUseProperty.indexOf(prop) !== -1) {
            info.mustUseProperty = true;
        }
        property[prop] = info;
        normal[normalize(prop)] = prop;
        normal[normalize(info.attribute)] = prop;
    }
    return new Schema(property, normal, space);
}
}}),
"[project]/node_modules/property-information/lib/xlink.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
var create = __turbopack_context__.r("[project]/node_modules/property-information/lib/util/create.js [app-client] (ecmascript)");
module.exports = create({
    space: 'xlink',
    transform: xlinkTransform,
    properties: {
        xLinkActuate: null,
        xLinkArcRole: null,
        xLinkHref: null,
        xLinkRole: null,
        xLinkShow: null,
        xLinkTitle: null,
        xLinkType: null
    }
});
function xlinkTransform(_, prop) {
    return 'xlink:' + prop.slice(5).toLowerCase();
}
}}),
"[project]/node_modules/property-information/lib/xml.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
var create = __turbopack_context__.r("[project]/node_modules/property-information/lib/util/create.js [app-client] (ecmascript)");
module.exports = create({
    space: 'xml',
    transform: xmlTransform,
    properties: {
        xmlLang: null,
        xmlBase: null,
        xmlSpace: null
    }
});
function xmlTransform(_, prop) {
    return 'xml:' + prop.slice(3).toLowerCase();
}
}}),
"[project]/node_modules/property-information/lib/util/case-sensitive-transform.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
module.exports = caseSensitiveTransform;
function caseSensitiveTransform(attributes, attribute) {
    return attribute in attributes ? attributes[attribute] : attribute;
}
}}),
"[project]/node_modules/property-information/lib/util/case-insensitive-transform.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
var caseSensitiveTransform = __turbopack_context__.r("[project]/node_modules/property-information/lib/util/case-sensitive-transform.js [app-client] (ecmascript)");
module.exports = caseInsensitiveTransform;
function caseInsensitiveTransform(attributes, property) {
    return caseSensitiveTransform(attributes, property.toLowerCase());
}
}}),
"[project]/node_modules/property-information/lib/xmlns.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
var create = __turbopack_context__.r("[project]/node_modules/property-information/lib/util/create.js [app-client] (ecmascript)");
var caseInsensitiveTransform = __turbopack_context__.r("[project]/node_modules/property-information/lib/util/case-insensitive-transform.js [app-client] (ecmascript)");
module.exports = create({
    space: 'xmlns',
    attributes: {
        xmlnsxlink: 'xmlns:xlink'
    },
    transform: caseInsensitiveTransform,
    properties: {
        xmlns: null,
        xmlnsXLink: null
    }
});
}}),
"[project]/node_modules/property-information/lib/aria.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
var types = __turbopack_context__.r("[project]/node_modules/property-information/lib/util/types.js [app-client] (ecmascript)");
var create = __turbopack_context__.r("[project]/node_modules/property-information/lib/util/create.js [app-client] (ecmascript)");
var booleanish = types.booleanish;
var number = types.number;
var spaceSeparated = types.spaceSeparated;
module.exports = create({
    transform: ariaTransform,
    properties: {
        ariaActiveDescendant: null,
        ariaAtomic: booleanish,
        ariaAutoComplete: null,
        ariaBusy: booleanish,
        ariaChecked: booleanish,
        ariaColCount: number,
        ariaColIndex: number,
        ariaColSpan: number,
        ariaControls: spaceSeparated,
        ariaCurrent: null,
        ariaDescribedBy: spaceSeparated,
        ariaDetails: null,
        ariaDisabled: booleanish,
        ariaDropEffect: spaceSeparated,
        ariaErrorMessage: null,
        ariaExpanded: booleanish,
        ariaFlowTo: spaceSeparated,
        ariaGrabbed: booleanish,
        ariaHasPopup: null,
        ariaHidden: booleanish,
        ariaInvalid: null,
        ariaKeyShortcuts: null,
        ariaLabel: null,
        ariaLabelledBy: spaceSeparated,
        ariaLevel: number,
        ariaLive: null,
        ariaModal: booleanish,
        ariaMultiLine: booleanish,
        ariaMultiSelectable: booleanish,
        ariaOrientation: null,
        ariaOwns: spaceSeparated,
        ariaPlaceholder: null,
        ariaPosInSet: number,
        ariaPressed: booleanish,
        ariaReadOnly: booleanish,
        ariaRelevant: null,
        ariaRequired: booleanish,
        ariaRoleDescription: spaceSeparated,
        ariaRowCount: number,
        ariaRowIndex: number,
        ariaRowSpan: number,
        ariaSelected: booleanish,
        ariaSetSize: number,
        ariaSort: null,
        ariaValueMax: number,
        ariaValueMin: number,
        ariaValueNow: number,
        ariaValueText: null,
        role: null
    }
});
function ariaTransform(_, prop) {
    return prop === 'role' ? prop : 'aria-' + prop.slice(4).toLowerCase();
}
}}),
"[project]/node_modules/property-information/lib/html.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
var types = __turbopack_context__.r("[project]/node_modules/property-information/lib/util/types.js [app-client] (ecmascript)");
var create = __turbopack_context__.r("[project]/node_modules/property-information/lib/util/create.js [app-client] (ecmascript)");
var caseInsensitiveTransform = __turbopack_context__.r("[project]/node_modules/property-information/lib/util/case-insensitive-transform.js [app-client] (ecmascript)");
var boolean = types.boolean;
var overloadedBoolean = types.overloadedBoolean;
var booleanish = types.booleanish;
var number = types.number;
var spaceSeparated = types.spaceSeparated;
var commaSeparated = types.commaSeparated;
module.exports = create({
    space: 'html',
    attributes: {
        acceptcharset: 'accept-charset',
        classname: 'class',
        htmlfor: 'for',
        httpequiv: 'http-equiv'
    },
    transform: caseInsensitiveTransform,
    mustUseProperty: [
        'checked',
        'multiple',
        'muted',
        'selected'
    ],
    properties: {
        // Standard Properties.
        abbr: null,
        accept: commaSeparated,
        acceptCharset: spaceSeparated,
        accessKey: spaceSeparated,
        action: null,
        allow: null,
        allowFullScreen: boolean,
        allowPaymentRequest: boolean,
        allowUserMedia: boolean,
        alt: null,
        as: null,
        async: boolean,
        autoCapitalize: null,
        autoComplete: spaceSeparated,
        autoFocus: boolean,
        autoPlay: boolean,
        capture: boolean,
        charSet: null,
        checked: boolean,
        cite: null,
        className: spaceSeparated,
        cols: number,
        colSpan: null,
        content: null,
        contentEditable: booleanish,
        controls: boolean,
        controlsList: spaceSeparated,
        coords: number | commaSeparated,
        crossOrigin: null,
        data: null,
        dateTime: null,
        decoding: null,
        default: boolean,
        defer: boolean,
        dir: null,
        dirName: null,
        disabled: boolean,
        download: overloadedBoolean,
        draggable: booleanish,
        encType: null,
        enterKeyHint: null,
        form: null,
        formAction: null,
        formEncType: null,
        formMethod: null,
        formNoValidate: boolean,
        formTarget: null,
        headers: spaceSeparated,
        height: number,
        hidden: boolean,
        high: number,
        href: null,
        hrefLang: null,
        htmlFor: spaceSeparated,
        httpEquiv: spaceSeparated,
        id: null,
        imageSizes: null,
        imageSrcSet: commaSeparated,
        inputMode: null,
        integrity: null,
        is: null,
        isMap: boolean,
        itemId: null,
        itemProp: spaceSeparated,
        itemRef: spaceSeparated,
        itemScope: boolean,
        itemType: spaceSeparated,
        kind: null,
        label: null,
        lang: null,
        language: null,
        list: null,
        loading: null,
        loop: boolean,
        low: number,
        manifest: null,
        max: null,
        maxLength: number,
        media: null,
        method: null,
        min: null,
        minLength: number,
        multiple: boolean,
        muted: boolean,
        name: null,
        nonce: null,
        noModule: boolean,
        noValidate: boolean,
        onAbort: null,
        onAfterPrint: null,
        onAuxClick: null,
        onBeforePrint: null,
        onBeforeUnload: null,
        onBlur: null,
        onCancel: null,
        onCanPlay: null,
        onCanPlayThrough: null,
        onChange: null,
        onClick: null,
        onClose: null,
        onContextMenu: null,
        onCopy: null,
        onCueChange: null,
        onCut: null,
        onDblClick: null,
        onDrag: null,
        onDragEnd: null,
        onDragEnter: null,
        onDragExit: null,
        onDragLeave: null,
        onDragOver: null,
        onDragStart: null,
        onDrop: null,
        onDurationChange: null,
        onEmptied: null,
        onEnded: null,
        onError: null,
        onFocus: null,
        onFormData: null,
        onHashChange: null,
        onInput: null,
        onInvalid: null,
        onKeyDown: null,
        onKeyPress: null,
        onKeyUp: null,
        onLanguageChange: null,
        onLoad: null,
        onLoadedData: null,
        onLoadedMetadata: null,
        onLoadEnd: null,
        onLoadStart: null,
        onMessage: null,
        onMessageError: null,
        onMouseDown: null,
        onMouseEnter: null,
        onMouseLeave: null,
        onMouseMove: null,
        onMouseOut: null,
        onMouseOver: null,
        onMouseUp: null,
        onOffline: null,
        onOnline: null,
        onPageHide: null,
        onPageShow: null,
        onPaste: null,
        onPause: null,
        onPlay: null,
        onPlaying: null,
        onPopState: null,
        onProgress: null,
        onRateChange: null,
        onRejectionHandled: null,
        onReset: null,
        onResize: null,
        onScroll: null,
        onSecurityPolicyViolation: null,
        onSeeked: null,
        onSeeking: null,
        onSelect: null,
        onSlotChange: null,
        onStalled: null,
        onStorage: null,
        onSubmit: null,
        onSuspend: null,
        onTimeUpdate: null,
        onToggle: null,
        onUnhandledRejection: null,
        onUnload: null,
        onVolumeChange: null,
        onWaiting: null,
        onWheel: null,
        open: boolean,
        optimum: number,
        pattern: null,
        ping: spaceSeparated,
        placeholder: null,
        playsInline: boolean,
        poster: null,
        preload: null,
        readOnly: boolean,
        referrerPolicy: null,
        rel: spaceSeparated,
        required: boolean,
        reversed: boolean,
        rows: number,
        rowSpan: number,
        sandbox: spaceSeparated,
        scope: null,
        scoped: boolean,
        seamless: boolean,
        selected: boolean,
        shape: null,
        size: number,
        sizes: null,
        slot: null,
        span: number,
        spellCheck: booleanish,
        src: null,
        srcDoc: null,
        srcLang: null,
        srcSet: commaSeparated,
        start: number,
        step: null,
        style: null,
        tabIndex: number,
        target: null,
        title: null,
        translate: null,
        type: null,
        typeMustMatch: boolean,
        useMap: null,
        value: booleanish,
        width: number,
        wrap: null,
        // Legacy.
        // See: https://html.spec.whatwg.org/#other-elements,-attributes-and-apis
        align: null,
        aLink: null,
        archive: spaceSeparated,
        axis: null,
        background: null,
        bgColor: null,
        border: number,
        borderColor: null,
        bottomMargin: number,
        cellPadding: null,
        cellSpacing: null,
        char: null,
        charOff: null,
        classId: null,
        clear: null,
        code: null,
        codeBase: null,
        codeType: null,
        color: null,
        compact: boolean,
        declare: boolean,
        event: null,
        face: null,
        frame: null,
        frameBorder: null,
        hSpace: number,
        leftMargin: number,
        link: null,
        longDesc: null,
        lowSrc: null,
        marginHeight: number,
        marginWidth: number,
        noResize: boolean,
        noHref: boolean,
        noShade: boolean,
        noWrap: boolean,
        object: null,
        profile: null,
        prompt: null,
        rev: null,
        rightMargin: number,
        rules: null,
        scheme: null,
        scrolling: booleanish,
        standby: null,
        summary: null,
        text: null,
        topMargin: number,
        valueType: null,
        version: null,
        vAlign: null,
        vLink: null,
        vSpace: number,
        // Non-standard Properties.
        allowTransparency: null,
        autoCorrect: null,
        autoSave: null,
        disablePictureInPicture: boolean,
        disableRemotePlayback: boolean,
        prefix: null,
        property: null,
        results: number,
        security: null,
        unselectable: null
    }
});
}}),
"[project]/node_modules/property-information/html.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
var merge = __turbopack_context__.r("[project]/node_modules/property-information/lib/util/merge.js [app-client] (ecmascript)");
var xlink = __turbopack_context__.r("[project]/node_modules/property-information/lib/xlink.js [app-client] (ecmascript)");
var xml = __turbopack_context__.r("[project]/node_modules/property-information/lib/xml.js [app-client] (ecmascript)");
var xmlns = __turbopack_context__.r("[project]/node_modules/property-information/lib/xmlns.js [app-client] (ecmascript)");
var aria = __turbopack_context__.r("[project]/node_modules/property-information/lib/aria.js [app-client] (ecmascript)");
var html = __turbopack_context__.r("[project]/node_modules/property-information/lib/html.js [app-client] (ecmascript)");
module.exports = merge([
    xml,
    xlink,
    xmlns,
    aria,
    html
]);
}}),
"[project]/node_modules/property-information/find.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
var normalize = __turbopack_context__.r("[project]/node_modules/property-information/normalize.js [app-client] (ecmascript)");
var DefinedInfo = __turbopack_context__.r("[project]/node_modules/property-information/lib/util/defined-info.js [app-client] (ecmascript)");
var Info = __turbopack_context__.r("[project]/node_modules/property-information/lib/util/info.js [app-client] (ecmascript)");
var data = 'data';
module.exports = find;
var valid = /^data[-\w.:]+$/i;
var dash = /-[a-z]/g;
var cap = /[A-Z]/g;
function find(schema, value) {
    var normal = normalize(value);
    var prop = value;
    var Type = Info;
    if (normal in schema.normal) {
        return schema.property[schema.normal[normal]];
    }
    if (normal.length > 4 && normal.slice(0, 4) === data && valid.test(value)) {
        // Attribute or property.
        if (value.charAt(4) === '-') {
            prop = datasetToProperty(value);
        } else {
            value = datasetToAttribute(value);
        }
        Type = DefinedInfo;
    }
    return new Type(prop, value);
}
function datasetToProperty(attribute) {
    var value = attribute.slice(5).replace(dash, camelcase);
    return data + value.charAt(0).toUpperCase() + value.slice(1);
}
function datasetToAttribute(property) {
    var value = property.slice(4);
    if (dash.test(value)) {
        return property;
    }
    value = value.replace(cap, kebab);
    if (value.charAt(0) !== '-') {
        value = '-' + value;
    }
    return data + value;
}
function kebab($0) {
    return '-' + $0.toLowerCase();
}
function camelcase($0) {
    return $0.charAt(1).toUpperCase();
}
}}),
"[project]/node_modules/hast-util-parse-selector/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
module.exports = parse;
var search = /[#.]/g;
// Create a hast element from a simple CSS selector.
function parse(selector, defaultTagName) {
    var value = selector || '';
    var name = defaultTagName || 'div';
    var props = {};
    var start = 0;
    var subvalue;
    var previous;
    var match;
    while(start < value.length){
        search.lastIndex = start;
        match = search.exec(value);
        subvalue = value.slice(start, match ? match.index : value.length);
        if (subvalue) {
            if (!previous) {
                name = subvalue;
            } else if (previous === '#') {
                props.id = subvalue;
            } else if (props.className) {
                props.className.push(subvalue);
            } else {
                props.className = [
                    subvalue
                ];
            }
            start += subvalue.length;
        }
        if (match) {
            previous = match[0];
            start++;
        }
    }
    return {
        type: 'element',
        tagName: name,
        properties: props,
        children: []
    };
}
}}),
"[project]/node_modules/space-separated-tokens/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
exports.parse = parse;
exports.stringify = stringify;
var empty = '';
var space = ' ';
var whiteSpace = /[ \t\n\r\f]+/g;
function parse(value) {
    var input = String(value || empty).trim();
    return input === empty ? [] : input.split(whiteSpace);
}
function stringify(values) {
    return values.join(space).trim();
}
}}),
"[project]/node_modules/comma-separated-tokens/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
exports.parse = parse;
exports.stringify = stringify;
var comma = ',';
var space = ' ';
var empty = '';
// Parse comma-separated tokens to an array.
function parse(value) {
    var values = [];
    var input = String(value || empty);
    var index = input.indexOf(comma);
    var lastIndex = 0;
    var end = false;
    var val;
    while(!end){
        if (index === -1) {
            index = input.length;
            end = true;
        }
        val = input.slice(lastIndex, index).trim();
        if (val || !end) {
            values.push(val);
        }
        lastIndex = index + 1;
        index = input.indexOf(comma, lastIndex);
    }
    return values;
}
// Compile an array to comma-separated tokens.
// `options.padLeft` (default: `true`) pads a space left of each token, and
// `options.padRight` (default: `false`) pads a space to the right of each token.
function stringify(values, options) {
    var settings = options || {};
    var left = settings.padLeft === false ? empty : space;
    var right = settings.padRight ? space : empty;
    // Ensure the last empty entry is seen.
    if (values[values.length - 1] === empty) {
        values = values.concat(empty);
    }
    return values.join(right + comma + left).trim();
}
}}),
"[project]/node_modules/hastscript/factory.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
var find = __turbopack_context__.r("[project]/node_modules/property-information/find.js [app-client] (ecmascript)");
var normalize = __turbopack_context__.r("[project]/node_modules/property-information/normalize.js [app-client] (ecmascript)");
var parseSelector = __turbopack_context__.r("[project]/node_modules/hast-util-parse-selector/index.js [app-client] (ecmascript)");
var spaces = __turbopack_context__.r("[project]/node_modules/space-separated-tokens/index.js [app-client] (ecmascript)").parse;
var commas = __turbopack_context__.r("[project]/node_modules/comma-separated-tokens/index.js [app-client] (ecmascript)").parse;
module.exports = factory;
var own = {}.hasOwnProperty;
function factory(schema, defaultTagName, caseSensitive) {
    var adjust = caseSensitive ? createAdjustMap(caseSensitive) : null;
    return h;
    //TURBOPACK unreachable
    ;
    // Hyperscript compatible DSL for creating virtual hast trees.
    function h(selector, properties) {
        var node = parseSelector(selector, defaultTagName);
        var children = Array.prototype.slice.call(arguments, 2);
        var name = node.tagName.toLowerCase();
        var property;
        node.tagName = adjust && own.call(adjust, name) ? adjust[name] : name;
        if (properties && isChildren(properties, node)) {
            children.unshift(properties);
            properties = null;
        }
        if (properties) {
            for(property in properties){
                addProperty(node.properties, property, properties[property]);
            }
        }
        addChild(node.children, children);
        if (node.tagName === 'template') {
            node.content = {
                type: 'root',
                children: node.children
            };
            node.children = [];
        }
        return node;
    }
    function addProperty(properties, key, value) {
        var info;
        var property;
        var result;
        // Ignore nullish and NaN values.
        if (value === null || value === undefined || value !== value) {
            return;
        }
        info = find(schema, key);
        property = info.property;
        result = value;
        // Handle list values.
        if (typeof result === 'string') {
            if (info.spaceSeparated) {
                result = spaces(result);
            } else if (info.commaSeparated) {
                result = commas(result);
            } else if (info.commaOrSpaceSeparated) {
                result = spaces(commas(result).join(' '));
            }
        }
        // Accept `object` on style.
        if (property === 'style' && typeof value !== 'string') {
            result = style(result);
        }
        // Class-names (which can be added both on the `selector` and here).
        if (property === 'className' && properties.className) {
            result = properties.className.concat(result);
        }
        properties[property] = parsePrimitives(info, property, result);
    }
}
function isChildren(value, node) {
    return typeof value === 'string' || 'length' in value || isNode(node.tagName, value);
}
function isNode(tagName, value) {
    var type = value.type;
    if (tagName === 'input' || !type || typeof type !== 'string') {
        return false;
    }
    if (typeof value.children === 'object' && 'length' in value.children) {
        return true;
    }
    type = type.toLowerCase();
    if (tagName === 'button') {
        return type !== 'menu' && type !== 'submit' && type !== 'reset' && type !== 'button';
    }
    return 'value' in value;
}
function addChild(nodes, value) {
    var index;
    var length;
    if (typeof value === 'string' || typeof value === 'number') {
        nodes.push({
            type: 'text',
            value: String(value)
        });
        return;
    }
    if (typeof value === 'object' && 'length' in value) {
        index = -1;
        length = value.length;
        while(++index < length){
            addChild(nodes, value[index]);
        }
        return;
    }
    if (typeof value !== 'object' || !('type' in value)) {
        throw new Error('Expected node, nodes, or string, got `' + value + '`');
    }
    nodes.push(value);
}
// Parse a (list of) primitives.
function parsePrimitives(info, name, value) {
    var index;
    var length;
    var result;
    if (typeof value !== 'object' || !('length' in value)) {
        return parsePrimitive(info, name, value);
    }
    length = value.length;
    index = -1;
    result = [];
    while(++index < length){
        result[index] = parsePrimitive(info, name, value[index]);
    }
    return result;
}
// Parse a single primitives.
function parsePrimitive(info, name, value) {
    var result = value;
    if (info.number || info.positiveNumber) {
        if (!isNaN(result) && result !== '') {
            result = Number(result);
        }
    } else if (info.boolean || info.overloadedBoolean) {
        // Accept `boolean` and `string`.
        if (typeof result === 'string' && (result === '' || normalize(value) === normalize(name))) {
            result = true;
        }
    }
    return result;
}
function style(value) {
    var result = [];
    var key;
    for(key in value){
        result.push([
            key,
            value[key]
        ].join(': '));
    }
    return result.join('; ');
}
function createAdjustMap(values) {
    var length = values.length;
    var index = -1;
    var result = {};
    var value;
    while(++index < length){
        value = values[index];
        result[value.toLowerCase()] = value;
    }
    return result;
}
}}),
"[project]/node_modules/hastscript/html.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
var schema = __turbopack_context__.r("[project]/node_modules/property-information/html.js [app-client] (ecmascript)");
var factory = __turbopack_context__.r("[project]/node_modules/hastscript/factory.js [app-client] (ecmascript)");
var html = factory(schema, 'div');
html.displayName = 'html';
module.exports = html;
}}),
"[project]/node_modules/hastscript/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
module.exports = __turbopack_context__.r("[project]/node_modules/hastscript/html.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/character-entities-legacy/index.json (json)": ((__turbopack_context__) => {

__turbopack_context__.v(JSON.parse("{\"AElig\":\"Æ\",\"AMP\":\"&\",\"Aacute\":\"Á\",\"Acirc\":\"Â\",\"Agrave\":\"À\",\"Aring\":\"Å\",\"Atilde\":\"Ã\",\"Auml\":\"Ä\",\"COPY\":\"©\",\"Ccedil\":\"Ç\",\"ETH\":\"Ð\",\"Eacute\":\"É\",\"Ecirc\":\"Ê\",\"Egrave\":\"È\",\"Euml\":\"Ë\",\"GT\":\">\",\"Iacute\":\"Í\",\"Icirc\":\"Î\",\"Igrave\":\"Ì\",\"Iuml\":\"Ï\",\"LT\":\"<\",\"Ntilde\":\"Ñ\",\"Oacute\":\"Ó\",\"Ocirc\":\"Ô\",\"Ograve\":\"Ò\",\"Oslash\":\"Ø\",\"Otilde\":\"Õ\",\"Ouml\":\"Ö\",\"QUOT\":\"\\\"\",\"REG\":\"®\",\"THORN\":\"Þ\",\"Uacute\":\"Ú\",\"Ucirc\":\"Û\",\"Ugrave\":\"Ù\",\"Uuml\":\"Ü\",\"Yacute\":\"Ý\",\"aacute\":\"á\",\"acirc\":\"â\",\"acute\":\"´\",\"aelig\":\"æ\",\"agrave\":\"à\",\"amp\":\"&\",\"aring\":\"å\",\"atilde\":\"ã\",\"auml\":\"ä\",\"brvbar\":\"¦\",\"ccedil\":\"ç\",\"cedil\":\"¸\",\"cent\":\"¢\",\"copy\":\"©\",\"curren\":\"¤\",\"deg\":\"°\",\"divide\":\"÷\",\"eacute\":\"é\",\"ecirc\":\"ê\",\"egrave\":\"è\",\"eth\":\"ð\",\"euml\":\"ë\",\"frac12\":\"½\",\"frac14\":\"¼\",\"frac34\":\"¾\",\"gt\":\">\",\"iacute\":\"í\",\"icirc\":\"î\",\"iexcl\":\"¡\",\"igrave\":\"ì\",\"iquest\":\"¿\",\"iuml\":\"ï\",\"laquo\":\"«\",\"lt\":\"<\",\"macr\":\"¯\",\"micro\":\"µ\",\"middot\":\"·\",\"nbsp\":\" \",\"not\":\"¬\",\"ntilde\":\"ñ\",\"oacute\":\"ó\",\"ocirc\":\"ô\",\"ograve\":\"ò\",\"ordf\":\"ª\",\"ordm\":\"º\",\"oslash\":\"ø\",\"otilde\":\"õ\",\"ouml\":\"ö\",\"para\":\"¶\",\"plusmn\":\"±\",\"pound\":\"£\",\"quot\":\"\\\"\",\"raquo\":\"»\",\"reg\":\"®\",\"sect\":\"§\",\"shy\":\"­\",\"sup1\":\"¹\",\"sup2\":\"²\",\"sup3\":\"³\",\"szlig\":\"ß\",\"thorn\":\"þ\",\"times\":\"×\",\"uacute\":\"ú\",\"ucirc\":\"û\",\"ugrave\":\"ù\",\"uml\":\"¨\",\"uuml\":\"ü\",\"yacute\":\"ý\",\"yen\":\"¥\",\"yuml\":\"ÿ\"}"));}),
"[project]/node_modules/character-reference-invalid/index.json (json)": ((__turbopack_context__) => {

__turbopack_context__.v(JSON.parse("{\"0\":\"�\",\"128\":\"€\",\"130\":\"‚\",\"131\":\"ƒ\",\"132\":\"„\",\"133\":\"…\",\"134\":\"†\",\"135\":\"‡\",\"136\":\"ˆ\",\"137\":\"‰\",\"138\":\"Š\",\"139\":\"‹\",\"140\":\"Œ\",\"142\":\"Ž\",\"145\":\"‘\",\"146\":\"’\",\"147\":\"“\",\"148\":\"”\",\"149\":\"•\",\"150\":\"–\",\"151\":\"—\",\"152\":\"˜\",\"153\":\"™\",\"154\":\"š\",\"155\":\"›\",\"156\":\"œ\",\"158\":\"ž\",\"159\":\"Ÿ\"}"));}),
"[project]/node_modules/is-decimal/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
module.exports = decimal;
// Check if the given character code, or the character code at the first
// character, is decimal.
function decimal(character) {
    var code = typeof character === 'string' ? character.charCodeAt(0) : character;
    return code >= 48 && code <= 57 /* 0-9 */ ;
}
}}),
"[project]/node_modules/is-hexadecimal/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
module.exports = hexadecimal;
// Check if the given character code, or the character code at the first
// character, is hexadecimal.
function hexadecimal(character) {
    var code = typeof character === 'string' ? character.charCodeAt(0) : character;
    return code >= 97 /* a */  && code <= 102 || code >= 65 /* A */  && code <= 70 || code >= 48 /* A */  && code <= 57;
}
}}),
"[project]/node_modules/is-alphabetical/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
module.exports = alphabetical;
// Check if the given character code, or the character code at the first
// character, is alphabetical.
function alphabetical(character) {
    var code = typeof character === 'string' ? character.charCodeAt(0) : character;
    return code >= 97 && code <= 122 || code >= 65 && code <= 90;
}
}}),
"[project]/node_modules/is-alphanumerical/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
var alphabetical = __turbopack_context__.r("[project]/node_modules/is-alphabetical/index.js [app-client] (ecmascript)");
var decimal = __turbopack_context__.r("[project]/node_modules/is-decimal/index.js [app-client] (ecmascript)");
module.exports = alphanumerical;
// Check if the given character code, or the character code at the first
// character, is alphanumerical.
function alphanumerical(character) {
    return alphabetical(character) || decimal(character);
}
}}),
"[project]/node_modules/parse-entities/decode-entity.browser.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
/* eslint-env browser */ var el;
var semicolon = 59 //  ';'
;
module.exports = decodeEntity;
function decodeEntity(characters) {
    var entity = '&' + characters + ';';
    var char;
    el = el || document.createElement('i');
    el.innerHTML = entity;
    char = el.textContent;
    // Some entities do not require the closing semicolon (`&not` - for instance),
    // which leads to situations where parsing the assumed entity of &notit; will
    // result in the string `¬it;`.  When we encounter a trailing semicolon after
    // parsing and the entity to decode was not a semicolon (`&semi;`), we can
    // assume that the matching was incomplete
    if (char.charCodeAt(char.length - 1) === semicolon && characters !== 'semi') {
        return false;
    }
    // If the decoded string is equal to the input, the entity was not valid
    return char === entity ? false : char;
}
}}),
"[project]/node_modules/parse-entities/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
var legacy = __turbopack_context__.r("[project]/node_modules/character-entities-legacy/index.json (json)");
var invalid = __turbopack_context__.r("[project]/node_modules/character-reference-invalid/index.json (json)");
var decimal = __turbopack_context__.r("[project]/node_modules/is-decimal/index.js [app-client] (ecmascript)");
var hexadecimal = __turbopack_context__.r("[project]/node_modules/is-hexadecimal/index.js [app-client] (ecmascript)");
var alphanumerical = __turbopack_context__.r("[project]/node_modules/is-alphanumerical/index.js [app-client] (ecmascript)");
var decodeEntity = __turbopack_context__.r("[project]/node_modules/parse-entities/decode-entity.browser.js [app-client] (ecmascript)");
module.exports = parseEntities;
var own = {}.hasOwnProperty;
var fromCharCode = String.fromCharCode;
var noop = Function.prototype;
// Default settings.
var defaults = {
    warning: null,
    reference: null,
    text: null,
    warningContext: null,
    referenceContext: null,
    textContext: null,
    position: {},
    additional: null,
    attribute: false,
    nonTerminated: true
};
// Characters.
var tab = 9 // '\t'
;
var lineFeed = 10 // '\n'
;
var formFeed = 12 // '\f'
;
var space = 32 // ' '
;
var ampersand = 38 // '&'
;
var semicolon = 59 // ';'
;
var lessThan = 60 // '<'
;
var equalsTo = 61 // '='
;
var numberSign = 35 // '#'
;
var uppercaseX = 88 // 'X'
;
var lowercaseX = 120 // 'x'
;
var replacementCharacter = 65533 // '�'
;
// Reference types.
var name = 'named';
var hexa = 'hexadecimal';
var deci = 'decimal';
// Map of bases.
var bases = {};
bases[hexa] = 16;
bases[deci] = 10;
// Map of types to tests.
// Each type of character reference accepts different characters.
// This test is used to detect whether a reference has ended (as the semicolon
// is not strictly needed).
var tests = {};
tests[name] = alphanumerical;
tests[deci] = decimal;
tests[hexa] = hexadecimal;
// Warning types.
var namedNotTerminated = 1;
var numericNotTerminated = 2;
var namedEmpty = 3;
var numericEmpty = 4;
var namedUnknown = 5;
var numericDisallowed = 6;
var numericProhibited = 7;
// Warning messages.
var messages = {};
messages[namedNotTerminated] = 'Named character references must be terminated by a semicolon';
messages[numericNotTerminated] = 'Numeric character references must be terminated by a semicolon';
messages[namedEmpty] = 'Named character references cannot be empty';
messages[numericEmpty] = 'Numeric character references cannot be empty';
messages[namedUnknown] = 'Named character references must be known';
messages[numericDisallowed] = 'Numeric character references cannot be disallowed';
messages[numericProhibited] = 'Numeric character references cannot be outside the permissible Unicode range';
// Wrap to ensure clean parameters are given to `parse`.
function parseEntities(value, options) {
    var settings = {};
    var option;
    var key;
    if (!options) {
        options = {};
    }
    for(key in defaults){
        option = options[key];
        settings[key] = option === null || option === undefined ? defaults[key] : option;
    }
    if (settings.position.indent || settings.position.start) {
        settings.indent = settings.position.indent || [];
        settings.position = settings.position.start;
    }
    return parse(value, settings);
}
// Parse entities.
// eslint-disable-next-line complexity
function parse(value, settings) {
    var additional = settings.additional;
    var nonTerminated = settings.nonTerminated;
    var handleText = settings.text;
    var handleReference = settings.reference;
    var handleWarning = settings.warning;
    var textContext = settings.textContext;
    var referenceContext = settings.referenceContext;
    var warningContext = settings.warningContext;
    var pos = settings.position;
    var indent = settings.indent || [];
    var length = value.length;
    var index = 0;
    var lines = -1;
    var column = pos.column || 1;
    var line = pos.line || 1;
    var queue = '';
    var result = [];
    var entityCharacters;
    var namedEntity;
    var terminated;
    var characters;
    var character;
    var reference;
    var following;
    var warning;
    var reason;
    var output;
    var entity;
    var begin;
    var start;
    var type;
    var test;
    var prev;
    var next;
    var diff;
    var end;
    if (typeof additional === 'string') {
        additional = additional.charCodeAt(0);
    }
    // Cache the current point.
    prev = now();
    // Wrap `handleWarning`.
    warning = handleWarning ? parseError : noop;
    // Ensure the algorithm walks over the first character and the end
    // (inclusive).
    index--;
    length++;
    while(++index < length){
        // If the previous character was a newline.
        if (character === lineFeed) {
            column = indent[lines] || 1;
        }
        character = value.charCodeAt(index);
        if (character === ampersand) {
            following = value.charCodeAt(index + 1);
            // The behaviour depends on the identity of the next character.
            if (following === tab || following === lineFeed || following === formFeed || following === space || following === ampersand || following === lessThan || following !== following || additional && following === additional) {
                // Not a character reference.
                // No characters are consumed, and nothing is returned.
                // This is not an error, either.
                queue += fromCharCode(character);
                column++;
                continue;
            }
            start = index + 1;
            begin = start;
            end = start;
            if (following === numberSign) {
                // Numerical entity.
                end = ++begin;
                // The behaviour further depends on the next character.
                following = value.charCodeAt(end);
                if (following === uppercaseX || following === lowercaseX) {
                    // ASCII hex digits.
                    type = hexa;
                    end = ++begin;
                } else {
                    // ASCII digits.
                    type = deci;
                }
            } else {
                // Named entity.
                type = name;
            }
            entityCharacters = '';
            entity = '';
            characters = '';
            test = tests[type];
            end--;
            while(++end < length){
                following = value.charCodeAt(end);
                if (!test(following)) {
                    break;
                }
                characters += fromCharCode(following);
                // Check if we can match a legacy named reference.
                // If so, we cache that as the last viable named reference.
                // This ensures we do not need to walk backwards later.
                if (type === name && own.call(legacy, characters)) {
                    entityCharacters = characters;
                    entity = legacy[characters];
                }
            }
            terminated = value.charCodeAt(end) === semicolon;
            if (terminated) {
                end++;
                namedEntity = type === name ? decodeEntity(characters) : false;
                if (namedEntity) {
                    entityCharacters = characters;
                    entity = namedEntity;
                }
            }
            diff = 1 + end - start;
            if (!terminated && !nonTerminated) {
            // Empty.
            } else if (!characters) {
                // An empty (possible) entity is valid, unless it’s numeric (thus an
                // ampersand followed by an octothorp).
                if (type !== name) {
                    warning(numericEmpty, diff);
                }
            } else if (type === name) {
                // An ampersand followed by anything unknown, and not terminated, is
                // invalid.
                if (terminated && !entity) {
                    warning(namedUnknown, 1);
                } else {
                    // If theres something after an entity name which is not known, cap
                    // the reference.
                    if (entityCharacters !== characters) {
                        end = begin + entityCharacters.length;
                        diff = 1 + end - begin;
                        terminated = false;
                    }
                    // If the reference is not terminated, warn.
                    if (!terminated) {
                        reason = entityCharacters ? namedNotTerminated : namedEmpty;
                        if (settings.attribute) {
                            following = value.charCodeAt(end);
                            if (following === equalsTo) {
                                warning(reason, diff);
                                entity = null;
                            } else if (alphanumerical(following)) {
                                entity = null;
                            } else {
                                warning(reason, diff);
                            }
                        } else {
                            warning(reason, diff);
                        }
                    }
                }
                reference = entity;
            } else {
                if (!terminated) {
                    // All non-terminated numeric entities are not rendered, and trigger a
                    // warning.
                    warning(numericNotTerminated, diff);
                }
                // When terminated and number, parse as either hexadecimal or decimal.
                reference = parseInt(characters, bases[type]);
                // Trigger a warning when the parsed number is prohibited, and replace
                // with replacement character.
                if (prohibited(reference)) {
                    warning(numericProhibited, diff);
                    reference = fromCharCode(replacementCharacter);
                } else if (reference in invalid) {
                    // Trigger a warning when the parsed number is disallowed, and replace
                    // by an alternative.
                    warning(numericDisallowed, diff);
                    reference = invalid[reference];
                } else {
                    // Parse the number.
                    output = '';
                    // Trigger a warning when the parsed number should not be used.
                    if (disallowed(reference)) {
                        warning(numericDisallowed, diff);
                    }
                    // Stringify the number.
                    if (reference > 0xffff) {
                        reference -= 0x10000;
                        output += fromCharCode(reference >>> (10 & 0x3ff) | 0xd800);
                        reference = 0xdc00 | reference & 0x3ff;
                    }
                    reference = output + fromCharCode(reference);
                }
            }
            // Found it!
            // First eat the queued characters as normal text, then eat an entity.
            if (reference) {
                flush();
                prev = now();
                index = end - 1;
                column += end - start + 1;
                result.push(reference);
                next = now();
                next.offset++;
                if (handleReference) {
                    handleReference.call(referenceContext, reference, {
                        start: prev,
                        end: next
                    }, value.slice(start - 1, end));
                }
                prev = next;
            } else {
                // If we could not find a reference, queue the checked characters (as
                // normal characters), and move the pointer to their end.
                // This is possible because we can be certain neither newlines nor
                // ampersands are included.
                characters = value.slice(start - 1, end);
                queue += characters;
                column += characters.length;
                index = end - 1;
            }
        } else {
            // Handle anything other than an ampersand, including newlines and EOF.
            if (character === 10 // Line feed
            ) {
                line++;
                lines++;
                column = 0;
            }
            if (character === character) {
                queue += fromCharCode(character);
                column++;
            } else {
                flush();
            }
        }
    }
    // Return the reduced nodes.
    return result.join('');
    //TURBOPACK unreachable
    ;
    // Get current position.
    function now() {
        return {
            line: line,
            column: column,
            offset: index + (pos.offset || 0)
        };
    }
    // “Throw” a parse-error: a warning.
    function parseError(code, offset) {
        var position = now();
        position.column += offset;
        position.offset += offset;
        handleWarning.call(warningContext, messages[code], position, code);
    }
    // Flush `queue` (normal text).
    // Macro invoked before each entity and at the end of `value`.
    // Does nothing when `queue` is empty.
    function flush() {
        if (queue) {
            result.push(queue);
            if (handleText) {
                handleText.call(textContext, queue, {
                    start: prev,
                    end: now()
                });
            }
            queue = '';
        }
    }
}
// Check if `character` is outside the permissible unicode range.
function prohibited(code) {
    return code >= 0xd800 && code <= 0xdfff || code > 0x10ffff;
}
// Check if `character` is disallowed.
function disallowed(code) {
    return code >= 0x0001 && code <= 0x0008 || code === 0x000b || code >= 0x000d && code <= 0x001f || code >= 0x007f && code <= 0x009f || code >= 0xfdd0 && code <= 0xfdef || (code & 0xffff) === 0xffff || (code & 0xffff) === 0xfffe;
}
}}),
"[project]/node_modules/refractor/node_modules/prismjs/components/prism-core.js [app-client] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
/// <reference lib="WebWorker"/>
var _self = typeof window !== 'undefined' ? window // if in browser
 : typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope ? self // if in worker
 : {} // if in node js
;
/**
 * Prism: Lightweight, robust, elegant syntax highlighting
 *
 * @license MIT <https://opensource.org/licenses/MIT>
 * <AUTHOR> Verou <https://lea.verou.me>
 * @namespace
 * @public
 */ var Prism = function(_self) {
    // Private helper vars
    var lang = /(?:^|\s)lang(?:uage)?-([\w-]+)(?=\s|$)/i;
    var uniqueId = 0;
    // The grammar object for plaintext
    var plainTextGrammar = {};
    var _ = {
        /**
		 * By default, Prism will attempt to highlight all code elements (by calling {@link Prism.highlightAll}) on the
		 * current page after the page finished loading. This might be a problem if e.g. you wanted to asynchronously load
		 * additional languages or plugins yourself.
		 *
		 * By setting this value to `true`, Prism will not automatically highlight all code elements on the page.
		 *
		 * You obviously have to change this value before the automatic highlighting started. To do this, you can add an
		 * empty Prism object into the global scope before loading the Prism script like this:
		 *
		 * ```js
		 * window.Prism = window.Prism || {};
		 * Prism.manual = true;
		 * // add a new <script> to load Prism's script
		 * ```
		 *
		 * @default false
		 * @type {boolean}
		 * @memberof Prism
		 * @public
		 */ manual: _self.Prism && _self.Prism.manual,
        /**
		 * By default, if Prism is in a web worker, it assumes that it is in a worker it created itself, so it uses
		 * `addEventListener` to communicate with its parent instance. However, if you're using Prism manually in your
		 * own worker, you don't want it to do this.
		 *
		 * By setting this value to `true`, Prism will not add its own listeners to the worker.
		 *
		 * You obviously have to change this value before Prism executes. To do this, you can add an
		 * empty Prism object into the global scope before loading the Prism script like this:
		 *
		 * ```js
		 * window.Prism = window.Prism || {};
		 * Prism.disableWorkerMessageHandler = true;
		 * // Load Prism's script
		 * ```
		 *
		 * @default false
		 * @type {boolean}
		 * @memberof Prism
		 * @public
		 */ disableWorkerMessageHandler: _self.Prism && _self.Prism.disableWorkerMessageHandler,
        /**
		 * A namespace for utility methods.
		 *
		 * All function in this namespace that are not explicitly marked as _public_ are for __internal use only__ and may
		 * change or disappear at any time.
		 *
		 * @namespace
		 * @memberof Prism
		 */ util: {
            encode: function encode(tokens) {
                if (tokens instanceof Token) {
                    return new Token(tokens.type, encode(tokens.content), tokens.alias);
                } else if (Array.isArray(tokens)) {
                    return tokens.map(encode);
                } else {
                    return tokens.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/\u00a0/g, ' ');
                }
            },
            /**
			 * Returns the name of the type of the given value.
			 *
			 * @param {any} o
			 * @returns {string}
			 * @example
			 * type(null)      === 'Null'
			 * type(undefined) === 'Undefined'
			 * type(123)       === 'Number'
			 * type('foo')     === 'String'
			 * type(true)      === 'Boolean'
			 * type([1, 2])    === 'Array'
			 * type({})        === 'Object'
			 * type(String)    === 'Function'
			 * type(/abc+/)    === 'RegExp'
			 */ type: function(o) {
                return Object.prototype.toString.call(o).slice(8, -1);
            },
            /**
			 * Returns a unique number for the given object. Later calls will still return the same number.
			 *
			 * @param {Object} obj
			 * @returns {number}
			 */ objId: function(obj) {
                if (!obj['__id']) {
                    Object.defineProperty(obj, '__id', {
                        value: ++uniqueId
                    });
                }
                return obj['__id'];
            },
            /**
			 * Creates a deep clone of the given object.
			 *
			 * The main intended use of this function is to clone language definitions.
			 *
			 * @param {T} o
			 * @param {Record<number, any>} [visited]
			 * @returns {T}
			 * @template T
			 */ clone: function deepClone(o, visited) {
                visited = visited || {};
                var clone;
                var id;
                switch(_.util.type(o)){
                    case 'Object':
                        id = _.util.objId(o);
                        if (visited[id]) {
                            return visited[id];
                        }
                        clone = {};
                        visited[id] = clone;
                        for(var key in o){
                            if (o.hasOwnProperty(key)) {
                                clone[key] = deepClone(o[key], visited);
                            }
                        }
                        return clone;
                    case 'Array':
                        id = _.util.objId(o);
                        if (visited[id]) {
                            return visited[id];
                        }
                        clone = [];
                        visited[id] = clone;
                        o.forEach(function(v, i) {
                            clone[i] = deepClone(v, visited);
                        });
                        return clone;
                    default:
                        return o;
                }
            },
            /**
			 * Returns the Prism language of the given element set by a `language-xxxx` or `lang-xxxx` class.
			 *
			 * If no language is set for the element or the element is `null` or `undefined`, `none` will be returned.
			 *
			 * @param {Element} element
			 * @returns {string}
			 */ getLanguage: function(element) {
                while(element){
                    var m = lang.exec(element.className);
                    if (m) {
                        return m[1].toLowerCase();
                    }
                    element = element.parentElement;
                }
                return 'none';
            },
            /**
			 * Sets the Prism `language-xxxx` class of the given element.
			 *
			 * @param {Element} element
			 * @param {string} language
			 * @returns {void}
			 */ setLanguage: function(element, language) {
                // remove all `language-xxxx` classes
                // (this might leave behind a leading space)
                element.className = element.className.replace(RegExp(lang, 'gi'), '');
                // add the new `language-xxxx` class
                // (using `classList` will automatically clean up spaces for us)
                element.classList.add('language-' + language);
            },
            /**
			 * Returns the script element that is currently executing.
			 *
			 * This does __not__ work for line script element.
			 *
			 * @returns {HTMLScriptElement | null}
			 */ currentScript: function() {
                if (typeof document === 'undefined') {
                    return null;
                }
                if ('currentScript' in document && 1 < 2 /* hack to trip TS' flow analysis */ ) {
                    return document.currentScript;
                }
                // IE11 workaround
                // we'll get the src of the current script by parsing IE11's error stack trace
                // this will not work for inline scripts
                try {
                    throw new Error();
                } catch (err) {
                    // Get file src url from stack. Specifically works with the format of stack traces in IE.
                    // A stack will look like this:
                    //
                    // Error
                    //    at _.util.currentScript (http://localhost/components/prism-core.js:119:5)
                    //    at Global code (http://localhost/components/prism-core.js:606:1)
                    var src = (/at [^(\r\n]*\((.*):[^:]+:[^:]+\)$/i.exec(err.stack) || [])[1];
                    if (src) {
                        var scripts = document.getElementsByTagName('script');
                        for(var i in scripts){
                            if (scripts[i].src == src) {
                                return scripts[i];
                            }
                        }
                    }
                    return null;
                }
            },
            /**
			 * Returns whether a given class is active for `element`.
			 *
			 * The class can be activated if `element` or one of its ancestors has the given class and it can be deactivated
			 * if `element` or one of its ancestors has the negated version of the given class. The _negated version_ of the
			 * given class is just the given class with a `no-` prefix.
			 *
			 * Whether the class is active is determined by the closest ancestor of `element` (where `element` itself is
			 * closest ancestor) that has the given class or the negated version of it. If neither `element` nor any of its
			 * ancestors have the given class or the negated version of it, then the default activation will be returned.
			 *
			 * In the paradoxical situation where the closest ancestor contains __both__ the given class and the negated
			 * version of it, the class is considered active.
			 *
			 * @param {Element} element
			 * @param {string} className
			 * @param {boolean} [defaultActivation=false]
			 * @returns {boolean}
			 */ isActive: function(element, className, defaultActivation) {
                var no = 'no-' + className;
                while(element){
                    var classList = element.classList;
                    if (classList.contains(className)) {
                        return true;
                    }
                    if (classList.contains(no)) {
                        return false;
                    }
                    element = element.parentElement;
                }
                return !!defaultActivation;
            }
        },
        /**
		 * This namespace contains all currently loaded languages and the some helper functions to create and modify languages.
		 *
		 * @namespace
		 * @memberof Prism
		 * @public
		 */ languages: {
            /**
			 * The grammar for plain, unformatted text.
			 */ plain: plainTextGrammar,
            plaintext: plainTextGrammar,
            text: plainTextGrammar,
            txt: plainTextGrammar,
            /**
			 * Creates a deep copy of the language with the given id and appends the given tokens.
			 *
			 * If a token in `redef` also appears in the copied language, then the existing token in the copied language
			 * will be overwritten at its original position.
			 *
			 * ## Best practices
			 *
			 * Since the position of overwriting tokens (token in `redef` that overwrite tokens in the copied language)
			 * doesn't matter, they can technically be in any order. However, this can be confusing to others that trying to
			 * understand the language definition because, normally, the order of tokens matters in Prism grammars.
			 *
			 * Therefore, it is encouraged to order overwriting tokens according to the positions of the overwritten tokens.
			 * Furthermore, all non-overwriting tokens should be placed after the overwriting ones.
			 *
			 * @param {string} id The id of the language to extend. This has to be a key in `Prism.languages`.
			 * @param {Grammar} redef The new tokens to append.
			 * @returns {Grammar} The new language created.
			 * @public
			 * @example
			 * Prism.languages['css-with-colors'] = Prism.languages.extend('css', {
			 *     // Prism.languages.css already has a 'comment' token, so this token will overwrite CSS' 'comment' token
			 *     // at its original position
			 *     'comment': { ... },
			 *     // CSS doesn't have a 'color' token, so this token will be appended
			 *     'color': /\b(?:red|green|blue)\b/
			 * });
			 */ extend: function(id, redef) {
                var lang = _.util.clone(_.languages[id]);
                for(var key in redef){
                    lang[key] = redef[key];
                }
                return lang;
            },
            /**
			 * Inserts tokens _before_ another token in a language definition or any other grammar.
			 *
			 * ## Usage
			 *
			 * This helper method makes it easy to modify existing languages. For example, the CSS language definition
			 * not only defines CSS highlighting for CSS documents, but also needs to define highlighting for CSS embedded
			 * in HTML through `<style>` elements. To do this, it needs to modify `Prism.languages.markup` and add the
			 * appropriate tokens. However, `Prism.languages.markup` is a regular JavaScript object literal, so if you do
			 * this:
			 *
			 * ```js
			 * Prism.languages.markup.style = {
			 *     // token
			 * };
			 * ```
			 *
			 * then the `style` token will be added (and processed) at the end. `insertBefore` allows you to insert tokens
			 * before existing tokens. For the CSS example above, you would use it like this:
			 *
			 * ```js
			 * Prism.languages.insertBefore('markup', 'cdata', {
			 *     'style': {
			 *         // token
			 *     }
			 * });
			 * ```
			 *
			 * ## Special cases
			 *
			 * If the grammars of `inside` and `insert` have tokens with the same name, the tokens in `inside`'s grammar
			 * will be ignored.
			 *
			 * This behavior can be used to insert tokens after `before`:
			 *
			 * ```js
			 * Prism.languages.insertBefore('markup', 'comment', {
			 *     'comment': Prism.languages.markup.comment,
			 *     // tokens after 'comment'
			 * });
			 * ```
			 *
			 * ## Limitations
			 *
			 * The main problem `insertBefore` has to solve is iteration order. Since ES2015, the iteration order for object
			 * properties is guaranteed to be the insertion order (except for integer keys) but some browsers behave
			 * differently when keys are deleted and re-inserted. So `insertBefore` can't be implemented by temporarily
			 * deleting properties which is necessary to insert at arbitrary positions.
			 *
			 * To solve this problem, `insertBefore` doesn't actually insert the given tokens into the target object.
			 * Instead, it will create a new object and replace all references to the target object with the new one. This
			 * can be done without temporarily deleting properties, so the iteration order is well-defined.
			 *
			 * However, only references that can be reached from `Prism.languages` or `insert` will be replaced. I.e. if
			 * you hold the target object in a variable, then the value of the variable will not change.
			 *
			 * ```js
			 * var oldMarkup = Prism.languages.markup;
			 * var newMarkup = Prism.languages.insertBefore('markup', 'comment', { ... });
			 *
			 * assert(oldMarkup !== Prism.languages.markup);
			 * assert(newMarkup === Prism.languages.markup);
			 * ```
			 *
			 * @param {string} inside The property of `root` (e.g. a language id in `Prism.languages`) that contains the
			 * object to be modified.
			 * @param {string} before The key to insert before.
			 * @param {Grammar} insert An object containing the key-value pairs to be inserted.
			 * @param {Object<string, any>} [root] The object containing `inside`, i.e. the object that contains the
			 * object to be modified.
			 *
			 * Defaults to `Prism.languages`.
			 * @returns {Grammar} The new grammar object.
			 * @public
			 */ insertBefore: function(inside, before, insert, root) {
                root = root || _.languages;
                var grammar = root[inside];
                /** @type {Grammar} */ var ret = {};
                for(var token in grammar){
                    if (grammar.hasOwnProperty(token)) {
                        if (token == before) {
                            for(var newToken in insert){
                                if (insert.hasOwnProperty(newToken)) {
                                    ret[newToken] = insert[newToken];
                                }
                            }
                        }
                        // Do not insert token which also occur in insert. See #1525
                        if (!insert.hasOwnProperty(token)) {
                            ret[token] = grammar[token];
                        }
                    }
                }
                var old = root[inside];
                root[inside] = ret;
                // Update references in other language definitions
                _.languages.DFS(_.languages, function(key, value) {
                    if (value === old && key != inside) {
                        this[key] = ret;
                    }
                });
                return ret;
            },
            // Traverse a language definition with Depth First Search
            DFS: function DFS(o, callback, type, visited) {
                visited = visited || {};
                var objId = _.util.objId;
                for(var i in o){
                    if (o.hasOwnProperty(i)) {
                        callback.call(o, i, o[i], type || i);
                        var property = o[i];
                        var propertyType = _.util.type(property);
                        if (propertyType === 'Object' && !visited[objId(property)]) {
                            visited[objId(property)] = true;
                            DFS(property, callback, null, visited);
                        } else if (propertyType === 'Array' && !visited[objId(property)]) {
                            visited[objId(property)] = true;
                            DFS(property, callback, i, visited);
                        }
                    }
                }
            }
        },
        plugins: {},
        /**
		 * This is the most high-level function in Prism’s API.
		 * It fetches all the elements that have a `.language-xxxx` class and then calls {@link Prism.highlightElement} on
		 * each one of them.
		 *
		 * This is equivalent to `Prism.highlightAllUnder(document, async, callback)`.
		 *
		 * @param {boolean} [async=false] Same as in {@link Prism.highlightAllUnder}.
		 * @param {HighlightCallback} [callback] Same as in {@link Prism.highlightAllUnder}.
		 * @memberof Prism
		 * @public
		 */ highlightAll: function(async, callback) {
            _.highlightAllUnder(document, async, callback);
        },
        /**
		 * Fetches all the descendants of `container` that have a `.language-xxxx` class and then calls
		 * {@link Prism.highlightElement} on each one of them.
		 *
		 * The following hooks will be run:
		 * 1. `before-highlightall`
		 * 2. `before-all-elements-highlight`
		 * 3. All hooks of {@link Prism.highlightElement} for each element.
		 *
		 * @param {ParentNode} container The root element, whose descendants that have a `.language-xxxx` class will be highlighted.
		 * @param {boolean} [async=false] Whether each element is to be highlighted asynchronously using Web Workers.
		 * @param {HighlightCallback} [callback] An optional callback to be invoked on each element after its highlighting is done.
		 * @memberof Prism
		 * @public
		 */ highlightAllUnder: function(container, async, callback) {
            var env = {
                callback: callback,
                container: container,
                selector: 'code[class*="language-"], [class*="language-"] code, code[class*="lang-"], [class*="lang-"] code'
            };
            _.hooks.run('before-highlightall', env);
            env.elements = Array.prototype.slice.apply(env.container.querySelectorAll(env.selector));
            _.hooks.run('before-all-elements-highlight', env);
            for(var i = 0, element; element = env.elements[i++];){
                _.highlightElement(element, async === true, env.callback);
            }
        },
        /**
		 * Highlights the code inside a single element.
		 *
		 * The following hooks will be run:
		 * 1. `before-sanity-check`
		 * 2. `before-highlight`
		 * 3. All hooks of {@link Prism.highlight}. These hooks will be run by an asynchronous worker if `async` is `true`.
		 * 4. `before-insert`
		 * 5. `after-highlight`
		 * 6. `complete`
		 *
		 * Some the above hooks will be skipped if the element doesn't contain any text or there is no grammar loaded for
		 * the element's language.
		 *
		 * @param {Element} element The element containing the code.
		 * It must have a class of `language-xxxx` to be processed, where `xxxx` is a valid language identifier.
		 * @param {boolean} [async=false] Whether the element is to be highlighted asynchronously using Web Workers
		 * to improve performance and avoid blocking the UI when highlighting very large chunks of code. This option is
		 * [disabled by default](https://prismjs.com/faq.html#why-is-asynchronous-highlighting-disabled-by-default).
		 *
		 * Note: All language definitions required to highlight the code must be included in the main `prism.js` file for
		 * asynchronous highlighting to work. You can build your own bundle on the
		 * [Download page](https://prismjs.com/download.html).
		 * @param {HighlightCallback} [callback] An optional callback to be invoked after the highlighting is done.
		 * Mostly useful when `async` is `true`, since in that case, the highlighting is done asynchronously.
		 * @memberof Prism
		 * @public
		 */ highlightElement: function(element, async, callback) {
            // Find language
            var language = _.util.getLanguage(element);
            var grammar = _.languages[language];
            // Set language on the element, if not present
            _.util.setLanguage(element, language);
            // Set language on the parent, for styling
            var parent = element.parentElement;
            if (parent && parent.nodeName.toLowerCase() === 'pre') {
                _.util.setLanguage(parent, language);
            }
            var code = element.textContent;
            var env = {
                element: element,
                language: language,
                grammar: grammar,
                code: code
            };
            function insertHighlightedCode(highlightedCode) {
                env.highlightedCode = highlightedCode;
                _.hooks.run('before-insert', env);
                env.element.innerHTML = env.highlightedCode;
                _.hooks.run('after-highlight', env);
                _.hooks.run('complete', env);
                callback && callback.call(env.element);
            }
            _.hooks.run('before-sanity-check', env);
            // plugins may change/add the parent/element
            parent = env.element.parentElement;
            if (parent && parent.nodeName.toLowerCase() === 'pre' && !parent.hasAttribute('tabindex')) {
                parent.setAttribute('tabindex', '0');
            }
            if (!env.code) {
                _.hooks.run('complete', env);
                callback && callback.call(env.element);
                return;
            }
            _.hooks.run('before-highlight', env);
            if (!env.grammar) {
                insertHighlightedCode(_.util.encode(env.code));
                return;
            }
            if (async && _self.Worker) {
                var worker = new Worker(_.filename);
                worker.onmessage = function(evt) {
                    insertHighlightedCode(evt.data);
                };
                worker.postMessage(JSON.stringify({
                    language: env.language,
                    code: env.code,
                    immediateClose: true
                }));
            } else {
                insertHighlightedCode(_.highlight(env.code, env.grammar, env.language));
            }
        },
        /**
		 * Low-level function, only use if you know what you’re doing. It accepts a string of text as input
		 * and the language definitions to use, and returns a string with the HTML produced.
		 *
		 * The following hooks will be run:
		 * 1. `before-tokenize`
		 * 2. `after-tokenize`
		 * 3. `wrap`: On each {@link Token}.
		 *
		 * @param {string} text A string with the code to be highlighted.
		 * @param {Grammar} grammar An object containing the tokens to use.
		 *
		 * Usually a language definition like `Prism.languages.markup`.
		 * @param {string} language The name of the language definition passed to `grammar`.
		 * @returns {string} The highlighted HTML.
		 * @memberof Prism
		 * @public
		 * @example
		 * Prism.highlight('var foo = true;', Prism.languages.javascript, 'javascript');
		 */ highlight: function(text, grammar, language) {
            var env = {
                code: text,
                grammar: grammar,
                language: language
            };
            _.hooks.run('before-tokenize', env);
            if (!env.grammar) {
                throw new Error('The language "' + env.language + '" has no grammar.');
            }
            env.tokens = _.tokenize(env.code, env.grammar);
            _.hooks.run('after-tokenize', env);
            return Token.stringify(_.util.encode(env.tokens), env.language);
        },
        /**
		 * This is the heart of Prism, and the most low-level function you can use. It accepts a string of text as input
		 * and the language definitions to use, and returns an array with the tokenized code.
		 *
		 * When the language definition includes nested tokens, the function is called recursively on each of these tokens.
		 *
		 * This method could be useful in other contexts as well, as a very crude parser.
		 *
		 * @param {string} text A string with the code to be highlighted.
		 * @param {Grammar} grammar An object containing the tokens to use.
		 *
		 * Usually a language definition like `Prism.languages.markup`.
		 * @returns {TokenStream} An array of strings and tokens, a token stream.
		 * @memberof Prism
		 * @public
		 * @example
		 * let code = `var foo = 0;`;
		 * let tokens = Prism.tokenize(code, Prism.languages.javascript);
		 * tokens.forEach(token => {
		 *     if (token instanceof Prism.Token && token.type === 'number') {
		 *         console.log(`Found numeric literal: ${token.content}`);
		 *     }
		 * });
		 */ tokenize: function(text, grammar) {
            var rest = grammar.rest;
            if (rest) {
                for(var token in rest){
                    grammar[token] = rest[token];
                }
                delete grammar.rest;
            }
            var tokenList = new LinkedList();
            addAfter(tokenList, tokenList.head, text);
            matchGrammar(text, tokenList, grammar, tokenList.head, 0);
            return toArray(tokenList);
        },
        /**
		 * @namespace
		 * @memberof Prism
		 * @public
		 */ hooks: {
            all: {},
            /**
			 * Adds the given callback to the list of callbacks for the given hook.
			 *
			 * The callback will be invoked when the hook it is registered for is run.
			 * Hooks are usually directly run by a highlight function but you can also run hooks yourself.
			 *
			 * One callback function can be registered to multiple hooks and the same hook multiple times.
			 *
			 * @param {string} name The name of the hook.
			 * @param {HookCallback} callback The callback function which is given environment variables.
			 * @public
			 */ add: function(name, callback) {
                var hooks = _.hooks.all;
                hooks[name] = hooks[name] || [];
                hooks[name].push(callback);
            },
            /**
			 * Runs a hook invoking all registered callbacks with the given environment variables.
			 *
			 * Callbacks will be invoked synchronously and in the order in which they were registered.
			 *
			 * @param {string} name The name of the hook.
			 * @param {Object<string, any>} env The environment variables of the hook passed to all callbacks registered.
			 * @public
			 */ run: function(name, env) {
                var callbacks = _.hooks.all[name];
                if (!callbacks || !callbacks.length) {
                    return;
                }
                for(var i = 0, callback; callback = callbacks[i++];){
                    callback(env);
                }
            }
        },
        Token: Token
    };
    _self.Prism = _;
    // Typescript note:
    // The following can be used to import the Token type in JSDoc:
    //
    //   @typedef {InstanceType<import("./prism-core")["Token"]>} Token
    /**
	 * Creates a new token.
	 *
	 * @param {string} type See {@link Token#type type}
	 * @param {string | TokenStream} content See {@link Token#content content}
	 * @param {string|string[]} [alias] The alias(es) of the token.
	 * @param {string} [matchedStr=""] A copy of the full string this token was created from.
	 * @class
	 * @global
	 * @public
	 */ function Token(type, content, alias, matchedStr) {
        /**
		 * The type of the token.
		 *
		 * This is usually the key of a pattern in a {@link Grammar}.
		 *
		 * @type {string}
		 * @see GrammarToken
		 * @public
		 */ this.type = type;
        /**
		 * The strings or tokens contained by this token.
		 *
		 * This will be a token stream if the pattern matched also defined an `inside` grammar.
		 *
		 * @type {string | TokenStream}
		 * @public
		 */ this.content = content;
        /**
		 * The alias(es) of the token.
		 *
		 * @type {string|string[]}
		 * @see GrammarToken
		 * @public
		 */ this.alias = alias;
        // Copy of the full string this token was created from
        this.length = (matchedStr || '').length | 0;
    }
    /**
	 * A token stream is an array of strings and {@link Token Token} objects.
	 *
	 * Token streams have to fulfill a few properties that are assumed by most functions (mostly internal ones) that process
	 * them.
	 *
	 * 1. No adjacent strings.
	 * 2. No empty strings.
	 *
	 *    The only exception here is the token stream that only contains the empty string and nothing else.
	 *
	 * @typedef {Array<string | Token>} TokenStream
	 * @global
	 * @public
	 */ /**
	 * Converts the given token or token stream to an HTML representation.
	 *
	 * The following hooks will be run:
	 * 1. `wrap`: On each {@link Token}.
	 *
	 * @param {string | Token | TokenStream} o The token or token stream to be converted.
	 * @param {string} language The name of current language.
	 * @returns {string} The HTML representation of the token or token stream.
	 * @memberof Token
	 * @static
	 */ Token.stringify = function stringify(o, language) {
        if (typeof o == 'string') {
            return o;
        }
        if (Array.isArray(o)) {
            var s = '';
            o.forEach(function(e) {
                s += stringify(e, language);
            });
            return s;
        }
        var env = {
            type: o.type,
            content: stringify(o.content, language),
            tag: 'span',
            classes: [
                'token',
                o.type
            ],
            attributes: {},
            language: language
        };
        var aliases = o.alias;
        if (aliases) {
            if (Array.isArray(aliases)) {
                Array.prototype.push.apply(env.classes, aliases);
            } else {
                env.classes.push(aliases);
            }
        }
        _.hooks.run('wrap', env);
        var attributes = '';
        for(var name in env.attributes){
            attributes += ' ' + name + '="' + (env.attributes[name] || '').replace(/"/g, '&quot;') + '"';
        }
        return '<' + env.tag + ' class="' + env.classes.join(' ') + '"' + attributes + '>' + env.content + '</' + env.tag + '>';
    };
    /**
	 * @param {RegExp} pattern
	 * @param {number} pos
	 * @param {string} text
	 * @param {boolean} lookbehind
	 * @returns {RegExpExecArray | null}
	 */ function matchPattern(pattern, pos, text, lookbehind) {
        pattern.lastIndex = pos;
        var match = pattern.exec(text);
        if (match && lookbehind && match[1]) {
            // change the match to remove the text matched by the Prism lookbehind group
            var lookbehindLength = match[1].length;
            match.index += lookbehindLength;
            match[0] = match[0].slice(lookbehindLength);
        }
        return match;
    }
    /**
	 * @param {string} text
	 * @param {LinkedList<string | Token>} tokenList
	 * @param {any} grammar
	 * @param {LinkedListNode<string | Token>} startNode
	 * @param {number} startPos
	 * @param {RematchOptions} [rematch]
	 * @returns {void}
	 * @private
	 *
	 * @typedef RematchOptions
	 * @property {string} cause
	 * @property {number} reach
	 */ function matchGrammar(text, tokenList, grammar, startNode, startPos, rematch) {
        for(var token in grammar){
            if (!grammar.hasOwnProperty(token) || !grammar[token]) {
                continue;
            }
            var patterns = grammar[token];
            patterns = Array.isArray(patterns) ? patterns : [
                patterns
            ];
            for(var j = 0; j < patterns.length; ++j){
                if (rematch && rematch.cause == token + ',' + j) {
                    return;
                }
                var patternObj = patterns[j];
                var inside = patternObj.inside;
                var lookbehind = !!patternObj.lookbehind;
                var greedy = !!patternObj.greedy;
                var alias = patternObj.alias;
                if (greedy && !patternObj.pattern.global) {
                    // Without the global flag, lastIndex won't work
                    var flags = patternObj.pattern.toString().match(/[imsuy]*$/)[0];
                    patternObj.pattern = RegExp(patternObj.pattern.source, flags + 'g');
                }
                /** @type {RegExp} */ var pattern = patternObj.pattern || patternObj;
                for(var currentNode = startNode.next, pos = startPos; currentNode !== tokenList.tail; pos += currentNode.value.length, currentNode = currentNode.next){
                    if (rematch && pos >= rematch.reach) {
                        break;
                    }
                    var str = currentNode.value;
                    if (tokenList.length > text.length) {
                        // Something went terribly wrong, ABORT, ABORT!
                        return;
                    }
                    if (str instanceof Token) {
                        continue;
                    }
                    var removeCount = 1; // this is the to parameter of removeBetween
                    var match;
                    if (greedy) {
                        match = matchPattern(pattern, pos, text, lookbehind);
                        if (!match || match.index >= text.length) {
                            break;
                        }
                        var from = match.index;
                        var to = match.index + match[0].length;
                        var p = pos;
                        // find the node that contains the match
                        p += currentNode.value.length;
                        while(from >= p){
                            currentNode = currentNode.next;
                            p += currentNode.value.length;
                        }
                        // adjust pos (and p)
                        p -= currentNode.value.length;
                        pos = p;
                        // the current node is a Token, then the match starts inside another Token, which is invalid
                        if (currentNode.value instanceof Token) {
                            continue;
                        }
                        // find the last node which is affected by this match
                        for(var k = currentNode; k !== tokenList.tail && (p < to || typeof k.value === 'string'); k = k.next){
                            removeCount++;
                            p += k.value.length;
                        }
                        removeCount--;
                        // replace with the new match
                        str = text.slice(pos, p);
                        match.index -= pos;
                    } else {
                        match = matchPattern(pattern, 0, str, lookbehind);
                        if (!match) {
                            continue;
                        }
                    }
                    // eslint-disable-next-line no-redeclare
                    var from = match.index;
                    var matchStr = match[0];
                    var before = str.slice(0, from);
                    var after = str.slice(from + matchStr.length);
                    var reach = pos + str.length;
                    if (rematch && reach > rematch.reach) {
                        rematch.reach = reach;
                    }
                    var removeFrom = currentNode.prev;
                    if (before) {
                        removeFrom = addAfter(tokenList, removeFrom, before);
                        pos += before.length;
                    }
                    removeRange(tokenList, removeFrom, removeCount);
                    var wrapped = new Token(token, inside ? _.tokenize(matchStr, inside) : matchStr, alias, matchStr);
                    currentNode = addAfter(tokenList, removeFrom, wrapped);
                    if (after) {
                        addAfter(tokenList, currentNode, after);
                    }
                    if (removeCount > 1) {
                        // at least one Token object was removed, so we have to do some rematching
                        // this can only happen if the current pattern is greedy
                        /** @type {RematchOptions} */ var nestedRematch = {
                            cause: token + ',' + j,
                            reach: reach
                        };
                        matchGrammar(text, tokenList, grammar, currentNode.prev, pos, nestedRematch);
                        // the reach might have been extended because of the rematching
                        if (rematch && nestedRematch.reach > rematch.reach) {
                            rematch.reach = nestedRematch.reach;
                        }
                    }
                }
            }
        }
    }
    /**
	 * @typedef LinkedListNode
	 * @property {T} value
	 * @property {LinkedListNode<T> | null} prev The previous node.
	 * @property {LinkedListNode<T> | null} next The next node.
	 * @template T
	 * @private
	 */ /**
	 * @template T
	 * @private
	 */ function LinkedList() {
        /** @type {LinkedListNode<T>} */ var head = {
            value: null,
            prev: null,
            next: null
        };
        /** @type {LinkedListNode<T>} */ var tail = {
            value: null,
            prev: head,
            next: null
        };
        head.next = tail;
        /** @type {LinkedListNode<T>} */ this.head = head;
        /** @type {LinkedListNode<T>} */ this.tail = tail;
        this.length = 0;
    }
    /**
	 * Adds a new node with the given value to the list.
	 *
	 * @param {LinkedList<T>} list
	 * @param {LinkedListNode<T>} node
	 * @param {T} value
	 * @returns {LinkedListNode<T>} The added node.
	 * @template T
	 */ function addAfter(list, node, value) {
        // assumes that node != list.tail && values.length >= 0
        var next = node.next;
        var newNode = {
            value: value,
            prev: node,
            next: next
        };
        node.next = newNode;
        next.prev = newNode;
        list.length++;
        return newNode;
    }
    /**
	 * Removes `count` nodes after the given node. The given node will not be removed.
	 *
	 * @param {LinkedList<T>} list
	 * @param {LinkedListNode<T>} node
	 * @param {number} count
	 * @template T
	 */ function removeRange(list, node, count) {
        var next = node.next;
        for(var i = 0; i < count && next !== list.tail; i++){
            next = next.next;
        }
        node.next = next;
        next.prev = node;
        list.length -= i;
    }
    /**
	 * @param {LinkedList<T>} list
	 * @returns {T[]}
	 * @template T
	 */ function toArray(list) {
        var array = [];
        var node = list.head.next;
        while(node !== list.tail){
            array.push(node.value);
            node = node.next;
        }
        return array;
    }
    if (!_self.document) {
        if (!_self.addEventListener) {
            // in Node.js
            return _;
        }
        if (!_.disableWorkerMessageHandler) {
            // In worker
            _self.addEventListener('message', function(evt) {
                var message = JSON.parse(evt.data);
                var lang = message.language;
                var code = message.code;
                var immediateClose = message.immediateClose;
                _self.postMessage(_.highlight(code, _.languages[lang], lang));
                if (immediateClose) {
                    _self.close();
                }
            }, false);
        }
        return _;
    }
    // Get current script and highlight
    var script = _.util.currentScript();
    if (script) {
        _.filename = script.src;
        if (script.hasAttribute('data-manual')) {
            _.manual = true;
        }
    }
    function highlightAutomaticallyCallback() {
        if (!_.manual) {
            _.highlightAll();
        }
    }
    if (!_.manual) {
        // If the document state is "loading", then we'll use DOMContentLoaded.
        // If the document state is "interactive" and the prism.js script is deferred, then we'll also use the
        // DOMContentLoaded event because there might be some plugins or languages which have also been deferred and they
        // might take longer one animation frame to execute which can create a race condition where only some plugins have
        // been loaded when Prism.highlightAll() is executed, depending on how fast resources are loaded.
        // See https://github.com/PrismJS/prism/issues/2102
        var readyState = document.readyState;
        if (readyState === 'loading' || readyState === 'interactive' && script && script.defer) {
            document.addEventListener('DOMContentLoaded', highlightAutomaticallyCallback);
        } else {
            if (window.requestAnimationFrame) {
                window.requestAnimationFrame(highlightAutomaticallyCallback);
            } else {
                window.setTimeout(highlightAutomaticallyCallback, 16);
            }
        }
    }
    return _;
}(_self);
if (("TURBOPACK compile-time value", "object") !== 'undefined' && module.exports) {
    module.exports = Prism;
}
// hack for components to work correctly in node.js
if ("TURBOPACK compile-time truthy", 1) {
    ("TURBOPACK ident replacement", globalThis).Prism = Prism;
} // some additional documentation/types
 /**
 * The expansion of a simple `RegExp` literal to support additional properties.
 *
 * @typedef GrammarToken
 * @property {RegExp} pattern The regular expression of the token.
 * @property {boolean} [lookbehind=false] If `true`, then the first capturing group of `pattern` will (effectively)
 * behave as a lookbehind group meaning that the captured text will not be part of the matched text of the new token.
 * @property {boolean} [greedy=false] Whether the token is greedy.
 * @property {string|string[]} [alias] An optional alias or list of aliases.
 * @property {Grammar} [inside] The nested grammar of this token.
 *
 * The `inside` grammar will be used to tokenize the text value of each token of this kind.
 *
 * This can be used to make nested and even recursive language definitions.
 *
 * Note: This can cause infinite recursion. Be careful when you embed different languages or even the same language into
 * each another.
 * @global
 * @public
 */  /**
 * @typedef Grammar
 * @type {Object<string, RegExp | GrammarToken | Array<RegExp | GrammarToken>>}
 * @property {Grammar} [rest] An optional grammar object that will be appended to this grammar.
 * @global
 * @public
 */  /**
 * A function which will invoked after an element was successfully highlighted.
 *
 * @callback HighlightCallback
 * @param {Element} element The element successfully highlighted.
 * @returns {void}
 * @global
 * @public
 */  /**
 * @callback HookCallback
 * @param {Object<string, any>} env The environment variables of the hook.
 * @returns {void}
 * @global
 * @public
 */ 
}}),
}]);

//# sourceMappingURL=node_modules_eb30a3e2._.js.map
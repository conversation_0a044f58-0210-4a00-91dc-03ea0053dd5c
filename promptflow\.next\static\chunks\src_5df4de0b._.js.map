{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,KAAyD;QAAzD,EAAE,SAAS,EAAE,GAAG,OAAyC,GAAzD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,KAKoC;QALpC,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD,GALpC;IAMjB,qBACE,6LAAC,wKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/store/app-store.ts"], "sourcesContent": ["import { create } from 'zustand'\r\nimport { persist } from 'zustand/middleware'\r\n\r\ninterface AppState {\r\n  activeProjectId: string | null\r\n  setActiveProjectId: (projectId: string | null) => void\r\n  isContextEnabled: boolean\r\n  setIsContextEnabled: (enabled: boolean) => void\r\n  // Sidebar collapse states\r\n  isProjectSidebarCollapsed: boolean\r\n  setIsProjectSidebarCollapsed: (collapsed: boolean) => void\r\n  isContextSidebarCollapsed: boolean\r\n  setIsContextSidebarCollapsed: (collapsed: boolean) => void\r\n}\r\n\r\nexport const useAppStore = create<AppState>()(\r\n  persist(\r\n    (set) => ({\r\n      activeProjectId: null,\r\n      setActiveProjectId: (projectId) => set({ activeProjectId: projectId }),\r\n      isContextEnabled: true,\r\n      setIsContextEnabled: (enabled) => set({ isContextEnabled: enabled }),\r\n      // Sidebar collapse states\r\n      isProjectSidebarCollapsed: false,\r\n      setIsProjectSidebarCollapsed: (collapsed) => set({ isProjectSidebarCollapsed: collapsed }),\r\n      isContextSidebarCollapsed: false,\r\n      setIsContextSidebarCollapsed: (collapsed) => set({ isContextSidebarCollapsed: collapsed }),\r\n    }),\r\n    {\r\n      name: 'promptflow-app-store',\r\n    }\r\n  )\r\n)"], "names": [], "mappings": ";;;AAAA;AACA;;;AAcO,MAAM,cAAc,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC9B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,iBAAiB;QACjB,oBAAoB,CAAC,YAAc,IAAI;gBAAE,iBAAiB;YAAU;QACpE,kBAAkB;QAClB,qBAAqB,CAAC,UAAY,IAAI;gBAAE,kBAAkB;YAAQ;QAClE,0BAA0B;QAC1B,2BAA2B;QAC3B,8BAA8B,CAAC,YAAc,IAAI;gBAAE,2BAA2B;YAAU;QACxF,2BAA2B;QAC3B,8BAA8B,CAAC,YAAc,IAAI;gBAAE,2BAA2B;YAAU;IAC1F,CAAC,GACD;IACE,MAAM;AACR", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/plan-limits.ts"], "sourcesContent": ["// Plan Limitleri Kontrol Middleware ve Utility Fonksiyonları\n\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\n\nexport interface PlanLimits {\n  current_projects: number\n  current_prompts: number\n  max_projects: number\n  max_prompts_per_project: number\n  can_create_project: boolean\n  can_create_prompt: boolean\n}\n\nexport interface UserPlan {\n  plan_id: string\n  plan_name: string\n  display_name: string\n  max_projects: number\n  max_prompts_per_project: number\n  features: Record<string, boolean | string | number>\n  status: string\n  expires_at: string | null\n}\n\n// Plan limitlerini kontrol et\nexport async function checkUserLimits(): Promise<PlanLimits | null> {\n  try {\n    const { data: user } = await supabase.auth.getUser()\n    if (!user.user) {\n      throw new Error('Kullanıcı oturumu bulunamadı')\n    }\n\n    const { data, error } = await supabase.rpc('check_user_limits', {\n      user_uuid: user.user.id\n    })\n\n    if (error) {\n      console.error('Plan limitleri kontrol edilemedi:', error)\n      throw new Error(error.message)\n    }\n\n    return data?.[0] || null\n  } catch (error) {\n    console.error('Plan limitleri kontrol hatası:', error)\n    return null\n  }\n}\n\n// Kullanıcının aktif planını getir\nexport async function getUserActivePlan(): Promise<UserPlan | null> {\n  try {\n    const { data: user } = await supabase.auth.getUser()\n    if (!user.user) {\n      throw new Error('Kullanıcı oturumu bulunamadı')\n    }\n\n    const { data, error } = await supabase.rpc('get_user_active_plan', {\n      user_uuid: user.user.id\n    })\n\n    if (error) {\n      console.error('Aktif plan getirilemedi:', error)\n      throw new Error(error.message)\n    }\n\n    return data?.[0] || null\n  } catch (error) {\n    console.error('Aktif plan getirme hatası:', error)\n    return null\n  }\n}\n\n// Proje oluşturma limiti kontrol et\nexport async function canCreateProject(): Promise<{ allowed: boolean; reason?: string }> {\n  const limits = await checkUserLimits()\n  \n  if (!limits) {\n    return { allowed: false, reason: 'Plan bilgileri alınamadı' }\n  }\n\n  if (!limits.can_create_project) {\n    return { \n      allowed: false, \n      reason: `Proje limiti aşıldı (${limits.current_projects}/${limits.max_projects})` \n    }\n  }\n\n  return { allowed: true }\n}\n\n// Prompt oluşturma limiti kontrol et\nexport async function canCreatePrompt(projectId?: string): Promise<{ allowed: boolean; reason?: string }> {\n  const limits = await checkUserLimits()\n  \n  if (!limits) {\n    return { allowed: false, reason: 'Plan bilgileri alınamadı' }\n  }\n\n  if (!limits.can_create_prompt) {\n    return { \n      allowed: false, \n      reason: `Prompt limiti aşıldı (${limits.current_prompts}/${limits.max_prompts_per_project})` \n    }\n  }\n\n  // Eğer belirli bir proje için kontrol ediliyorsa, o projenin prompt sayısını kontrol et\n  if (projectId) {\n    try {\n      const { count, error } = await supabase\n        .from('prompts')\n        .select('*', { count: 'exact', head: true })\n        .eq('project_id', projectId)\n\n      if (error) {\n        console.error('Proje prompt sayısı kontrol edilemedi:', error)\n        return { allowed: false, reason: 'Proje bilgileri alınamadı' }\n      }\n\n      const currentPrompts = count || 0\n      if (limits.max_prompts_per_project !== -1 && currentPrompts >= limits.max_prompts_per_project) {\n        return { \n          allowed: false, \n          reason: `Bu proje için prompt limiti aşıldı (${currentPrompts}/${limits.max_prompts_per_project})` \n        }\n      }\n    } catch (error) {\n      console.error('Proje prompt sayısı kontrol hatası:', error)\n      return { allowed: false, reason: 'Proje bilgileri kontrol edilemedi' }\n    }\n  }\n\n  return { allowed: true }\n}\n\n// Plan özelliği kontrol et\nexport async function hasPlanFeature(featureName: string): Promise<boolean> {\n  const plan = await getUserActivePlan()\n  return plan?.features?.[featureName] === true\n}\n\n// Kullanım istatistiklerini güncelle\nexport async function updateUsageStats(): Promise<void> {\n  try {\n    const { data: user } = await supabase.auth.getUser()\n    if (!user.user) {\n      throw new Error('Kullanıcı oturumu bulunamadı')\n    }\n\n    const { error } = await supabase.rpc('update_usage_stats', {\n      user_uuid: user.user.id\n    })\n\n    if (error) {\n      console.error('Kullanım istatistikleri güncellenemedi:', error)\n      throw new Error(error.message)\n    }\n  } catch (error) {\n    console.error('Kullanım istatistikleri güncelleme hatası:', error)\n    throw error\n  }\n}\n\n// Plan upgrade önerisi\nexport function getPlanUpgradeSuggestion(limits: PlanLimits): {\n  shouldUpgrade: boolean\n  reason: string\n  suggestedPlan: string\n} {\n  // Proje limiti %80'e ulaştıysa\n  if (limits.max_projects !== -1 && limits.current_projects / limits.max_projects >= 0.8) {\n    return {\n      shouldUpgrade: true,\n      reason: 'Proje limitinizin %80\\'ine ulaştınız',\n      suggestedPlan: 'professional'\n    }\n  }\n\n  // Prompt limiti %80'e ulaştıysa\n  if (limits.max_prompts_per_project !== -1 && limits.current_prompts / limits.max_prompts_per_project >= 0.8) {\n    return {\n      shouldUpgrade: true,\n      reason: 'Prompt limitinizin %80\\'ine ulaştınız',\n      suggestedPlan: 'professional'\n    }\n  }\n\n  return {\n    shouldUpgrade: false,\n    reason: '',\n    suggestedPlan: ''\n  }\n}\n\n// Plan limiti hata mesajları\nexport const PLAN_LIMIT_MESSAGES = {\n  PROJECT_LIMIT_REACHED: 'Proje oluşturma limitinize ulaştınız. Daha fazla proje oluşturmak için planınızı yükseltin.',\n  PROMPT_LIMIT_REACHED: 'Prompt oluşturma limitinize ulaştınız. Daha fazla prompt oluşturmak için planınızı yükseltin.',\n  FEATURE_NOT_AVAILABLE: 'Bu özellik mevcut planınızda bulunmamaktadır. Planınızı yükseltin.',\n  PLAN_EXPIRED: 'Planınızın süresi dolmuştur. Lütfen planınızı yenileyin.',\n  PLAN_SUSPENDED: 'Hesabınız askıya alınmıştır. Lütfen destek ekibi ile iletişime geçin.'\n}\n\n// Plan durumu kontrol et\nexport async function checkPlanStatus(): Promise<{\n  isActive: boolean\n  status: string\n  message?: string\n}> {\n  const plan = await getUserActivePlan()\n  \n  if (!plan) {\n    return {\n      isActive: false,\n      status: 'no_plan',\n      message: 'Aktif plan bulunamadı'\n    }\n  }\n\n  if (plan.status !== 'active') {\n    return {\n      isActive: false,\n      status: plan.status,\n      message: PLAN_LIMIT_MESSAGES.PLAN_SUSPENDED\n    }\n  }\n\n  if (plan.expires_at && new Date(plan.expires_at) < new Date()) {\n    return {\n      isActive: false,\n      status: 'expired',\n      message: PLAN_LIMIT_MESSAGES.PLAN_EXPIRED\n    }\n  }\n\n  return {\n    isActive: true,\n    status: 'active'\n  }\n}\n"], "names": [], "mappings": "AAAA,6DAA6D;;;;;;;;;;;;AAE7D;;AAuBO,eAAe;IACpB,IAAI;QACF,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;QAClD,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,qBAAqB;YAC9D,WAAW,KAAK,IAAI,CAAC,EAAE;QACzB;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;QAEA,OAAO,CAAA,iBAAA,2BAAA,IAAM,CAAC,EAAE,KAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;IACT;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;QAClD,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,wBAAwB;YACjE,WAAW,KAAK,IAAI,CAAC,EAAE;QACzB;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;QAEA,OAAO,CAAA,iBAAA,2BAAA,IAAM,CAAC,EAAE,KAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF;AAGO,eAAe;IACpB,MAAM,SAAS,MAAM;IAErB,IAAI,CAAC,QAAQ;QACX,OAAO;YAAE,SAAS;YAAO,QAAQ;QAA2B;IAC9D;IAEA,IAAI,CAAC,OAAO,kBAAkB,EAAE;QAC9B,OAAO;YACL,SAAS;YACT,QAAQ,AAAC,wBAAkD,OAA3B,OAAO,gBAAgB,EAAC,KAAuB,OAApB,OAAO,YAAY,EAAC;QACjF;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,eAAe,gBAAgB,SAAkB;IACtD,MAAM,SAAS,MAAM;IAErB,IAAI,CAAC,QAAQ;QACX,OAAO;YAAE,SAAS;YAAO,QAAQ;QAA2B;IAC9D;IAEA,IAAI,CAAC,OAAO,iBAAiB,EAAE;QAC7B,OAAO;YACL,SAAS;YACT,QAAQ,AAAC,yBAAkD,OAA1B,OAAO,eAAe,EAAC,KAAkC,OAA/B,OAAO,uBAAuB,EAAC;QAC5F;IACF;IAEA,wFAAwF;IACxF,IAAI,WAAW;QACb,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACpC,IAAI,CAAC,WACL,MAAM,CAAC,KAAK;gBAAE,OAAO;gBAAS,MAAM;YAAK,GACzC,EAAE,CAAC,cAAc;YAEpB,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,0CAA0C;gBACxD,OAAO;oBAAE,SAAS;oBAAO,QAAQ;gBAA4B;YAC/D;YAEA,MAAM,iBAAiB,SAAS;YAChC,IAAI,OAAO,uBAAuB,KAAK,CAAC,KAAK,kBAAkB,OAAO,uBAAuB,EAAE;gBAC7F,OAAO;oBACL,SAAS;oBACT,QAAQ,AAAC,uCAAwD,OAAlB,gBAAe,KAAkC,OAA/B,OAAO,uBAAuB,EAAC;gBAClG;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,OAAO;gBAAE,SAAS;gBAAO,QAAQ;YAAoC;QACvE;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,eAAe,eAAe,WAAmB;QAE/C;IADP,MAAM,OAAO,MAAM;IACnB,OAAO,CAAA,iBAAA,4BAAA,iBAAA,KAAM,QAAQ,cAAd,qCAAA,cAAgB,CAAC,YAAY,MAAK;AAC3C;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;QAClD,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,sBAAsB;YACzD,WAAW,KAAK,IAAI,CAAC,EAAE;QACzB;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2CAA2C;YACzD,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,MAAM;IACR;AACF;AAGO,SAAS,yBAAyB,MAAkB;IAKzD,+BAA+B;IAC/B,IAAI,OAAO,YAAY,KAAK,CAAC,KAAK,OAAO,gBAAgB,GAAG,OAAO,YAAY,IAAI,KAAK;QACtF,OAAO;YACL,eAAe;YACf,QAAQ;YACR,eAAe;QACjB;IACF;IAEA,gCAAgC;IAChC,IAAI,OAAO,uBAAuB,KAAK,CAAC,KAAK,OAAO,eAAe,GAAG,OAAO,uBAAuB,IAAI,KAAK;QAC3G,OAAO;YACL,eAAe;YACf,QAAQ;YACR,eAAe;QACjB;IACF;IAEA,OAAO;QACL,eAAe;QACf,QAAQ;QACR,eAAe;IACjB;AACF;AAGO,MAAM,sBAAsB;IACjC,uBAAuB;IACvB,sBAAsB;IACtB,uBAAuB;IACvB,cAAc;IACd,gBAAgB;AAClB;AAGO,eAAe;IAKpB,MAAM,OAAO,MAAM;IAEnB,IAAI,CAAC,MAAM;QACT,OAAO;YACL,UAAU;YACV,QAAQ;YACR,SAAS;QACX;IACF;IAEA,IAAI,KAAK,MAAM,KAAK,UAAU;QAC5B,OAAO;YACL,UAAU;YACV,QAAQ,KAAK,MAAM;YACnB,SAAS,oBAAoB,cAAc;QAC7C;IACF;IAEA,IAAI,KAAK,UAAU,IAAI,IAAI,KAAK,KAAK,UAAU,IAAI,IAAI,QAAQ;QAC7D,OAAO;YACL,UAAU;YACV,QAAQ;YACR,SAAS,oBAAoB,YAAY;QAC3C;IACF;IAEA,OAAO;QACL,UAAU;QACV,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 320, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/project-validation.ts"], "sourcesContent": ["/**\n * Proje adı validation ve güvenlik utilities\n * Güvenlik odaklı input validation ve sanitization\n */\n\n// Proje adı limitleri ve kuralları\nexport const PROJECT_NAME_RULES = {\n  minLength: 3,\n  maxLength: 50,\n  allowedCharsRegex: /^[a-zA-Z0-9\\s\\-_.]+$/,\n  debounceMs: 300,\n  rateLimit: {\n    maxRequests: 10,\n    windowMinutes: 1\n  }\n} as const;\n\n// Validation sonuç tipi\nexport interface ValidationResult {\n  isValid: boolean;\n  error?: string;\n  sanitizedValue?: string;\n}\n\n// Rate limiting için local storage key\nconst RATE_LIMIT_KEY = 'project_name_update_rate_limit';\n\n/**\n * Proje adını sanitize eder\n */\nexport function sanitizeProjectName(name: string): string {\n  if (!name) return '';\n  \n  // Trim ve normalize\n  let sanitized = name.trim();\n  \n  // Çoklu boşlukları tek boşluğa çevir\n  sanitized = sanitized.replace(/\\s+/g, ' ');\n  \n  // Başlangıç ve bitiş boşluklarını kaldır\n  sanitized = sanitized.trim();\n  \n  return sanitized;\n}\n\n/**\n * Proje adını validate eder\n */\nexport function validateProjectName(name: string): ValidationResult {\n  const sanitized = sanitizeProjectName(name);\n  \n  // Boş kontrol\n  if (!sanitized) {\n    return {\n      isValid: false,\n      error: 'Proje adı boş olamaz'\n    };\n  }\n  \n  // Uzunluk kontrol\n  if (sanitized.length < PROJECT_NAME_RULES.minLength) {\n    return {\n      isValid: false,\n      error: `Proje adı en az ${PROJECT_NAME_RULES.minLength} karakter olmalıdır`\n    };\n  }\n  \n  if (sanitized.length > PROJECT_NAME_RULES.maxLength) {\n    return {\n      isValid: false,\n      error: `Proje adı en fazla ${PROJECT_NAME_RULES.maxLength} karakter olabilir`\n    };\n  }\n  \n  // Karakter kontrol\n  if (!PROJECT_NAME_RULES.allowedCharsRegex.test(sanitized)) {\n    return {\n      isValid: false,\n      error: 'Proje adı sadece harf, rakam, boşluk ve -_. karakterlerini içerebilir'\n    };\n  }\n  \n  // Sadece boşluk kontrolü\n  if (sanitized.replace(/\\s/g, '').length === 0) {\n    return {\n      isValid: false,\n      error: 'Proje adı sadece boşluk karakterlerinden oluşamaz'\n    };\n  }\n  \n  return {\n    isValid: true,\n    sanitizedValue: sanitized\n  };\n}\n\n/**\n * XSS koruması için HTML encode\n */\nexport function escapeHtml(text: string): string {\n  const div = document.createElement('div');\n  div.textContent = text;\n  return div.innerHTML;\n}\n\n/**\n * Client-side rate limiting kontrolü\n */\nexport function checkClientRateLimit(): boolean {\n  try {\n    const stored = localStorage.getItem(RATE_LIMIT_KEY);\n    const now = Date.now();\n    \n    if (!stored) {\n      // İlk istek\n      localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify({\n        count: 1,\n        windowStart: now\n      }));\n      return true;\n    }\n    \n    const data = JSON.parse(stored);\n    const windowDuration = PROJECT_NAME_RULES.rateLimit.windowMinutes * 60 * 1000; // ms\n    \n    // Pencere süresi dolmuş mu?\n    if (now - data.windowStart > windowDuration) {\n      // Yeni pencere başlat\n      localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify({\n        count: 1,\n        windowStart: now\n      }));\n      return true;\n    }\n    \n    // Limit kontrolü\n    if (data.count >= PROJECT_NAME_RULES.rateLimit.maxRequests) {\n      return false;\n    }\n    \n    // Sayacı artır\n    localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify({\n      count: data.count + 1,\n      windowStart: data.windowStart\n    }));\n    \n    return true;\n  } catch (error) {\n    console.warn('Rate limit check failed:', error);\n    return true; // Hata durumunda izin ver\n  }\n}\n\n/**\n * Rate limit reset\n */\nexport function resetClientRateLimit(): void {\n  try {\n    localStorage.removeItem(RATE_LIMIT_KEY);\n  } catch (error) {\n    console.warn('Rate limit reset failed:', error);\n  }\n}\n\n/**\n * Debounced validation hook için utility\n */\nexport function createDebouncedValidator(\n  validator: (value: string) => ValidationResult,\n  delay: number = PROJECT_NAME_RULES.debounceMs\n) {\n  let timeoutId: NodeJS.Timeout;\n  \n  return (value: string, callback: (result: ValidationResult) => void) => {\n    clearTimeout(timeoutId);\n    timeoutId = setTimeout(() => {\n      const result = validator(value);\n      callback(result);\n    }, delay);\n  };\n}\n\n/**\n * Güvenli proje adı karşılaştırması\n */\nexport function isSameProjectName(name1: string, name2: string): boolean {\n  const sanitized1 = sanitizeProjectName(name1).toLowerCase();\n  const sanitized2 = sanitizeProjectName(name2).toLowerCase();\n  return sanitized1 === sanitized2;\n}\n\n/**\n * Duplicate name kontrolü için async validation\n */\nexport async function validateProjectNameUnique(\n  name: string,\n  currentProjectId: string,\n  projects: Array<{ id: string; name: string }>\n): Promise<ValidationResult> {\n  const sanitized = sanitizeProjectName(name);\n\n  // Önce temel validation\n  const basicValidation = validateProjectName(name);\n  if (!basicValidation.isValid) {\n    return basicValidation;\n  }\n\n  // Duplicate kontrolü (case-insensitive)\n  const isDuplicate = projects.some(project =>\n    project.id !== currentProjectId &&\n    isSameProjectName(project.name, sanitized)\n  );\n\n  if (isDuplicate) {\n    return {\n      isValid: false,\n      error: 'Bu isimde bir proje zaten mevcut'\n    };\n  }\n\n  return {\n    isValid: true,\n    sanitizedValue: sanitized\n  };\n}\n\n/**\n * Error mesajlarını kullanıcı dostu hale getir\n */\nexport function formatValidationError(error: string): string {\n  // Teknik hataları kullanıcı dostu mesajlara çevir\n  const errorMap: Record<string, string> = {\n    'unique_violation': 'Bu isimde bir proje zaten mevcut',\n    'check_violation': 'Proje adı geçersiz karakterler içeriyor',\n    'not_null_violation': 'Proje adı boş olamaz',\n    'foreign_key_violation': 'Geçersiz proje referansı',\n    'RateLimitExceeded': 'Çok fazla güncelleme isteği. Lütfen bekleyin.',\n    'InvalidInput': 'Geçersiz proje adı',\n    'DuplicateName': 'Bu isimde bir proje zaten mevcut',\n    'NotFound': 'Proje bulunamadı',\n    'Unauthorized': 'Bu işlem için yetkiniz yok'\n  };\n\n  return errorMap[error] || error;\n}\n\n/**\n * Advanced debounced validator with duplicate check\n */\nexport function createAdvancedDebouncedValidator(\n  projects: Array<{ id: string; name: string }>,\n  currentProjectId: string,\n  delay: number = PROJECT_NAME_RULES.debounceMs\n) {\n  let timeoutId: NodeJS.Timeout;\n\n  return (value: string, callback: (result: ValidationResult) => void) => {\n    clearTimeout(timeoutId);\n    timeoutId = setTimeout(async () => {\n      const result = await validateProjectNameUnique(value, currentProjectId, projects);\n      callback(result);\n    }, delay);\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,mCAAmC;;;;;;;;;;;;;;AAC5B,MAAM,qBAAqB;IAChC,WAAW;IACX,WAAW;IACX,mBAAmB;IACnB,YAAY;IACZ,WAAW;QACT,aAAa;QACb,eAAe;IACjB;AACF;AASA,uCAAuC;AACvC,MAAM,iBAAiB;AAKhB,SAAS,oBAAoB,IAAY;IAC9C,IAAI,CAAC,MAAM,OAAO;IAElB,oBAAoB;IACpB,IAAI,YAAY,KAAK,IAAI;IAEzB,qCAAqC;IACrC,YAAY,UAAU,OAAO,CAAC,QAAQ;IAEtC,yCAAyC;IACzC,YAAY,UAAU,IAAI;IAE1B,OAAO;AACT;AAKO,SAAS,oBAAoB,IAAY;IAC9C,MAAM,YAAY,oBAAoB;IAEtC,cAAc;IACd,IAAI,CAAC,WAAW;QACd,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,kBAAkB;IAClB,IAAI,UAAU,MAAM,GAAG,mBAAmB,SAAS,EAAE;QACnD,OAAO;YACL,SAAS;YACT,OAAO,AAAC,mBAA+C,OAA7B,mBAAmB,SAAS,EAAC;QACzD;IACF;IAEA,IAAI,UAAU,MAAM,GAAG,mBAAmB,SAAS,EAAE;QACnD,OAAO;YACL,SAAS;YACT,OAAO,AAAC,sBAAkD,OAA7B,mBAAmB,SAAS,EAAC;QAC5D;IACF;IAEA,mBAAmB;IACnB,IAAI,CAAC,mBAAmB,iBAAiB,CAAC,IAAI,CAAC,YAAY;QACzD,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,yBAAyB;IACzB,IAAI,UAAU,OAAO,CAAC,OAAO,IAAI,MAAM,KAAK,GAAG;QAC7C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,OAAO;QACL,SAAS;QACT,gBAAgB;IAClB;AACF;AAKO,SAAS,WAAW,IAAY;IACrC,MAAM,MAAM,SAAS,aAAa,CAAC;IACnC,IAAI,WAAW,GAAG;IAClB,OAAO,IAAI,SAAS;AACtB;AAKO,SAAS;IACd,IAAI;QACF,MAAM,SAAS,aAAa,OAAO,CAAC;QACpC,MAAM,MAAM,KAAK,GAAG;QAEpB,IAAI,CAAC,QAAQ;YACX,YAAY;YACZ,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;gBAClD,OAAO;gBACP,aAAa;YACf;YACA,OAAO;QACT;QAEA,MAAM,OAAO,KAAK,KAAK,CAAC;QACxB,MAAM,iBAAiB,mBAAmB,SAAS,CAAC,aAAa,GAAG,KAAK,MAAM,KAAK;QAEpF,4BAA4B;QAC5B,IAAI,MAAM,KAAK,WAAW,GAAG,gBAAgB;YAC3C,sBAAsB;YACtB,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;gBAClD,OAAO;gBACP,aAAa;YACf;YACA,OAAO;QACT;QAEA,iBAAiB;QACjB,IAAI,KAAK,KAAK,IAAI,mBAAmB,SAAS,CAAC,WAAW,EAAE;YAC1D,OAAO;QACT;QAEA,eAAe;QACf,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;YAClD,OAAO,KAAK,KAAK,GAAG;YACpB,aAAa,KAAK,WAAW;QAC/B;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,4BAA4B;QACzC,OAAO,MAAM,0BAA0B;IACzC;AACF;AAKO,SAAS;IACd,IAAI;QACF,aAAa,UAAU,CAAC;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,4BAA4B;IAC3C;AACF;AAKO,SAAS,yBACd,SAA8C;QAC9C,QAAA,iEAAgB,mBAAmB,UAAU;IAE7C,IAAI;IAEJ,OAAO,CAAC,OAAe;QACrB,aAAa;QACb,YAAY,WAAW;YACrB,MAAM,SAAS,UAAU;YACzB,SAAS;QACX,GAAG;IACL;AACF;AAKO,SAAS,kBAAkB,KAAa,EAAE,KAAa;IAC5D,MAAM,aAAa,oBAAoB,OAAO,WAAW;IACzD,MAAM,aAAa,oBAAoB,OAAO,WAAW;IACzD,OAAO,eAAe;AACxB;AAKO,eAAe,0BACpB,IAAY,EACZ,gBAAwB,EACxB,QAA6C;IAE7C,MAAM,YAAY,oBAAoB;IAEtC,wBAAwB;IACxB,MAAM,kBAAkB,oBAAoB;IAC5C,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAC5B,OAAO;IACT;IAEA,wCAAwC;IACxC,MAAM,cAAc,SAAS,IAAI,CAAC,CAAA,UAChC,QAAQ,EAAE,KAAK,oBACf,kBAAkB,QAAQ,IAAI,EAAE;IAGlC,IAAI,aAAa;QACf,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,OAAO;QACL,SAAS;QACT,gBAAgB;IAClB;AACF;AAKO,SAAS,sBAAsB,KAAa;IACjD,kDAAkD;IAClD,MAAM,WAAmC;QACvC,oBAAoB;QACpB,mBAAmB;QACnB,sBAAsB;QACtB,yBAAyB;QACzB,qBAAqB;QACrB,gBAAgB;QAChB,iBAAiB;QACjB,YAAY;QACZ,gBAAgB;IAClB;IAEA,OAAO,QAAQ,CAAC,MAAM,IAAI;AAC5B;AAKO,SAAS,iCACd,QAA6C,EAC7C,gBAAwB;QACxB,QAAA,iEAAgB,mBAAmB,UAAU;IAE7C,IAAI;IAEJ,OAAO,CAAC,OAAe;QACrB,aAAa;QACb,YAAY,WAAW;YACrB,MAAM,SAAS,MAAM,0BAA0B,OAAO,kBAAkB;YACxE,SAAS;QACX,GAAG;IACL;AACF", "debugId": null}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/rate-limiter.ts"], "sourcesContent": ["'use client'\n\n/**\n * Advanced Rate Limiting System for PromptFlow\n * Provides both client-side and server-side rate limiting\n */\n\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\n\n// Rate limiting configurations for different actions\nexport const RATE_LIMITS = {\n  // Authentication actions\n  login: { maxRequests: 5, windowMinutes: 15 },\n  signup: { maxRequests: 3, windowMinutes: 60 },\n  password_reset: { maxRequests: 3, windowMinutes: 60 },\n  \n  // Project actions\n  project_create: { maxRequests: 10, windowMinutes: 60 },\n  project_update: { maxRequests: 20, windowMinutes: 10 },\n  project_delete: { maxRequests: 5, windowMinutes: 60 },\n  \n  // Prompt actions\n  prompt_create: { maxRequests: 50, windowMinutes: 10 },\n  prompt_update: { maxRequests: 100, windowMinutes: 10 },\n  prompt_delete: { maxRequests: 20, windowMinutes: 10 },\n  \n  // Context actions\n  context_create: { maxRequests: 20, windowMinutes: 10 },\n  context_batch_add: { maxRequests: 5, windowMinutes: 10 },\n  \n  // Plan actions\n  plan_change: { maxRequests: 3, windowMinutes: 60 },\n  \n  // General API\n  api_general: { maxRequests: 200, windowMinutes: 10 },\n} as const\n\nexport type RateLimitAction = keyof typeof RATE_LIMITS\n\ninterface RateLimitData {\n  count: number\n  windowStart: number\n  lastRequest: number\n}\n\n/**\n * Client-side rate limiting with localStorage\n */\nexport class ClientRateLimiter {\n  private getStorageKey(action: RateLimitAction, userId?: string): string {\n    const userSuffix = userId ? `_${userId}` : ''\n    return `rate_limit_${action}${userSuffix}`\n  }\n\n  /**\n   * Check if action is allowed based on rate limits\n   */\n  async checkLimit(action: RateLimitAction, userId?: string): Promise<{\n    allowed: boolean\n    remainingRequests: number\n    resetTime: number\n    retryAfter?: number\n  }> {\n    try {\n      const config = RATE_LIMITS[action]\n      const storageKey = this.getStorageKey(action, userId)\n      const now = Date.now()\n      const windowMs = config.windowMinutes * 60 * 1000\n\n      // Get current data\n      const stored = localStorage.getItem(storageKey)\n      let data: RateLimitData\n\n      if (!stored) {\n        // First request\n        data = {\n          count: 1,\n          windowStart: now,\n          lastRequest: now\n        }\n        localStorage.setItem(storageKey, JSON.stringify(data))\n        \n        return {\n          allowed: true,\n          remainingRequests: config.maxRequests - 1,\n          resetTime: now + windowMs\n        }\n      }\n\n      data = JSON.parse(stored)\n\n      // Check if window has expired\n      if (now - data.windowStart > windowMs) {\n        // Reset window\n        data = {\n          count: 1,\n          windowStart: now,\n          lastRequest: now\n        }\n        localStorage.setItem(storageKey, JSON.stringify(data))\n        \n        return {\n          allowed: true,\n          remainingRequests: config.maxRequests - 1,\n          resetTime: now + windowMs\n        }\n      }\n\n      // Check if limit exceeded\n      if (data.count >= config.maxRequests) {\n        const resetTime = data.windowStart + windowMs\n        const retryAfter = Math.ceil((resetTime - now) / 1000)\n        \n        return {\n          allowed: false,\n          remainingRequests: 0,\n          resetTime,\n          retryAfter\n        }\n      }\n\n      // Increment counter\n      data.count++\n      data.lastRequest = now\n      localStorage.setItem(storageKey, JSON.stringify(data))\n\n      return {\n        allowed: true,\n        remainingRequests: config.maxRequests - data.count,\n        resetTime: data.windowStart + windowMs\n      }\n    } catch (error) {\n      console.warn(`Rate limit check failed for ${action}:`, error)\n      // On error, allow the request but log it\n      return {\n        allowed: true,\n        remainingRequests: 0,\n        resetTime: Date.now() + 60000 // 1 minute fallback\n      }\n    }\n  }\n\n  /**\n   * Reset rate limit for specific action\n   */\n  resetLimit(action: RateLimitAction, userId?: string): void {\n    try {\n      const storageKey = this.getStorageKey(action, userId)\n      localStorage.removeItem(storageKey)\n    } catch (error) {\n      console.warn(`Rate limit reset failed for ${action}:`, error)\n    }\n  }\n\n  /**\n   * Get current rate limit status\n   */\n  getStatus(action: RateLimitAction, userId?: string): {\n    count: number\n    remaining: number\n    resetTime: number\n  } | null {\n    try {\n      const config = RATE_LIMITS[action]\n      const storageKey = this.getStorageKey(action, userId)\n      const stored = localStorage.getItem(storageKey)\n      \n      if (!stored) {\n        return {\n          count: 0,\n          remaining: config.maxRequests,\n          resetTime: Date.now() + config.windowMinutes * 60 * 1000\n        }\n      }\n\n      const data: RateLimitData = JSON.parse(stored)\n      const windowMs = config.windowMinutes * 60 * 1000\n      const now = Date.now()\n\n      // Check if window expired\n      if (now - data.windowStart > windowMs) {\n        return {\n          count: 0,\n          remaining: config.maxRequests,\n          resetTime: now + windowMs\n        }\n      }\n\n      return {\n        count: data.count,\n        remaining: Math.max(0, config.maxRequests - data.count),\n        resetTime: data.windowStart + windowMs\n      }\n    } catch (error) {\n      console.warn(`Rate limit status check failed for ${action}:`, error)\n      return null\n    }\n  }\n}\n\n/**\n * Server-side rate limiting with Supabase\n */\nexport class ServerRateLimiter {\n  /**\n   * Check server-side rate limit using Supabase function\n   */\n  async checkLimit(\n    action: RateLimitAction,\n    userId?: string\n  ): Promise<{\n    allowed: boolean\n    remainingRequests: number\n    resetTime: number\n    retryAfter?: number\n  }> {\n    try {\n      const config = RATE_LIMITS[action]\n      \n      // Get current user if not provided\n      let targetUserId = userId\n      if (!targetUserId) {\n        const { data: { user } } = await supabase.auth.getUser()\n        if (!user) {\n          throw new Error('User not authenticated')\n        }\n        targetUserId = user.id\n      }\n\n      // Call Supabase rate limiting function\n      const { data, error } = await supabase.rpc('check_rate_limit', {\n        p_user_id: targetUserId,\n        p_action_type: action,\n        p_max_requests: config.maxRequests,\n        p_window_minutes: config.windowMinutes\n      })\n\n      if (error) {\n        console.error(`Server rate limit check failed for ${action}:`, error)\n        // On error, allow but log\n        return {\n          allowed: true,\n          remainingRequests: 0,\n          resetTime: Date.now() + config.windowMinutes * 60 * 1000\n        }\n      }\n\n      const allowed = data === true\n      const windowMs = config.windowMinutes * 60 * 1000\n      const resetTime = Date.now() + windowMs\n\n      if (!allowed) {\n        return {\n          allowed: false,\n          remainingRequests: 0,\n          resetTime,\n          retryAfter: Math.ceil(windowMs / 1000)\n        }\n      }\n\n      return {\n        allowed: true,\n        remainingRequests: config.maxRequests - 1, // Approximate\n        resetTime\n      }\n    } catch (error) {\n      console.error(`Server rate limit error for ${action}:`, error)\n      // On error, allow but log\n      return {\n        allowed: true,\n        remainingRequests: 0,\n        resetTime: Date.now() + 60000\n      }\n    }\n  }\n}\n\n// Global instances\nexport const clientRateLimiter = new ClientRateLimiter()\nexport const serverRateLimiter = new ServerRateLimiter()\n\n/**\n * Combined rate limiting check (client + server)\n */\nexport async function checkRateLimit(\n  action: RateLimitAction,\n  options: {\n    clientOnly?: boolean\n    serverOnly?: boolean\n    userId?: string\n  } = {}\n): Promise<{\n  allowed: boolean\n  remainingRequests: number\n  resetTime: number\n  retryAfter?: number\n  source: 'client' | 'server' | 'both'\n}> {\n  const { clientOnly = false, serverOnly = false, userId } = options\n\n  try {\n    // Client-side check\n    if (!serverOnly) {\n      const clientResult = await clientRateLimiter.checkLimit(action, userId)\n      if (!clientResult.allowed) {\n        return {\n          ...clientResult,\n          source: 'client'\n        }\n      }\n      \n      if (clientOnly) {\n        return {\n          ...clientResult,\n          source: 'client'\n        }\n      }\n    }\n\n    // Server-side check\n    if (!clientOnly) {\n      const serverResult = await serverRateLimiter.checkLimit(action, userId)\n      return {\n        ...serverResult,\n        source: serverOnly ? 'server' : 'both'\n      }\n    }\n\n    // Fallback (shouldn't reach here)\n    return {\n      allowed: true,\n      remainingRequests: 0,\n      resetTime: Date.now() + 60000,\n      source: 'client'\n    }\n  } catch (error) {\n    console.error(`Rate limit check failed for ${action}:`, error)\n    return {\n      allowed: true,\n      remainingRequests: 0,\n      resetTime: Date.now() + 60000,\n      source: 'client'\n    }\n  }\n}\n\n/**\n * Rate limit error class\n */\nexport class RateLimitError extends Error {\n  constructor(\n    public action: RateLimitAction,\n    public retryAfter: number,\n    public resetTime: number\n  ) {\n    super(`Rate limit exceeded for ${action}. Try again in ${retryAfter} seconds.`)\n    this.name = 'RateLimitError'\n  }\n}\n\n/**\n * Rate limiting hook for React components\n */\nexport function useRateLimit(action: RateLimitAction) {\n  return {\n    checkLimit: (options?: { clientOnly?: boolean; serverOnly?: boolean }) =>\n      checkRateLimit(action, options),\n    resetLimit: () => clientRateLimiter.resetLimit(action),\n    getStatus: () => clientRateLimiter.getStatus(action)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;;;CAGC,GAED;AAPA;;;AAUO,MAAM,cAAc;IACzB,yBAAyB;IACzB,OAAO;QAAE,aAAa;QAAG,eAAe;IAAG;IAC3C,QAAQ;QAAE,aAAa;QAAG,eAAe;IAAG;IAC5C,gBAAgB;QAAE,aAAa;QAAG,eAAe;IAAG;IAEpD,kBAAkB;IAClB,gBAAgB;QAAE,aAAa;QAAI,eAAe;IAAG;IACrD,gBAAgB;QAAE,aAAa;QAAI,eAAe;IAAG;IACrD,gBAAgB;QAAE,aAAa;QAAG,eAAe;IAAG;IAEpD,iBAAiB;IACjB,eAAe;QAAE,aAAa;QAAI,eAAe;IAAG;IACpD,eAAe;QAAE,aAAa;QAAK,eAAe;IAAG;IACrD,eAAe;QAAE,aAAa;QAAI,eAAe;IAAG;IAEpD,kBAAkB;IAClB,gBAAgB;QAAE,aAAa;QAAI,eAAe;IAAG;IACrD,mBAAmB;QAAE,aAAa;QAAG,eAAe;IAAG;IAEvD,eAAe;IACf,aAAa;QAAE,aAAa;QAAG,eAAe;IAAG;IAEjD,cAAc;IACd,aAAa;QAAE,aAAa;QAAK,eAAe;IAAG;AACrD;AAaO,MAAM;IACH,cAAc,MAAuB,EAAE,MAAe,EAAU;QACtE,MAAM,aAAa,SAAS,AAAC,IAAU,OAAP,UAAW;QAC3C,OAAO,AAAC,cAAsB,OAAT,QAAoB,OAAX;IAChC;IAEA;;GAEC,GACD,MAAM,WAAW,MAAuB,EAAE,MAAe,EAKtD;QACD,IAAI;YACF,MAAM,SAAS,WAAW,CAAC,OAAO;YAClC,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC,QAAQ;YAC9C,MAAM,MAAM,KAAK,GAAG;YACpB,MAAM,WAAW,OAAO,aAAa,GAAG,KAAK;YAE7C,mBAAmB;YACnB,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,IAAI;YAEJ,IAAI,CAAC,QAAQ;gBACX,gBAAgB;gBAChB,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,aAAa;gBACf;gBACA,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;gBAEhD,OAAO;oBACL,SAAS;oBACT,mBAAmB,OAAO,WAAW,GAAG;oBACxC,WAAW,MAAM;gBACnB;YACF;YAEA,OAAO,KAAK,KAAK,CAAC;YAElB,8BAA8B;YAC9B,IAAI,MAAM,KAAK,WAAW,GAAG,UAAU;gBACrC,eAAe;gBACf,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,aAAa;gBACf;gBACA,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;gBAEhD,OAAO;oBACL,SAAS;oBACT,mBAAmB,OAAO,WAAW,GAAG;oBACxC,WAAW,MAAM;gBACnB;YACF;YAEA,0BAA0B;YAC1B,IAAI,KAAK,KAAK,IAAI,OAAO,WAAW,EAAE;gBACpC,MAAM,YAAY,KAAK,WAAW,GAAG;gBACrC,MAAM,aAAa,KAAK,IAAI,CAAC,CAAC,YAAY,GAAG,IAAI;gBAEjD,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB;oBACA;gBACF;YACF;YAEA,oBAAoB;YACpB,KAAK,KAAK;YACV,KAAK,WAAW,GAAG;YACnB,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;YAEhD,OAAO;gBACL,SAAS;gBACT,mBAAmB,OAAO,WAAW,GAAG,KAAK,KAAK;gBAClD,WAAW,KAAK,WAAW,GAAG;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,AAAC,+BAAqC,OAAP,QAAO,MAAI;YACvD,yCAAyC;YACzC,OAAO;gBACL,SAAS;gBACT,mBAAmB;gBACnB,WAAW,KAAK,GAAG,KAAK,MAAM,oBAAoB;YACpD;QACF;IACF;IAEA;;GAEC,GACD,WAAW,MAAuB,EAAE,MAAe,EAAQ;QACzD,IAAI;YACF,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC,QAAQ;YAC9C,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,AAAC,+BAAqC,OAAP,QAAO,MAAI;QACzD;IACF;IAEA;;GAEC,GACD,UAAU,MAAuB,EAAE,MAAe,EAIzC;QACP,IAAI;YACF,MAAM,SAAS,WAAW,CAAC,OAAO;YAClC,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC,QAAQ;YAC9C,MAAM,SAAS,aAAa,OAAO,CAAC;YAEpC,IAAI,CAAC,QAAQ;gBACX,OAAO;oBACL,OAAO;oBACP,WAAW,OAAO,WAAW;oBAC7B,WAAW,KAAK,GAAG,KAAK,OAAO,aAAa,GAAG,KAAK;gBACtD;YACF;YAEA,MAAM,OAAsB,KAAK,KAAK,CAAC;YACvC,MAAM,WAAW,OAAO,aAAa,GAAG,KAAK;YAC7C,MAAM,MAAM,KAAK,GAAG;YAEpB,0BAA0B;YAC1B,IAAI,MAAM,KAAK,WAAW,GAAG,UAAU;gBACrC,OAAO;oBACL,OAAO;oBACP,WAAW,OAAO,WAAW;oBAC7B,WAAW,MAAM;gBACnB;YACF;YAEA,OAAO;gBACL,OAAO,KAAK,KAAK;gBACjB,WAAW,KAAK,GAAG,CAAC,GAAG,OAAO,WAAW,GAAG,KAAK,KAAK;gBACtD,WAAW,KAAK,WAAW,GAAG;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,AAAC,sCAA4C,OAAP,QAAO,MAAI;YAC9D,OAAO;QACT;IACF;AACF;AAKO,MAAM;IACX;;GAEC,GACD,MAAM,WACJ,MAAuB,EACvB,MAAe,EAMd;QACD,IAAI;YACF,MAAM,SAAS,WAAW,CAAC,OAAO;YAElC,mCAAmC;YACnC,IAAI,eAAe;YACnB,IAAI,CAAC,cAAc;gBACjB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;gBACtD,IAAI,CAAC,MAAM;oBACT,MAAM,IAAI,MAAM;gBAClB;gBACA,eAAe,KAAK,EAAE;YACxB;YAEA,uCAAuC;YACvC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,oBAAoB;gBAC7D,WAAW;gBACX,eAAe;gBACf,gBAAgB,OAAO,WAAW;gBAClC,kBAAkB,OAAO,aAAa;YACxC;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,AAAC,sCAA4C,OAAP,QAAO,MAAI;gBAC/D,0BAA0B;gBAC1B,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB,WAAW,KAAK,GAAG,KAAK,OAAO,aAAa,GAAG,KAAK;gBACtD;YACF;YAEA,MAAM,UAAU,SAAS;YACzB,MAAM,WAAW,OAAO,aAAa,GAAG,KAAK;YAC7C,MAAM,YAAY,KAAK,GAAG,KAAK;YAE/B,IAAI,CAAC,SAAS;gBACZ,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB;oBACA,YAAY,KAAK,IAAI,CAAC,WAAW;gBACnC;YACF;YAEA,OAAO;gBACL,SAAS;gBACT,mBAAmB,OAAO,WAAW,GAAG;gBACxC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,AAAC,+BAAqC,OAAP,QAAO,MAAI;YACxD,0BAA0B;YAC1B,OAAO;gBACL,SAAS;gBACT,mBAAmB;gBACnB,WAAW,KAAK,GAAG,KAAK;YAC1B;QACF;IACF;AACF;AAGO,MAAM,oBAAoB,IAAI;AAC9B,MAAM,oBAAoB,IAAI;AAK9B,eAAe,eACpB,MAAuB;QACvB,UAAA,iEAII,CAAC;IAQL,MAAM,EAAE,aAAa,KAAK,EAAE,aAAa,KAAK,EAAE,MAAM,EAAE,GAAG;IAE3D,IAAI;QACF,oBAAoB;QACpB,IAAI,CAAC,YAAY;YACf,MAAM,eAAe,MAAM,kBAAkB,UAAU,CAAC,QAAQ;YAChE,IAAI,CAAC,aAAa,OAAO,EAAE;gBACzB,OAAO;oBACL,GAAG,YAAY;oBACf,QAAQ;gBACV;YACF;YAEA,IAAI,YAAY;gBACd,OAAO;oBACL,GAAG,YAAY;oBACf,QAAQ;gBACV;YACF;QACF;QAEA,oBAAoB;QACpB,IAAI,CAAC,YAAY;YACf,MAAM,eAAe,MAAM,kBAAkB,UAAU,CAAC,QAAQ;YAChE,OAAO;gBACL,GAAG,YAAY;gBACf,QAAQ,aAAa,WAAW;YAClC;QACF;QAEA,kCAAkC;QAClC,OAAO;YACL,SAAS;YACT,mBAAmB;YACnB,WAAW,KAAK,GAAG,KAAK;YACxB,QAAQ;QACV;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,AAAC,+BAAqC,OAAP,QAAO,MAAI;QACxD,OAAO;YACL,SAAS;YACT,mBAAmB;YACnB,WAAW,KAAK,GAAG,KAAK;YACxB,QAAQ;QACV;IACF;AACF;AAKO,MAAM,uBAAuB;IAClC,YACE,AAAO,MAAuB,EAC9B,AAAO,UAAkB,EACzB,AAAO,SAAiB,CACxB;QACA,KAAK,CAAC,AAAC,2BAAkD,OAAxB,QAAO,mBAA4B,OAAX,YAAW,imBAJ7D,SAAA,aACA,aAAA,iBACA,YAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAKO,SAAS,aAAa,MAAuB;IAClD,OAAO;QACL,YAAY,CAAC,UACX,eAAe,QAAQ;QACzB,YAAY,IAAM,kBAAkB,UAAU,CAAC;QAC/C,WAAW,IAAM,kBAAkB,SAAS,CAAC;IAC/C;AACF", "debugId": null}}, {"offset": {"line": 848, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/hooks/use-projects.ts"], "sourcesContent": ["'use client'\r\n\r\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\r\nimport { Database } from '@/lib/supabase'\r\nimport { canCreateProject, updateUsageStats, PLAN_LIMIT_MESSAGES } from '@/lib/plan-limits'\r\nimport {\r\n  validateProjectName,\r\n  formatValidationError\r\n} from '@/lib/project-validation'\r\nimport { checkRateLimit, RateLimitError } from '@/lib/rate-limiter'\r\nimport { OptimizedQueries, QueryPerformanceMonitor } from '@/lib/database-optimization'\r\nimport { CACHE_CONFIGS } from '@/lib/cache-strategies'\r\n\r\ntype Project = Database['public']['Tables']['projects']['Row']\r\ntype ProjectInsert = Database['public']['Tables']['projects']['Insert']\r\ntype ProjectUpdate = Database['public']['Tables']['projects']['Update']\r\n\r\n// Projeleri getir\r\nexport function useProjects() {\r\n  return useQuery({\r\n    queryKey: ['projects'],\r\n    queryFn: async (): Promise<Project[]> => {\r\n      console.log(`📁 [USE_PROJECTS] Fetching projects...`)\r\n\r\n      try {\r\n        // Check if we have a session first\r\n        const { data: { session }, error: sessionError } = await supabase.auth.getSession()\r\n        console.log(`📁 [USE_PROJECTS] Session check:`, {\r\n          hasSession: !!session,\r\n          sessionError: sessionError?.message,\r\n          userId: session?.user?.id\r\n        })\r\n\r\n        const { data, error } = await supabase\r\n          .from('projects')\r\n          .select('*')\r\n          .order('created_at', { ascending: false })\r\n\r\n        if (error) {\r\n          console.error(`❌ [USE_PROJECTS] Error fetching projects:`, error)\r\n          throw new Error(error.message)\r\n        }\r\n\r\n        console.log(`✅ [USE_PROJECTS] Projects fetched:`, data?.length || 0, 'projects')\r\n        return data || []\r\n      } catch (err) {\r\n        console.error(`💥 [USE_PROJECTS] Exception:`, err)\r\n        throw err\r\n      }\r\n    },\r\n  })\r\n}\r\n\r\n// Tek proje getir\r\nexport function useProject(projectId: string | null) {\r\n  return useQuery({\r\n    queryKey: ['project', projectId],\r\n    queryFn: async (): Promise<Project | null> => {\r\n      if (!projectId) return null\r\n\r\n      const { data, error } = await supabase\r\n        .from('projects')\r\n        .select('*')\r\n        .eq('id', projectId)\r\n        .single()\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n\r\n      return data\r\n    },\r\n    enabled: !!projectId,\r\n  })\r\n}\r\n\r\n// Proje oluştur\r\nexport function useCreateProject() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async (project: Omit<ProjectInsert, 'user_id'>): Promise<Project> => {\r\n      // Plan limiti kontrol et\r\n      const limitCheck = await canCreateProject()\r\n      if (!limitCheck.allowed) {\r\n        throw new Error(limitCheck.reason || PLAN_LIMIT_MESSAGES.PROJECT_LIMIT_REACHED)\r\n      }\r\n\r\n      const { data: { user }, error: userError } = await supabase.auth.getUser()\r\n\r\n      if (userError || !user) {\r\n        throw new Error('Kullanıcı oturumu bulunamadı')\r\n      }\r\n\r\n      const { data, error } = await supabase\r\n        .from('projects')\r\n        .insert({\r\n          ...project,\r\n          user_id: user.id,\r\n        })\r\n        .select()\r\n        .single()\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n\r\n      return data\r\n    },\r\n    onSuccess: async () => {\r\n      queryClient.invalidateQueries({ queryKey: ['projects'] })\r\n      // Kullanım istatistiklerini güncelle\r\n      try {\r\n        await updateUsageStats()\r\n        queryClient.invalidateQueries({ queryKey: ['user-limits'] })\r\n        queryClient.invalidateQueries({ queryKey: ['usage-stats'] })\r\n      } catch (error) {\r\n        console.warn('Kullanım istatistikleri güncellenemedi:', error)\r\n      }\r\n    },\r\n  })\r\n}\r\n\r\n// Proje güncelle\r\nexport function useUpdateProject() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async ({ id, ...updates }: ProjectUpdate & { id: string }): Promise<Project> => {\r\n      console.log('🔄 [UPDATE_PROJECT] Starting update:', { id, updates })\r\n\r\n      // Önce mevcut kullanıcıyı kontrol et\r\n      const { data: user } = await supabase.auth.getUser()\r\n      if (!user.user) {\r\n        throw new Error('Kullanıcı girişi gerekli')\r\n      }\r\n\r\n      // Güncelleme işlemini yap - RLS politikaları otomatik olarak kontrol edecek\r\n      const { data, error } = await supabase\r\n        .from('projects')\r\n        .update({\r\n          ...updates,\r\n          updated_at: new Date().toISOString()\r\n        })\r\n        .eq('id', id)\r\n        .eq('user_id', user.user.id) // Ekstra güvenlik için user_id kontrolü\r\n        .select()\r\n        .single()\r\n\r\n      if (error) {\r\n        console.error('❌ [UPDATE_PROJECT] Database error:', error)\r\n        throw new Error(`Proje güncellenirken hata oluştu: ${error.message}`)\r\n      }\r\n\r\n      if (!data) {\r\n        throw new Error('Proje bulunamadı veya güncelleme yetkisi yok')\r\n      }\r\n\r\n      console.log('✅ [UPDATE_PROJECT] Success:', data)\r\n      return data\r\n    },\r\n    onSuccess: (data) => {\r\n      queryClient.invalidateQueries({ queryKey: ['projects'] })\r\n      queryClient.invalidateQueries({ queryKey: ['project', data.id] })\r\n    },\r\n    onError: (error) => {\r\n      console.error('❌ [UPDATE_PROJECT] Mutation error:', error)\r\n    }\r\n  })\r\n}\r\n\r\n// Güvenli proje adı güncelleme\r\nexport function useUpdateProjectNameSecure() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async ({\r\n      projectId,\r\n      newName\r\n    }: {\r\n      projectId: string;\r\n      newName: string\r\n    }): Promise<Project> => {\r\n      console.log(`🔒 [UPDATE_PROJECT_NAME] Starting secure update:`, { projectId, newName })\r\n\r\n      // Advanced rate limiting check\r\n      const rateLimitResult = await checkRateLimit('project_update')\r\n      if (!rateLimitResult.allowed) {\r\n        throw new RateLimitError(\r\n          'project_update',\r\n          rateLimitResult.retryAfter || 60,\r\n          rateLimitResult.resetTime\r\n        )\r\n      }\r\n\r\n      // Client-side validation\r\n      const validation = validateProjectName(newName)\r\n      if (!validation.isValid) {\r\n        throw new Error(validation.error || 'Geçersiz proje adı')\r\n      }\r\n\r\n      const sanitizedName = validation.sanitizedValue!\r\n\r\n      try {\r\n        // Güvenli database function'ını çağır\r\n        const { data, error } = await supabase.rpc('update_project_name_secure', {\r\n          p_project_id: projectId,\r\n          p_new_name: sanitizedName\r\n        })\r\n\r\n        if (error) {\r\n          console.error(`❌ [UPDATE_PROJECT_NAME] Database error:`, error)\r\n          throw new Error(formatValidationError(error.message))\r\n        }\r\n\r\n        // Function response kontrolü\r\n        if (!data?.success) {\r\n          console.error(`❌ [UPDATE_PROJECT_NAME] Function error:`, data)\r\n          throw new Error(data?.message || 'Proje adı güncellenemedi')\r\n        }\r\n\r\n        console.log(`✅ [UPDATE_PROJECT_NAME] Success:`, data.data)\r\n        return data.data as Project\r\n\r\n      } catch (err) {\r\n        console.error(`💥 [UPDATE_PROJECT_NAME] Exception:`, err)\r\n\r\n        // Error mesajını kullanıcı dostu hale getir\r\n        let errorMessage = err instanceof Error ? err.message : 'Bilinmeyen hata'\r\n\r\n        if (errorMessage.includes('unique_violation') || errorMessage.includes('DuplicateName')) {\r\n          errorMessage = 'Bu isimde bir proje zaten mevcut'\r\n        } else if (errorMessage.includes('check_violation') || errorMessage.includes('InvalidInput')) {\r\n          errorMessage = 'Proje adı geçersiz karakterler içeriyor'\r\n        } else if (errorMessage.includes('RateLimitExceeded')) {\r\n          errorMessage = 'Çok fazla güncelleme isteği. Lütfen bekleyin.'\r\n        }\r\n\r\n        throw new Error(errorMessage)\r\n      }\r\n    },\r\n    onMutate: async ({ projectId, newName }) => {\r\n      console.log(`🚀 [UPDATE_PROJECT_NAME] Starting optimistic update:`, { projectId, newName })\r\n\r\n      // Cancel outgoing refetches\r\n      await queryClient.cancelQueries({ queryKey: ['projects'] })\r\n      await queryClient.cancelQueries({ queryKey: ['project', projectId] })\r\n\r\n      // Snapshot previous values\r\n      const previousProjects = queryClient.getQueryData<Project[]>(['projects'])\r\n      const previousProject = queryClient.getQueryData<Project>(['project', projectId])\r\n\r\n      // Optimistically update projects list\r\n      if (previousProjects) {\r\n        queryClient.setQueryData<Project[]>(['projects'], (old) => {\r\n          if (!old) return old\r\n          return old.map(project =>\r\n            project.id === projectId\r\n              ? { ...project, name: newName.trim(), updated_at: new Date().toISOString() }\r\n              : project\r\n          )\r\n        })\r\n      }\r\n\r\n      // Optimistically update single project\r\n      if (previousProject) {\r\n        queryClient.setQueryData<Project>(['project', projectId], {\r\n          ...previousProject,\r\n          name: newName.trim(),\r\n          updated_at: new Date().toISOString()\r\n        })\r\n      }\r\n\r\n      return { previousProjects, previousProject }\r\n    },\r\n    onSuccess: (data) => {\r\n      console.log(`✅ [UPDATE_PROJECT_NAME] Success, updating cache with server data:`, data.id)\r\n\r\n      // Update with actual server data\r\n      queryClient.setQueryData<Project[]>(['projects'], (old) => {\r\n        if (!old) return old\r\n        return old.map(project =>\r\n          project.id === data.id ? { ...project, ...data } : project\r\n        )\r\n      })\r\n\r\n      queryClient.setQueryData<Project>(['project', data.id], data)\r\n    },\r\n    onError: (error, variables, context) => {\r\n      console.error(`💥 [UPDATE_PROJECT_NAME] Error, rolling back:`, error)\r\n\r\n      // Rollback optimistic updates\r\n      if (context?.previousProjects) {\r\n        queryClient.setQueryData(['projects'], context.previousProjects)\r\n      }\r\n      if (context?.previousProject) {\r\n        queryClient.setQueryData(['project', variables.projectId], context.previousProject)\r\n      }\r\n    },\r\n    onSettled: () => {\r\n      // Always refetch to ensure consistency\r\n      queryClient.invalidateQueries({ queryKey: ['projects'] })\r\n    }\r\n  })\r\n}\r\n\r\n// Proje sil\r\nexport function useDeleteProject() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async (projectId: string): Promise<void> => {\r\n      const { error } = await supabase\r\n        .from('projects')\r\n        .delete()\r\n        .eq('id', projectId)\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n    },\r\n    onSuccess: async () => {\r\n      queryClient.invalidateQueries({ queryKey: ['projects'] })\r\n      // Kullanım istatistiklerini güncelle\r\n      try {\r\n        await updateUsageStats()\r\n        queryClient.invalidateQueries({ queryKey: ['user-limits'] })\r\n        queryClient.invalidateQueries({ queryKey: ['usage-stats'] })\r\n      } catch (error) {\r\n        console.warn('Kullanım istatistikleri güncellenemedi:', error)\r\n      }\r\n    },\r\n  })\r\n} "], "names": [], "mappings": ";;;;;;;;AAEA;AAAA;AAAA;AACA;AAEA;AACA;AAIA;;AAVA;;;;;;AAmBO,SAAS;;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAW;QACtB,OAAO;oCAAE;gBACP,QAAQ,GAAG,CAAE;gBAEb,IAAI;wBAMQ;oBALV,mCAAmC;oBACnC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,UAAU;oBACjF,QAAQ,GAAG,CAAE,oCAAmC;wBAC9C,YAAY,CAAC,CAAC;wBACd,YAAY,EAAE,yBAAA,mCAAA,aAAc,OAAO;wBACnC,MAAM,EAAE,oBAAA,+BAAA,gBAAA,QAAS,IAAI,cAAb,oCAAA,cAAe,EAAE;oBAC3B;oBAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;wBAAE,WAAW;oBAAM;oBAE1C,IAAI,OAAO;wBACT,QAAQ,KAAK,CAAE,6CAA4C;wBAC3D,MAAM,IAAI,MAAM,MAAM,OAAO;oBAC/B;oBAEA,QAAQ,GAAG,CAAE,sCAAqC,CAAA,iBAAA,2BAAA,KAAM,MAAM,KAAI,GAAG;oBACrE,OAAO,QAAQ,EAAE;gBACnB,EAAE,OAAO,KAAK;oBACZ,QAAQ,KAAK,CAAE,gCAA+B;oBAC9C,MAAM;gBACR;YACF;;IACF;AACF;GAjCgB;;QACP,8KAAA,CAAA,WAAQ;;;AAmCV,SAAS,WAAW,SAAwB;;IACjD,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;SAAU;QAChC,OAAO;mCAAE;gBACP,IAAI,CAAC,WAAW,OAAO;gBAEvB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,WACT,MAAM;gBAET,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,OAAO;YACT;;QACA,SAAS,CAAC,CAAC;IACb;AACF;IApBgB;;QACP,8KAAA,CAAA,WAAQ;;;AAsBV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;4CAAE,OAAO;gBACjB,yBAAyB;gBACzB,MAAM,aAAa,MAAM,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD;gBACxC,IAAI,CAAC,WAAW,OAAO,EAAE;oBACvB,MAAM,IAAI,MAAM,WAAW,MAAM,IAAI,+HAAA,CAAA,sBAAmB,CAAC,qBAAqB;gBAChF;gBAEA,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;gBAExE,IAAI,aAAa,CAAC,MAAM;oBACtB,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC;oBACN,GAAG,OAAO;oBACV,SAAS,KAAK,EAAE;gBAClB,GACC,MAAM,GACN,MAAM;gBAET,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,OAAO;YACT;;QACA,SAAS;4CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,qCAAqC;gBACrC,IAAI;oBACF,MAAM,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD;oBACrB,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;yBAAc;oBAAC;oBAC1D,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;yBAAc;oBAAC;gBAC5D,EAAE,OAAO,OAAO;oBACd,QAAQ,IAAI,CAAC,2CAA2C;gBAC1D;YACF;;IACF;AACF;IA5CgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AA4Cb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;4CAAE;oBAAO,EAAE,EAAE,EAAE,GAAG,SAAyC;gBACnE,QAAQ,GAAG,CAAC,wCAAwC;oBAAE;oBAAI;gBAAQ;gBAElE,qCAAqC;gBACrC,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;gBAClD,IAAI,CAAC,KAAK,IAAI,EAAE;oBACd,MAAM,IAAI,MAAM;gBAClB;gBAEA,4EAA4E;gBAC5E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC;oBACN,GAAG,OAAO;oBACV,YAAY,IAAI,OAAO,WAAW;gBACpC,GACC,EAAE,CAAC,MAAM,IACT,EAAE,CAAC,WAAW,KAAK,IAAI,CAAC,EAAE,EAAE,wCAAwC;iBACpE,MAAM,GACN,MAAM;gBAET,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,sCAAsC;oBACpD,MAAM,IAAI,MAAM,AAAC,qCAAkD,OAAd,MAAM,OAAO;gBACpE;gBAEA,IAAI,CAAC,MAAM;oBACT,MAAM,IAAI,MAAM;gBAClB;gBAEA,QAAQ,GAAG,CAAC,+BAA+B;gBAC3C,OAAO;YACT;;QACA,SAAS;4CAAE,CAAC;gBACV,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAW,KAAK,EAAE;qBAAC;gBAAC;YACjE;;QACA,OAAO;4CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,sCAAsC;YACtD;;IACF;AACF;IA7CgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AA6Cb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;sDAAE;oBAAO,EACjB,SAAS,EACT,OAAO,EAIR;gBACC,QAAQ,GAAG,CAAE,oDAAmD;oBAAE;oBAAW;gBAAQ;gBAErF,+BAA+B;gBAC/B,MAAM,kBAAkB,MAAM,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD,EAAE;gBAC7C,IAAI,CAAC,gBAAgB,OAAO,EAAE;oBAC5B,MAAM,IAAI,gIAAA,CAAA,iBAAc,CACtB,kBACA,gBAAgB,UAAU,IAAI,IAC9B,gBAAgB,SAAS;gBAE7B;gBAEA,yBAAyB;gBACzB,MAAM,aAAa,CAAA,GAAA,sIAAA,CAAA,sBAAmB,AAAD,EAAE;gBACvC,IAAI,CAAC,WAAW,OAAO,EAAE;oBACvB,MAAM,IAAI,MAAM,WAAW,KAAK,IAAI;gBACtC;gBAEA,MAAM,gBAAgB,WAAW,cAAc;gBAE/C,IAAI;oBACF,sCAAsC;oBACtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,8BAA8B;wBACvE,cAAc;wBACd,YAAY;oBACd;oBAEA,IAAI,OAAO;wBACT,QAAQ,KAAK,CAAE,2CAA0C;wBACzD,MAAM,IAAI,MAAM,CAAA,GAAA,sIAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM,OAAO;oBACrD;oBAEA,6BAA6B;oBAC7B,IAAI,EAAC,iBAAA,2BAAA,KAAM,OAAO,GAAE;wBAClB,QAAQ,KAAK,CAAE,2CAA0C;wBACzD,MAAM,IAAI,MAAM,CAAA,iBAAA,2BAAA,KAAM,OAAO,KAAI;oBACnC;oBAEA,QAAQ,GAAG,CAAE,oCAAmC,KAAK,IAAI;oBACzD,OAAO,KAAK,IAAI;gBAElB,EAAE,OAAO,KAAK;oBACZ,QAAQ,KAAK,CAAE,uCAAsC;oBAErD,4CAA4C;oBAC5C,IAAI,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;oBAExD,IAAI,aAAa,QAAQ,CAAC,uBAAuB,aAAa,QAAQ,CAAC,kBAAkB;wBACvF,eAAe;oBACjB,OAAO,IAAI,aAAa,QAAQ,CAAC,sBAAsB,aAAa,QAAQ,CAAC,iBAAiB;wBAC5F,eAAe;oBACjB,OAAO,IAAI,aAAa,QAAQ,CAAC,sBAAsB;wBACrD,eAAe;oBACjB;oBAEA,MAAM,IAAI,MAAM;gBAClB;YACF;;QACA,QAAQ;sDAAE;oBAAO,EAAE,SAAS,EAAE,OAAO,EAAE;gBACrC,QAAQ,GAAG,CAAE,wDAAuD;oBAAE;oBAAW;gBAAQ;gBAEzF,4BAA4B;gBAC5B,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACzD,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU;wBAAC;wBAAW;qBAAU;gBAAC;gBAEnE,2BAA2B;gBAC3B,MAAM,mBAAmB,YAAY,YAAY,CAAY;oBAAC;iBAAW;gBACzE,MAAM,kBAAkB,YAAY,YAAY,CAAU;oBAAC;oBAAW;iBAAU;gBAEhF,sCAAsC;gBACtC,IAAI,kBAAkB;oBACpB,YAAY,YAAY,CAAY;wBAAC;qBAAW;kEAAE,CAAC;4BACjD,IAAI,CAAC,KAAK,OAAO;4BACjB,OAAO,IAAI,GAAG;0EAAC,CAAA,UACb,QAAQ,EAAE,KAAK,YACX;wCAAE,GAAG,OAAO;wCAAE,MAAM,QAAQ,IAAI;wCAAI,YAAY,IAAI,OAAO,WAAW;oCAAG,IACzE;;wBAER;;gBACF;gBAEA,uCAAuC;gBACvC,IAAI,iBAAiB;oBACnB,YAAY,YAAY,CAAU;wBAAC;wBAAW;qBAAU,EAAE;wBACxD,GAAG,eAAe;wBAClB,MAAM,QAAQ,IAAI;wBAClB,YAAY,IAAI,OAAO,WAAW;oBACpC;gBACF;gBAEA,OAAO;oBAAE;oBAAkB;gBAAgB;YAC7C;;QACA,SAAS;sDAAE,CAAC;gBACV,QAAQ,GAAG,CAAE,qEAAoE,KAAK,EAAE;gBAExF,iCAAiC;gBACjC,YAAY,YAAY,CAAY;oBAAC;iBAAW;8DAAE,CAAC;wBACjD,IAAI,CAAC,KAAK,OAAO;wBACjB,OAAO,IAAI,GAAG;sEAAC,CAAA,UACb,QAAQ,EAAE,KAAK,KAAK,EAAE,GAAG;oCAAE,GAAG,OAAO;oCAAE,GAAG,IAAI;gCAAC,IAAI;;oBAEvD;;gBAEA,YAAY,YAAY,CAAU;oBAAC;oBAAW,KAAK,EAAE;iBAAC,EAAE;YAC1D;;QACA,OAAO;sDAAE,CAAC,OAAO,WAAW;gBAC1B,QAAQ,KAAK,CAAE,iDAAgD;gBAE/D,8BAA8B;gBAC9B,IAAI,oBAAA,8BAAA,QAAS,gBAAgB,EAAE;oBAC7B,YAAY,YAAY,CAAC;wBAAC;qBAAW,EAAE,QAAQ,gBAAgB;gBACjE;gBACA,IAAI,oBAAA,8BAAA,QAAS,eAAe,EAAE;oBAC5B,YAAY,YAAY,CAAC;wBAAC;wBAAW,UAAU,SAAS;qBAAC,EAAE,QAAQ,eAAe;gBACpF;YACF;;QACA,SAAS;sDAAE;gBACT,uCAAuC;gBACvC,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;YACzD;;IACF;AACF;IApIgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAoIb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;4CAAE,OAAO;gBACjB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAC7B,IAAI,CAAC,YACL,MAAM,GACN,EAAE,CAAC,MAAM;gBAEZ,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;YACF;;QACA,SAAS;4CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,qCAAqC;gBACrC,IAAI;oBACF,MAAM,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD;oBACrB,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;yBAAc;oBAAC;oBAC1D,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;yBAAc;oBAAC;gBAC5D,EAAE,OAAO,OAAO;oBACd,QAAQ,IAAI,CAAC,2CAA2C;gBAC1D;YACF;;IACF;AACF;IA1BgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 1281, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/context-sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, Suspense, lazy } from \"react\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Separator } from \"@/components/ui/separator\";\nimport { ContextGallerySkeleton } from \"@/components/progressive-loader\";\n\nimport { FileText, Save, Trash2, Settings, AlertTriangle, ToggleLeft, ToggleRight, X, ChevronLeft, ChevronRight, Grid3X3 } from \"lucide-react\";\nimport { useAppStore } from \"@/store/app-store\";\nimport { useProject, useUpdateProject, useDeleteProject } from \"@/hooks/use-projects\";\n\n\n\ninterface ContextSidebarProps {\n  onClose?: () => void;\n  isCollapsed?: boolean;\n  onToggleCollapse?: () => void;\n  isContextGalleryOpen?: boolean;\n  onToggleContextGallery?: () => void;\n}\n\nexport function ContextSidebar({\n  onClose,\n  isCollapsed = false,\n  onToggleCollapse,\n  isContextGalleryOpen = false,\n  onToggleContextGallery\n}: ContextSidebarProps) {\n  const [contextText, setContextText] = useState(\"\");\n  const [isSaving, setIsSaving] = useState(false);\n  const [lastSaved, setLastSaved] = useState<Date | null>(null);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  const [deleteConfirmText, setDeleteConfirmText] = useState(\"\");\n  const { activeProjectId, setActiveProjectId, isContextEnabled, setIsContextEnabled } = useAppStore();\n  \n  const { data: project } = useProject(activeProjectId);\n  const updateProjectMutation = useUpdateProject();\n  const deleteProjectMutation = useDeleteProject();\n\n\n  // Aktif proje değiştiğinde context'i yükle\n  useEffect(() => {\n    if (project) {\n      setContextText(project.context_text || \"\");\n    } else {\n      setContextText(\"\");\n    }\n  }, [project]);\n\n  // Otomatik kayıt devre dışı bırakıldı - artık sadece manuel kayıt\n  // useEffect(() => {\n  //   if (!activeProjectId || !contextText.trim()) return;\n\n  //   const timer = setTimeout(() => {\n  //     handleSaveContext();\n  //   }, 2000);\n\n  //   return () => clearTimeout(timer);\n  // // eslint-disable-next-line react-hooks/exhaustive-deps\n  // }, [contextText, activeProjectId]);\n\n  const handleSaveContext = async () => {\n    if (!activeProjectId) return;\n\n    setIsSaving(true);\n    try {\n      await updateProjectMutation.mutateAsync({\n        id: activeProjectId,\n        context_text: contextText,\n      });\n      \n      setLastSaved(new Date());\n    } catch (error) {\n      console.error('Context kaydetme hatası:', error);\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const handleClearContext = () => {\n    setContextText(\"\");\n    setLastSaved(null);\n  };\n\n  const handleManualSave = () => {\n    handleSaveContext();\n  };\n\n  const handleDeleteProject = async () => {\n    if (!activeProjectId || !project) return;\n\n    // Proje adı kontrolü\n    if (deleteConfirmText !== project.name) {\n      alert('Proje adını doğru yazın!');\n      return;\n    }\n\n    try {\n      await deleteProjectMutation.mutateAsync(activeProjectId);\n      setActiveProjectId(null);\n      setShowDeleteConfirm(false);\n      setDeleteConfirmText(\"\");\n    } catch (error) {\n      console.error('Proje silme hatası:', error);\n      alert('Proje silinirken bir hata oluştu!');\n    }\n  };\n\n  const handleCancelDelete = () => {\n    setShowDeleteConfirm(false);\n    setDeleteConfirmText(\"\");\n  };\n\n\n\n  if (!activeProjectId) {\n    return (\n      <div className=\"flex items-center justify-center h-full p-6\">\n        <div className=\"text-center\">\n          <FileText className={`${isCollapsed ? 'h-6 w-6' : 'h-12 w-12'} text-gray-400 mx-auto mb-4`} />\n          {!isCollapsed && (\n            <>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Ayarlar</h3>\n              <p className=\"text-gray-500 text-sm\">Proje seçtikten sonra ayarları buradan yönetebilirsiniz</p>\n            </>\n          )}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex flex-col h-full\">\n      {/* Header */}\n      <div className={`border-b border-gray-200 ${isCollapsed ? 'p-2' : 'p-4 lg:p-6'}`}>\n        <div className={`flex items-center ${isCollapsed ? 'justify-center mb-2' : 'justify-between mb-4'}`}>\n          <div className={`flex items-center gap-2 ${isCollapsed ? 'flex-col' : ''}`}>\n            {/* Desktop Toggle Button */}\n            {onToggleCollapse && (\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={onToggleCollapse}\n                className=\"hidden xl:flex\"\n                title={isCollapsed ? 'Ayarlar panelini genişlet' : 'Ayarlar panelini daralt'}\n              >\n                {isCollapsed ? <ChevronLeft className=\"h-4 w-4\" /> : <ChevronRight className=\"h-4 w-4\" />}\n              </Button>\n            )}\n            {!isCollapsed && <h3 className=\"text-lg font-semibold text-gray-900\">Context Ayarları</h3>}\n          </div>\n          <div className={`flex items-center gap-2 ${isCollapsed ? 'flex-col' : ''}`}>\n            {/* Mobile Close Button */}\n            {onClose && (\n              <Button variant=\"ghost\" size=\"sm\" onClick={onClose} className=\"xl:hidden\">\n                <X className=\"h-4 w-4\" />\n              </Button>\n            )}\n          </div>\n        </div>\n\n\n      </div>\n\n      {/* Context Editor */}\n      <div className={`flex-1 ${isCollapsed ? 'p-2' : 'p-6'}`}>\n        {isCollapsed ? (\n          <div className=\"flex flex-col items-center space-y-2\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setIsContextEnabled(!isContextEnabled)}\n              className={`w-8 h-8 p-0 rounded-md transition-all ${\n                isContextEnabled\n                  ? 'bg-green-100 text-green-700 hover:bg-green-200'\n                  : 'bg-gray-100 text-gray-500 hover:bg-gray-200'\n              }`}\n              title={`Context ${isContextEnabled ? 'Aktif' : 'Pasif'}`}\n            >\n              {isContextEnabled ? (\n                <ToggleRight className=\"h-4 w-4\" />\n              ) : (\n                <ToggleLeft className=\"h-4 w-4\" />\n              )}\n            </Button>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={handleSaveContext}\n              disabled={isSaving}\n              className=\"w-8 h-8 p-0\"\n              title=\"Context Kaydet\"\n            >\n              <Save className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n          {/* Context Gallery Button */}\n          <div className=\"mb-4\">\n            <Button\n              variant={isContextGalleryOpen ? \"default\" : \"outline\"}\n              size=\"sm\"\n              onClick={onToggleContextGallery}\n              className={`w-full justify-center transition-all duration-200 ${\n                isContextGalleryOpen\n                  ? \"bg-purple-600 hover:bg-purple-700 text-white shadow-md\"\n                  : \"border-purple-200 text-purple-600 hover:text-purple-700 hover:border-purple-300 hover:bg-purple-50\"\n              }`}\n            >\n              <Grid3X3 className=\"h-4 w-4 mr-2\" />\n              Context Gallery\n            </Button>\n          </div>\n\n          <div className=\"flex items-center justify-between mb-3\">\n            <div className=\"flex items-center gap-2\">\n              <label className=\"text-sm font-medium text-gray-700\">\n                Ön Tanımlı Metin\n              </label>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => setIsContextEnabled(!isContextEnabled)}\n                className={`flex items-center gap-2 px-3 py-1 rounded-md transition-all ${\n                  isContextEnabled \n                    ? 'bg-green-100 text-green-700 hover:bg-green-200' \n                    : 'bg-gray-100 text-gray-500 hover:bg-gray-200'\n                }`}\n              >\n                {isContextEnabled ? (\n                  <ToggleRight className=\"h-4 w-4\" />\n                ) : (\n                  <ToggleLeft className=\"h-4 w-4\" />\n                )}\n                <span className=\"text-xs font-medium\">\n                  {isContextEnabled ? 'Aktif' : 'Pasif'}\n                </span>\n              </Button>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              {isSaving && (\n                <Badge variant=\"secondary\" className=\"text-xs\">\n                  Kaydediliyor...\n                </Badge>\n              )}\n              {lastSaved && (\n                <span className=\"text-xs text-gray-500\">\n                  Son kaydedilme: {lastSaved.toLocaleTimeString('tr-TR')}\n                </span>\n              )}\n            </div>\n          </div>\n\n\n\n          <Textarea\n            placeholder=\"Tüm promptların başına eklenecek context metninizi buraya yazın...\"\n            value={contextText}\n            onChange={(e) => setContextText(e.target.value)}\n            className=\"min-h-[200px] resize-none\"\n            disabled={isSaving}\n          />\n\n          <div className=\"flex gap-2\">\n            <Button\n              variant=\"default\"\n              size=\"sm\"\n              onClick={handleManualSave}\n              disabled={isSaving || !contextText.trim()}\n              className=\"bg-blue-600 hover:bg-blue-700 text-white\"\n            >\n              <Save className=\"h-4 w-4 mr-2\" />\n              {isSaving ? 'Kaydediliyor...' : 'Kaydet'}\n            </Button>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={handleClearContext}\n              disabled={isSaving}\n              className=\"text-red-600 hover:text-red-700\"\n            >\n              <Trash2 className=\"h-4 w-4 mr-2\" />\n              Temizle\n            </Button>\n          </div>\n\n          <Separator className=\"my-6\" />\n\n          {/* Project Settings */}\n          <Card>\n          <CardHeader>\n            <CardTitle className=\"text-base\">Proje Ayarları</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-3\">\n\n            {/* Character Count Display */}\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-600\">Karakter Sayısı</span>\n              <Badge variant=\"outline\">\n                {contextText.length.toLocaleString()}\n              </Badge>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-600\">Proje Adı</span>\n              <Badge variant=\"outline\">{project?.name}</Badge>\n            </div>\n            <Separator />\n            \n            {/* Proje Silme Bölümü */}\n            {!showDeleteConfirm ? (\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                className=\"w-full text-red-600 hover:text-red-700 hover:bg-red-50\"\n                onClick={() => setShowDeleteConfirm(true)}\n              >\n                <Trash2 className=\"h-4 w-4 mr-2\" />\n                Projeyi Sil\n              </Button>\n            ) : (\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md\">\n                  <AlertTriangle className=\"h-4 w-4 text-red-600 flex-shrink-0\" />\n                  <div>\n                    <p className=\"text-sm text-red-800 font-medium\">Dikkat!</p>\n                    <p className=\"text-xs text-red-700\">\n                      Bu işlem geri alınamaz. Proje ve tüm prompt&apos;lar silinecek.\n                    </p>\n                  </div>\n                </div>\n                \n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium text-gray-700\">\n                    Onaylamak için proje adını yazın: <span className=\"text-red-600 font-semibold\">{project?.name}</span>\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={deleteConfirmText}\n                    onChange={(e) => setDeleteConfirmText(e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500\"\n                    placeholder=\"Proje adını yazın...\"\n                  />\n                </div>\n                \n                <div className=\"flex gap-2\">\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={handleDeleteProject}\n                    disabled={deleteConfirmText !== project?.name || deleteProjectMutation.isPending}\n                    className=\"flex-1 text-red-600 hover:text-red-700 hover:bg-red-50\"\n                  >\n                    <Trash2 className=\"h-4 w-4 mr-2\" />\n                    {deleteProjectMutation.isPending ? 'Siliniyor...' : 'Sil'}\n                  </Button>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={handleCancelDelete}\n                    className=\"flex-1\"\n                  >\n                    İptal\n                  </Button>\n                </div>\n              </div>\n            )}\n          </CardContent>\n        </Card>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\nexport default ContextSidebar;"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AAZA;;;;;;;;;;AAwBO,SAAS,eAAe,KAMT;QANS,EAC7B,OAAO,EACP,cAAc,KAAK,EACnB,gBAAgB,EAChB,uBAAuB,KAAK,EAC5B,sBAAsB,EACF,GANS;;IAO7B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,EAAE,eAAe,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD;IAEjG,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,aAAU,AAAD,EAAE;IACrC,MAAM,wBAAwB,CAAA,GAAA,kIAAA,CAAA,mBAAgB,AAAD;IAC7C,MAAM,wBAAwB,CAAA,GAAA,kIAAA,CAAA,mBAAgB,AAAD;IAG7C,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,SAAS;gBACX,eAAe,QAAQ,YAAY,IAAI;YACzC,OAAO;gBACL,eAAe;YACjB;QACF;mCAAG;QAAC;KAAQ;IAEZ,kEAAkE;IAClE,oBAAoB;IACpB,yDAAyD;IAEzD,qCAAqC;IACrC,2BAA2B;IAC3B,cAAc;IAEd,sCAAsC;IACtC,0DAA0D;IAC1D,sCAAsC;IAEtC,MAAM,oBAAoB;QACxB,IAAI,CAAC,iBAAiB;QAEtB,YAAY;QACZ,IAAI;YACF,MAAM,sBAAsB,WAAW,CAAC;gBACtC,IAAI;gBACJ,cAAc;YAChB;YAEA,aAAa,IAAI;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,qBAAqB;QACzB,eAAe;QACf,aAAa;IACf;IAEA,MAAM,mBAAmB;QACvB;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,mBAAmB,CAAC,SAAS;QAElC,qBAAqB;QACrB,IAAI,sBAAsB,QAAQ,IAAI,EAAE;YACtC,MAAM;YACN;QACF;QAEA,IAAI;YACF,MAAM,sBAAsB,WAAW,CAAC;YACxC,mBAAmB;YACnB,qBAAqB;YACrB,qBAAqB;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM;QACR;IACF;IAEA,MAAM,qBAAqB;QACzB,qBAAqB;QACrB,qBAAqB;IACvB;IAIA,IAAI,CAAC,iBAAiB;QACpB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,iNAAA,CAAA,WAAQ;wBAAC,WAAW,AAAC,GAAwC,OAAtC,cAAc,YAAY,aAAY;;;;;;oBAC7D,CAAC,6BACA;;0CACE,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;IAMjD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAW,AAAC,4BAA8D,OAAnC,cAAc,QAAQ;0BAChE,cAAA,6LAAC;oBAAI,WAAW,AAAC,qBAAiF,OAA7D,cAAc,wBAAwB;;sCACzE,6LAAC;4BAAI,WAAW,AAAC,2BAAwD,OAA9B,cAAc,aAAa;;gCAEnE,kCACC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,OAAO,cAAc,8BAA8B;8CAElD,4BAAc,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;6DAAe,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;gCAGhF,CAAC,6BAAe,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;;;;;;;sCAEvE,6LAAC;4BAAI,WAAW,AAAC,2BAAwD,OAA9B,cAAc,aAAa;sCAEnE,yBACC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,SAAS;gCAAS,WAAU;0CAC5D,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvB,6LAAC;gBAAI,WAAW,AAAC,UAAqC,OAA5B,cAAc,QAAQ;0BAC7C,4BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAW,AAAC,yCAIX,OAHC,mBACI,mDACA;4BAEN,OAAO,AAAC,WAA+C,OAArC,mBAAmB,UAAU;sCAE9C,iCACC,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAEvB,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;sCAG1B,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,UAAU;4BACV,WAAU;4BACV,OAAM;sCAEN,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;yCAIpB,6LAAC;oBAAI,WAAU;;sCAEf,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS,uBAAuB,YAAY;gCAC5C,MAAK;gCACL,SAAS;gCACT,WAAW,AAAC,qDAIX,OAHC,uBACI,2DACA;;kDAGN,6LAAC,+MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;sCAKxC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAAoC;;;;;;sDAGrD,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,oBAAoB,CAAC;4CACpC,WAAW,AAAC,+DAIX,OAHC,mBACI,mDACA;;gDAGL,iCACC,6LAAC,uNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;yEAEvB,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DAExB,6LAAC;oDAAK,WAAU;8DACb,mBAAmB,UAAU;;;;;;;;;;;;;;;;;;8CAIpC,6LAAC;oCAAI,WAAU;;wCACZ,0BACC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAAU;;;;;;wCAIhD,2BACC,6LAAC;4CAAK,WAAU;;gDAAwB;gDACrB,UAAU,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;sCAQtD,6LAAC,uIAAA,CAAA,WAAQ;4BACP,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4BAC9C,WAAU;4BACV,UAAU;;;;;;sCAGZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,UAAU,YAAY,CAAC,YAAY,IAAI;oCACvC,WAAU;;sDAEV,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,WAAW,oBAAoB;;;;;;;8CAElC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAKvC,6LAAC,wIAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCAGrB,6LAAC,mIAAA,CAAA,OAAI;;8CACL,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAY;;;;;;;;;;;8CAEnC,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDAGrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DACZ,YAAY,MAAM,CAAC,cAAc;;;;;;;;;;;;sDAGtC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW,oBAAA,8BAAA,QAAS,IAAI;;;;;;;;;;;;sDAEzC,6LAAC,wIAAA,CAAA,YAAS;;;;;wCAGT,CAAC,kCACA,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,qBAAqB;;8DAEpC,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;iEAIrC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,2NAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;sEACzB,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAmC;;;;;;8EAChD,6LAAC;oEAAE,WAAU;8EAAuB;;;;;;;;;;;;;;;;;;8DAMxC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAM,WAAU;;gEAAoC;8EACjB,6LAAC;oEAAK,WAAU;8EAA8B,oBAAA,8BAAA,QAAS,IAAI;;;;;;;;;;;;sEAE/F,6LAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;4DACpD,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAIhB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS;4DACT,UAAU,uBAAsB,oBAAA,8BAAA,QAAS,IAAI,KAAI,sBAAsB,SAAS;4DAChF,WAAU;;8EAEV,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEACjB,sBAAsB,SAAS,GAAG,iBAAiB;;;;;;;sEAEtD,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS;4DACT,WAAU;sEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAanB;GAjWgB;;QAYyE,+HAAA,CAAA,cAAW;QAExE,kIAAA,CAAA,aAAU;QACN,kIAAA,CAAA,mBAAgB;QAChB,kIAAA,CAAA,mBAAgB;;;KAhBhC;uCAmWD", "debugId": null}}]}